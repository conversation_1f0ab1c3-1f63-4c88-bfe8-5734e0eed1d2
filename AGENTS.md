# Repository Guidelines

## Components & Conventions
- Naming: Vue SFCs PascalCase (e.g., `AppBadge.vue`, `InvoiceReviewDialog.vue`). Directories kebab-case. Utilities `*.util.ts`; composables `*.composable.ts`.
- Props/Emits: Use `defineProps`/`withDefaults` and typed `defineEmits`. Prefer explicit booleans and enums (see `BadgeColor`).
- Test IDs: Expose `:data-test-id` bound to constants in `src/constants/testId.constant.ts` (e.g., `:test-id="TEST_ID.SHARED.CONFIRM_DIALOG.DIALOG"`). Never hardcode strings in templates.
- UI Kits: Prefer `@wisemen/vue-core-components` for primitives (`VcDialog`, `VcTextField`, `VcTable*`) and `motion-v` for transitions. Configure visuals via `:class-config` and Tailwind utility classes.
- Styling: 2-space indent, Tailwind utilities for layout/spacing/typography. Add scoped CSS only when utilities are insufficient.
- i18n: Use `useI18n()` and snake_case keys (rule enforced). Example: `i18n.t('module.invoice.overview.title')`.

## Logic & State
- Composables: Encapsulate behavior; name with verbs (e.g., `useDebounceSearch`). Return refs/computed and minimal methods; avoid side effects. Example: `useDebounceSearch({ onDebounceSearch: cb })` provides `search`, `debouncedSearch`, `isDebouncing`.
- Tables: Follow `DataTable.vue` generics and provide `columns`, `data`, `getKey`, optional `rowAction`, `filters`, `search`, `sort`, and `onNextPage`. Derive state with `computed`; use `useInfiniteScroll` for pagination and provide context via `useProvideDataTableContext`.
- Stores: Use Pinia `defineStore` (e.g., `auth.store.ts`). Keep public API small (`getAuthUser`, `hasPermission`, `logout`), and integrate Sentry/user context where required.
- Forms/Mutations: Use `formango` for form state/validation and explicit mutation composables in `modules/**/api/mutations`.

## Testing
- Unit: Co-locate `*.spec.ts` with source (Vitest, jsdom). Keep tests deterministic; use MSW where needed.
- E2E: `tests/**.spec.ts` (Playwright). Prefer `data-test-id` selectors and reuse `TEST_ID` constants.

## Build & Workflow
- Commands: `pnpm dev`, `pnpm build`, `pnpm preview`, `pnpm test:unit`, `pnpm test:e2e`, `pnpm test:coverage`, `pnpm lint`, `pnpm type-check`.
- Commits/PRs: Imperative subject with ticket (e.g., `IND-599: …`). Use the PR template; include tests and screenshots for UI.
