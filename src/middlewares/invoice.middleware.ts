import { Permission } from '@/client'
import { useAuthStore } from '@/stores/auth.store'
import { MiddlewareUtil } from '@/utils/middleware.util'

export const invoiceAccessMiddleware = MiddlewareUtil.createMiddleware(() => {
  const authStore = useAuthStore()

  const hasReadPermission = authStore.hasPermission(Permission.INVOICE_READ)
  const hasManagePermission = authStore.hasPermission(Permission.INVOICE_MANAGE)

  if (!hasReadPermission && !hasManagePermission) {
    return { name: 'index' }
  }
})

export const invoiceReadMiddleware = MiddlewareUtil.createMiddleware(() => {
  const authStore = useAuthStore()

  const hasReadPermission = authStore.hasPermission(Permission.INVOICE_READ)
  const hasManagePermission = authStore.hasPermission(Permission.INVOICE_MANAGE)

  if (!hasReadPermission && !hasManagePermission) {
    return { name: 'index' }
  }
})

export const invoiceManageMiddleware = MiddlewareUtil.createMiddleware(() => {
  const authStore = useAuthStore()

  if (!authStore.hasPermission(Permission.INVOICE_MANAGE)) {
    if (authStore.hasPermission(Permission.INVOICE_READ)) {
      return { name: 'invoices-overview-sent' }
    }

    return { name: 'index' }
  }
})
