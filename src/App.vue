<script setup lang="ts">
import {
  VcConfigProvider,
  VcDialogContainer,
  VcThemeProvider,
  VcToastContainer,
} from '@wisemen/vue-core-components'
import { VcLayoutStackRoot } from '@wisemen/vue-core-modules'
import { useI18n } from 'vue-i18n'
import {
  RouterView,
  useRouter,
} from 'vue-router'

import AppPageLoader from '@/components/app/loader/AppPageLoader.vue'
import { useAutoRefresh } from '@/composables/auto-refresh/autoRefresh.composable.ts'
import { useDocumentTitle } from '@/composables/document-title/documentTitle.composable'
import { logBuildInformation } from '@/constants/environment.constant.ts'
import {
  useFontSizePreference,
  useLanguagePreference,
  usePreferences,
} from '@/modules/settings/composables/preferences.composable'
import { useAuthStore } from '@/stores/auth.store.ts'

const i18n = useI18n()
const router = useRouter()
const authStore = useAuthStore()
const documentTitle = useDocumentTitle()

documentTitle.setTemplate('{title} | Indaver')

useAutoRefresh()
useLanguagePreference()
useFontSizePreference()

const preferences = usePreferences()

logBuildInformation()

authStore.onLogout(() => {
  router.replace({ name: 'auth-login' })
})
</script>

<template>
  <VcConfigProvider
    :are-keyboard-shortcut-hints-hidden="!preferences.enableKeyboardShortcutHints"
    :locale="i18n.locale.value"
    :auto-close-toast="{
      error: true,
      success: true,
      info: true,
    }"
    teleport-target-selector="body"
  >
    <VcThemeProvider
      :theme="preferences.enableHighContrast ? 'high-contrast' : 'default'"
      appearance="light"
      class="flex size-full flex-1 flex-col overflow-hidden"
    >
      <RouterView />

      <AppPageLoader />

      <VcLayoutStackRoot>
        <VcDialogContainer />
      </VcLayoutStackRoot>

      <div id="teleport-target" />
      <VcToastContainer />
    </VcThemeProvider>
  </VcConfigProvider>
</template>
