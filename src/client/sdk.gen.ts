// This file is auto-generated by @hey-api/openapi-ts

import type { Options as ClientOptions, Client, TDataShape } from './client';
import type { MigrateCollectionsV1Data, MigrateCollectionsV1Responses, MigrateCollectionsV1Errors, ImportCollectionsV1Data, ImportCollectionsV1Responses, ImportCollectionsV1Errors, ViewCollectionsV1Data, ViewCollectionsV1Responses, ViewCollectionsV1Errors, ViewMeV1Data, ViewMeV1Responses, ViewMeV1Errors, ViewUserDetailV1Data, ViewUserDetailV1Responses, ViewUserDetailV1Errors, ViewUserIndexV1Data, ViewUserIndexV1Responses, ViewUserIndexV1Errors, SyncEntraUsersV1Data, SyncEntraUsersV1Responses, SyncEntraUsersV1Errors, StartUserImpersonationV1Data, StartUserImpersonationV1Responses, StartUserImpersonationV1Errors, ViewPermissionIndexV1Data, ViewPermissionIndexV1Responses, ViewPermissionIndexV1Errors, ViewCustomerIndexV1Data, ViewCustomerIndexV1Responses, ViewCustomerIndexV1Errors, CreateFileV1Data, CreateFileV1Responses, CreateFileV1Errors, ConfirmFileUploadV1Data, ConfirmFileUploadV1Responses, ConfirmFileUploadV1Errors, DownloadFileV1Data, DownloadFileV1Errors, ProxyExternalFileV1Data, ProxyExternalFileV1Responses, ProxyExternalFileV1Errors, ViewSuggestedCustomersV1Data, ViewSuggestedCustomersV1Responses, ViewSuggestedCustomersV1Errors, ViewCustomerCountryV1Data, ViewCustomerCountryV1Responses, ViewCustomerCountryV1Errors, ViewDomainEventLogIndexV1Data, ViewDomainEventLogIndexV1Responses, ViewDomainEventLogIndexV1Errors, SearchCollectionsV1Data, SearchCollectionsV1Responses, SearchCollectionsV1Errors, ViewJobsIndexV1Data, ViewJobsIndexV1Responses, ViewJobsIndexV1Errors, ViewJobDetailV1Data, ViewJobDetailV1Responses, ViewJobDetailV1Errors, GetMyNotificationPreferencesV1Data, GetMyNotificationPreferencesV1Responses, GetMyNotificationPreferencesV1Errors, GetNotificationTypesConfigV1Data, GetNotificationTypesConfigV1Responses, GetNotificationTypesConfigV1Errors, UpdateMyChannelNotificationPreferenceV1Data, UpdateMyChannelNotificationPreferenceV1Responses, UpdateMyChannelNotificationPreferenceV1Errors, SendTestNotificationV1Data, SendTestNotificationV1Responses, SendTestNotificationV1Errors, GetMyNotificationsV1Data, GetMyNotificationsV1Responses, GetMyNotificationsV1Errors, ViewUnreadNotificationsCountV1Data, ViewUnreadNotificationsCountV1Responses, ViewUnreadNotificationsCountV1Errors, ViewUserNotificationDetailV1Data, ViewUserNotificationDetailV1Responses, ViewUserNotificationDetailV1Errors, MarkAllNotificationAsReadV1Data, MarkAllNotificationAsReadV1Responses, MarkAllNotificationAsReadV1Errors, UpdateMyNotificationTypePreferenceV1Data, UpdateMyNotificationTypePreferenceV1Responses, UpdateMyNotificationTypePreferenceV1Errors, MarkNotificationAsReadV1Data, MarkNotificationAsReadV1Responses, MarkNotificationAsReadV1Errors, MarkNotificationAsUnreadV1Data, MarkNotificationAsUnreadV1Responses, MarkNotificationAsUnreadV1Errors, UpdateMyNotificationPreferencePresetV1Data, UpdateMyNotificationPreferencePresetV1Responses, UpdateMyNotificationPreferencePresetV1Errors, MigrateNotificationTypesV1Data, MigrateNotificationTypesV1Responses, MigrateNotificationTypesV1Errors, ViewRoleIndexV1Data, ViewRoleIndexV1Responses, ViewRoleIndexV1Errors, UpdateRolesPermissionsV1Data, UpdateRolesPermissionsV1Responses, UpdateRolesPermissionsV1Errors, CreateRoleV1Data, CreateRoleV1Responses, CreateRoleV1Errors, ClearRolePermissionsCacheV1Data, ClearRolePermissionsCacheV1Responses, ClearRolePermissionsCacheV1Errors, DeleteRoleV1Data, DeleteRoleV1Responses, DeleteRoleV1Errors, ViewRoleDetailV1Data, ViewRoleDetailV1Responses, ViewRoleDetailV1Errors, UpdateRoleV1Data, UpdateRoleV1Responses, UpdateRoleV1Errors, GetApiInfoData, GetApiInfoResponses, GetApiInfoErrors, SwaggerData, SwaggerResponses, SwaggerErrors, ViewUiPreferencesV1Data, ViewUiPreferencesV1Responses, ViewUiPreferencesV1Errors, UpdateUiPreferencesV1Data, UpdateUiPreferencesV1Responses, UpdateUiPreferencesV1Errors, ViewAnnouncementIndexV1Data, ViewAnnouncementIndexV1Responses, ViewAnnouncementIndexV1Errors, CreateAnnouncementV1Data, CreateAnnouncementV1Responses, CreateAnnouncementV1Errors, DeleteAnnouncementV1Data, DeleteAnnouncementV1Responses, DeleteAnnouncementV1Errors, ViewAnnouncementV1Data, ViewAnnouncementV1Responses, ViewAnnouncementV1Errors, UpdateAnnouncementV1Data, UpdateAnnouncementV1Responses, UpdateAnnouncementV1Errors, ViewDashboardAnnouncementIndexV1Data, ViewDashboardAnnouncementIndexV1Responses, ViewDashboardAnnouncementIndexV1Errors, ViewDashboardAnnouncementV1Data, ViewDashboardAnnouncementV1Responses, ViewDashboardAnnouncementV1Errors, DownloadCertificateV1Data, DownloadCertificateV1Responses, DownloadCertificateV1Errors, ViewCertificateIndexV1Data, ViewCertificateIndexV1Responses, ViewCertificateIndexV1Errors, ViewContactIndexV1Data, ViewContactIndexV1Responses, ViewContactIndexV1Errors, CreateContactV1Data, CreateContactV1Responses, CreateContactV1Errors, DeleteContactV1Data, DeleteContactV1Responses, DeleteContactV1Errors, UpdateContactV1Data, UpdateContactV1Responses, UpdateContactV1Errors, ViewContainerTypeIndexV1Data, ViewContainerTypeIndexV1Responses, ViewContainerTypeIndexV1Errors, ViewContractLineIndexV1Data, ViewContractLineIndexV1Responses, ViewContractLineIndexV1Errors, ViewWprContractLineIndexV1Data, ViewWprContractLineIndexV1Responses, ViewWprContractLineIndexV1Errors, ViewPackagingRequestContractLineIndexV1Data, ViewPackagingRequestContractLineIndexV1Responses, ViewPackagingRequestContractLineIndexV1Errors, GenerateContractLinesPdfV1Data, GenerateContractLinesPdfV1Responses, GenerateContractLinesPdfV1Errors, DownloadDocumentV1Data, DownloadDocumentV1Errors, GetDocumentFiltersV1Data, GetDocumentFiltersV1Responses, GetDocumentFiltersV1Errors, ViewDocumentIndexV1Data, ViewDocumentIndexV1Responses, ViewDocumentIndexV1Errors, ViewUserSiteIndexV1Data, ViewUserSiteIndexV1Responses, ViewUserSiteIndexV1Errors, ViewDynamicTableColumnIndexV1Data, ViewDynamicTableColumnIndexV1Responses, ViewDynamicTableColumnIndexV1Errors, ViewDynamicTableViewIndexV1Data, ViewDynamicTableViewIndexV1Responses, ViewDynamicTableViewIndexV1Errors, CreateDynamicTableViewV1Data, CreateDynamicTableViewV1Responses, CreateDynamicTableViewV1Errors, ViewDefaultDynamicTableViewV1Data, ViewDefaultDynamicTableViewV1Responses, ViewDefaultDynamicTableViewV1Errors, DeleteDynamicTableViewV1Data, DeleteDynamicTableViewV1Responses, DeleteDynamicTableViewV1Errors, UpdateDynamicTableViewV1Data, UpdateDynamicTableViewV1Responses, UpdateDynamicTableViewV1Errors, ViewEwcCodeIndexV1Data, ViewEwcCodeIndexV1Responses, ViewEwcCodeIndexV1Errors, DownloadGuidanceLetterSapV1Data, DownloadGuidanceLetterSapV1Responses, DownloadGuidanceLetterSapV1Errors, ViewGuidanceLetterIndexV1Data, ViewGuidanceLetterIndexV1Responses, ViewGuidanceLetterIndexV1Errors, DownloadInvoiceCertificateV1Data, DownloadInvoiceCertificateV1Responses, DownloadInvoiceCertificateV1Errors, DownloadInvoiceV1Data, DownloadInvoiceV1Responses, DownloadInvoiceV1Errors, ExportInvoicesExcelV1Data, ExportInvoicesExcelV1Responses, ExportInvoicesExcelV1Errors, ViewInvoiceIndexV1Data, ViewInvoiceIndexV1Responses, ViewInvoiceIndexV1Errors, ViewInvoiceDetailV1Data, ViewInvoiceDetailV1Responses, ViewInvoiceDetailV1Errors, ViewDraftInvoiceDetailV1Data, ViewDraftInvoiceDetailV1Responses, ViewDraftInvoiceDetailV1Errors, ViewDraftInvoiceIndexV1Data, ViewDraftInvoiceIndexV1Responses, ViewDraftInvoiceIndexV1Errors, ExportDraftInvoicesExcelV1Data, ExportDraftInvoicesExcelV1Responses, ExportDraftInvoicesExcelV1Errors, ApproveDraftInvoiceV1Data, ApproveDraftInvoiceV1Responses, ApproveDraftInvoiceV1Errors, RejectDraftInvoiceV1Data, RejectDraftInvoiceV1Responses, RejectDraftInvoiceV1Errors, SubscribeToNewsletterV1Data, SubscribeToNewsletterV1Responses, SubscribeToNewsletterV1Errors, ViewNewsItemIndexV1Data, ViewNewsItemIndexV1Responses, ViewNewsItemIndexV1Errors, CreateNewsItemV1Data, CreateNewsItemV1Responses, CreateNewsItemV1Errors, DeleteNewsItemV1Data, DeleteNewsItemV1Responses, DeleteNewsItemV1Errors, ViewNewsItemV1Data, ViewNewsItemV1Responses, ViewNewsItemV1Errors, UpdateNewsItemV1Data, UpdateNewsItemV1Responses, UpdateNewsItemV1Errors, ViewDashboardNewsItemIndexV1Data, ViewDashboardNewsItemIndexV1Responses, ViewDashboardNewsItemIndexV1Errors, ViewDashboardNewsItemV1Data, ViewDashboardNewsItemV1Responses, ViewDashboardNewsItemV1Errors, CreatePackagingRequestV1Data, CreatePackagingRequestV1Responses, CreatePackagingRequestV1Errors, ViewPackagingRequestV1Data, ViewPackagingRequestV1Responses, ViewPackagingRequestV1Errors, UpdatePackagingRequestV1Data, UpdatePackagingRequestV1Responses, UpdatePackagingRequestV1Errors, SubmitPackagingRequestV1Data, SubmitPackagingRequestV1Responses, SubmitPackagingRequestV1Errors, BulkDeletePackagingRequestV1Data, BulkDeletePackagingRequestV1Responses, BulkDeletePackagingRequestV1Errors, CopyPackagingRequestSapV1Data, CopyPackagingRequestSapV1Responses, CopyPackagingRequestSapV1Errors, ViewPackagingTypeIndexV1Data, ViewPackagingTypeIndexV1Responses, ViewPackagingTypeIndexV1Errors, ViewPayerIndexV1Data, ViewPayerIndexV1Responses, ViewPayerIndexV1Errors, ViewPickUpAddressIndexV1Data, ViewPickUpAddressIndexV1Responses, ViewPickUpAddressIndexV1Errors, ViewSuggestedPickUpAddressesV1Data, ViewSuggestedPickUpAddressesV1Responses, ViewSuggestedPickUpAddressesV1Errors, GetIsPoNumberAndCostCenterRequiredV1Data, GetIsPoNumberAndCostCenterRequiredV1Responses, GetIsPoNumberAndCostCenterRequiredV1Errors, ViewPickUpRequestIndexV1Data, ViewPickUpRequestIndexV1Responses, ViewPickUpRequestIndexV1Errors, CreatePickUpRequestV1Data, CreatePickUpRequestV1Responses, CreatePickUpRequestV1Errors, ViewPickUpRequestV1Data, ViewPickUpRequestV1Responses, ViewPickUpRequestV1Errors, UpdatePickUpRequestV1Data, UpdatePickUpRequestV1Responses, UpdatePickUpRequestV1Errors, ViewWasteProducerIndexV1Data, ViewWasteProducerIndexV1Responses, ViewWasteProducerIndexV1Errors, ViewSuggestedWasteProducersV1Data, ViewSuggestedWasteProducersV1Responses, ViewSuggestedWasteProducersV1Errors, SubmitPickUpRequestV1Data, SubmitPickUpRequestV1Responses, SubmitPickUpRequestV1Errors, BulkDeletePickUpRequestV1Data, BulkDeletePickUpRequestV1Responses, BulkDeletePickUpRequestV1Errors, ViewPickUpRequestSapV1Data, ViewPickUpRequestSapV1Responses, ViewPickUpRequestSapV1Errors, UpdatePickUpRequestSapV1Data, UpdatePickUpRequestSapV1Responses, UpdatePickUpRequestSapV1Errors, ViewPickUpRequestTemplateIndexV1Data, ViewPickUpRequestTemplateIndexV1Responses, ViewPickUpRequestTemplateIndexV1Errors, CreatePickUpRequestTemplateV1Data, CreatePickUpRequestTemplateV1Responses, CreatePickUpRequestTemplateV1Errors, ViewPickUpRequestTemplateV1Data, ViewPickUpRequestTemplateV1Responses, ViewPickUpRequestTemplateV1Errors, UpdatePickUpRequestTemplateV1Data, UpdatePickUpRequestTemplateV1Responses, UpdatePickUpRequestTemplateV1Errors, BulkDeletePickUpRequestTemplatesV1Data, BulkDeletePickUpRequestTemplatesV1Responses, BulkDeletePickUpRequestTemplatesV1Errors, CreatePickUpRequestFromTemplateV1Data, CreatePickUpRequestFromTemplateV1Responses, CreatePickUpRequestFromTemplateV1Errors, UploadDocumentSubmittedPickUpRequestV1Data, UploadDocumentSubmittedPickUpRequestV1Responses, UploadDocumentSubmittedPickUpRequestV1Errors, DownloadDocumentSubmittedPickUpRequestV1Data, DownloadDocumentSubmittedPickUpRequestV1Responses, DownloadDocumentSubmittedPickUpRequestV1Errors, CopyPickUpRequestSapV1Data, CopyPickUpRequestSapV1Responses, CopyPickUpRequestSapV1Errors, SubmitPickUpRequestSapV1Data, SubmitPickUpRequestSapV1Responses, SubmitPickUpRequestSapV1Errors, ViewSalesOrganisationIndexV1Data, ViewSalesOrganisationIndexV1Responses, ViewSalesOrganisationIndexV1Errors, ViewTankerTypeIndexV1Data, ViewTankerTypeIndexV1Responses, ViewTankerTypeIndexV1Errors, ViewTransportTypeIndexV1Data, ViewTransportTypeIndexV1Responses, ViewTransportTypeIndexV1Errors, ViewUnNumberIndexV1Data, ViewUnNumberIndexV1Responses, ViewUnNumberIndexV1Errors, ViewUnNumberIndexForPickUpRequestV1Data, ViewUnNumberIndexForPickUpRequestV1Responses, ViewUnNumberIndexForPickUpRequestV1Errors, ViewWasteInquiryIndexV1Data, ViewWasteInquiryIndexV1Responses, ViewWasteInquiryIndexV1Errors, CreateWasteInquiryV1Data, CreateWasteInquiryV1Responses, CreateWasteInquiryV1Errors, DownloadWasteInquirySummarySapV1Data, DownloadWasteInquirySummarySapV1Responses, DownloadWasteInquirySummarySapV1Errors, DownloadWasteInquiryDocumentSapV1Data, DownloadWasteInquiryDocumentSapV1Responses, DownloadWasteInquiryDocumentSapV1Errors, ViewWasteInquiryV1Data, ViewWasteInquiryV1Responses, ViewWasteInquiryV1Errors, UpdateWasteInquiryV1Data, UpdateWasteInquiryV1Responses, UpdateWasteInquiryV1Errors, SubmitWasteInquiryV1Data, SubmitWasteInquiryV1Responses, SubmitWasteInquiryV1Errors, ViewWasteInquirySapV1Data, ViewWasteInquirySapV1Responses, ViewWasteInquirySapV1Errors, UploadDocumentSubmittedWasteInquiryV1Data, UploadDocumentSubmittedWasteInquiryV1Responses, UploadDocumentSubmittedWasteInquiryV1Errors, BulkDeleteWasteInquiryV1Data, BulkDeleteWasteInquiryV1Responses, BulkDeleteWasteInquiryV1Errors, CopyWasteInquirySapV1Data, CopyWasteInquirySapV1Responses, CopyWasteInquirySapV1Errors, CreateWeeklyPlanningRequestV1Data, CreateWeeklyPlanningRequestV1Responses, CreateWeeklyPlanningRequestV1Errors, ViewWeeklyPlanningRequestV1Data, ViewWeeklyPlanningRequestV1Responses, ViewWeeklyPlanningRequestV1Errors, UpdateWeeklyPlanningRequestV1Data, UpdateWeeklyPlanningRequestV1Responses, UpdateWeeklyPlanningRequestV1Errors, AddWprPickUpRequestV1Data, AddWprPickUpRequestV1Responses, AddWprPickUpRequestV1Errors, SubmitWeeklyPlanningRequestV1Data, SubmitWeeklyPlanningRequestV1Responses, SubmitWeeklyPlanningRequestV1Errors } from './types.gen';
import { zMigrateCollectionsV1Data, zImportCollectionsV1Data, zViewCollectionsV1Data, zViewMeV1Data, zViewMeV1Response, zViewUserDetailV1Data, zViewUserDetailV1Response, zViewUserIndexV1Data, zViewUserIndexV1Response, zSyncEntraUsersV1Data, zStartUserImpersonationV1Data, zStartUserImpersonationV1Response, zViewPermissionIndexV1Data, zViewPermissionIndexV1Response, zViewCustomerIndexV1Data, zViewCustomerIndexV1Response, zCreateFileV1Data, zCreateFileV1Response, zConfirmFileUploadV1Data, zDownloadFileV1Data, zProxyExternalFileV1Data, zViewSuggestedCustomersV1Data, zViewSuggestedCustomersV1Response, zViewCustomerCountryV1Data, zViewCustomerCountryV1Response, zViewDomainEventLogIndexV1Data, zViewDomainEventLogIndexV1Response, zSearchCollectionsV1Data, zSearchCollectionsV1Response, zViewJobsIndexV1Data, zViewJobsIndexV1Response, zViewJobDetailV1Data, zViewJobDetailV1Response, zGetMyNotificationPreferencesV1Data, zGetMyNotificationPreferencesV1Response, zGetNotificationTypesConfigV1Data, zGetNotificationTypesConfigV1Response, zUpdateMyChannelNotificationPreferenceV1Data, zUpdateMyChannelNotificationPreferenceV1Response, zSendTestNotificationV1Data, zSendTestNotificationV1Response, zGetMyNotificationsV1Data, zGetMyNotificationsV1Response, zViewUnreadNotificationsCountV1Data, zViewUnreadNotificationsCountV1Response, zViewUserNotificationDetailV1Data, zViewUserNotificationDetailV1Response, zMarkAllNotificationAsReadV1Data, zMarkAllNotificationAsReadV1Response, zUpdateMyNotificationTypePreferenceV1Data, zUpdateMyNotificationTypePreferenceV1Response, zMarkNotificationAsReadV1Data, zMarkNotificationAsReadV1Response, zMarkNotificationAsUnreadV1Data, zMarkNotificationAsUnreadV1Response, zUpdateMyNotificationPreferencePresetV1Data, zUpdateMyNotificationPreferencePresetV1Response, zMigrateNotificationTypesV1Data, zMigrateNotificationTypesV1Response, zViewRoleIndexV1Data, zViewRoleIndexV1Response, zUpdateRolesPermissionsV1Data, zUpdateRolesPermissionsV1Response, zCreateRoleV1Data, zCreateRoleV1Response, zClearRolePermissionsCacheV1Data, zClearRolePermissionsCacheV1Response, zDeleteRoleV1Data, zDeleteRoleV1Response, zViewRoleDetailV1Data, zViewRoleDetailV1Response, zUpdateRoleV1Data, zGetApiInfoData, zGetApiInfoResponse2 as zGetApiInfoResponse, zSwaggerData, zViewUiPreferencesV1Data, zViewUiPreferencesV1Response, zUpdateUiPreferencesV1Data, zViewAnnouncementIndexV1Data, zViewAnnouncementIndexV1Response, zCreateAnnouncementV1Data, zCreateAnnouncementV1Response, zDeleteAnnouncementV1Data, zViewAnnouncementV1Data, zViewAnnouncementV1Response, zUpdateAnnouncementV1Data, zUpdateAnnouncementV1Response, zViewDashboardAnnouncementIndexV1Data, zViewDashboardAnnouncementIndexV1Response, zViewDashboardAnnouncementV1Data, zViewDashboardAnnouncementV1Response, zDownloadCertificateV1Data, zViewCertificateIndexV1Data, zViewCertificateIndexV1Response, zViewContactIndexV1Data, zViewContactIndexV1Response, zCreateContactV1Data, zCreateContactV1Response, zDeleteContactV1Data, zDeleteContactV1Response, zUpdateContactV1Data, zUpdateContactV1Response, zViewContainerTypeIndexV1Data, zViewContainerTypeIndexV1Response, zViewContractLineIndexV1Data, zViewContractLineIndexV1Response, zViewWprContractLineIndexV1Data, zViewWprContractLineIndexV1Response, zViewPackagingRequestContractLineIndexV1Data, zViewPackagingRequestContractLineIndexV1Response, zGenerateContractLinesPdfV1Data, zGenerateContractLinesPdfV1Response, zDownloadDocumentV1Data, zGetDocumentFiltersV1Data, zGetDocumentFiltersV1Response, zViewDocumentIndexV1Data, zViewDocumentIndexV1Response, zViewUserSiteIndexV1Data, zViewUserSiteIndexV1Response, zViewDynamicTableColumnIndexV1Data, zViewDynamicTableColumnIndexV1Response, zViewDynamicTableViewIndexV1Data, zViewDynamicTableViewIndexV1Response, zCreateDynamicTableViewV1Data, zCreateDynamicTableViewV1Response, zViewDefaultDynamicTableViewV1Data, zViewDefaultDynamicTableViewV1Response, zDeleteDynamicTableViewV1Data, zUpdateDynamicTableViewV1Data, zUpdateDynamicTableViewV1Response, zViewEwcCodeIndexV1Data, zViewEwcCodeIndexV1Response, zDownloadGuidanceLetterSapV1Data, zViewGuidanceLetterIndexV1Data, zViewGuidanceLetterIndexV1Response, zDownloadInvoiceCertificateV1Data, zDownloadInvoiceV1Data, zExportInvoicesExcelV1Data, zViewInvoiceIndexV1Data, zViewInvoiceIndexV1Response, zViewInvoiceDetailV1Data, zViewInvoiceDetailV1Response, zViewDraftInvoiceDetailV1Data, zViewDraftInvoiceDetailV1Response, zViewDraftInvoiceIndexV1Data, zViewDraftInvoiceIndexV1Response, zExportDraftInvoicesExcelV1Data, zApproveDraftInvoiceV1Data, zRejectDraftInvoiceV1Data, zSubscribeToNewsletterV1Data, zSubscribeToNewsletterV1Response, zViewNewsItemIndexV1Data, zViewNewsItemIndexV1Response, zCreateNewsItemV1Data, zCreateNewsItemV1Response, zDeleteNewsItemV1Data, zViewNewsItemV1Data, zViewNewsItemV1Response, zUpdateNewsItemV1Data, zUpdateNewsItemV1Response, zViewDashboardNewsItemIndexV1Data, zViewDashboardNewsItemIndexV1Response, zViewDashboardNewsItemV1Data, zViewDashboardNewsItemV1Response, zCreatePackagingRequestV1Data, zCreatePackagingRequestV1Response, zViewPackagingRequestV1Data, zViewPackagingRequestV1Response, zUpdatePackagingRequestV1Data, zUpdatePackagingRequestV1Response, zSubmitPackagingRequestV1Data, zSubmitPackagingRequestV1Response, zBulkDeletePackagingRequestV1Data, zCopyPackagingRequestSapV1Data, zCopyPackagingRequestSapV1Response, zViewPackagingTypeIndexV1Data, zViewPackagingTypeIndexV1Response, zViewPayerIndexV1Data, zViewPayerIndexV1Response, zViewPickUpAddressIndexV1Data, zViewPickUpAddressIndexV1Response, zViewSuggestedPickUpAddressesV1Data, zViewSuggestedPickUpAddressesV1Response, zGetIsPoNumberAndCostCenterRequiredV1Data, zGetIsPoNumberAndCostCenterRequiredV1Response, zViewPickUpRequestIndexV1Data, zViewPickUpRequestIndexV1Response, zCreatePickUpRequestV1Data, zCreatePickUpRequestV1Response, zViewPickUpRequestV1Data, zViewPickUpRequestV1Response, zUpdatePickUpRequestV1Data, zUpdatePickUpRequestV1Response, zViewWasteProducerIndexV1Data, zViewWasteProducerIndexV1Response, zViewSuggestedWasteProducersV1Data, zViewSuggestedWasteProducersV1Response, zSubmitPickUpRequestV1Data, zSubmitPickUpRequestV1Response, zBulkDeletePickUpRequestV1Data, zViewPickUpRequestSapV1Data, zViewPickUpRequestSapV1Response, zUpdatePickUpRequestSapV1Data, zViewPickUpRequestTemplateIndexV1Data, zViewPickUpRequestTemplateIndexV1Response, zCreatePickUpRequestTemplateV1Data, zCreatePickUpRequestTemplateV1Response, zViewPickUpRequestTemplateV1Data, zViewPickUpRequestTemplateV1Response, zUpdatePickUpRequestTemplateV1Data, zUpdatePickUpRequestTemplateV1Response, zBulkDeletePickUpRequestTemplatesV1Data, zCreatePickUpRequestFromTemplateV1Data, zCreatePickUpRequestFromTemplateV1Response, zUploadDocumentSubmittedPickUpRequestV1Data, zDownloadDocumentSubmittedPickUpRequestV1Data, zCopyPickUpRequestSapV1Data, zCopyPickUpRequestSapV1Response, zSubmitPickUpRequestSapV1Data, zViewSalesOrganisationIndexV1Data, zViewSalesOrganisationIndexV1Response, zViewTankerTypeIndexV1Data, zViewTankerTypeIndexV1Response, zViewTransportTypeIndexV1Data, zViewTransportTypeIndexV1Response, zViewUnNumberIndexV1Data, zViewUnNumberIndexV1Response, zViewUnNumberIndexForPickUpRequestV1Data, zViewUnNumberIndexForPickUpRequestV1Response, zViewWasteInquiryIndexV1Data, zViewWasteInquiryIndexV1Response, zCreateWasteInquiryV1Data, zCreateWasteInquiryV1Response, zDownloadWasteInquirySummarySapV1Data, zDownloadWasteInquiryDocumentSapV1Data, zViewWasteInquiryV1Data, zViewWasteInquiryV1Response, zUpdateWasteInquiryV1Data, zUpdateWasteInquiryV1Response, zSubmitWasteInquiryV1Data, zSubmitWasteInquiryV1Response, zViewWasteInquirySapV1Data, zViewWasteInquirySapV1Response, zUploadDocumentSubmittedWasteInquiryV1Data, zBulkDeleteWasteInquiryV1Data, zCopyWasteInquirySapV1Data, zCopyWasteInquirySapV1Response, zCreateWeeklyPlanningRequestV1Data, zCreateWeeklyPlanningRequestV1Response, zViewWeeklyPlanningRequestV1Data, zViewWeeklyPlanningRequestV1Response, zUpdateWeeklyPlanningRequestV1Data, zUpdateWeeklyPlanningRequestV1Response, zAddWprPickUpRequestV1Data, zAddWprPickUpRequestV1Response, zSubmitWeeklyPlanningRequestV1Data, zSubmitWeeklyPlanningRequestV1Response } from './zod.gen';
import { client } from './client.gen';

export type Options<TData extends TDataShape = TDataShape, ThrowOnError extends boolean = boolean> = ClientOptions<TData, ThrowOnError> & {
    /**
     * You can provide a client instance returned by `createClient()` instead of
     * individual options. This might be also useful if you want to implement a
     * custom client.
     */
    client?: Client;
    /**
     * You can pass arbitrary values through the `meta` object. This can be
     * used to access values that aren't defined as part of the SDK function.
     */
    meta?: Record<string, unknown>;
};

export const migrateCollectionsV1 = <ThrowOnError extends boolean = true>(options?: Options<MigrateCollectionsV1Data, ThrowOnError>) => {
    return (options?.client ?? client).get<MigrateCollectionsV1Responses, MigrateCollectionsV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zMigrateCollectionsV1Data.parseAsync(data);
        },
        url: '/api/v1/typesense/migrate',
        ...options
    });
};

export const importCollectionsV1 = <ThrowOnError extends boolean = true>(options?: Options<ImportCollectionsV1Data, ThrowOnError>) => {
    return (options?.client ?? client).get<ImportCollectionsV1Responses, ImportCollectionsV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zImportCollectionsV1Data.parseAsync(data);
        },
        url: '/api/v1/typesense/import',
        ...options
    });
};

export const viewCollectionsV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewCollectionsV1Data, ThrowOnError>) => {
    return (options?.client ?? client).get<ViewCollectionsV1Responses, ViewCollectionsV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewCollectionsV1Data.parseAsync(data);
        },
        url: '/api/v1/typesense/collections',
        ...options
    });
};

export const viewMeV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewMeV1Data, ThrowOnError>) => {
    return (options?.client ?? client).get<ViewMeV1Responses, ViewMeV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewMeV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewMeV1Response.parseAsync(data);
        },
        url: '/api/v1/users/me',
        ...options
    });
};

export const viewUserDetailV1 = <ThrowOnError extends boolean = true>(options: Options<ViewUserDetailV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<ViewUserDetailV1Responses, ViewUserDetailV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewUserDetailV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewUserDetailV1Response.parseAsync(data);
        },
        url: '/api/v1/users/{uuid}',
        ...options
    });
};

export const viewUserIndexV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewUserIndexV1Data, ThrowOnError>) => {
    return (options?.client ?? client).get<ViewUserIndexV1Responses, ViewUserIndexV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewUserIndexV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewUserIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/users',
        ...options
    });
};

export const syncEntraUsersV1 = <ThrowOnError extends boolean = true>(options?: Options<SyncEntraUsersV1Data, ThrowOnError>) => {
    return (options?.client ?? client).post<SyncEntraUsersV1Responses, SyncEntraUsersV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zSyncEntraUsersV1Data.parseAsync(data);
        },
        url: '/api/v1/users/sync-external',
        ...options
    });
};

export const startUserImpersonationV1 = <ThrowOnError extends boolean = true>(options: Options<StartUserImpersonationV1Data, ThrowOnError>) => {
    return (options.client ?? client).post<StartUserImpersonationV1Responses, StartUserImpersonationV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zStartUserImpersonationV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zStartUserImpersonationV1Response.parseAsync(data);
        },
        url: '/api/v1/users/{uuid}/impersonate',
        ...options
    });
};

export const viewPermissionIndexV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewPermissionIndexV1Data, ThrowOnError>) => {
    return (options?.client ?? client).get<ViewPermissionIndexV1Responses, ViewPermissionIndexV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewPermissionIndexV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewPermissionIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/permissions',
        ...options
    });
};

export const viewCustomerIndexV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewCustomerIndexV1Data, ThrowOnError>) => {
    return (options?.client ?? client).get<ViewCustomerIndexV1Responses, ViewCustomerIndexV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewCustomerIndexV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewCustomerIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/customers',
        ...options
    });
};

export const createFileV1 = <ThrowOnError extends boolean = true>(options: Options<CreateFileV1Data, ThrowOnError>) => {
    return (options.client ?? client).post<CreateFileV1Responses, CreateFileV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zCreateFileV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zCreateFileV1Response.parseAsync(data);
        },
        url: '/api/v1/files',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const confirmFileUploadV1 = <ThrowOnError extends boolean = true>(options: Options<ConfirmFileUploadV1Data, ThrowOnError>) => {
    return (options.client ?? client).post<ConfirmFileUploadV1Responses, ConfirmFileUploadV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zConfirmFileUploadV1Data.parseAsync(data);
        },
        url: '/api/v1/files/{file}/confirm-upload',
        ...options
    });
};

export const downloadFileV1 = <ThrowOnError extends boolean = true>(options: Options<DownloadFileV1Data, ThrowOnError>) => {
    return (options.client ?? client).post<unknown, DownloadFileV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zDownloadFileV1Data.parseAsync(data);
        },
        url: '/api/v1/files/{file}/download',
        ...options
    });
};

export const proxyExternalFileV1 = <ThrowOnError extends boolean = true>(options: Options<ProxyExternalFileV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<ProxyExternalFileV1Responses, ProxyExternalFileV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zProxyExternalFileV1Data.parseAsync(data);
        },
        url: '/api/v1/files/proxy-external',
        ...options
    });
};

export const viewSuggestedCustomersV1 = <ThrowOnError extends boolean = true>(options: Options<ViewSuggestedCustomersV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<ViewSuggestedCustomersV1Responses, ViewSuggestedCustomersV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewSuggestedCustomersV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewSuggestedCustomersV1Response.parseAsync(data);
        },
        url: '/api/v1/suggested-customers',
        ...options
    });
};

export const viewCustomerCountryV1 = <ThrowOnError extends boolean = true>(options: Options<ViewCustomerCountryV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<ViewCustomerCountryV1Responses, ViewCustomerCountryV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewCustomerCountryV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewCustomerCountryV1Response.parseAsync(data);
        },
        url: '/api/v1/customers/{id}/country',
        ...options
    });
};

export const viewDomainEventLogIndexV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewDomainEventLogIndexV1Data, ThrowOnError>) => {
    return (options?.client ?? client).get<ViewDomainEventLogIndexV1Responses, ViewDomainEventLogIndexV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewDomainEventLogIndexV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewDomainEventLogIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/event-logs',
        ...options
    });
};

export const searchCollectionsV1 = <ThrowOnError extends boolean = true>(options: Options<SearchCollectionsV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<SearchCollectionsV1Responses, SearchCollectionsV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zSearchCollectionsV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zSearchCollectionsV1Response.parseAsync(data);
        },
        url: '/api/v1/search',
        ...options
    });
};

export const viewJobsIndexV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewJobsIndexV1Data, ThrowOnError>) => {
    return (options?.client ?? client).get<ViewJobsIndexV1Responses, ViewJobsIndexV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewJobsIndexV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewJobsIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/jobs',
        ...options
    });
};

export const viewJobDetailV1 = <ThrowOnError extends boolean = true>(options: Options<ViewJobDetailV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<ViewJobDetailV1Responses, ViewJobDetailV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewJobDetailV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewJobDetailV1Response.parseAsync(data);
        },
        url: '/api/v1/jobs/{jobId}',
        ...options
    });
};

export const getMyNotificationPreferencesV1 = <ThrowOnError extends boolean = true>(options?: Options<GetMyNotificationPreferencesV1Data, ThrowOnError>) => {
    return (options?.client ?? client).get<GetMyNotificationPreferencesV1Responses, GetMyNotificationPreferencesV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zGetMyNotificationPreferencesV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zGetMyNotificationPreferencesV1Response.parseAsync(data);
        },
        url: '/api/v1/me/notification-preferences',
        ...options
    });
};

export const getNotificationTypesConfigV1 = <ThrowOnError extends boolean = true>(options?: Options<GetNotificationTypesConfigV1Data, ThrowOnError>) => {
    return (options?.client ?? client).get<GetNotificationTypesConfigV1Responses, GetNotificationTypesConfigV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zGetNotificationTypesConfigV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zGetNotificationTypesConfigV1Response.parseAsync(data);
        },
        url: '/api/v1/notification-preferences/config',
        ...options
    });
};

export const updateMyChannelNotificationPreferenceV1 = <ThrowOnError extends boolean = true>(options: Options<UpdateMyChannelNotificationPreferenceV1Data, ThrowOnError>) => {
    return (options.client ?? client).patch<UpdateMyChannelNotificationPreferenceV1Responses, UpdateMyChannelNotificationPreferenceV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zUpdateMyChannelNotificationPreferenceV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zUpdateMyChannelNotificationPreferenceV1Response.parseAsync(data);
        },
        url: '/api/v1/me/notification-preferences/channels',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const sendTestNotificationV1 = <ThrowOnError extends boolean = true>(options: Options<SendTestNotificationV1Data, ThrowOnError>) => {
    return (options.client ?? client).post<SendTestNotificationV1Responses, SendTestNotificationV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zSendTestNotificationV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zSendTestNotificationV1Response.parseAsync(data);
        },
        url: '/api/v1/notifications/test-notification',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const getMyNotificationsV1 = <ThrowOnError extends boolean = true>(options?: Options<GetMyNotificationsV1Data, ThrowOnError>) => {
    return (options?.client ?? client).get<GetMyNotificationsV1Responses, GetMyNotificationsV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zGetMyNotificationsV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zGetMyNotificationsV1Response.parseAsync(data);
        },
        url: '/api/v1/me/notifications',
        ...options
    });
};

export const viewUnreadNotificationsCountV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewUnreadNotificationsCountV1Data, ThrowOnError>) => {
    return (options?.client ?? client).get<ViewUnreadNotificationsCountV1Responses, ViewUnreadNotificationsCountV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewUnreadNotificationsCountV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewUnreadNotificationsCountV1Response.parseAsync(data);
        },
        url: '/api/v1/me/notifications/unread-count',
        ...options
    });
};

export const viewUserNotificationDetailV1 = <ThrowOnError extends boolean = true>(options: Options<ViewUserNotificationDetailV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<ViewUserNotificationDetailV1Responses, ViewUserNotificationDetailV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewUserNotificationDetailV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewUserNotificationDetailV1Response.parseAsync(data);
        },
        url: '/api/v1/me/notifications/{notificationUuid}',
        ...options
    });
};

export const markAllNotificationAsReadV1 = <ThrowOnError extends boolean = true>(options?: Options<MarkAllNotificationAsReadV1Data, ThrowOnError>) => {
    return (options?.client ?? client).patch<MarkAllNotificationAsReadV1Responses, MarkAllNotificationAsReadV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zMarkAllNotificationAsReadV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zMarkAllNotificationAsReadV1Response.parseAsync(data);
        },
        url: '/api/v1/me/notifications/mark-as-read',
        ...options
    });
};

export const updateMyNotificationTypePreferenceV1 = <ThrowOnError extends boolean = true>(options: Options<UpdateMyNotificationTypePreferenceV1Data, ThrowOnError>) => {
    return (options.client ?? client).patch<UpdateMyNotificationTypePreferenceV1Responses, UpdateMyNotificationTypePreferenceV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zUpdateMyNotificationTypePreferenceV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zUpdateMyNotificationTypePreferenceV1Response.parseAsync(data);
        },
        url: '/api/v1/me/notification-preferences/types',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const markNotificationAsReadV1 = <ThrowOnError extends boolean = true>(options: Options<MarkNotificationAsReadV1Data, ThrowOnError>) => {
    return (options.client ?? client).patch<MarkNotificationAsReadV1Responses, MarkNotificationAsReadV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zMarkNotificationAsReadV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zMarkNotificationAsReadV1Response.parseAsync(data);
        },
        url: '/api/v1/me/notifications/{notificationUuid}/mark-as-read',
        ...options
    });
};

export const markNotificationAsUnreadV1 = <ThrowOnError extends boolean = true>(options: Options<MarkNotificationAsUnreadV1Data, ThrowOnError>) => {
    return (options.client ?? client).patch<MarkNotificationAsUnreadV1Responses, MarkNotificationAsUnreadV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zMarkNotificationAsUnreadV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zMarkNotificationAsUnreadV1Response.parseAsync(data);
        },
        url: '/api/v1/me/notifications/{notificationUuid}/mark-as-unread',
        ...options
    });
};

export const updateMyNotificationPreferencePresetV1 = <ThrowOnError extends boolean = true>(options: Options<UpdateMyNotificationPreferencePresetV1Data, ThrowOnError>) => {
    return (options.client ?? client).patch<UpdateMyNotificationPreferencePresetV1Responses, UpdateMyNotificationPreferencePresetV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zUpdateMyNotificationPreferencePresetV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zUpdateMyNotificationPreferencePresetV1Response.parseAsync(data);
        },
        url: '/api/v1/me/notification-preferences/preset',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const migrateNotificationTypesV1 = <ThrowOnError extends boolean = true>(options: Options<MigrateNotificationTypesV1Data, ThrowOnError>) => {
    return (options.client ?? client).post<MigrateNotificationTypesV1Responses, MigrateNotificationTypesV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zMigrateNotificationTypesV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zMigrateNotificationTypesV1Response.parseAsync(data);
        },
        url: '/api/v1/notifications/migrate',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const viewRoleIndexV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewRoleIndexV1Data, ThrowOnError>) => {
    return (options?.client ?? client).get<ViewRoleIndexV1Responses, ViewRoleIndexV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewRoleIndexV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewRoleIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/roles',
        ...options
    });
};

export const updateRolesPermissionsV1 = <ThrowOnError extends boolean = true>(options: Options<UpdateRolesPermissionsV1Data, ThrowOnError>) => {
    return (options.client ?? client).patch<UpdateRolesPermissionsV1Responses, UpdateRolesPermissionsV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zUpdateRolesPermissionsV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zUpdateRolesPermissionsV1Response.parseAsync(data);
        },
        url: '/api/v1/roles',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const createRoleV1 = <ThrowOnError extends boolean = true>(options: Options<CreateRoleV1Data, ThrowOnError>) => {
    return (options.client ?? client).post<CreateRoleV1Responses, CreateRoleV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zCreateRoleV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zCreateRoleV1Response.parseAsync(data);
        },
        url: '/api/v1/roles',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const clearRolePermissionsCacheV1 = <ThrowOnError extends boolean = true>(options: Options<ClearRolePermissionsCacheV1Data, ThrowOnError>) => {
    return (options.client ?? client).post<ClearRolePermissionsCacheV1Responses, ClearRolePermissionsCacheV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zClearRolePermissionsCacheV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zClearRolePermissionsCacheV1Response.parseAsync(data);
        },
        url: '/api/v1/roles/clear-cache',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const deleteRoleV1 = <ThrowOnError extends boolean = true>(options: Options<DeleteRoleV1Data, ThrowOnError>) => {
    return (options.client ?? client).delete<DeleteRoleV1Responses, DeleteRoleV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zDeleteRoleV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zDeleteRoleV1Response.parseAsync(data);
        },
        url: '/api/v1/roles/{role}',
        ...options
    });
};

export const viewRoleDetailV1 = <ThrowOnError extends boolean = true>(options: Options<ViewRoleDetailV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<ViewRoleDetailV1Responses, ViewRoleDetailV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewRoleDetailV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewRoleDetailV1Response.parseAsync(data);
        },
        url: '/api/v1/roles/{role}',
        ...options
    });
};

export const updateRoleV1 = <ThrowOnError extends boolean = true>(options: Options<UpdateRoleV1Data, ThrowOnError>) => {
    return (options.client ?? client).post<UpdateRoleV1Responses, UpdateRoleV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zUpdateRoleV1Data.parseAsync(data);
        },
        url: '/api/v1/roles/{uuid}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const getApiInfo = <ThrowOnError extends boolean = true>(options?: Options<GetApiInfoData, ThrowOnError>) => {
    return (options?.client ?? client).get<GetApiInfoResponses, GetApiInfoErrors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zGetApiInfoData.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zGetApiInfoResponse.parseAsync(data);
        },
        url: '/api',
        ...options
    });
};

export const swagger = <ThrowOnError extends boolean = true>(options?: Options<SwaggerData, ThrowOnError>) => {
    return (options?.client ?? client).get<SwaggerResponses, SwaggerErrors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zSwaggerData.parseAsync(data);
        },
        url: '/api/oauth2-redirect',
        ...options
    });
};

export const viewUiPreferencesV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewUiPreferencesV1Data, ThrowOnError>) => {
    return (options?.client ?? client).get<ViewUiPreferencesV1Responses, ViewUiPreferencesV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewUiPreferencesV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewUiPreferencesV1Response.parseAsync(data);
        },
        url: '/api/v1/me/ui-preferences',
        ...options
    });
};

export const updateUiPreferencesV1 = <ThrowOnError extends boolean = true>(options: Options<UpdateUiPreferencesV1Data, ThrowOnError>) => {
    return (options.client ?? client).patch<UpdateUiPreferencesV1Responses, UpdateUiPreferencesV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zUpdateUiPreferencesV1Data.parseAsync(data);
        },
        url: '/api/v1/me/ui-preferences',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const viewAnnouncementIndexV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewAnnouncementIndexV1Data, ThrowOnError>) => {
    return (options?.client ?? client).get<ViewAnnouncementIndexV1Responses, ViewAnnouncementIndexV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewAnnouncementIndexV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewAnnouncementIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/announcements',
        ...options
    });
};

export const createAnnouncementV1 = <ThrowOnError extends boolean = true>(options: Options<CreateAnnouncementV1Data, ThrowOnError>) => {
    return (options.client ?? client).post<CreateAnnouncementV1Responses, CreateAnnouncementV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zCreateAnnouncementV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zCreateAnnouncementV1Response.parseAsync(data);
        },
        url: '/api/v1/announcements',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const deleteAnnouncementV1 = <ThrowOnError extends boolean = true>(options: Options<DeleteAnnouncementV1Data, ThrowOnError>) => {
    return (options.client ?? client).delete<DeleteAnnouncementV1Responses, DeleteAnnouncementV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zDeleteAnnouncementV1Data.parseAsync(data);
        },
        url: '/api/v1/announcements/{uuid}',
        ...options
    });
};

export const viewAnnouncementV1 = <ThrowOnError extends boolean = true>(options: Options<ViewAnnouncementV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<ViewAnnouncementV1Responses, ViewAnnouncementV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewAnnouncementV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewAnnouncementV1Response.parseAsync(data);
        },
        url: '/api/v1/announcements/{uuid}',
        ...options
    });
};

export const updateAnnouncementV1 = <ThrowOnError extends boolean = true>(options: Options<UpdateAnnouncementV1Data, ThrowOnError>) => {
    return (options.client ?? client).patch<UpdateAnnouncementV1Responses, UpdateAnnouncementV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zUpdateAnnouncementV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zUpdateAnnouncementV1Response.parseAsync(data);
        },
        url: '/api/v1/announcements/{uuid}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const viewDashboardAnnouncementIndexV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewDashboardAnnouncementIndexV1Data, ThrowOnError>) => {
    return (options?.client ?? client).get<ViewDashboardAnnouncementIndexV1Responses, ViewDashboardAnnouncementIndexV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewDashboardAnnouncementIndexV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewDashboardAnnouncementIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/dashboard-announcements',
        ...options
    });
};

export const viewDashboardAnnouncementV1 = <ThrowOnError extends boolean = true>(options: Options<ViewDashboardAnnouncementV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<ViewDashboardAnnouncementV1Responses, ViewDashboardAnnouncementV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewDashboardAnnouncementV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewDashboardAnnouncementV1Response.parseAsync(data);
        },
        url: '/api/v1/dashboard-announcements/{uuid}',
        ...options
    });
};

export const downloadCertificateV1 = <ThrowOnError extends boolean = true>(options: Options<DownloadCertificateV1Data, ThrowOnError>) => {
    return (options.client ?? client).post<DownloadCertificateV1Responses, DownloadCertificateV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zDownloadCertificateV1Data.parseAsync(data);
        },
        url: '/api/v1/certificates/download',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const viewCertificateIndexV1 = <ThrowOnError extends boolean = true>(options: Options<ViewCertificateIndexV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<ViewCertificateIndexV1Responses, ViewCertificateIndexV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewCertificateIndexV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewCertificateIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/certificates',
        ...options
    });
};

export const viewContactIndexV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewContactIndexV1Data, ThrowOnError>) => {
    return (options?.client ?? client).get<ViewContactIndexV1Responses, ViewContactIndexV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewContactIndexV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewContactIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/contacts',
        ...options
    });
};

export const createContactV1 = <ThrowOnError extends boolean = true>(options: Options<CreateContactV1Data, ThrowOnError>) => {
    return (options.client ?? client).post<CreateContactV1Responses, CreateContactV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zCreateContactV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zCreateContactV1Response.parseAsync(data);
        },
        url: '/api/v1/contacts',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const deleteContactV1 = <ThrowOnError extends boolean = true>(options: Options<DeleteContactV1Data, ThrowOnError>) => {
    return (options.client ?? client).delete<DeleteContactV1Responses, DeleteContactV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zDeleteContactV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zDeleteContactV1Response.parseAsync(data);
        },
        url: '/api/v1/contacts/{uuid}',
        ...options
    });
};

export const updateContactV1 = <ThrowOnError extends boolean = true>(options: Options<UpdateContactV1Data, ThrowOnError>) => {
    return (options.client ?? client).patch<UpdateContactV1Responses, UpdateContactV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zUpdateContactV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zUpdateContactV1Response.parseAsync(data);
        },
        url: '/api/v1/contacts/{uuid}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const viewContainerTypeIndexV1 = <ThrowOnError extends boolean = true>(options: Options<ViewContainerTypeIndexV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<ViewContainerTypeIndexV1Responses, ViewContainerTypeIndexV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewContainerTypeIndexV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewContainerTypeIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/container-types',
        ...options
    });
};

export const viewContractLineIndexV1 = <ThrowOnError extends boolean = true>(options: Options<ViewContractLineIndexV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<ViewContractLineIndexV1Responses, ViewContractLineIndexV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewContractLineIndexV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewContractLineIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/contract-lines',
        ...options
    });
};

export const viewWprContractLineIndexV1 = <ThrowOnError extends boolean = true>(options: Options<ViewWprContractLineIndexV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<ViewWprContractLineIndexV1Responses, ViewWprContractLineIndexV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewWprContractLineIndexV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewWprContractLineIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/contract-lines/weekly-planning-requests',
        ...options
    });
};

export const viewPackagingRequestContractLineIndexV1 = <ThrowOnError extends boolean = true>(options: Options<ViewPackagingRequestContractLineIndexV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<ViewPackagingRequestContractLineIndexV1Responses, ViewPackagingRequestContractLineIndexV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewPackagingRequestContractLineIndexV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewPackagingRequestContractLineIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/contract-lines/packaging-requests',
        ...options
    });
};

export const generateContractLinesPdfV1 = <ThrowOnError extends boolean = true>(options: Options<GenerateContractLinesPdfV1Data, ThrowOnError>) => {
    return (options.client ?? client).post<GenerateContractLinesPdfV1Responses, GenerateContractLinesPdfV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zGenerateContractLinesPdfV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zGenerateContractLinesPdfV1Response.parseAsync(data);
        },
        url: '/api/v1/contract-lines/generate-pdf',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const downloadDocumentV1 = <ThrowOnError extends boolean = true>(options: Options<DownloadDocumentV1Data, ThrowOnError>) => {
    return (options.client ?? client).post<unknown, DownloadDocumentV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zDownloadDocumentV1Data.parseAsync(data);
        },
        url: '/api/v1/documents/download',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const getDocumentFiltersV1 = <ThrowOnError extends boolean = true>(options: Options<GetDocumentFiltersV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<GetDocumentFiltersV1Responses, GetDocumentFiltersV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zGetDocumentFiltersV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zGetDocumentFiltersV1Response.parseAsync(data);
        },
        url: '/api/v1/documents/filters',
        ...options
    });
};

export const viewDocumentIndexV1 = <ThrowOnError extends boolean = true>(options: Options<ViewDocumentIndexV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<ViewDocumentIndexV1Responses, ViewDocumentIndexV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewDocumentIndexV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewDocumentIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/documents',
        ...options
    });
};

export const viewUserSiteIndexV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewUserSiteIndexV1Data, ThrowOnError>) => {
    return (options?.client ?? client).get<ViewUserSiteIndexV1Responses, ViewUserSiteIndexV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewUserSiteIndexV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewUserSiteIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/documents/sites',
        ...options
    });
};

export const viewDynamicTableColumnIndexV1 = <ThrowOnError extends boolean = true>(options: Options<ViewDynamicTableColumnIndexV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<ViewDynamicTableColumnIndexV1Responses, ViewDynamicTableColumnIndexV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewDynamicTableColumnIndexV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewDynamicTableColumnIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/dynamic-tables/{name}/columns',
        ...options
    });
};

export const viewDynamicTableViewIndexV1 = <ThrowOnError extends boolean = true>(options: Options<ViewDynamicTableViewIndexV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<ViewDynamicTableViewIndexV1Responses, ViewDynamicTableViewIndexV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewDynamicTableViewIndexV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewDynamicTableViewIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/dynamic-tables/{name}/views',
        ...options
    });
};

export const createDynamicTableViewV1 = <ThrowOnError extends boolean = true>(options: Options<CreateDynamicTableViewV1Data, ThrowOnError>) => {
    return (options.client ?? client).post<CreateDynamicTableViewV1Responses, CreateDynamicTableViewV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zCreateDynamicTableViewV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zCreateDynamicTableViewV1Response.parseAsync(data);
        },
        url: '/api/v1/dynamic-tables/{name}/views',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const viewDefaultDynamicTableViewV1 = <ThrowOnError extends boolean = true>(options: Options<ViewDefaultDynamicTableViewV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<ViewDefaultDynamicTableViewV1Responses, ViewDefaultDynamicTableViewV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewDefaultDynamicTableViewV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewDefaultDynamicTableViewV1Response.parseAsync(data);
        },
        url: '/api/v1/dynamic-tables/{name}/default-view',
        ...options
    });
};

export const deleteDynamicTableViewV1 = <ThrowOnError extends boolean = true>(options: Options<DeleteDynamicTableViewV1Data, ThrowOnError>) => {
    return (options.client ?? client).delete<DeleteDynamicTableViewV1Responses, DeleteDynamicTableViewV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zDeleteDynamicTableViewV1Data.parseAsync(data);
        },
        url: '/api/v1/dynamic-tables/{name}/views/{uuid}',
        ...options
    });
};

export const updateDynamicTableViewV1 = <ThrowOnError extends boolean = true>(options: Options<UpdateDynamicTableViewV1Data, ThrowOnError>) => {
    return (options.client ?? client).patch<UpdateDynamicTableViewV1Responses, UpdateDynamicTableViewV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zUpdateDynamicTableViewV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zUpdateDynamicTableViewV1Response.parseAsync(data);
        },
        url: '/api/v1/dynamic-tables/{name}/views/{uuid}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const viewEwcCodeIndexV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewEwcCodeIndexV1Data, ThrowOnError>) => {
    return (options?.client ?? client).get<ViewEwcCodeIndexV1Responses, ViewEwcCodeIndexV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewEwcCodeIndexV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewEwcCodeIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/ewc-codes',
        ...options
    });
};

export const downloadGuidanceLetterSapV1 = <ThrowOnError extends boolean = true>(options: Options<DownloadGuidanceLetterSapV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<DownloadGuidanceLetterSapV1Responses, DownloadGuidanceLetterSapV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zDownloadGuidanceLetterSapV1Data.parseAsync(data);
        },
        url: '/api/v1/guidance-letters/{shipmentId}/download',
        ...options
    });
};

export const viewGuidanceLetterIndexV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewGuidanceLetterIndexV1Data, ThrowOnError>) => {
    return (options?.client ?? client).get<ViewGuidanceLetterIndexV1Responses, ViewGuidanceLetterIndexV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewGuidanceLetterIndexV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewGuidanceLetterIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/guidance-letters',
        ...options
    });
};

export const downloadInvoiceCertificateV1 = <ThrowOnError extends boolean = true>(options: Options<DownloadInvoiceCertificateV1Data, ThrowOnError>) => {
    return (options.client ?? client).post<DownloadInvoiceCertificateV1Responses, DownloadInvoiceCertificateV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zDownloadInvoiceCertificateV1Data.parseAsync(data);
        },
        url: '/api/v1/invoices/certificates/download',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const downloadInvoiceV1 = <ThrowOnError extends boolean = true>(options: Options<DownloadInvoiceV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<DownloadInvoiceV1Responses, DownloadInvoiceV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zDownloadInvoiceV1Data.parseAsync(data);
        },
        url: '/api/v1/invoices/{invoiceNumber}/download',
        ...options
    });
};

export const exportInvoicesExcelV1 = <ThrowOnError extends boolean = true>(options: Options<ExportInvoicesExcelV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<ExportInvoicesExcelV1Responses, ExportInvoicesExcelV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zExportInvoicesExcelV1Data.parseAsync(data);
        },
        url: '/api/v1/invoices/export-excel',
        ...options
    });
};

export const viewInvoiceIndexV1 = <ThrowOnError extends boolean = true>(options: Options<ViewInvoiceIndexV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<ViewInvoiceIndexV1Responses, ViewInvoiceIndexV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewInvoiceIndexV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewInvoiceIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/invoices',
        ...options
    });
};

export const viewInvoiceDetailV1 = <ThrowOnError extends boolean = true>(options: Options<ViewInvoiceDetailV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<ViewInvoiceDetailV1Responses, ViewInvoiceDetailV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewInvoiceDetailV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewInvoiceDetailV1Response.parseAsync(data);
        },
        url: '/api/v1/invoices/{requestNumber}',
        ...options
    });
};

export const viewDraftInvoiceDetailV1 = <ThrowOnError extends boolean = true>(options: Options<ViewDraftInvoiceDetailV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<ViewDraftInvoiceDetailV1Responses, ViewDraftInvoiceDetailV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewDraftInvoiceDetailV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewDraftInvoiceDetailV1Response.parseAsync(data);
        },
        url: '/api/v1/draft-invoices/{invoiceNumber}',
        ...options
    });
};

export const viewDraftInvoiceIndexV1 = <ThrowOnError extends boolean = true>(options: Options<ViewDraftInvoiceIndexV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<ViewDraftInvoiceIndexV1Responses, ViewDraftInvoiceIndexV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewDraftInvoiceIndexV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewDraftInvoiceIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/draft-invoices',
        ...options
    });
};

export const exportDraftInvoicesExcelV1 = <ThrowOnError extends boolean = true>(options: Options<ExportDraftInvoicesExcelV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<ExportDraftInvoicesExcelV1Responses, ExportDraftInvoicesExcelV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zExportDraftInvoicesExcelV1Data.parseAsync(data);
        },
        url: '/api/v1/draft-invoices/export-excel',
        ...options
    });
};

export const approveDraftInvoiceV1 = <ThrowOnError extends boolean = true>(options: Options<ApproveDraftInvoiceV1Data, ThrowOnError>) => {
    return (options.client ?? client).post<ApproveDraftInvoiceV1Responses, ApproveDraftInvoiceV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zApproveDraftInvoiceV1Data.parseAsync(data);
        },
        url: '/api/v1/draft-invoices/{invoiceNumber}/approve',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const rejectDraftInvoiceV1 = <ThrowOnError extends boolean = true>(options: Options<RejectDraftInvoiceV1Data, ThrowOnError>) => {
    return (options.client ?? client).post<RejectDraftInvoiceV1Responses, RejectDraftInvoiceV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zRejectDraftInvoiceV1Data.parseAsync(data);
        },
        url: '/api/v1/draft-invoices/{invoiceNumber}/reject',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const subscribeToNewsletterV1 = <ThrowOnError extends boolean = true>(options: Options<SubscribeToNewsletterV1Data, ThrowOnError>) => {
    return (options.client ?? client).post<SubscribeToNewsletterV1Responses, SubscribeToNewsletterV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zSubscribeToNewsletterV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zSubscribeToNewsletterV1Response.parseAsync(data);
        },
        url: '/api/v1/newsletters/subscribe',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const viewNewsItemIndexV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewNewsItemIndexV1Data, ThrowOnError>) => {
    return (options?.client ?? client).get<ViewNewsItemIndexV1Responses, ViewNewsItemIndexV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewNewsItemIndexV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewNewsItemIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/news-items',
        ...options
    });
};

export const createNewsItemV1 = <ThrowOnError extends boolean = true>(options: Options<CreateNewsItemV1Data, ThrowOnError>) => {
    return (options.client ?? client).post<CreateNewsItemV1Responses, CreateNewsItemV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zCreateNewsItemV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zCreateNewsItemV1Response.parseAsync(data);
        },
        url: '/api/v1/news-items',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const deleteNewsItemV1 = <ThrowOnError extends boolean = true>(options: Options<DeleteNewsItemV1Data, ThrowOnError>) => {
    return (options.client ?? client).delete<DeleteNewsItemV1Responses, DeleteNewsItemV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zDeleteNewsItemV1Data.parseAsync(data);
        },
        url: '/api/v1/news-items/{uuid}',
        ...options
    });
};

export const viewNewsItemV1 = <ThrowOnError extends boolean = true>(options: Options<ViewNewsItemV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<ViewNewsItemV1Responses, ViewNewsItemV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewNewsItemV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewNewsItemV1Response.parseAsync(data);
        },
        url: '/api/v1/news-items/{uuid}',
        ...options
    });
};

export const updateNewsItemV1 = <ThrowOnError extends boolean = true>(options: Options<UpdateNewsItemV1Data, ThrowOnError>) => {
    return (options.client ?? client).patch<UpdateNewsItemV1Responses, UpdateNewsItemV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zUpdateNewsItemV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zUpdateNewsItemV1Response.parseAsync(data);
        },
        url: '/api/v1/news-items/{uuid}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const viewDashboardNewsItemIndexV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewDashboardNewsItemIndexV1Data, ThrowOnError>) => {
    return (options?.client ?? client).get<ViewDashboardNewsItemIndexV1Responses, ViewDashboardNewsItemIndexV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewDashboardNewsItemIndexV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewDashboardNewsItemIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/dashboard-news-items',
        ...options
    });
};

export const viewDashboardNewsItemV1 = <ThrowOnError extends boolean = true>(options: Options<ViewDashboardNewsItemV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<ViewDashboardNewsItemV1Responses, ViewDashboardNewsItemV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewDashboardNewsItemV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewDashboardNewsItemV1Response.parseAsync(data);
        },
        url: '/api/v1/dashboard-news-items/{uuid}',
        ...options
    });
};

export const createPackagingRequestV1 = <ThrowOnError extends boolean = true>(options: Options<CreatePackagingRequestV1Data, ThrowOnError>) => {
    return (options.client ?? client).post<CreatePackagingRequestV1Responses, CreatePackagingRequestV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zCreatePackagingRequestV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zCreatePackagingRequestV1Response.parseAsync(data);
        },
        url: '/api/v1/packaging-requests',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const viewPackagingRequestV1 = <ThrowOnError extends boolean = true>(options: Options<ViewPackagingRequestV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<ViewPackagingRequestV1Responses, ViewPackagingRequestV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewPackagingRequestV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewPackagingRequestV1Response.parseAsync(data);
        },
        url: '/api/v1/packaging-requests/{uuid}',
        ...options
    });
};

export const updatePackagingRequestV1 = <ThrowOnError extends boolean = true>(options: Options<UpdatePackagingRequestV1Data, ThrowOnError>) => {
    return (options.client ?? client).patch<UpdatePackagingRequestV1Responses, UpdatePackagingRequestV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zUpdatePackagingRequestV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zUpdatePackagingRequestV1Response.parseAsync(data);
        },
        url: '/api/v1/packaging-requests/{uuid}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const submitPackagingRequestV1 = <ThrowOnError extends boolean = true>(options: Options<SubmitPackagingRequestV1Data, ThrowOnError>) => {
    return (options.client ?? client).post<SubmitPackagingRequestV1Responses, SubmitPackagingRequestV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zSubmitPackagingRequestV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zSubmitPackagingRequestV1Response.parseAsync(data);
        },
        url: '/api/v1/packaging-requests/{uuid}/submit',
        ...options
    });
};

export const bulkDeletePackagingRequestV1 = <ThrowOnError extends boolean = true>(options: Options<BulkDeletePackagingRequestV1Data, ThrowOnError>) => {
    return (options.client ?? client).delete<BulkDeletePackagingRequestV1Responses, BulkDeletePackagingRequestV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zBulkDeletePackagingRequestV1Data.parseAsync(data);
        },
        url: '/api/v1/packaging-requests/bulk',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const copyPackagingRequestSapV1 = <ThrowOnError extends boolean = true>(options: Options<CopyPackagingRequestSapV1Data, ThrowOnError>) => {
    return (options.client ?? client).post<CopyPackagingRequestSapV1Responses, CopyPackagingRequestSapV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zCopyPackagingRequestSapV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zCopyPackagingRequestSapV1Response.parseAsync(data);
        },
        url: '/api/v1/packaging-requests/sap/{requestNumber}/copy',
        ...options
    });
};

export const viewPackagingTypeIndexV1 = <ThrowOnError extends boolean = true>(options: Options<ViewPackagingTypeIndexV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<ViewPackagingTypeIndexV1Responses, ViewPackagingTypeIndexV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewPackagingTypeIndexV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewPackagingTypeIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/packaging-types',
        ...options
    });
};

export const viewPayerIndexV1 = <ThrowOnError extends boolean = true>(options: Options<ViewPayerIndexV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<ViewPayerIndexV1Responses, ViewPayerIndexV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewPayerIndexV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewPayerIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/payers',
        ...options
    });
};

export const viewPickUpAddressIndexV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewPickUpAddressIndexV1Data, ThrowOnError>) => {
    return (options?.client ?? client).get<ViewPickUpAddressIndexV1Responses, ViewPickUpAddressIndexV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewPickUpAddressIndexV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewPickUpAddressIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/pick-up-addresses',
        ...options
    });
};

export const viewSuggestedPickUpAddressesV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewSuggestedPickUpAddressesV1Data, ThrowOnError>) => {
    return (options?.client ?? client).get<ViewSuggestedPickUpAddressesV1Responses, ViewSuggestedPickUpAddressesV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewSuggestedPickUpAddressesV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewSuggestedPickUpAddressesV1Response.parseAsync(data);
        },
        url: '/api/v1/suggested-pick-up-addresses',
        ...options
    });
};

/**
 * Check if PO number and cost center are required
 */
export const getIsPoNumberAndCostCenterRequiredV1 = <ThrowOnError extends boolean = true>(options: Options<GetIsPoNumberAndCostCenterRequiredV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<GetIsPoNumberAndCostCenterRequiredV1Responses, GetIsPoNumberAndCostCenterRequiredV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zGetIsPoNumberAndCostCenterRequiredV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zGetIsPoNumberAndCostCenterRequiredV1Response.parseAsync(data);
        },
        url: '/api/v1/pick-up-requests/is-po-number-and-cost-center-required',
        ...options
    });
};

export const viewPickUpRequestIndexV1 = <ThrowOnError extends boolean = true>(options: Options<ViewPickUpRequestIndexV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<ViewPickUpRequestIndexV1Responses, ViewPickUpRequestIndexV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewPickUpRequestIndexV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewPickUpRequestIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/pick-up-requests',
        ...options
    });
};

export const createPickUpRequestV1 = <ThrowOnError extends boolean = true>(options: Options<CreatePickUpRequestV1Data, ThrowOnError>) => {
    return (options.client ?? client).post<CreatePickUpRequestV1Responses, CreatePickUpRequestV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zCreatePickUpRequestV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zCreatePickUpRequestV1Response.parseAsync(data);
        },
        url: '/api/v1/pick-up-requests',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const viewPickUpRequestV1 = <ThrowOnError extends boolean = true>(options: Options<ViewPickUpRequestV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<ViewPickUpRequestV1Responses, ViewPickUpRequestV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewPickUpRequestV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewPickUpRequestV1Response.parseAsync(data);
        },
        url: '/api/v1/pick-up-requests/{uuid}',
        ...options
    });
};

export const updatePickUpRequestV1 = <ThrowOnError extends boolean = true>(options: Options<UpdatePickUpRequestV1Data, ThrowOnError>) => {
    return (options.client ?? client).patch<UpdatePickUpRequestV1Responses, UpdatePickUpRequestV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zUpdatePickUpRequestV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zUpdatePickUpRequestV1Response.parseAsync(data);
        },
        url: '/api/v1/pick-up-requests/{uuid}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const viewWasteProducerIndexV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewWasteProducerIndexV1Data, ThrowOnError>) => {
    return (options?.client ?? client).get<ViewWasteProducerIndexV1Responses, ViewWasteProducerIndexV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewWasteProducerIndexV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewWasteProducerIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/waste-producers',
        ...options
    });
};

export const viewSuggestedWasteProducersV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewSuggestedWasteProducersV1Data, ThrowOnError>) => {
    return (options?.client ?? client).get<ViewSuggestedWasteProducersV1Responses, ViewSuggestedWasteProducersV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewSuggestedWasteProducersV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewSuggestedWasteProducersV1Response.parseAsync(data);
        },
        url: '/api/v1/suggested-waste-producers',
        ...options
    });
};

export const submitPickUpRequestV1 = <ThrowOnError extends boolean = true>(options: Options<SubmitPickUpRequestV1Data, ThrowOnError>) => {
    return (options.client ?? client).post<SubmitPickUpRequestV1Responses, SubmitPickUpRequestV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zSubmitPickUpRequestV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zSubmitPickUpRequestV1Response.parseAsync(data);
        },
        url: '/api/v1/pick-up-requests/{uuid}/submit',
        ...options
    });
};

export const bulkDeletePickUpRequestV1 = <ThrowOnError extends boolean = true>(options: Options<BulkDeletePickUpRequestV1Data, ThrowOnError>) => {
    return (options.client ?? client).delete<BulkDeletePickUpRequestV1Responses, BulkDeletePickUpRequestV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zBulkDeletePickUpRequestV1Data.parseAsync(data);
        },
        url: '/api/v1/pick-up-requests/bulk',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const viewPickUpRequestSapV1 = <ThrowOnError extends boolean = true>(options: Options<ViewPickUpRequestSapV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<ViewPickUpRequestSapV1Responses, ViewPickUpRequestSapV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewPickUpRequestSapV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewPickUpRequestSapV1Response.parseAsync(data);
        },
        url: '/api/v1/pick-up-requests/sap/{requestNumber}',
        ...options
    });
};

export const updatePickUpRequestSapV1 = <ThrowOnError extends boolean = true>(options: Options<UpdatePickUpRequestSapV1Data, ThrowOnError>) => {
    return (options.client ?? client).patch<UpdatePickUpRequestSapV1Responses, UpdatePickUpRequestSapV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zUpdatePickUpRequestSapV1Data.parseAsync(data);
        },
        url: '/api/v1/pick-up-requests/sap/{requestNumber}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const viewPickUpRequestTemplateIndexV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewPickUpRequestTemplateIndexV1Data, ThrowOnError>) => {
    return (options?.client ?? client).get<ViewPickUpRequestTemplateIndexV1Responses, ViewPickUpRequestTemplateIndexV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewPickUpRequestTemplateIndexV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewPickUpRequestTemplateIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/pick-up-request-templates',
        ...options
    });
};

export const createPickUpRequestTemplateV1 = <ThrowOnError extends boolean = true>(options: Options<CreatePickUpRequestTemplateV1Data, ThrowOnError>) => {
    return (options.client ?? client).post<CreatePickUpRequestTemplateV1Responses, CreatePickUpRequestTemplateV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zCreatePickUpRequestTemplateV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zCreatePickUpRequestTemplateV1Response.parseAsync(data);
        },
        url: '/api/v1/pick-up-request-templates',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const viewPickUpRequestTemplateV1 = <ThrowOnError extends boolean = true>(options: Options<ViewPickUpRequestTemplateV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<ViewPickUpRequestTemplateV1Responses, ViewPickUpRequestTemplateV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewPickUpRequestTemplateV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewPickUpRequestTemplateV1Response.parseAsync(data);
        },
        url: '/api/v1/pick-up-request-templates/{uuid}',
        ...options
    });
};

export const updatePickUpRequestTemplateV1 = <ThrowOnError extends boolean = true>(options: Options<UpdatePickUpRequestTemplateV1Data, ThrowOnError>) => {
    return (options.client ?? client).patch<UpdatePickUpRequestTemplateV1Responses, UpdatePickUpRequestTemplateV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zUpdatePickUpRequestTemplateV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zUpdatePickUpRequestTemplateV1Response.parseAsync(data);
        },
        url: '/api/v1/pick-up-request-templates/{uuid}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const bulkDeletePickUpRequestTemplatesV1 = <ThrowOnError extends boolean = true>(options: Options<BulkDeletePickUpRequestTemplatesV1Data, ThrowOnError>) => {
    return (options.client ?? client).delete<BulkDeletePickUpRequestTemplatesV1Responses, BulkDeletePickUpRequestTemplatesV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zBulkDeletePickUpRequestTemplatesV1Data.parseAsync(data);
        },
        url: '/api/v1/pick-up-request-templates/bulk',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const createPickUpRequestFromTemplateV1 = <ThrowOnError extends boolean = true>(options: Options<CreatePickUpRequestFromTemplateV1Data, ThrowOnError>) => {
    return (options.client ?? client).post<CreatePickUpRequestFromTemplateV1Responses, CreatePickUpRequestFromTemplateV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zCreatePickUpRequestFromTemplateV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zCreatePickUpRequestFromTemplateV1Response.parseAsync(data);
        },
        url: '/api/v1/pick-up-request-templates/{uuid}/create-pick-up-request',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const uploadDocumentSubmittedPickUpRequestV1 = <ThrowOnError extends boolean = true>(options: Options<UploadDocumentSubmittedPickUpRequestV1Data, ThrowOnError>) => {
    return (options.client ?? client).post<UploadDocumentSubmittedPickUpRequestV1Responses, UploadDocumentSubmittedPickUpRequestV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zUploadDocumentSubmittedPickUpRequestV1Data.parseAsync(data);
        },
        url: '/api/v1/pick-up-requests/sap/{requestNumber}/documents/upload',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const downloadDocumentSubmittedPickUpRequestV1 = <ThrowOnError extends boolean = true>(options: Options<DownloadDocumentSubmittedPickUpRequestV1Data, ThrowOnError>) => {
    return (options.client ?? client).post<DownloadDocumentSubmittedPickUpRequestV1Responses, DownloadDocumentSubmittedPickUpRequestV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zDownloadDocumentSubmittedPickUpRequestV1Data.parseAsync(data);
        },
        url: '/api/v1/pick-up-requests/sap/{requestNumber}/documents/download',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const copyPickUpRequestSapV1 = <ThrowOnError extends boolean = true>(options: Options<CopyPickUpRequestSapV1Data, ThrowOnError>) => {
    return (options.client ?? client).post<CopyPickUpRequestSapV1Responses, CopyPickUpRequestSapV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zCopyPickUpRequestSapV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zCopyPickUpRequestSapV1Response.parseAsync(data);
        },
        url: '/api/v1/pick-up-requests/sap/{requestNumber}/copy',
        ...options
    });
};

export const submitPickUpRequestSapV1 = <ThrowOnError extends boolean = true>(options: Options<SubmitPickUpRequestSapV1Data, ThrowOnError>) => {
    return (options.client ?? client).post<SubmitPickUpRequestSapV1Responses, SubmitPickUpRequestSapV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zSubmitPickUpRequestSapV1Data.parseAsync(data);
        },
        url: '/api/v1/pick-up-requests/sap/{requestNumber}/submit',
        ...options
    });
};

export const viewSalesOrganisationIndexV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewSalesOrganisationIndexV1Data, ThrowOnError>) => {
    return (options?.client ?? client).get<ViewSalesOrganisationIndexV1Responses, ViewSalesOrganisationIndexV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewSalesOrganisationIndexV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewSalesOrganisationIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/sales-organisations',
        ...options
    });
};

export const viewTankerTypeIndexV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewTankerTypeIndexV1Data, ThrowOnError>) => {
    return (options?.client ?? client).get<ViewTankerTypeIndexV1Responses, ViewTankerTypeIndexV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewTankerTypeIndexV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewTankerTypeIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/tanker-types',
        ...options
    });
};

export const viewTransportTypeIndexV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewTransportTypeIndexV1Data, ThrowOnError>) => {
    return (options?.client ?? client).get<ViewTransportTypeIndexV1Responses, ViewTransportTypeIndexV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewTransportTypeIndexV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewTransportTypeIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/transport-types',
        ...options
    });
};

export const viewUnNumberIndexV1 = <ThrowOnError extends boolean = true>(options?: Options<ViewUnNumberIndexV1Data, ThrowOnError>) => {
    return (options?.client ?? client).get<ViewUnNumberIndexV1Responses, ViewUnNumberIndexV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewUnNumberIndexV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewUnNumberIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/un-numbers',
        ...options
    });
};

export const viewUnNumberIndexForPickUpRequestV1 = <ThrowOnError extends boolean = true>(options: Options<ViewUnNumberIndexForPickUpRequestV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<ViewUnNumberIndexForPickUpRequestV1Responses, ViewUnNumberIndexForPickUpRequestV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewUnNumberIndexForPickUpRequestV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewUnNumberIndexForPickUpRequestV1Response.parseAsync(data);
        },
        url: '/api/v1/un-numbers-pick-up-request',
        ...options
    });
};

export const viewWasteInquiryIndexV1 = <ThrowOnError extends boolean = true>(options: Options<ViewWasteInquiryIndexV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<ViewWasteInquiryIndexV1Responses, ViewWasteInquiryIndexV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewWasteInquiryIndexV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewWasteInquiryIndexV1Response.parseAsync(data);
        },
        url: '/api/v1/waste-inquiries',
        ...options
    });
};

export const createWasteInquiryV1 = <ThrowOnError extends boolean = true>(options: Options<CreateWasteInquiryV1Data, ThrowOnError>) => {
    return (options.client ?? client).post<CreateWasteInquiryV1Responses, CreateWasteInquiryV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zCreateWasteInquiryV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zCreateWasteInquiryV1Response.parseAsync(data);
        },
        url: '/api/v1/waste-inquiries',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const downloadWasteInquirySummarySapV1 = <ThrowOnError extends boolean = true>(options: Options<DownloadWasteInquirySummarySapV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<DownloadWasteInquirySummarySapV1Responses, DownloadWasteInquirySummarySapV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zDownloadWasteInquirySummarySapV1Data.parseAsync(data);
        },
        url: '/api/v1/waste-inquiries/sap/{inquiryNumber}/summary',
        ...options
    });
};

export const downloadWasteInquiryDocumentSapV1 = <ThrowOnError extends boolean = true>(options: Options<DownloadWasteInquiryDocumentSapV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<DownloadWasteInquiryDocumentSapV1Responses, DownloadWasteInquiryDocumentSapV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zDownloadWasteInquiryDocumentSapV1Data.parseAsync(data);
        },
        url: '/api/v1/waste-inquiries/sap/{inquiryNumber}/documents/{sapFileUuid}',
        ...options
    });
};

export const viewWasteInquiryV1 = <ThrowOnError extends boolean = true>(options: Options<ViewWasteInquiryV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<ViewWasteInquiryV1Responses, ViewWasteInquiryV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewWasteInquiryV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewWasteInquiryV1Response.parseAsync(data);
        },
        url: '/api/v1/waste-inquiries/{uuid}',
        ...options
    });
};

export const updateWasteInquiryV1 = <ThrowOnError extends boolean = true>(options: Options<UpdateWasteInquiryV1Data, ThrowOnError>) => {
    return (options.client ?? client).patch<UpdateWasteInquiryV1Responses, UpdateWasteInquiryV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zUpdateWasteInquiryV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zUpdateWasteInquiryV1Response.parseAsync(data);
        },
        url: '/api/v1/waste-inquiries/{uuid}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const submitWasteInquiryV1 = <ThrowOnError extends boolean = true>(options: Options<SubmitWasteInquiryV1Data, ThrowOnError>) => {
    return (options.client ?? client).post<SubmitWasteInquiryV1Responses, SubmitWasteInquiryV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zSubmitWasteInquiryV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zSubmitWasteInquiryV1Response.parseAsync(data);
        },
        url: '/api/v1/waste-inquiries/{uuid}/submit',
        ...options
    });
};

export const viewWasteInquirySapV1 = <ThrowOnError extends boolean = true>(options: Options<ViewWasteInquirySapV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<ViewWasteInquirySapV1Responses, ViewWasteInquirySapV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewWasteInquirySapV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewWasteInquirySapV1Response.parseAsync(data);
        },
        url: '/api/v1/waste-inquiries/sap/{inquiryNumber}',
        ...options
    });
};

export const uploadDocumentSubmittedWasteInquiryV1 = <ThrowOnError extends boolean = true>(options: Options<UploadDocumentSubmittedWasteInquiryV1Data, ThrowOnError>) => {
    return (options.client ?? client).post<UploadDocumentSubmittedWasteInquiryV1Responses, UploadDocumentSubmittedWasteInquiryV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zUploadDocumentSubmittedWasteInquiryV1Data.parseAsync(data);
        },
        url: '/api/v1/waste-inquiries/{inquiryNumber}/upload-documents',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const bulkDeleteWasteInquiryV1 = <ThrowOnError extends boolean = true>(options: Options<BulkDeleteWasteInquiryV1Data, ThrowOnError>) => {
    return (options.client ?? client).delete<BulkDeleteWasteInquiryV1Responses, BulkDeleteWasteInquiryV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zBulkDeleteWasteInquiryV1Data.parseAsync(data);
        },
        url: '/api/v1/waste-inquiries/bulk',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const copyWasteInquirySapV1 = <ThrowOnError extends boolean = true>(options: Options<CopyWasteInquirySapV1Data, ThrowOnError>) => {
    return (options.client ?? client).post<CopyWasteInquirySapV1Responses, CopyWasteInquirySapV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zCopyWasteInquirySapV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zCopyWasteInquirySapV1Response.parseAsync(data);
        },
        url: '/api/v1/waste-inquiries/sap/{inquiryNumber}/copy',
        ...options
    });
};

export const createWeeklyPlanningRequestV1 = <ThrowOnError extends boolean = true>(options: Options<CreateWeeklyPlanningRequestV1Data, ThrowOnError>) => {
    return (options.client ?? client).post<CreateWeeklyPlanningRequestV1Responses, CreateWeeklyPlanningRequestV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zCreateWeeklyPlanningRequestV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zCreateWeeklyPlanningRequestV1Response.parseAsync(data);
        },
        url: '/api/v1/weekly-planning-requests',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const viewWeeklyPlanningRequestV1 = <ThrowOnError extends boolean = true>(options: Options<ViewWeeklyPlanningRequestV1Data, ThrowOnError>) => {
    return (options.client ?? client).get<ViewWeeklyPlanningRequestV1Responses, ViewWeeklyPlanningRequestV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zViewWeeklyPlanningRequestV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zViewWeeklyPlanningRequestV1Response.parseAsync(data);
        },
        url: '/api/v1/weekly-planning-requests/{uuid}',
        ...options
    });
};

export const updateWeeklyPlanningRequestV1 = <ThrowOnError extends boolean = true>(options: Options<UpdateWeeklyPlanningRequestV1Data, ThrowOnError>) => {
    return (options.client ?? client).patch<UpdateWeeklyPlanningRequestV1Responses, UpdateWeeklyPlanningRequestV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zUpdateWeeklyPlanningRequestV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zUpdateWeeklyPlanningRequestV1Response.parseAsync(data);
        },
        url: '/api/v1/weekly-planning-requests/{uuid}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const addWprPickUpRequestV1 = <ThrowOnError extends boolean = true>(options: Options<AddWprPickUpRequestV1Data, ThrowOnError>) => {
    return (options.client ?? client).post<AddWprPickUpRequestV1Responses, AddWprPickUpRequestV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zAddWprPickUpRequestV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zAddWprPickUpRequestV1Response.parseAsync(data);
        },
        url: '/api/v1/weekly-planning-requests/{uuid}/add-pick-up-request',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const submitWeeklyPlanningRequestV1 = <ThrowOnError extends boolean = true>(options: Options<SubmitWeeklyPlanningRequestV1Data, ThrowOnError>) => {
    return (options.client ?? client).post<SubmitWeeklyPlanningRequestV1Responses, SubmitWeeklyPlanningRequestV1Errors, ThrowOnError>({
        requestValidator: async (data) => {
            return await zSubmitWeeklyPlanningRequestV1Data.parseAsync(data);
        },
        responseValidator: async (data) => {
            return await zSubmitWeeklyPlanningRequestV1Response.parseAsync(data);
        },
        url: '/api/v1/weekly-planning-requests/{uuid}/submit',
        ...options
    });
};
