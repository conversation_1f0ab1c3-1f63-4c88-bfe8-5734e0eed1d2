// This file is auto-generated by @hey-api/openapi-ts

import { z } from 'zod';

export const zPermission = z.enum([
    'all_permissions',
    'announcement.manage',
    'certificate.manage',
    'certificate.read',
    'contact.manage',
    'contact.read',
    'contract-line.read',
    'contract-line.manage',
    'document.master-table',
    'document.tfs',
    'document.quotation',
    'document.minutes-and-presentations',
    'document.manual',
    'document.bsc',
    'document.contract',
    'document.transport',
    'dynamic-table-view.manage',
    'event-log.read',
    'invoice.read',
    'invoice.manage',
    'jobs.read.index',
    'jobs.read.detail',
    'news-item.manage',
    'newsletter.subscribe',
    'pick-up-request.read',
    'pick-up-request.manage',
    'packaging-request.read',
    'packaging-request.manage',
    'power-bi.read',
    'role.read',
    'role.manage',
    'send_push_notification',
    'typesense',
    'useful-link.ecmr',
    'useful-link.permits',
    'useful-link.reporting',
    'user.read',
    'user.manage',
    'user.impersonate',
    'waste-inquiry.read',
    'waste-inquiry.manage',
    'weekly-planning-request.read',
    'weekly-planning-request.manage',
    'guidance-letter.read'
]);

export const zViewRoleDetailResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    name: z.string(),
    permissions: z.array(zPermission),
    isDefault: z.boolean(),
    isSystemAdmin: z.boolean()
});

export const zViewMeResponse = z.object({
    uuid: z.string().uuid(),
    email: z.string().email(),
    firstName: z.union([
        z.string(),
        z.null()
    ]),
    lastName: z.union([
        z.string(),
        z.null()
    ]),
    roles: z.array(zViewRoleDetailResponse),
    isInternalUser: z.boolean()
});

export const zViewUserDetailResponse = z.object({
    uuid: z.string().uuid(),
    email: z.string().email(),
    firstName: z.union([
        z.string(),
        z.null()
    ]),
    lastName: z.union([
        z.string(),
        z.null()
    ]),
    roles: z.array(zViewRoleDetailResponse)
});

export const zPaginatedOffsetQuery = z.object({
    limit: z.number().gte(1).lte(100),
    offset: z.number().gte(0)
});

export const zUserIndexRoleView = z.object({
    uuid: z.string().uuid(),
    name: z.string()
});

export const zUserIndexView = z.object({
    uuid: z.string().uuid(),
    upn: z.string(),
    email: z.string().email(),
    firstName: z.union([
        z.string(),
        z.null()
    ]),
    lastName: z.union([
        z.string(),
        z.null()
    ]),
    roles: z.array(zUserIndexRoleView)
});

export const zPaginatedOffsetResponseMeta = z.object({
    total: z.number(),
    offset: z.number(),
    limit: z.number()
});

export const zViewUserIndexResponse = z.object({
    items: z.array(zUserIndexView),
    meta: zPaginatedOffsetResponseMeta
});

export const zStartUserImpersonationResponse = z.object({
    impersonationToken: z.string()
});

export const zViewPermissionIndexPermissionResponse = z.object({
    name: z.string(),
    key: zPermission,
    description: z.string()
});

export const zViewPermissionIndexGroupResponse = z.object({
    name: z.string(),
    permissions: z.array(zViewPermissionIndexPermissionResponse)
});

export const zViewPermissionIndexResponse = z.object({
    groups: z.array(zViewPermissionIndexGroupResponse)
});

export const zCoordinatesResponse = z.object({
    latitude: z.number(),
    longitude: z.number()
});

export const zAddressResponse = z.object({
    countryCode: z.union([
        z.string(),
        z.null()
    ]),
    postalCode: z.string(),
    locality: z.string(),
    addressLine1: z.string(),
    addressLine2: z.union([
        z.string(),
        z.null()
    ]),
    coordinates: z.union([
        zCoordinatesResponse,
        z.null()
    ])
});

export const zCustomerResponse = z.object({
    id: z.string(),
    name: z.string(),
    address: z.union([
        z.string(),
        z.unknown(),
        z.null()
    ])
});

export const zViewCustomerIndexResponse = z.object({
    items: z.array(zCustomerResponse),
    meta: zPaginatedOffsetResponseMeta
});

export const zMimeType = z.enum([
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain',
    'text/html',
    'image/jpeg',
    'image/png',
    'image/tiff',
    'image/bmp',
    'image/heic',
    'image/webp',
    'image/gif',
    'text/csv',
    'application/vnd.ms-outlook',
    'application/vnd.openxmlformats-officedocument.presentationml.slideshow',
    'application/rtf',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/octet-stream'
]);

export const zCreateFileCommand = z.object({
    name: z.string(),
    mimeType: zMimeType
});

export const zCreateFileResponse = z.object({
    uuid: z.string().uuid(),
    name: z.string(),
    mimeType: z.union([
        zMimeType,
        z.null()
    ]),
    uploadUrl: z.string()
});

export const zRequestType = z.enum([
    'waste',
    'pick-up'
]);

export const zViewSuggestedCustomersFilterQuery = z.object({
    requestType: zRequestType
});

export const zViewSuggestedCustomersResponse = z.object({
    items: z.array(zCustomerResponse)
});

export const zCustomerNotFoundError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '404'
    ]),
    code: z.enum([
        'customer_not_found'
    ])
});

export const zViewCustomerCountryResponse = z.object({
    countryCode: z.string()
});

export const zSubjectType = z.enum([
    'announcement',
    'contact',
    'dynamic-table-view',
    'file',
    'news-item',
    'pick-up-request',
    'packaging-request',
    'role',
    'user',
    'waste-inquiry',
    'weekly-planning-request'
]);

export const zViewDomainEventLogIndexFilterQuery = z.object({
    subjectType: zSubjectType.optional(),
    subjectId: z.string().uuid().optional(),
    userUuid: z.string().uuid().optional()
});

export const zViewDomainEventLogIndexQueryKey = z.object({
    createdAt: z.string(),
    uuid: z.string().uuid()
});

export const zViewDomainEventLogIndexPaginationQuery = z.object({
    limit: z.number().gte(0).lte(100),
    key: z.union([
        zViewDomainEventLogIndexQueryKey,
        z.null()
    ]).optional()
});

export const zUserCreatedEventContent = z.object({
    userUuid: z.string().uuid()
});

export const zUserCreatedDomainEventLog = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ]),
    type: z.enum([
        'user.created'
    ]),
    content: zUserCreatedEventContent
});

export const zUserUpdatedEventContent = z.object({
    userUuid: z.string().uuid()
});

export const zUserUpdatedDomainEventLog = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ]),
    type: z.enum([
        'user.updated'
    ]),
    content: zUserUpdatedEventContent
});

export const zRoleCreatedEventContent = z.object({
    roleUuid: z.string().uuid(),
    roleName: z.string()
});

export const zRoleCreatedDomainEventLog = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ]),
    type: z.enum([
        'role.created'
    ]),
    content: zRoleCreatedEventContent
});

export const zRoleDeletedEventContent = z.object({
    roleUuid: z.string().uuid(),
    roleName: z.string()
});

export const zRoleDeletedDomainEventLog = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ]),
    type: z.enum([
        'role.deleted'
    ]),
    content: zRoleDeletedEventContent
});

export const zRoleRenamedEventContent = z.object({
    roleUuid: z.string().uuid(),
    previousName: z.string(),
    newName: z.string()
});

export const zRoleRenamedDomainEventLog = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ]),
    type: z.enum([
        'role.renamed'
    ]),
    content: zRoleRenamedEventContent
});

export const zRolePermissionsUpdatedEventContent = z.object({
    roleUuid: z.string().uuid(),
    newPermissions: z.array(zPermission),
    roleName: z.string()
});

export const zRolePermissionsUpdatedDomainEventLog = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ]),
    type: z.enum([
        'role.permissions.updated'
    ]),
    content: zRolePermissionsUpdatedEventContent
});

export const zRolePermissionsCacheClearedEventContent = z.object({
    roleUuids: z.string().uuid()
});

export const zRolePermissionsCacheClearedDomainEventLog = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ]),
    type: z.enum([
        'role.permissions.cache.cleared'
    ]),
    content: zRolePermissionsCacheClearedEventContent
});

export const zFileCreatedEventContent = z.object({
    fileUuid: z.string().uuid(),
    fileName: z.string()
});

export const zFileCreatedDomainEventLog = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ]),
    type: z.enum([
        'file.created'
    ]),
    content: zFileCreatedEventContent
});

export const zFileUploadedEventContent = z.object({
    fileUuid: z.string().uuid(),
    fileName: z.string()
});

export const zFileUploadedDomainEventLog = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ]),
    type: z.enum([
        'file.uploaded'
    ]),
    content: zFileUploadedEventContent
});

export const zNotificationType = z.enum([
    'user.created',
    'test-notification'
]);

export const zNotificationCreatedEventContent = z.object({
    uuid: z.string().uuid(),
    type: zNotificationType
});

export const zNotificationCreatedDomainEventLog = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ]),
    type: z.enum([
        'notification.created'
    ]),
    content: zNotificationCreatedEventContent
});

export const zNotificationChannel = z.enum([
    'email',
    'sms',
    'app',
    'push'
]);

export const zUserNotificationCreatedEventContent = z.object({
    notificationUuid: z.string().uuid(),
    channel: zNotificationChannel,
    userUuid: z.string().uuid()
});

export const zUserNotificationCreatedDomainEventLog = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ]),
    type: z.enum([
        'user.notification.created'
    ]),
    content: zUserNotificationCreatedEventContent
});

export const zContactCreatedEventContent = z.object({
    contactUuid: z.string().uuid()
});

export const zContactCreatedDomainEventLog = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ]),
    type: z.enum([
        'contact.created'
    ]),
    content: zContactCreatedEventContent
});

export const zContactUpdatedEventContent = z.object({
    contactUuid: z.string().uuid()
});

export const zContactUpdatedDomainEventLog = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ]),
    type: z.enum([
        'contact.updated'
    ]),
    content: zContactUpdatedEventContent
});

export const zContactDeletedEventContent = z.object({
    contactUuid: z.string().uuid()
});

export const zContactDeletedDomainEventLog = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ]),
    type: z.enum([
        'contact.deleted'
    ]),
    content: zContactDeletedEventContent
});

export const zNotificationReadEventContent = z.object({
    notificationUuid: z.string().uuid(),
    userUuid: z.string().uuid()
});

export const zNotificationReadDomainEventLog = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ]),
    type: z.enum([
        'notification.read'
    ]),
    content: zNotificationReadEventContent
});

export const zNotificationUnreadEventContent = z.object({
    notificationUuid: z.string().uuid(),
    userUuid: z.string().uuid()
});

export const zNotificationUnreadDomainEventLog = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ]),
    type: z.enum([
        'notification.unread'
    ]),
    content: zNotificationUnreadEventContent
});

export const zNotificationPreset = z.enum([
    'all',
    'default',
    'custom',
    'none'
]);

export const zNotificationPreferencePresetEventContent = z.object({
    userUuid: z.string().uuid(),
    preset: zNotificationPreset
});

export const zNotificationPreferencePresetUpdatedDomainEventLog = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ]),
    type: z.enum([
        'notification.preference.preset.updated'
    ]),
    content: zNotificationPreferencePresetEventContent
});

export const zNotificationTypesMigratedEventContent = z.object({
    types: z.array(zNotificationType)
});

export const zNotificationTypesMigratedDomainEventLog = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ]),
    type: z.enum([
        'notification.types.migrated'
    ]),
    content: zNotificationTypesMigratedEventContent
});

export const zTestNotificationSentEventContent = z.object({
    message: z.string()
});

export const zTestNotificationSentDomainEventLog = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ]),
    type: z.enum([
        'test-notification.sent'
    ]),
    content: zTestNotificationSentEventContent
});

export const zAllNotificationsMarkedAsReadEventContent = z.object({
    userUuid: z.string().uuid()
});

export const zNotificationReadAllDomainEventLog = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ]),
    type: z.enum([
        'notification.read.all'
    ]),
    content: zAllNotificationsMarkedAsReadEventContent
});

export const zDomainEventLogResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    version: z.number().int().gte(0),
    source: z.string(),
    userUuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    message: z.string(),
    subjectType: z.union([
        zSubjectType,
        z.null()
    ]),
    subjectId: z.union([
        z.string().uuid(),
        z.null()
    ])
});

export const zViewDomainEventLogIndexResponseMeta = z.object({
    next: z.union([
        zViewDomainEventLogIndexQueryKey,
        z.null()
    ])
});

export const zViewDomainEventLogIndexResponse = z.object({
    items: z.array(z.union([
        zUserCreatedDomainEventLog,
        zUserUpdatedDomainEventLog,
        zRoleCreatedDomainEventLog,
        zRoleDeletedDomainEventLog,
        zRoleRenamedDomainEventLog,
        zRolePermissionsUpdatedDomainEventLog,
        zRolePermissionsCacheClearedDomainEventLog,
        zFileCreatedDomainEventLog,
        zFileUploadedDomainEventLog,
        zNotificationCreatedDomainEventLog,
        zUserNotificationCreatedDomainEventLog,
        zContactCreatedDomainEventLog,
        zContactUpdatedDomainEventLog,
        zContactDeletedDomainEventLog,
        zNotificationReadDomainEventLog,
        zNotificationUnreadDomainEventLog,
        zNotificationPreferencePresetUpdatedDomainEventLog,
        zNotificationTypesMigratedDomainEventLog,
        zTestNotificationSentDomainEventLog,
        zNotificationReadAllDomainEventLog
    ])),
    meta: zViewDomainEventLogIndexResponseMeta
});

export const zGlobalSearchCollectionName = z.enum([
    'user',
    'contact'
]);

export const zSearchCollectionsFilterQuery = z.object({
    collections: z.array(zGlobalSearchCollectionName).optional()
});

export const zSearchCollectionUserResponse = z.object({
    uuid: z.string().uuid(),
    firstName: z.string(),
    lastName: z.string(),
    email: z.string().email()
});

export const zSearchCollectionContactResponse = z.object({
    uuid: z.string().uuid(),
    firstName: z.string(),
    lastName: z.string(),
    email: z.string().email()
});

export const zSearchCollectionsResponseItem = z.object({
    collection: zGlobalSearchCollectionName,
    entity: z.union([
        zSearchCollectionUserResponse,
        zSearchCollectionContactResponse
    ]),
    text_match: z.number()
});

export const zSearchCollectionsResponse = z.object({
    items: z.array(zSearchCollectionsResponseItem)
});

export const zViewJobsIndexSortQueryKey = z.enum([
    'createdAt'
]);

export const zSortDirection = z.enum([
    'asc',
    'desc'
]);

export const zViewJobsIndexSortQuery = z.object({
    key: zViewJobsIndexSortQueryKey,
    order: zSortDirection
});

export const zQueueName = z.enum([
    'system'
]);

export const zViewJobsIndexFilterQuery = z.object({
    queueNames: z.array(zQueueName).optional(),
    archived: z.boolean().optional().default(false)
});

export const zViewJobsIndexQueryKey = z.object({
    createdAt: z.string().datetime().optional(),
    id: z.string()
});

export const zViewJobsIndexPaginationQuery = z.object({
    limit: z.number().gte(0).lte(100),
    key: z.union([
        zViewJobsIndexQueryKey,
        z.null()
    ]).optional()
});

export const zJobStatus = z.enum([
    'created',
    'active',
    'completed',
    'retry',
    'failed',
    'cancelled'
]);

export const zViewJobsIndexItemResponse = z.object({
    queueName: zQueueName,
    id: z.string().uuid(),
    name: z.string(),
    status: zJobStatus,
    createdAt: z.string().datetime(),
    completedAt: z.union([
        z.string().datetime(),
        z.null()
    ])
});

export const zViewJobsIndexResponseMeta = z.object({
    next: z.union([
        zViewJobsIndexQueryKey,
        z.null()
    ])
});

export const zViewJobsIndexResponse = z.object({
    items: z.array(zViewJobsIndexItemResponse),
    meta: zViewJobsIndexResponseMeta
});

export const zViewJobDetailResponse = z.object({
    id: z.string().uuid(),
    queueName: zQueueName,
    priority: z.number(),
    name: z.string(),
    data: z.record(z.unknown()),
    status: zJobStatus,
    retryLimit: z.number(),
    retryCount: z.number(),
    retryDelay: z.number(),
    retryBackoff: z.boolean(),
    startAfter: z.string().datetime(),
    startedAt: z.union([
        z.string().datetime(),
        z.null()
    ]),
    singletonKey: z.union([
        z.string(),
        z.null()
    ]),
    singletonOn: z.union([
        z.string().datetime(),
        z.null()
    ]),
    expireIn: z.record(z.unknown()),
    createdAt: z.string().datetime(),
    completedAt: z.union([
        z.string().datetime(),
        z.null()
    ]),
    keepUntil: z.string().datetime(),
    output: z.union([
        z.record(z.unknown()),
        z.null()
    ]),
    deadLetter: z.union([
        z.string(),
        z.null()
    ]),
    policy: z.union([
        z.string(),
        z.null()
    ])
});

export const zPreferenceTypes = z.object({
    email: z.array(zNotificationType),
    sms: z.array(zNotificationType),
    app: z.array(zNotificationType),
    push: z.array(zNotificationType)
});

export const zGetMyNotificationPreferencesResponse = z.object({
    preset: zNotificationPreset,
    emailEnabled: z.boolean(),
    smsEnabled: z.boolean(),
    appEnabled: z.boolean(),
    pushEnabled: z.boolean(),
    preferences: zPreferenceTypes
});

export const zNotificationTypeChannelConfig = z.object({
    channel: zNotificationChannel,
    defaultValue: z.boolean(),
    isSupported: z.boolean()
});

export const zNotificationTypeConfig = z.object({
    type: zNotificationType,
    channelConfigs: z.array(zNotificationTypeChannelConfig)
});

export const zGetNotificationTypesConfigResponse = z.object({
    items: z.array(zNotificationTypeConfig)
});

export const zUpdateMyChannelNotificationPreferenceCommand = z.object({
    channel: zNotificationChannel,
    isEnabled: z.boolean()
});

export const zSendTestNotificationCommand = z.object({
    message: z.string()
});

export const zGetMyNotificationsFilterQuery = z.object({
    onlyUnread: z.string().optional()
});

export const zGetMyNotificationsQueryKey = z.object({
    createdAt: z.string(),
    notificationUuid: z.string().uuid()
});

export const zGetMyNotificationsPaginationQuery = z.object({
    limit: z.number().gte(0).lte(100),
    key: zGetMyNotificationsQueryKey.optional()
});

export const zCreatedByUserResponse = z.object({
    uuid: z.string().uuid(),
    name: z.string()
});

export const zTestNotificationContent = z.object({
    message: z.string()
});

export const zTestNotificationNotification = z.object({
    createdAt: z.string().datetime(),
    readAt: z.union([
        z.string().datetime(),
        z.null()
    ]),
    notificationUuid: z.string().uuid(),
    createdByUser: z.union([
        zCreatedByUserResponse,
        z.null()
    ]),
    message: z.string(),
    type: z.enum([
        'test-notification'
    ]),
    meta: zTestNotificationContent
});

export const zGetMyNotificationsResponseMeta = z.object({
    next: z.union([
        zGetMyNotificationsQueryKey,
        z.null()
    ])
});

export const zGetMyNotificationsResponse = z.object({
    items: z.array(zTestNotificationNotification),
    meta: zGetMyNotificationsResponseMeta
});

export const zViewUnreadNotificationsCountResponse = z.object({
    amount: z.number().gte(0),
    exceedsLimit: z.boolean()
});

export const zUpdateMyNotificationTypePreferenceCommand = z.object({
    channel: zNotificationChannel,
    isEnabled: z.boolean(),
    types: z.array(zNotificationType)
});

export const zUserNotificationNotFoundError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '404'
    ]),
    code: z.enum([
        'user_notification_not_found'
    ])
});

export const zUpdateMyNotificationPreferencePresetCommand = z.object({
    preset: zNotificationPreset
});

export const zErrorSource = z.object({
    pointer: z.string()
});

export const zMigrationAlreadyPerformedErrorMeta = z.object({
    type: z.array(zNotificationType)
});

export const zMigrationAlreadyPerformedError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'migration_already_performed'
    ]),
    meta: zMigrationAlreadyPerformedErrorMeta
});

export const zMigrateNotificationTypesCommand = z.object({
    types: z.array(zNotificationType)
});

export const zCreateRoleCommand = z.object({
    name: z.string()
});

export const zCreateRoleResponse = z.object({
    uuid: z.string().uuid()
});

export const zClearRolePermissionsCacheCommand = z.object({
    roleUuids: z.union([
        z.array(z.string().uuid()),
        z.null()
    ]).optional()
});

export const zUpdateRoleCommand = z.object({
    name: z.string()
});

export const zRoleResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    name: z.string(),
    permissions: z.array(zPermission),
    isDefault: z.boolean(),
    isSystemAdmin: z.boolean()
});

export const zViewRoleIndexResponse = z.object({
    items: z.array(zRoleResponse)
});

export const zRoleNotFoundError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '404'
    ]),
    code: z.enum([
        'role_not_found'
    ])
});

export const zUpdateRolesPermissionsCommandItem = z.object({
    roleUuid: z.string().uuid(),
    permissions: z.array(zPermission)
});

export const zUpdateRolesPermissionsCommand = z.object({
    roles: z.array(zUpdateRolesPermissionsCommandItem)
});

export const zGetApiInfoResponse = z.object({
    environment: z.string(),
    commit: z.string(),
    version: z.string(),
    timestamp: z.string().datetime()
});

export const zUiTheme = z.enum([
    'light',
    'dark',
    'system'
]);

export const zLocale = z.enum([
    'en-GB',
    'nl-BE',
    'fr-FR',
    'es-ES',
    'de-DE'
]);

export const zFontSize = z.enum([
    'smaller',
    'small',
    'default',
    'large',
    'larger'
]);

export const zUpdateUiPreferencesCommand = z.object({
    theme: zUiTheme.optional(),
    language: zLocale.optional(),
    fontSize: zFontSize.optional(),
    showShortcuts: z.boolean().optional(),
    reduceMotion: z.boolean().optional(),
    highContrast: z.boolean().optional()
});

export const zViewUiPreferencesResponse = z.object({
    theme: zUiTheme,
    language: zLocale,
    fontSize: zFontSize,
    showShortcuts: z.boolean(),
    reduceMotion: z.boolean(),
    highContrast: z.boolean()
});

export const zAnnouncementType = z.enum([
    'informational',
    'urgent'
]);

export const zCreateAnnouncementTranslationCommand = z.object({
    title: z.string(),
    content: z.record(z.unknown()),
    language: zLocale
});

export const zCreateAnnouncementCommand = z.object({
    type: zAnnouncementType,
    startDate: z.string().datetime(),
    endDate: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    translations: z.array(zCreateAnnouncementTranslationCommand)
});

export const zCreateAnnouncementResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime()
});

export const zMissingRequiredFieldError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'missing_required_field'
    ])
});

export const zDateMustBeAfterError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'date_must_be_after'
    ])
});

export const zFieldMustBeNullError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'field_must_be_null'
    ])
});

export const zNotFoundError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '404'
    ]),
    code: z.enum([
        'not_found'
    ])
});

export const zUpdateAnnouncementTranslationCommand = z.object({
    title: z.string().optional(),
    content: z.record(z.unknown()).optional(),
    language: zLocale
});

export const zUpdateAnnouncementCommand = z.object({
    type: zAnnouncementType.optional(),
    startDate: z.string().datetime().optional(),
    endDate: z.union([
        z.string().datetime(),
        z.null()
    ]).optional(),
    translations: z.array(zUpdateAnnouncementTranslationCommand).optional()
});

export const zUpdateAnnouncementResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime()
});

export const zViewAnnouncementTranslationResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    title: z.string(),
    content: z.record(z.unknown()),
    language: zLocale
});

export const zDashboardAnnouncementIndexView = z.object({
    uuid: z.string().uuid(),
    type: zAnnouncementType,
    startDate: z.string().datetime(),
    endDate: z.union([
        z.string().datetime(),
        z.null()
    ]),
    translation: zViewAnnouncementTranslationResponse
});

export const zViewDashboardAnnouncementIndexResponse = z.object({
    items: z.array(zDashboardAnnouncementIndexView),
    meta: zPaginatedOffsetResponseMeta
});

export const zViewDashboardAnnouncementResponse = z.object({
    uuid: z.string().uuid(),
    type: zAnnouncementType,
    startDate: z.string().datetime(),
    endDate: z.union([
        z.string().datetime(),
        z.null()
    ]),
    translation: zViewAnnouncementTranslationResponse
});

export const zPublishStatus = z.enum([
    'scheduled',
    'published',
    'archived'
]);

export const zViewNewsItemTranslationResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    title: z.union([
        z.string(),
        z.null()
    ]),
    content: z.union([
        z.record(z.unknown()),
        z.null()
    ]),
    language: zLocale
});

export const zViewNewsItemAuthorResponse = z.object({
    uuid: z.string().uuid(),
    email: z.string().email(),
    firstName: z.union([
        z.string(),
        z.null()
    ]),
    lastName: z.union([
        z.string(),
        z.null()
    ])
});

export const zViewAnnouncementResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    publishStatus: zPublishStatus,
    type: zAnnouncementType,
    startDate: z.string().datetime(),
    endDate: z.union([
        z.string().datetime(),
        z.null()
    ]),
    translations: z.array(zViewNewsItemTranslationResponse),
    author: zViewNewsItemAuthorResponse
});

export const zViewAnnouncementTranslationIndexResponse = z.object({
    uuid: z.string().uuid(),
    title: z.string(),
    language: zLocale
});

export const zViewAnnouncementAuthorResponse = z.object({
    uuid: z.string().uuid(),
    email: z.string().email(),
    firstName: z.union([
        z.string(),
        z.null()
    ]),
    lastName: z.union([
        z.string(),
        z.null()
    ])
});

export const zAnnouncementIndexView = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    publishStatus: zPublishStatus,
    type: zAnnouncementType,
    startDate: z.string().datetime(),
    endDate: z.union([
        z.string().datetime(),
        z.null()
    ]),
    translations: z.array(zViewAnnouncementTranslationIndexResponse),
    author: zViewAnnouncementAuthorResponse
});

export const zViewAnnouncementIndexResponse = z.object({
    items: z.array(zAnnouncementIndexView),
    meta: zPaginatedOffsetResponseMeta
});

export const zCertificateFileNotFoundError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '404'
    ]),
    code: z.enum([
        'certificate_file_not_found'
    ])
});

export const zCertificateType = z.enum([
    'cor',
    'cot_cob'
]);

export const zDownloadCertificateCommand = z.object({
    fileName: z.string(),
    type: zCertificateType
});

export const zViewCertificateIndexSortKey = z.enum([
    'salesOrder',
    'salesOrderLine',
    'collectionDate',
    'deliveryDate',
    'dispositionPickUpDate',
    'dispositionDeliveryDate',
    'treatmentCentre',
    'endTreatmentCentre',
    'ewc',
    'wtfForm',
    'tfs',
    'disposalDate',
    'printDate',
    'pickUpAddress',
    'customerId',
    'wasteProducerId',
    'contractItem',
    'contract',
    'invoice'
]);

export const zViewCertificateIndexSortQuery = z.object({
    key: zViewCertificateIndexSortKey,
    order: zSortDirection
});

export const zCertificateDocType = z.enum([
    'receipt-confirmation',
    'blending-confirmation',
    'treatment-certificates',
    'certificate-of-treatment',
    'tf-certificates'
]);

export const zDateRange = z.object({
    from: z.string().date(),
    to: z.string().date()
});

export const zViewCertificateIndexFilterQuery = z.object({
    docTypes: z.array(zCertificateDocType).optional(),
    salesOrder: z.string().optional(),
    salesOrderLine: z.string().optional(),
    description: z.string().optional(),
    collectionDate: zDateRange.optional(),
    deliveryDate: zDateRange.optional(),
    dispositionPickUpDate: zDateRange.optional(),
    dispositionDeliveryDate: zDateRange.optional(),
    treatmentCentre: z.string().optional(),
    endTreatmentCentre: z.string().optional(),
    ewc: z.string().optional(),
    wtfForm: z.string().optional(),
    tfs: z.string().optional(),
    disposalDate: zDateRange.optional(),
    printDate: zDateRange.optional(),
    wasteProducerId: z.string().optional(),
    customerId: z.string().optional(),
    contract: z.string().optional(),
    contractItem: z.string().optional(),
    invoice: z.string().optional(),
    pickUpAddressId: z.string().optional()
});

export const zCertificateIndexResponse = z.object({
    docType: z.union([
        zCertificateDocType,
        z.null()
    ]),
    salesOrder: z.union([
        z.string(),
        z.null()
    ]),
    salesOrderLine: z.union([
        z.string(),
        z.null()
    ]),
    description: z.union([
        z.string(),
        z.null()
    ]),
    collectionDate: z.union([
        z.string().datetime(),
        z.null()
    ]),
    deliveryDate: z.union([
        z.string().datetime(),
        z.null()
    ]),
    dispositionPickUpDate: z.union([
        z.string().datetime(),
        z.null()
    ]),
    dispositionDeliveryDate: z.union([
        z.string().datetime(),
        z.null()
    ]),
    treatmentCentre: z.union([
        z.string(),
        z.null()
    ]),
    endTreatmentCentre: z.union([
        z.string(),
        z.null()
    ]),
    ewcCode: z.union([
        z.string(),
        z.null()
    ]),
    wtfForm: z.union([
        z.string(),
        z.null()
    ]),
    tfs: z.union([
        z.string(),
        z.null()
    ]),
    disposalDate: z.union([
        z.string().datetime(),
        z.null()
    ]),
    printDate: z.union([
        z.string().datetime(),
        z.null()
    ]),
    wasteProducerId: z.union([
        z.string(),
        z.null()
    ]),
    wasteProducerName: z.union([
        z.string(),
        z.null()
    ]),
    contract: z.union([
        z.string(),
        z.null()
    ]),
    contractItem: z.union([
        z.string(),
        z.null()
    ]),
    fileName: z.string(),
    invoice: z.union([
        z.string(),
        z.null()
    ]),
    pickUpAddressId: z.union([
        z.string(),
        z.null()
    ]),
    pickUpAddressName: z.union([
        z.string(),
        z.null()
    ]),
    customerId: z.union([
        z.string(),
        z.null()
    ]),
    customerName: z.union([
        z.string(),
        z.null()
    ])
});

export const zViewCertificateIndexResponse = z.object({
    items: z.array(zCertificateIndexResponse),
    meta: zPaginatedOffsetResponseMeta
});

export const zContactResponse = z.object({
    uuid: z.string().uuid(),
    firstName: z.string(),
    lastName: z.string(),
    email: z.string().email()
});

export const zViewContactIndexResponse = z.object({
    items: z.array(zContactResponse),
    meta: zPaginatedOffsetResponseMeta
});

export const zCreateContactCommand = z.object({
    firstName: z.string().max(40),
    lastName: z.string().max(40),
    email: z.string().email().max(241)
});

export const zCreateContactResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime()
});

export const zUpdateContactCommand = z.object({
    firstName: z.string().max(40).optional(),
    lastName: z.string().max(40).optional(),
    email: z.string().email().max(241).optional()
});

export const zViewContainerTypeIndexFilterQuery = z.object({
    customerId: z.string()
});

export const zContainerTypeResponse = z.object({
    name: z.string()
});

export const zViewContainerTypeIndexResponse = z.object({
    items: z.array(zContainerTypeResponse)
});

export const zViewContractLineIndexSortQueryKey = z.enum([
    'contractNumber',
    'contractItem',
    'wasteMaterial',
    'treatmentCenterName',
    'installationName',
    'wasteProducerId',
    'wasteProducerName',
    'pickUpAddressId',
    'pickUpAddressName',
    'asn',
    'tcNumber',
    'ewcCode',
    'endTreatmentCenterId',
    'endTreatmentCenterName',
    'processCode',
    'esnNumber'
]);

export const zViewContractLineIndexSortQuery = z.object({
    key: zViewContractLineIndexSortQueryKey,
    order: zSortDirection
});

export const zViewContractLineIndexFilterQuery = z.object({
    customerId: z.string().optional(),
    wasteProducerId: z.string().optional(),
    pickUpAddressIds: z.array(z.string()).optional(),
    contractNumber: z.string().optional(),
    contractItem: z.string().optional(),
    customerReference: z.string().optional(),
    wasteMaterial: z.string().optional(),
    materialNumber: z.string().optional(),
    treatmentCenterName: z.string().optional(),
    installationName: z.string().optional(),
    asn: z.string().optional(),
    tfs: z.boolean().optional(),
    isHazardous: z.boolean().optional(),
    packaged: z.boolean().optional(),
    tcNumber: z.string().optional(),
    materialAnalysis: z.string().optional(),
    ewcCode: z.string().optional(),
    endTreatmentCenterId: z.string().optional(),
    endTreatmentCenterName: z.string().optional(),
    processCode: z.string().optional(),
    deliveryInfo: z.string().optional(),
    esnNumber: z.string().optional()
});

export const zContractLinePackagingType = z.enum([
    'packaged',
    'bulk'
]);

export const zContractLineResponse = z.object({
    contractLineId: z.string(),
    contractNumber: z.string(),
    contractItem: z.string(),
    customerReference: z.union([
        z.string(),
        z.null()
    ]),
    wasteMaterial: z.union([
        z.string(),
        z.null()
    ]),
    materialNumber: z.union([
        z.string(),
        z.null()
    ]),
    treatmentCenterName: z.union([
        z.string(),
        z.null()
    ]),
    installationName: z.union([
        z.string(),
        z.null()
    ]),
    customerId: z.union([
        z.string(),
        z.null()
    ]),
    customerName: z.union([
        z.string(),
        z.null()
    ]),
    wasteProducerId: z.union([
        z.string(),
        z.null()
    ]),
    wasteProducerName: z.union([
        z.string(),
        z.null()
    ]),
    pickUpAddressId: z.union([
        z.string(),
        z.null()
    ]),
    pickUpAddressName: z.union([
        z.string(),
        z.null()
    ]),
    asn: z.union([
        z.string(),
        z.null()
    ]),
    tfs: z.union([
        z.boolean(),
        z.null()
    ]),
    isHazardous: z.union([
        z.boolean(),
        z.null()
    ]),
    packaged: z.union([
        zContractLinePackagingType,
        z.null()
    ]),
    tcNumber: z.union([
        z.string(),
        z.null()
    ]),
    materialAnalysis: z.union([
        z.string(),
        z.null()
    ]),
    ewcCode: z.union([
        z.string(),
        z.null()
    ]),
    endTreatmentCenterId: z.union([
        z.string(),
        z.null()
    ]),
    endTreatmentCenterName: z.union([
        z.string(),
        z.null()
    ]),
    remarks: z.union([
        z.string(),
        z.null()
    ]),
    processCode: z.union([
        z.string(),
        z.null()
    ]),
    esnNumber: z.union([
        z.string(),
        z.null()
    ]),
    deliveryInfo: z.union([
        z.string(),
        z.null()
    ])
});

export const zViewContractLineIndexResponse = z.object({
    items: z.array(zContractLineResponse),
    meta: zPaginatedOffsetResponseMeta
});

export const zViewWprContractLineIndexFilterQuery = z.object({
    customerId: z.string(),
    wasteProducerId: z.string().optional(),
    pickUpAddressIds: z.array(z.string()).optional()
});

export const zWprContractLineResponse = z.object({
    pickUpRequestUuid: z.string(),
    contractLineId: z.string(),
    contractNumber: z.string(),
    contractItem: z.string(),
    customerReference: z.union([
        z.string(),
        z.null()
    ]),
    wasteMaterial: z.union([
        z.string(),
        z.null()
    ]),
    materialNumber: z.union([
        z.string(),
        z.null()
    ]),
    treatmentCenterName: z.union([
        z.string(),
        z.null()
    ]),
    installationName: z.union([
        z.string(),
        z.null()
    ]),
    customerId: z.union([
        z.string(),
        z.null()
    ]),
    customerName: z.union([
        z.string(),
        z.null()
    ]),
    wasteProducerId: z.union([
        z.string(),
        z.null()
    ]),
    wasteProducerName: z.union([
        z.string(),
        z.null()
    ]),
    pickUpAddressId: z.union([
        z.string(),
        z.null()
    ]),
    pickUpAddressName: z.union([
        z.string(),
        z.null()
    ]),
    asn: z.union([
        z.string(),
        z.null()
    ]),
    tfs: z.union([
        z.boolean(),
        z.null()
    ]),
    isHazardous: z.union([
        z.boolean(),
        z.null()
    ]),
    packaged: z.union([
        z.string(),
        z.null()
    ]),
    tcNumber: z.union([
        z.string(),
        z.null()
    ]),
    materialAnalysis: z.union([
        z.string(),
        z.null()
    ]),
    ewcCode: z.union([
        z.string(),
        z.null()
    ]),
    endTreatmentCenterId: z.union([
        z.string(),
        z.null()
    ]),
    endTreatmentCenterName: z.union([
        z.string(),
        z.null()
    ]),
    remarks: z.union([
        z.string(),
        z.null()
    ]),
    processCode: z.union([
        z.string(),
        z.null()
    ]),
    esnNumber: z.union([
        z.string(),
        z.null()
    ]),
    deliveryInfo: z.union([
        z.string(),
        z.null()
    ])
});

export const zViewWprContractLineIndexResponse = z.object({
    items: z.array(zWprContractLineResponse),
    meta: zPaginatedOffsetResponseMeta
});

export const zViewPackagingRequestContractLineIndexFilterQuery = z.object({
    customerId: z.string(),
    wasteProducerId: z.string(),
    deliveryAddressIds: z.array(z.string()).optional()
});

export const zPackagingRequestContractLineResponse = z.object({
    contractLineId: z.string(),
    contractNumber: z.string(),
    contractItem: z.string(),
    materialNumber: z.union([
        z.string(),
        z.null()
    ]),
    wasteMaterial: z.union([
        z.string(),
        z.null()
    ]),
    isSales: z.union([
        z.boolean(),
        z.null()
    ]),
    imageUrl: z.union([
        z.string(),
        z.null()
    ])
});

export const zViewPackagingRequestContractLineIndexResponse = z.object({
    items: z.array(zPackagingRequestContractLineResponse),
    meta: zPaginatedOffsetResponseMeta
});

export const zContractLineNotAccessibleErrorMeta = z.object({
    contractNumber: z.string(),
    contractLineNumber: z.string(),
    tcNumber: z.union([
        z.string(),
        z.null()
    ])
});

export const zContractLineNotAccessibleError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'contract_line_not_accessible'
    ]),
    meta: zContractLineNotAccessibleErrorMeta
});

export const zContractLinesSelection = z.object({
    contractNumber: z.string(),
    contractItem: z.string()
});

export const zContractLineQuerySelection = z.object({
    filter: zViewContractLineIndexFilterQuery,
    excludeSelection: z.array(zContractLinesSelection).min(1).optional()
});

export const zGenerateContractLinesPdfCommand = z.object({
    selection: z.array(zContractLinesSelection).min(1).optional(),
    querySelection: zContractLineQuerySelection.optional()
});

export const zGenerateContractLinesPdfResponse = z.object({
    pageCount: z.number(),
    size: z.number(),
    name: z.string(),
    mimeType: z.string(),
    content: z.string()
});

export const zDocumentNotFoundErrorMeta = z.object({
    customerUuid: z.string().uuid(),
    documentId: z.string()
});

export const zDocumentNotFoundError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '404'
    ]),
    code: z.enum([
        'document_not_found'
    ]),
    meta: zDocumentNotFoundErrorMeta
});

export const zDownloadDocumentCommand = z.object({
    documentId: z.string(),
    customerUuid: z.string().uuid()
});

export const zForbiddenError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '403'
    ]),
    code: z.enum([
        'forbidden'
    ])
});

export const zSharepointDocumentViewName = z.enum([
    'mastertable',
    'tfs',
    'quotation',
    'meetings',
    'manual',
    'bsc',
    'contract',
    'transport'
]);

export const zGetDocumentFiltersFilterQuery = z.object({
    viewName: zSharepointDocumentViewName,
    customerUuid: z.string().uuid(),
    wasteProducerIds: z.array(z.string())
});

export const zDocumentFilterValue = z.object({
    key: z.string(),
    value: z.string()
});

export const zDocumentFilterResponse = z.object({
    filterName: z.string(),
    filterValues: z.array(zDocumentFilterValue)
});

export const zGetDocumentFiltersResponse = z.object({
    filters: z.array(zDocumentFilterResponse)
});

export const zViewDocumentIndexPaginationQuery = z.object({
    limit: z.number().gte(0).lte(100),
    key: z.union([
        z.string(),
        z.null()
    ]).optional()
});

export const zSharepointDocumentStatus = z.enum([
    'Active',
    'Archived'
]);

export const zViewDocumentIndexFilterQuery = z.object({
    viewName: zSharepointDocumentViewName,
    customerUuid: z.string().uuid(),
    wasteProducerIds: z.array(z.string()),
    status: zSharepointDocumentStatus.optional(),
    year: z.string().optional(),
    refExt: z.string().optional(),
    transportType: z.string().optional()
});

export const zSharepointDocumentType = z.unknown();

export const zViewDocumentIndexItemResponse = z.object({
    id: z.string(),
    name: z.string(),
    actionAt: z.union([
        z.string().datetime(),
        z.null()
    ]),
    applicableFrom: z.union([
        z.string().datetime(),
        z.null()
    ]),
    applicableTill: z.union([
        z.string().datetime(),
        z.null()
    ]),
    tfsType: z.union([
        zSharepointDocumentType,
        z.null()
    ]),
    wasteProducer: z.string(),
    status: z.union([
        zSharepointDocumentStatus,
        z.null()
    ])
});

export const zViewDocumentIndexResponseMeta = z.object({
    next: z.union([
        z.string(),
        z.null()
    ])
});

export const zViewDocumentIndexResponse = z.object({
    items: z.array(zViewDocumentIndexItemResponse),
    meta: zViewDocumentIndexResponseMeta
});

export const zViewUserSiteIndexWasteProducerResponse = z.object({
    id: z.number(),
    name: z.string()
});

export const zViewUserSiteIndexResponse = z.object({
    uuid: z.string().uuid(),
    name: z.string(),
    wasteProducers: z.array(zViewUserSiteIndexWasteProducerResponse)
});

export const zDynamicColumnNames = z.enum([
    'docType',
    'salesOrder',
    'salesOrderLine',
    'description',
    'collectionDate',
    'deliveryDate',
    'dispositionPickUpDate',
    'dispositionDeliveryDate',
    'treatmentCentre',
    'endTreatmentCentre',
    'ewcCode',
    'wtfForm',
    'tfs',
    'disposalDate',
    'printDate',
    'wasteProducerName',
    'wasteProducerId',
    'contract',
    'contractItem',
    'invoice',
    'pickUpAddressName',
    'pickUpAddressId',
    'customerId',
    'customerName',
    'contractNumber',
    'customerReference',
    'wasteMaterial',
    'materialNumber',
    'treatmentCenterName',
    'installationName',
    'asn',
    'isHazardous',
    'packaged',
    'tcNumber',
    'materialAnalysis',
    'endTreatmentCenterId',
    'endTreatmentCenterName',
    'remarks',
    'processCode',
    'esnNumber',
    'deliveryInfo',
    'materialType',
    'packagingIndicator',
    'invoiceNumber',
    'status',
    'payerId',
    'payerName',
    'issuedOn',
    'firstReminderMailStatus',
    'firstReminderOn',
    'secondReminderMailStatus',
    'secondReminderOn',
    'thirdReminderMailStatus',
    'autoApprovedOn',
    'netAmount',
    'vatAmount',
    'currency',
    'customerApprovalBy',
    'customerApprovalDate',
    'poNumber',
    'accountDocumentNumber',
    'estimatedWeightOrVolumeValue',
    'estimatedWeightOrVolumeUnit',
    'packagingType',
    'quantityPackages',
    'quantityLabels',
    'quantityPallets',
    'unNumber',
    'packingGroup',
    'dangerLabel1',
    'dangerLabel2',
    'dangerLabel3',
    'costCenter',
    'containerType',
    'containerVolumeSize',
    'containerNumber',
    'containerTransportType',
    'isContainerCovered',
    'tankerType',
    'totalQuantityPallets',
    'isReturnPackaging',
    'packagingRemark',
    'reconciliationNumber',
    'hazardInducers',
    'quantityContainers',
    'tfsNumber',
    'serialNumber',
    'dueOn',
    'type',
    'accountManagerName',
    'companyName',
    'requestNumber',
    'transportMode',
    'dateOfRequest',
    'accountManager',
    'isTransportByIndaver',
    'requestedStartDate',
    'requestedEndDate',
    'confirmedTransportDate',
    'nameOfApplicant',
    'orderNumber',
    'nameInstallation',
    'disposalCertificateNumber',
    'ewc',
    'inquiryNumber',
    'wasteStreamName',
    'date',
    'contractId',
    'salesOrganisationId',
    'salesOrganisationName',
    'requestorName',
    'conformityCheck',
    'shipmentId',
    'weightOrVolume',
    'unit',
    'transportDate'
]);

export const zDynamicTableColumnIndexView = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    name: zDynamicColumnNames,
    isHidable: z.boolean(),
    applicableFields: z.array(z.string()),
    filterableField: z.union([
        z.string(),
        z.null()
    ]),
    sortableFields: z.array(z.string()),
    searchableFields: z.array(z.string())
});

export const zDynamicTableIndexColumnResponse = z.object({
    items: z.array(zDynamicTableColumnIndexView)
});

export const zColumnNotFoundError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'column_not_found'
    ])
});

export const zColumnNotFilterableError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'column_not_filterable'
    ])
});

export const zColumnNotSortableError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'column_not_sortable'
    ])
});

export const zDuplicateColumnError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'duplicate_column'
    ])
});

export const zDynamicTableViewFilterCommand = z.object({
    columnUuid: z.string().uuid(),
    value: z.union([
        z.string(),
        z.array(z.string()),
        z.boolean(),
        z.object({
            key: z.string().optional(),
            value: z.string().optional()
        }),
        z.array(z.object({
            key: z.string().optional(),
            value: z.string().optional()
        }))
    ])
});

export const zDynamicTableViewSortCommand = z.object({
    columnUuid: z.string().uuid(),
    direction: zSortDirection
});

export const zDynamicTableViewVisibleColumnsCommand = z.object({
    columnUuid: z.string().uuid()
});

export const zCreateDynamicTableViewCommand = z.object({
    viewName: z.string(),
    isGlobal: z.boolean().optional(),
    isGlobalDefault: z.boolean().optional(),
    isDefault: z.boolean().optional(),
    filters: z.array(zDynamicTableViewFilterCommand),
    sorts: z.array(zDynamicTableViewSortCommand),
    visibleColumns: z.array(zDynamicTableViewVisibleColumnsCommand)
});

export const zCreateDynamicTableViewResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime()
});

export const zVisibilityConfigurationResponse = z.object({
    uuid: z.string().uuid(),
    order: z.number()
});

export const zSortConfigurationResponse = z.object({
    uuid: z.string().uuid(),
    order: z.number(),
    direction: zSortDirection
});

export const zFilterConfigurationResponse = z.object({
    uuid: z.string().uuid(),
    value: z.union([
        z.string(),
        z.array(z.string()),
        z.boolean(),
        z.object({
            key: z.string().optional(),
            value: z.string().optional()
        }),
        z.array(z.object({
            key: z.string().optional(),
            value: z.string().optional()
        }))
    ])
});

export const zViewDefaultDynamicTableViewResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    name: z.string(),
    isGlobal: z.boolean(),
    isDefaultGlobal: z.boolean(),
    isUserDefault: z.boolean(),
    visibleColumns: z.array(zVisibilityConfigurationResponse),
    sorts: z.array(zSortConfigurationResponse),
    filters: z.array(zFilterConfigurationResponse)
});

export const zUpdateDynamicTableViewCommand = z.object({
    viewName: z.string().optional(),
    isGlobal: z.boolean().optional(),
    isGlobalDefault: z.boolean().optional(),
    isDefault: z.boolean().optional(),
    filters: z.array(zDynamicTableViewFilterCommand).optional(),
    sorts: z.array(zDynamicTableViewSortCommand).optional(),
    visibleColumns: z.array(zDynamicTableViewVisibleColumnsCommand).optional()
});

export const zUpdateDynamicTableViewResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime()
});

export const zDynamicTableViewResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    name: z.string(),
    isGlobal: z.boolean(),
    isDefaultGlobal: z.boolean(),
    isUserDefault: z.boolean(),
    visibleColumns: z.array(zVisibilityConfigurationResponse),
    sorts: z.array(zSortConfigurationResponse),
    filters: z.array(zFilterConfigurationResponse)
});

export const zDynamicTableViewIndexResponse = z.object({
    items: z.array(zDynamicTableViewResponse),
    meta: zPaginatedOffsetResponseMeta
});

export const zGlobalDefaultViewNotDeletable = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'global_default_view_not_deletable'
    ])
});

export const zLastGlobalViewNotDeletable = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'last_global_view_not_deletable'
    ])
});

export const zEwcCodeResponse = z.object({
    code: z.string(),
    description: z.string()
});

export const zViewEwcCodeIndexResponse = z.object({
    items: z.array(zEwcCodeResponse)
});

export const zDownloadGuidanceLetterType = z.enum([
    'print',
    'preview',
    'attachment'
]);

export const zGuidanceLetterDownloadTypeNotFoundErrorMeta = z.object({
    type: zDownloadGuidanceLetterType
});

export const zGuidanceLetterDownloadTypeNotFoundError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '404'
    ]),
    code: z.enum([
        'guidance_letter_download_type_not_found'
    ]),
    meta: zGuidanceLetterDownloadTypeNotFoundErrorMeta
});

export const zGuidanceLetterNotFoundErrorMeta = z.object({
    id: z.string()
});

export const zGuidanceLetterNotFoundError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '404'
    ]),
    code: z.enum([
        'guidance_letter_not_found'
    ]),
    meta: zGuidanceLetterNotFoundErrorMeta
});

export const zViewGuidanceLetterIndexSortQueryKey = z.enum([
    'shipmentId',
    'salesDoc',
    'requestNumber',
    'wasteMaterial',
    'transportDate',
    'customerId',
    'wasteProducerId',
    'pickUpAddressId',
    'customerName',
    'wasteProducerName',
    'pickUpAddressName'
]);

export const zViewGuidanceLetterIndexSortQuery = z.object({
    key: zViewGuidanceLetterIndexSortQueryKey,
    order: zSortDirection
});

export const zViewGuidanceLetterIndexFilterQuery = z.object({
    shipmentId: z.string().optional(),
    requestNumber: z.string().optional(),
    salesDoc: z.string().optional(),
    wasteMaterial: z.string().optional(),
    transportDate: zDateRange.optional(),
    customerId: z.string().optional(),
    wasteProducerId: z.string().optional(),
    pickUpAddressId: z.string().optional()
});

export const zGuidanceLetterResponse = z.object({
    shipmentId: z.string(),
    salesDoc: z.string(),
    guidanceLetter: z.boolean(),
    attachment: z.boolean(),
    requestNumber: z.union([
        z.string(),
        z.null()
    ]),
    wasteMaterial: z.union([
        z.string(),
        z.null()
    ]),
    weightOrVolume: z.union([
        z.number(),
        z.null()
    ]),
    unit: z.union([
        z.string(),
        z.null()
    ]),
    transportDate: z.union([
        z.string(),
        z.null()
    ]),
    customerId: z.union([
        z.string(),
        z.null()
    ]),
    wasteProducerId: z.union([
        z.string(),
        z.null()
    ]),
    pickUpAddressId: z.union([
        z.string(),
        z.null()
    ]),
    customerName: z.union([
        z.string(),
        z.null()
    ]),
    wasteProducerName: z.union([
        z.string(),
        z.null()
    ]),
    pickUpAddressName: z.union([
        z.string(),
        z.null()
    ])
});

export const zViewGuidanceLetterIndexResponse = z.object({
    items: z.array(zGuidanceLetterResponse),
    meta: zPaginatedOffsetResponseMeta
});

export const zDownloadInvoiceCertificateCommand = z.object({
    fileName: z.string()
});

export const zInvoiceDocumentNotFoundErrorMeta = z.object({
    invoiceNumber: z.string()
});

export const zInvoiceDocumentNotFoundError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '404'
    ]),
    code: z.enum([
        'invoice_document_not_found'
    ]),
    meta: zInvoiceDocumentNotFoundErrorMeta
});

export const zInvoiceNotFoundErrorMeta = z.object({
    invoiceNumber: z.string()
});

export const zInvoiceNotFoundError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '404'
    ]),
    code: z.enum([
        'invoice_not_found'
    ]),
    meta: zInvoiceNotFoundErrorMeta
});

export const zViewInvoiceIndexSortKey = z.enum([
    'invoiceNumber',
    'customerReference',
    'issueDate',
    'payerId',
    'payerName',
    'customerName',
    'customerId',
    'accountDocumentNumber',
    'dueDate',
    'netAmount',
    'companyName'
]);

export const zViewInvoiceIndexSortQuery = z.object({
    key: zViewInvoiceIndexSortKey,
    order: zSortDirection
});

export const zInvoiceStatus = z.enum([
    'overdue',
    'outstanding',
    'cleared'
]);

export const zInvoiceColumnName = z.enum([
    'invoiceNumber',
    'status',
    'issuedOn',
    'customerName',
    'dueOn',
    'customerReference',
    'type',
    'payerId',
    'payerName',
    'netAmount',
    'vatAmount',
    'currency',
    'accountDocumentNumber',
    'accountManagerName',
    'companyName'
]);

export const zExportInvoicesExcelFilterQuery = z.object({
    statuses: z.array(zInvoiceStatus),
    columns: z.array(zInvoiceColumnName),
    translatedColumns: z.array(z.string())
});

export const zInvoiceFilterType = z.enum([
    'invoice',
    'credit_memo',
    'debit_memo'
]);

export const zViewInvoiceIndexFilterQuery = z.object({
    statuses: z.array(zInvoiceStatus),
    type: zInvoiceFilterType.optional(),
    invoiceNumber: z.string().optional(),
    issueDate: zDateRange.optional(),
    customerReference: z.string().optional(),
    payerId: z.string().optional(),
    customerId: z.string().optional(),
    dueDate: zDateRange.optional(),
    accountDocumentNumber: z.string().optional(),
    accountManagerName: z.string().optional(),
    companyName: z.string().optional()
});

export const zInvoiceType = z.enum([
    'invoice',
    'credit_memo',
    'debit_memo',
    'unknown'
]);

export const zInvoiceResponse = z.object({
    invoiceNumber: z.string(),
    status: zInvoiceStatus,
    issuedOn: z.string().date(),
    dueOn: z.union([
        z.string().date(),
        z.null()
    ]),
    customerId: z.string(),
    customerName: z.string(),
    customerReference: z.union([
        z.string(),
        z.null()
    ]),
    type: zInvoiceType,
    payerId: z.string(),
    payerName: z.string(),
    netAmount: z.string(),
    vatAmount: z.string(),
    currency: z.string(),
    accountDocumentNumber: z.union([
        z.string(),
        z.null()
    ]),
    accountManagerName: z.union([
        z.string(),
        z.null()
    ]),
    companyName: z.string()
});

export const zViewInvoiceIndexResponse = z.object({
    items: z.array(zInvoiceResponse),
    meta: zPaginatedOffsetResponseMeta
});

export const zViewInvoiceResponse = z.object({
    invoiceNumber: z.string(),
    status: zInvoiceStatus,
    issuedOn: z.string().date(),
    dueOn: z.union([
        z.string().date(),
        z.null()
    ]),
    customerName: z.string(),
    customerReference: z.union([
        z.string(),
        z.null()
    ]),
    type: zInvoiceType,
    payerId: z.string(),
    payerName: z.string(),
    netAmount: z.string(),
    vatAmount: z.string(),
    currency: z.string(),
    accountDocumentNumber: z.union([
        z.string(),
        z.null()
    ]),
    accountManagerName: z.union([
        z.string(),
        z.null()
    ]),
    companyName: z.string(),
    certificateFileName: z.union([
        z.string(),
        z.null()
    ])
});

export const zDraftInvoiceStatus = z.enum([
    'approved_by_customer',
    'auto_approved',
    'internal_approved',
    'rejected_by_customer',
    'rejected_by_indaver',
    'to_be_approved_by_customer',
    'to_be_approved_by_indaver'
]);

export const zMailStatus = z.enum([
    'sent',
    'not_sent'
]);

export const zViewDraftInvoiceDetailResponse = z.object({
    invoiceNumber: z.string(),
    status: zDraftInvoiceStatus,
    payerId: z.string(),
    payerName: z.string(),
    issuedOn: z.string().date(),
    firstReminderMailStatus: zMailStatus,
    firstReminderOn: z.union([
        z.string().date(),
        z.null()
    ]),
    secondReminderMailStatus: zMailStatus,
    secondReminderOn: z.union([
        z.string().date(),
        z.null()
    ]),
    thirdReminderMailStatus: zMailStatus,
    autoApprovedOn: z.union([
        z.string().date(),
        z.null()
    ]),
    netAmount: z.string(),
    vatAmount: z.string(),
    currency: z.string(),
    customerApprovalBy: z.union([
        z.string(),
        z.null()
    ]),
    customerApprovalDate: z.union([
        z.string().date(),
        z.null()
    ]),
    poNumber: z.union([
        z.string(),
        z.null()
    ]),
    accountDocumentNumber: z.union([
        z.string(),
        z.null()
    ]),
    customerId: z.string()
});

export const zViewDraftInvoiceIndexSortQueryKey = z.enum([
    'invoiceNumber'
]);

export const zViewDraftInvoiceIndexSortQuery = z.object({
    key: zViewDraftInvoiceIndexSortQueryKey,
    order: zSortDirection
});

export const zDraftInvoiceFilterStatus = z.enum([
    'to_be_approved',
    'approved',
    'rejected'
]);

export const zViewDraftInvoiceIndexFilterQuery = z.object({
    statuses: z.array(zDraftInvoiceFilterStatus).min(1),
    invoiceNumber: z.string().optional(),
    payerId: z.string().optional(),
    customerId: z.string().optional(),
    issuedOn: zDateRange.optional()
});

export const zDraftInvoiceResponse = z.object({
    invoiceNumber: z.string(),
    status: zDraftInvoiceStatus,
    payerId: z.string(),
    payerName: z.string(),
    issuedOn: z.string().date(),
    firstReminderMailStatus: zMailStatus,
    firstReminderOn: z.union([
        z.string().date(),
        z.null()
    ]),
    secondReminderMailStatus: zMailStatus,
    secondReminderOn: z.union([
        z.string().date(),
        z.null()
    ]),
    thirdReminderMailStatus: zMailStatus,
    autoApprovedOn: z.union([
        z.string().date(),
        z.null()
    ]),
    netAmount: z.string(),
    vatAmount: z.string(),
    currency: z.string(),
    customerApprovalBy: z.union([
        z.string(),
        z.null()
    ]),
    customerApprovalDate: z.union([
        z.string().date(),
        z.null()
    ]),
    poNumber: z.union([
        z.string(),
        z.null()
    ]),
    accountDocumentNumber: z.union([
        z.string(),
        z.null()
    ]),
    customerId: z.string()
});

export const zViewDraftInvoiceIndexResponse = z.object({
    items: z.array(zDraftInvoiceResponse),
    meta: zPaginatedOffsetResponseMeta
});

export const zExportDraftInvoicesExcelFilterQuery = z.object({
    statuses: z.array(zDraftInvoiceFilterStatus),
    columns: z.array(zInvoiceColumnName),
    translatedColumns: z.array(z.string())
});

export const zNonApproveOrRejectableDraftInvoiceError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '409'
    ]),
    code: z.enum([
        'non_approve_or_rejectable_draft_invoice'
    ])
});

export const zApproveDraftInvoiceCommand = z.object({
    poNumber: z.union([
        z.string(),
        z.null()
    ]),
    remark: z.union([
        z.string(),
        z.null()
    ])
});

export const zRejectDraftInvoiceCommand = z.object({
    remark: z.string()
});

export const zAlreadySubscribedError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '409'
    ]),
    code: z.enum([
        'already_subscribed'
    ])
});

export const zOptInAlreadyRequestedError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '409'
    ]),
    code: z.enum([
        'opt_in_already_requested'
    ])
});

export const zSubscribeToNewsletterCommand = z.object({
    email: z.string()
});

export const zCreateNewsItemTranslationCommand = z.object({
    title: z.string(),
    content: z.record(z.unknown()),
    language: zLocale
});

export const zNewsItemSalesOrganisation = z.object({
    id: z.string(),
    name: z.string()
});

export const zCreateNewsItemCommand = z.object({
    startDate: z.string().date(),
    endDate: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    imageUuid: z.string().uuid(),
    newsItemTranslations: z.array(zCreateNewsItemTranslationCommand),
    videoIFrame: z.union([
        z.string(),
        z.null()
    ]).optional(),
    salesOrganisations: z.array(zNewsItemSalesOrganisation)
});

export const zCreateNewsItemResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime()
});

export const zNewsItemTranslationExistsError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'news-item-translation-exists'
    ])
});

export const zNoStartDateOrEndDateExpectedError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'no-start-date-or-end-date-expected'
    ])
});

export const zUpdateNewsItemTranslationCommand = z.object({
    title: z.string().optional(),
    content: z.record(z.unknown()).optional(),
    language: zLocale
});

export const zUpdateNewsItemCommand = z.object({
    startDate: z.string().date().optional(),
    endDate: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    imageUuid: z.string().uuid().optional(),
    newsItemTranslations: z.array(zUpdateNewsItemTranslationCommand).optional(),
    videoIFrame: z.union([
        z.string(),
        z.null()
    ]).optional(),
    salesOrganisations: z.array(zNewsItemSalesOrganisation).optional()
});

export const zUpdateNewsItemResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime()
});

export const zNewsItemNotFoundErrorMeta = z.object({
    uuid: z.string().uuid()
});

export const zNewsItemNotFoundError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '404'
    ]),
    code: z.enum([
        'news_item_not_found'
    ]),
    meta: zNewsItemNotFoundErrorMeta
});

export const zFileResponse = z.object({
    uuid: z.string().uuid(),
    name: z.string(),
    mimeType: zMimeType,
    url: z.string()
});

export const zViewNewsItemResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    publishStatus: zPublishStatus,
    startDate: z.string().datetime(),
    endDate: z.union([
        z.string().datetime(),
        z.null()
    ]),
    translations: z.array(zViewNewsItemTranslationResponse),
    image: zFileResponse,
    videoIFrame: z.union([
        z.string(),
        z.null()
    ]),
    author: zViewNewsItemAuthorResponse,
    salesOrganisations: z.array(zNewsItemSalesOrganisation)
});

export const zViewNewsItemTranslationIndexResponse = z.object({
    uuid: z.string().uuid(),
    title: z.union([
        z.string(),
        z.null()
    ]),
    language: zLocale
});

export const zNewsItemIndexView = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    publishStatus: zPublishStatus,
    startDate: z.string().datetime(),
    endDate: z.union([
        z.string().datetime(),
        z.null()
    ]),
    translations: z.array(zViewNewsItemTranslationIndexResponse),
    author: zViewNewsItemAuthorResponse
});

export const zViewNewsIndexResponse = z.object({
    items: z.array(zNewsItemIndexView),
    meta: zPaginatedOffsetResponseMeta
});

export const zViewDashboardNewItemFilterQuery = z.object({
    excludeNewsItemUuids: z.array(z.string()).optional()
});

export const zDashboardNewsItemIndexView = z.object({
    uuid: z.string().uuid(),
    endDate: z.union([
        z.string().datetime(),
        z.null()
    ]),
    startDate: z.union([
        z.string().datetime(),
        z.null()
    ]),
    translation: zViewNewsItemTranslationResponse,
    image: z.union([
        zFileResponse,
        z.null()
    ]),
    videoIFrame: z.union([
        z.string(),
        z.null()
    ])
});

export const zViewDashboardNewsIndexResponse = z.object({
    items: z.array(zDashboardNewsItemIndexView),
    meta: zPaginatedOffsetResponseMeta
});

export const zViewDashboardNewsItemResponse = z.object({
    uuid: z.string().uuid(),
    endDate: z.union([
        z.string().datetime(),
        z.null()
    ]),
    startDate: z.union([
        z.string().datetime(),
        z.null()
    ]),
    translation: zViewNewsItemTranslationResponse,
    image: z.union([
        zFileResponse,
        z.null()
    ]),
    videoIFrame: z.union([
        z.string(),
        z.null()
    ])
});

export const zCreatePackagingRequestCommand = z.record(z.unknown());

export const zCreatePackagingRequestResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime()
});

export const zWasteProducerResponse = z.object({
    id: z.string(),
    name: z.string(),
    address: z.union([
        z.string(),
        z.unknown(),
        z.null()
    ])
});

export const zPickUpAddressResponse = z.object({
    id: z.string(),
    name: z.string(),
    address: z.union([
        z.string(),
        z.unknown(),
        z.null()
    ])
});

export const zPackagingRequestMaterialResponse = z.object({
    contractLineId: z.string(),
    materialNumber: z.union([
        z.string(),
        z.null()
    ]),
    wasteMaterial: z.union([
        z.string(),
        z.null()
    ]),
    isSales: z.union([
        z.boolean(),
        z.null()
    ]),
    contractNumber: z.string(),
    contractItem: z.string(),
    poNumber: z.union([
        z.string(),
        z.null()
    ]),
    costCenter: z.union([
        z.string(),
        z.null()
    ]),
    quantity: z.number(),
    contractLineAccessible: z.boolean().optional()
});

export const zContactTypeResponse = z.object({
    firstName: z.string(),
    lastName: z.string(),
    email: z.string().email()
});

export const zViewPackagingRequestResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    customer: z.union([
        zCustomerResponse,
        z.null()
    ]),
    wasteProducer: z.union([
        zWasteProducerResponse,
        z.null()
    ]),
    deliveryAddress: z.union([
        zPickUpAddressResponse,
        z.null()
    ]),
    packagingRequestMaterials: z.array(zPackagingRequestMaterialResponse),
    startDate: z.union([
        z.string().date(),
        z.null()
    ]),
    endDate: z.union([
        z.string().date(),
        z.null()
    ]),
    remarks: z.union([
        z.string(),
        z.null()
    ]),
    sendCopyToContacts: z.array(zContactTypeResponse)
});

export const zCustomerNotAccessibleError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'customer_not_accessible'
    ])
});

export const zWasteProducerNotAccessibleError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'waste_producer_not_accessible'
    ])
});

export const zPickUpAddressNotAccessibleError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'pick_up_address_not_accessible'
    ])
});

export const zPackagingRequestMaterialCommand = z.object({
    contractLineId: z.string(),
    costCenter: z.union([
        z.string().max(25),
        z.null()
    ]),
    poNumber: z.union([
        z.string().max(35),
        z.null()
    ]),
    contractNumber: z.string(),
    contractItem: z.string(),
    wasteMaterial: z.union([
        z.string(),
        z.null()
    ]),
    materialNumber: z.string(),
    isSales: z.union([
        z.boolean(),
        z.null()
    ]),
    quantity: z.number().gte(0)
});

export const zContact = z.object({
    email: z.string().email().max(241),
    firstName: z.string().max(40),
    lastName: z.string().max(40)
});

export const zUpdatePackagingRequestCommand = z.object({
    customerId: z.union([
        z.string(),
        z.null()
    ]).optional(),
    wasteProducerId: z.union([
        z.string(),
        z.null()
    ]).optional(),
    deliveryAddressId: z.union([
        z.string(),
        z.null()
    ]).optional(),
    packagingRequestMaterials: z.array(zPackagingRequestMaterialCommand).optional(),
    startDate: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    endDate: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    remarks: z.union([
        z.string().max(200),
        z.null()
    ]).optional(),
    sendCopyToContacts: z.array(zContact).optional()
});

export const zUpdatePackagingRequestResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime()
});

export const zContractLineNotOfCustomerErrorMeta = z.object({
    customerId: z.string(),
    contractNumber: z.string(),
    contractLineNumber: z.string(),
    tcNumber: z.union([
        z.string(),
        z.null()
    ])
});

export const zContractLineNotOfCustomerError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'contract_line_not_of_customer'
    ]),
    meta: zContractLineNotOfCustomerErrorMeta
});

export const zContractLineNotOfPickUpAddressesErrorMeta = z.object({
    pickUpAddressIds: z.array(z.string()),
    contractNumber: z.string(),
    contractLineNumber: z.string(),
    tcNumber: z.union([
        z.string(),
        z.null()
    ])
});

export const zContractLineNotOfPickUpAddressesError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'contract_line_not_of_pick_up_addresses'
    ]),
    meta: zContractLineNotOfPickUpAddressesErrorMeta
});

export const zUpdateOnlyPackagingRequestError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'update_only_packaging_request'
    ])
});

export const zPackagingRequestAlreadySubmitted = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '409'
    ]),
    code: z.enum([
        'packaging_request_already_submitted'
    ])
});

export const zSubmitPackagingRequestResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    submittedOn: z.string().datetime(),
    requestNumber: z.string()
});

export const zBulkDeletePackagingRequestCommand = z.object({
    packagingRequestUuids: z.array(z.string())
});

export const zPickUpRequestNotFoundErrorMeta = z.object({
    uuid: z.string().optional(),
    requestNumber: z.string().optional()
});

export const zPickUpRequestNotFoundError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '404'
    ]),
    code: z.enum([
        'pick_up_request_not_found'
    ]),
    meta: zPickUpRequestNotFoundErrorMeta
});

export const zCopyNonSubmittedPickUpRequestError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '409'
    ]),
    code: z.enum([
        'copy_non_submitted_pick_up_request'
    ])
});

export const zCopyPackagingRequestSapResponse = z.object({
    uuid: z.string().uuid()
});

export const zViewPackagingTypeIndexFilterQuery = z.object({
    customerId: z.string()
});

export const zPackagingTypeResponse = z.object({
    name: z.string()
});

export const zViewPackagingTypeIndexResponse = z.object({
    items: z.array(zPackagingTypeResponse)
});

export const zViewPayerIndexFilterQuery = z.object({
    customerId: z.string()
});

export const zPayerResponse = z.object({
    id: z.string(),
    name: z.string()
});

export const zViewPayerIndexResponse = z.object({
    items: z.array(zPayerResponse)
});

export const zViewPickUpAddressIndexFilterQuery = z.object({
    customerId: z.string().optional()
});

export const zViewPickUpAddressIndexResponse = z.object({
    items: z.array(zPickUpAddressResponse),
    meta: zPaginatedOffsetResponseMeta
});

export const zViewSuggestedPickUpAddressesFilterQuery = z.object({
    customerId: z.string().uuid(),
    requestType: zRequestType
});

export const zViewSuggestedPickUpAddressesResponse = z.object({
    items: z.array(zPickUpAddressResponse)
});

export const zGetIsPoNumberAndCostCenterRequiredFilterQuery = z.object({
    customerId: z.string()
});

export const zGetIsPoNumberAndCostCenterRequiredResponse = z.object({
    isPoNumberRequired: z.boolean(),
    isCostCenterRequired: z.boolean()
});

export const zViewPickUpRequestIndexSortKey = z.enum([
    'requestNumber',
    'wasteMaterial',
    'customerReference',
    'contractNumber',
    'contractItem',
    'applicationDate',
    'wasteProducerId',
    'pickUpAddressId',
    'treatmentCenterName',
    'requestedStartDate',
    'confirmedTransportDate',
    'salesOrder',
    'orderNumber',
    'containerNumber',
    'installationName',
    'disposalCertificateNumber'
]);

export const zViewPickUpRequestIndexSortQuery = z.object({
    key: zViewPickUpRequestIndexSortKey,
    order: zSortDirection
});

export const zPickUpRequestStatus = z.enum([
    'draft',
    'pending',
    'confirmed',
    'cancelled',
    'indascan_draft'
]);

export const zPickUpTransportMode = z.enum([
    'packaged-curtain-sider-truck',
    'bulk-skips-container',
    'bulk-vacuum-tankers-road-tankers',
    'bulk-iso-tank'
]);

export const zViewPickUpRequestIndexFilterQuery = z.object({
    statuses: z.array(zPickUpRequestStatus),
    requestNumber: z.string().optional(),
    wasteMaterial: z.string().optional(),
    customerReference: z.string().optional(),
    contractNumber: z.string().optional(),
    contractItem: z.string().optional(),
    transportMode: zPickUpTransportMode.optional(),
    dateOfRequest: zDateRange.optional(),
    customerId: z.string().optional(),
    wasteProducerId: z.string().optional(),
    pickUpAddressId: z.string().optional(),
    treatmentCenterName: z.string().optional(),
    costCenter: z.string().optional(),
    isTransportByIndaver: z.boolean().optional(),
    requestDate: zDateRange.optional(),
    confirmedTransportDate: zDateRange.optional(),
    salesOrder: z.string().optional(),
    isHazardous: z.boolean().optional(),
    nameOfApplicant: z.string().optional(),
    orderNumber: z.string().optional(),
    materialAnalysis: z.string().optional(),
    deliveryInfo: z.string().optional(),
    nameInstallation: z.string().optional(),
    disposalCertificateNumber: z.string().optional(),
    ewc: z.string().optional(),
    accountManager: z.string().optional(),
    containerNumber: z.string().optional()
});

export const zTransportMode = z.enum([
    'packaging-request-order',
    'packaged-curtain-sider-truck',
    'bulk-skips-container',
    'bulk-vacuum-tankers-road-tankers',
    'bulk-iso-tank'
]);

export const zPickUpRequestResponse = z.object({
    uuid: z.union([
        z.string(),
        z.null()
    ]),
    createdAt: z.union([
        z.string().datetime(),
        z.null()
    ]),
    requestNumber: z.union([
        z.string(),
        z.null()
    ]),
    status: zPickUpRequestStatus,
    wasteMaterial: z.array(z.string()),
    customerId: z.union([
        z.string(),
        z.null()
    ]),
    customerName: z.union([
        z.string(),
        z.null()
    ]),
    wasteProducerId: z.union([
        z.string(),
        z.null()
    ]),
    wasteProducerName: z.union([
        z.string(),
        z.null()
    ]),
    pickUpAddressId: z.array(z.string()),
    pickUpAddressName: z.array(z.string()),
    customerReference: z.union([
        z.string(),
        z.null()
    ]),
    contractNumber: z.array(z.string()),
    contractItem: z.array(z.string()),
    transportMode: z.union([
        zTransportMode,
        z.null()
    ]),
    dateOfRequest: z.union([
        z.string(),
        z.null()
    ]),
    treatmentCenterName: z.union([
        z.string(),
        z.null()
    ]),
    accountManager: z.union([
        z.string(),
        z.null()
    ]),
    costCenter: z.array(z.string()),
    isTransportByIndaver: z.union([
        z.boolean(),
        z.null()
    ]),
    requestedStartDate: z.union([
        z.string().date(),
        z.null()
    ]),
    requestedEndDate: z.union([
        z.string().date(),
        z.null()
    ]),
    confirmedTransportDate: z.union([
        z.string().date(),
        z.null()
    ]),
    salesOrder: z.union([
        z.string(),
        z.null()
    ]),
    isHazardous: z.array(z.boolean()),
    nameOfApplicant: z.union([
        z.string(),
        z.null()
    ]),
    orderNumber: z.union([
        z.string(),
        z.null()
    ]),
    containerNumber: z.array(z.string()),
    materialAnalysis: z.union([
        z.string(),
        z.null()
    ]),
    deliveryInfo: z.union([
        z.string(),
        z.null()
    ]),
    nameInstallation: z.union([
        z.string(),
        z.null()
    ]),
    disposalCertificateNumber: z.union([
        z.string(),
        z.null()
    ]),
    ewc: z.union([
        z.string(),
        z.null()
    ]),
    tfsNumber: z.union([
        z.string(),
        z.null()
    ])
});

export const zViewPickUpRequestIndexResponse = z.object({
    items: z.array(zPickUpRequestResponse),
    meta: zPaginatedOffsetResponseMeta
});

export const zCreatePickUpRequestCommand = z.record(z.unknown());

export const zCreatePickUpRequestResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime()
});

export const zWasteMeasurementUnit = z.enum([
    'kg',
    'm3',
    'pc',
    'to',
    'yd3'
]);

export const zMaterialResponse = z.object({
    contractLineId: z.string(),
    contractNumber: z.string(),
    contractItem: z.string(),
    tcNumber: z.union([
        z.string(),
        z.null()
    ]),
    customerReference: z.union([
        z.string(),
        z.null()
    ]),
    wasteMaterial: z.union([
        z.string(),
        z.null()
    ]),
    materialNumber: z.union([
        z.string(),
        z.null()
    ]),
    treatmentCenterName: z.union([
        z.string(),
        z.null()
    ]),
    installationName: z.union([
        z.string(),
        z.null()
    ]),
    customerId: z.union([
        z.string(),
        z.null()
    ]),
    customerName: z.union([
        z.string(),
        z.null()
    ]),
    wasteProducerId: z.union([
        z.string(),
        z.null()
    ]),
    wasteProducerName: z.union([
        z.string(),
        z.null()
    ]),
    pickUpAddressId: z.union([
        z.string(),
        z.null()
    ]),
    pickUpAddressName: z.union([
        z.string(),
        z.null()
    ]),
    asn: z.union([
        z.string(),
        z.null()
    ]),
    tfs: z.union([
        z.boolean(),
        z.null()
    ]),
    isHazardous: z.union([
        z.boolean(),
        z.null()
    ]),
    packaged: z.union([
        zContractLinePackagingType,
        z.null()
    ]),
    materialAnalysis: z.union([
        z.string(),
        z.null()
    ]),
    ewcCode: z.union([
        z.string(),
        z.null()
    ]),
    endTreatmentCenterId: z.union([
        z.string(),
        z.null()
    ]),
    endTreatmentCenterName: z.union([
        z.string(),
        z.null()
    ]),
    remarks: z.union([
        z.string(),
        z.null()
    ]),
    processCode: z.union([
        z.string(),
        z.null()
    ]),
    esnNumber: z.union([
        z.string(),
        z.null()
    ]),
    deliveryInfo: z.union([
        z.string(),
        z.null()
    ]),
    materialType: z.union([
        z.string(),
        z.null()
    ]),
    packagingIndicator: z.union([
        z.string(),
        z.null()
    ]),
    estimatedWeightOrVolumeValue: z.union([
        z.number(),
        z.null()
    ]),
    estimatedWeightOrVolumeUnit: z.union([
        zWasteMeasurementUnit,
        z.null()
    ]),
    costCenter: z.union([
        z.string(),
        z.null()
    ]),
    poNumber: z.union([
        z.string(),
        z.null()
    ]),
    unNumber: z.union([
        z.string(),
        z.null()
    ]),
    unNumberDescription: z.union([
        z.string(),
        z.null()
    ]),
    adrClass: z.union([
        z.string(),
        z.null()
    ]),
    packingGroup: z.union([
        z.string(),
        z.null()
    ]),
    dangerLabel1: z.union([
        z.string(),
        z.null()
    ]),
    dangerLabel2: z.union([
        z.string(),
        z.null()
    ]),
    dangerLabel3: z.union([
        z.string(),
        z.null()
    ]),
    packagingType: z.union([
        z.string(),
        z.null()
    ]),
    quantityPackages: z.union([
        z.number(),
        z.null()
    ]),
    quantityLabels: z.union([
        z.number(),
        z.null()
    ]),
    quantityPallets: z.union([
        z.number(),
        z.null()
    ]),
    containerType: z.union([
        z.string(),
        z.null()
    ]),
    containerVolumeSize: z.union([
        z.string(),
        z.null()
    ]),
    containerNumber: z.union([
        z.string(),
        z.null()
    ]),
    containerTransportType: z.union([
        z.string(),
        z.null()
    ]),
    isContainerCovered: z.union([
        z.boolean(),
        z.null()
    ]),
    tankerType: z.union([
        z.string(),
        z.null()
    ]),
    position: z.union([
        z.string(),
        z.null()
    ]),
    contractLineAccessible: z.union([
        z.boolean(),
        z.null()
    ]),
    unNumberHazardous: z.union([
        z.boolean(),
        z.null()
    ]),
    reconciliationNumber: z.union([
        z.string(),
        z.null()
    ]),
    hazardInducers: z.union([
        z.string(),
        z.null()
    ]),
    quantityContainers: z.union([
        z.number(),
        z.null()
    ]),
    tfsNumber: z.union([
        z.string(),
        z.null()
    ]),
    serialNumber: z.union([
        z.string(),
        z.null()
    ])
});

export const zFileLinkResponse = z.object({
    uuid: z.union([
        z.string().uuid(),
        z.null()
    ]),
    name: z.string(),
    mimeType: z.union([
        zMimeType,
        z.null()
    ]),
    url: z.union([
        z.string(),
        z.null()
    ]),
    order: z.union([
        z.number(),
        z.null()
    ])
});

export const zViewPickUpRequestResponse = z.object({
    status: zPickUpRequestStatus,
    customer: z.union([
        zCustomerResponse,
        z.null()
    ]),
    wasteProducer: z.union([
        zWasteProducerResponse,
        z.null()
    ]),
    pickUpAddresses: z.array(zPickUpAddressResponse),
    transportMode: z.union([
        zPickUpTransportMode,
        z.null()
    ]),
    isTransportByIndaver: z.union([
        z.boolean(),
        z.null()
    ]),
    startDate: z.union([
        z.string().date(),
        z.null()
    ]),
    endDate: z.union([
        z.string().date(),
        z.null()
    ]),
    remarks: z.union([
        z.string(),
        z.null()
    ]),
    totalQuantityPallets: z.union([
        z.number(),
        z.null()
    ]),
    isReturnPackaging: z.union([
        z.boolean(),
        z.null()
    ]),
    packagingRemark: z.union([
        z.string(),
        z.null()
    ]),
    sendCopyToContacts: z.array(zContactTypeResponse),
    materials: z.array(zMaterialResponse),
    packagingRequestMaterials: z.array(zPackagingRequestMaterialResponse),
    additionalFiles: z.array(zFileLinkResponse),
    isWicConfirmed: z.union([
        z.boolean(),
        z.null()
    ]),
    needsWicConfirmation: z.boolean(),
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime()
});

export const zViewWasteProducerIndexFilterQuery = z.object({
    customerId: z.string().optional()
});

export const zViewWasteProducerIndexResponse = z.object({
    items: z.array(zWasteProducerResponse),
    meta: zPaginatedOffsetResponseMeta
});

export const zViewSuggestedWasteProducersFilterQuery = z.object({
    customerId: z.string().uuid(),
    requestType: zRequestType
});

export const zViewSuggestedWasteProducersResponse = z.object({
    items: z.array(zWasteProducerResponse)
});

export const zMissingEwcLevelsError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'missing_ewc_levels'
    ])
});

export const zCreateFileLinkCommand = z.object({
    fileUuid: z.string().uuid(),
    order: z.number().int()
});

export const zPackingGroup = z.enum([
    'not-applicable',
    'one',
    'two',
    'three'
]);

export const zPickUpRequestMaterialCommand = z.object({
    contractLineId: z.string(),
    contractNumber: z.string(),
    contractItem: z.string(),
    processCode: z.union([
        z.string(),
        z.null()
    ]).optional(),
    tcNumber: z.union([
        z.string(),
        z.null()
    ]).optional(),
    pickUpAddressId: z.union([
        z.string(),
        z.null()
    ]).optional(),
    materialNumber: z.union([
        z.string(),
        z.null()
    ]).optional(),
    customerReference: z.union([
        z.string(),
        z.null()
    ]).optional(),
    isHazardous: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    wasteMaterial: z.union([
        z.string(),
        z.null()
    ]).optional(),
    ewcCode: z.union([
        z.string(),
        z.null()
    ]).optional(),
    asn: z.union([
        z.string(),
        z.null()
    ]).optional(),
    tfs: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    unNumberHazardous: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    estimatedWeightOrVolumeValue: z.union([
        z.number().gte(0.01).lte(99999999999.99),
        z.null()
    ]).optional(),
    estimatedWeightOrVolumeUnit: z.union([
        zWasteMeasurementUnit,
        z.null()
    ]).optional(),
    costCenter: z.union([
        z.string().max(25),
        z.null()
    ]).optional(),
    poNumber: z.union([
        z.string().max(25),
        z.null()
    ]).optional(),
    unNumber: z.union([
        z.string(),
        z.null()
    ]).optional(),
    unNumberDescription: z.union([
        z.string(),
        z.null()
    ]).optional(),
    adrClass: z.union([
        z.string(),
        z.null()
    ]).optional(),
    packingGroup: z.union([
        zPackingGroup,
        z.null()
    ]).optional(),
    dangerLabel1: z.union([
        z.string(),
        z.null()
    ]).optional(),
    dangerLabel2: z.union([
        z.string(),
        z.null()
    ]).optional(),
    dangerLabel3: z.union([
        z.string(),
        z.null()
    ]).optional(),
    packagingType: z.union([
        z.string(),
        z.null()
    ]).optional(),
    quantityPackages: z.union([
        z.number().gte(0).lte(999),
        z.null()
    ]).optional(),
    quantityLabels: z.union([
        z.number().gte(0).lte(999),
        z.null()
    ]).optional(),
    quantityPallets: z.union([
        z.number().gte(0).lte(999),
        z.null()
    ]).optional(),
    containerType: z.union([
        z.string(),
        z.null()
    ]).optional(),
    containerVolumeSize: z.union([
        z.string().max(13),
        z.null()
    ]).optional(),
    containerNumber: z.union([
        z.string().max(40),
        z.null()
    ]).optional(),
    containerTransportType: z.union([
        z.string(),
        z.null()
    ]).optional(),
    tankerType: z.union([
        z.string(),
        z.null()
    ]).optional(),
    isContainerCovered: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    reconciliationNumber: z.union([
        z.string().max(6),
        z.null()
    ]).optional(),
    hazardInducers: z.union([
        z.string().max(100),
        z.null()
    ]).optional(),
    quantityContainers: z.union([
        z.number().gte(0).lte(9),
        z.null()
    ]).optional(),
    tfsNumber: z.union([
        z.string().max(15),
        z.null()
    ]).optional(),
    serialNumber: z.union([
        z.string().max(4),
        z.null()
    ]).optional()
});

export const zUpdatePickUpRequestCommand = z.object({
    customerId: z.union([
        z.string(),
        z.null()
    ]).optional(),
    customerName: z.union([
        z.string(),
        z.null()
    ]).optional(),
    wasteProducerId: z.union([
        z.string(),
        z.null()
    ]).optional(),
    wasteProducerName: z.union([
        z.string(),
        z.null()
    ]).optional(),
    pickUpAddressIds: z.array(z.string()).optional(),
    pickUpAddressNames: z.array(z.string()).optional(),
    transportMode: z.union([
        zPickUpTransportMode,
        z.null()
    ]).optional(),
    isTransportByIndaver: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    totalQuantityPallets: z.union([
        z.number().gte(0).lte(999),
        z.null()
    ]).optional(),
    isReturnPackaging: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    startDate: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    endDate: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    startTime: z.union([
        z.string().time(),
        z.null()
    ]).optional(),
    packagingRemark: z.union([
        z.string().max(200),
        z.null()
    ]).optional(),
    remarks: z.union([
        z.string(),
        z.null()
    ]).optional(),
    additionalFiles: z.array(zCreateFileLinkCommand).optional(),
    sendCopyToContacts: z.array(zContact).optional(),
    materials: z.array(zPickUpRequestMaterialCommand).optional(),
    packagingRequestMaterials: z.array(zPackagingRequestMaterialCommand).optional(),
    isWicConfirmed: z.union([
        z.boolean(),
        z.null()
    ]).optional()
});

export const zUpdatePickUpRequestResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    isWicConfirmed: z.union([
        z.boolean(),
        z.null()
    ]),
    needsWicConfirmation: z.boolean()
});

export const zPickUpRequestAlreadySubmitted = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '409'
    ]),
    code: z.enum([
        'pick_up_request_already_submitted'
    ])
});

export const zSubmitPickUpRequestResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    submittedOn: z.string().datetime(),
    requestNumber: z.string()
});

export const zBulkDeletePickUpRequestCommand = z.object({
    pickUpRequestUuids: z.array(z.string())
});

export const zViewPickUpRequestSapResponse = z.object({
    status: zPickUpRequestStatus,
    customer: z.union([
        zCustomerResponse,
        z.null()
    ]),
    wasteProducer: z.union([
        zWasteProducerResponse,
        z.null()
    ]),
    pickUpAddresses: z.array(zPickUpAddressResponse),
    transportMode: z.union([
        zPickUpTransportMode,
        z.null()
    ]),
    isTransportByIndaver: z.union([
        z.boolean(),
        z.null()
    ]),
    startDate: z.union([
        z.string().date(),
        z.null()
    ]),
    endDate: z.union([
        z.string().date(),
        z.null()
    ]),
    remarks: z.union([
        z.string(),
        z.null()
    ]),
    totalQuantityPallets: z.union([
        z.number(),
        z.null()
    ]),
    isReturnPackaging: z.union([
        z.boolean(),
        z.null()
    ]),
    packagingRemark: z.union([
        z.string(),
        z.null()
    ]),
    sendCopyToContacts: z.array(zContactTypeResponse),
    materials: z.array(zMaterialResponse),
    packagingRequestMaterials: z.array(zPackagingRequestMaterialResponse),
    additionalFiles: z.array(zFileLinkResponse),
    isWicConfirmed: z.union([
        z.boolean(),
        z.null()
    ]),
    needsWicConfirmation: z.boolean(),
    requestNumber: z.union([
        z.string(),
        z.null()
    ]),
    createdBy: z.union([
        z.string(),
        z.null()
    ]),
    confirmedDate: z.union([
        z.string().date(),
        z.null()
    ])
});

export const zCreatePickUpRequestTemplateCommand = z.object({
    name: z.string().max(150)
});

export const zCreatePickUpRequestTemplateResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime()
});

export const zUpdatedByUserResponse = z.object({
    uuid: z.string().uuid(),
    email: z.string().email(),
    firstName: z.union([
        z.string(),
        z.null()
    ]),
    lastName: z.union([
        z.string(),
        z.null()
    ])
});

export const zViewPickUpRequestTemplateResponse = z.object({
    status: zPickUpRequestStatus,
    customer: z.union([
        zCustomerResponse,
        z.null()
    ]),
    wasteProducer: z.union([
        zWasteProducerResponse,
        z.null()
    ]),
    pickUpAddresses: z.array(zPickUpAddressResponse),
    transportMode: z.union([
        zPickUpTransportMode,
        z.null()
    ]),
    isTransportByIndaver: z.union([
        z.boolean(),
        z.null()
    ]),
    startDate: z.union([
        z.string().date(),
        z.null()
    ]),
    endDate: z.union([
        z.string().date(),
        z.null()
    ]),
    remarks: z.union([
        z.string(),
        z.null()
    ]),
    totalQuantityPallets: z.union([
        z.number(),
        z.null()
    ]),
    isReturnPackaging: z.union([
        z.boolean(),
        z.null()
    ]),
    packagingRemark: z.union([
        z.string(),
        z.null()
    ]),
    sendCopyToContacts: z.array(zContactTypeResponse),
    materials: z.array(zMaterialResponse),
    packagingRequestMaterials: z.array(zPackagingRequestMaterialResponse),
    additionalFiles: z.array(zFileLinkResponse),
    isWicConfirmed: z.union([
        z.boolean(),
        z.null()
    ]),
    needsWicConfirmation: z.boolean(),
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    templateName: z.string(),
    templateUpdatedByUser: zUpdatedByUserResponse.optional()
});

export const zViewPickUpRequestTemplateIndexSortQueryKey = z.enum([
    'name',
    'createdAt',
    'updatedAt',
    'createdBy',
    'updatedBy'
]);

export const zViewPickUpRequestTemplateIndexSortQuery = z.object({
    key: zViewPickUpRequestTemplateIndexSortQueryKey,
    order: zSortDirection
});

export const zPickUpRequestTemplateResponse = z.object({
    uuid: z.string().uuid(),
    name: z.string(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    createdBy: z.string(),
    updatedBy: z.string()
});

export const zViewPickUpRequestTemplateIndexResponse = z.object({
    items: z.array(zPickUpRequestTemplateResponse),
    meta: zPaginatedOffsetResponseMeta
});

export const zUpdatePickUpRequestTemplateCommand = z.object({
    customerId: z.union([
        z.string(),
        z.null()
    ]).optional(),
    customerName: z.union([
        z.string(),
        z.null()
    ]).optional(),
    wasteProducerId: z.union([
        z.string(),
        z.null()
    ]).optional(),
    wasteProducerName: z.union([
        z.string(),
        z.null()
    ]).optional(),
    pickUpAddressIds: z.array(z.string()).optional(),
    pickUpAddressNames: z.array(z.string()).optional(),
    transportMode: z.union([
        zPickUpTransportMode,
        z.null()
    ]).optional(),
    isTransportByIndaver: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    totalQuantityPallets: z.union([
        z.number().gte(0).lte(999),
        z.null()
    ]).optional(),
    isReturnPackaging: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    startDate: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    endDate: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    startTime: z.union([
        z.string().time(),
        z.null()
    ]).optional(),
    packagingRemark: z.union([
        z.string().max(200),
        z.null()
    ]).optional(),
    remarks: z.union([
        z.string(),
        z.null()
    ]).optional(),
    additionalFiles: z.array(zCreateFileLinkCommand).optional(),
    sendCopyToContacts: z.array(zContact).optional(),
    materials: z.array(zPickUpRequestMaterialCommand).optional(),
    packagingRequestMaterials: z.array(zPackagingRequestMaterialCommand).optional(),
    isWicConfirmed: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    templateName: z.union([
        z.string(),
        z.null()
    ]).optional()
});

export const zUpdatePickUpRequestTemplateResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime()
});

export const zBulkDeletePickUpRequestTemplatesCommand = z.object({
    pickUpRequestUuids: z.array(z.string())
});

export const zCreatePickUpRequestFromTemplateCommand = z.record(z.unknown());

export const zCreatePickUpRequestFromTemplateResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime()
});

export const zFileNotFoundError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '404'
    ]),
    code: z.enum([
        'file_not_found'
    ])
});

export const zUploadDocumentSubmittedPickUpRequestCommand = z.object({
    fileUuids: z.array(z.string().uuid())
});

export const zDownloadDocumentSubmittedPickUpRequestCommand = z.object({
    fileName: z.string()
});

export const zInvalidPickUpRequestCopyErrorMeta = z.object({
    requestNumber: z.string()
});

export const zInvalidPickUpRequestCopyError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '409'
    ]),
    code: z.enum([
        'invalid_pick_up_request_copy'
    ]),
    meta: zInvalidPickUpRequestCopyErrorMeta
});

export const zCopyPickUpRequestSapResponse = z.object({
    uuid: z.string().uuid()
});

export const zInvalidUpdateSapPickUpRequestError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'invalid_update_sap_pick_up_request'
    ])
});

export const zPickUpRequestContractLineNotFoundError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '404'
    ]),
    code: z.enum([
        'pick_up_request_contract_line_not_found'
    ])
});

export const zUpdatePickUpRequestMaterialSapCommand = z.object({
    costCenter: z.union([
        z.string().max(25),
        z.null()
    ]).optional(),
    poNumber: z.union([
        z.string().max(25),
        z.null()
    ]).optional(),
    position: z.string().max(6)
});

export const zUpdatePickUpRequestPackagingMaterialSapCommand = z.object({
    contractLineId: z.string(),
    costCenter: z.union([
        z.string().max(25),
        z.null()
    ]),
    poNumber: z.union([
        z.string().max(35),
        z.null()
    ]),
    contractNumber: z.string(),
    contractItem: z.string(),
    wasteMaterial: z.union([
        z.string(),
        z.null()
    ]),
    materialNumber: z.string(),
    isSales: z.union([
        z.boolean(),
        z.null()
    ]),
    quantity: z.number().gte(0),
    position: z.union([
        z.string().max(6),
        z.null()
    ])
});

export const zUpdatePickUpRequestPackagingSapCommand = z.object({
    customerId: z.union([
        z.string(),
        z.null()
    ]).optional(),
    wasteProducerId: z.union([
        z.string(),
        z.null()
    ]).optional(),
    deliveryAddressId: z.union([
        z.string(),
        z.null()
    ]).optional(),
    packagingRequestMaterials: z.array(zUpdatePickUpRequestPackagingMaterialSapCommand).optional()
});

export const zUpdatePickUpRequestSapCommand = z.object({
    contacts: z.array(zContact).optional(),
    materials: z.array(zUpdatePickUpRequestMaterialSapCommand).optional(),
    startDate: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    endDate: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    isReturnPackaging: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    packagingRemark: z.union([
        z.string().max(200),
        z.null()
    ]).optional(),
    packagingRequest: zUpdatePickUpRequestPackagingSapCommand.optional(),
    remarks: z.union([
        z.string(),
        z.null()
    ]).optional(),
    additionalFiles: z.array(zCreateFileLinkCommand).optional()
});

export const zInvalidIndascanSubmitStatusError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '409'
    ]),
    code: z.enum([
        'invalid_indascan_submit_status'
    ])
});

export const zViewSalesOrganisationResponse = z.object({
    id: z.string(),
    name: z.string()
});

export const zViewSalesOrganisationIndexResponse = z.object({
    items: z.array(zViewSalesOrganisationResponse),
    meta: zPaginatedOffsetResponseMeta
});

export const zTankerTypeResponse = z.object({
    id: z.string(),
    name: z.string()
});

export const zViewTankerTypeIndexResponse = z.object({
    items: z.array(zTankerTypeResponse)
});

export const zTransportTypeResponse = z.object({
    id: z.string(),
    name: z.string()
});

export const zViewTransportTypeIndexResponse = z.object({
    items: z.array(zTransportTypeResponse)
});

export const zViewUnNumberFilterQuery = z.object({
    keys: z.array(z.string()).optional()
});

export const zViewUnNumberIndexQueryKey = z.object({
    number: z.string()
});

export const zViewUnNumberIndexPaginationQuery = z.object({
    limit: z.number().gte(0).lte(100),
    key: z.union([
        zViewUnNumberIndexQueryKey,
        z.null()
    ]).optional()
});

export const zUnNumberResponse = z.object({
    number: z.string(),
    description: z.union([
        z.string(),
        z.null()
    ]),
    packingGroup: z.union([
        z.string(),
        z.null()
    ]),
    dangerLabel1: z.union([
        z.string(),
        z.null()
    ]),
    dangerLabel2: z.union([
        z.string(),
        z.null()
    ]),
    dangerLabel3: z.union([
        z.string(),
        z.null()
    ]),
    isHazardous: z.union([
        z.boolean(),
        z.null()
    ])
});

export const zViewUnNumberIndexResponseMeta = z.object({
    next: z.union([
        zViewUnNumberIndexQueryKey,
        z.null()
    ])
});

export const zViewUnNumberIndexResponse = z.object({
    items: z.array(zUnNumberResponse),
    meta: zViewUnNumberIndexResponseMeta
});

export const zViewUnNumberIndexForPickUpRequestFilterQuery = z.object({
    contractNumber: z.string(),
    contractItem: z.string(),
    tcNumber: z.string().optional()
});

export const zViewUnNumberIndexForPickUpRequestResponse = z.object({
    items: z.array(zUnNumberResponse)
});

export const zCreateWasteInquiryCommand = z.record(z.unknown());

export const zCreateWasteInquiryResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime()
});

export const zWasteInquirySummaryDocumentNotFoundErrorMeta = z.object({
    wasteInquiryId: z.string()
});

export const zWasteInquirySummaryDocumentNotFoundError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '404'
    ]),
    code: z.enum([
        'waste_inquiry_summary_document_not_found'
    ]),
    meta: zWasteInquirySummaryDocumentNotFoundErrorMeta
});

export const zWasteInquiryNotFoundErrorMeta = z.object({
    inquiryNumber: z.string().optional(),
    uuid: z.string().uuid().optional()
});

export const zWasteInquiryNotFoundError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '404'
    ]),
    code: z.enum([
        'waste_inquiry_not_found'
    ]),
    meta: zWasteInquiryNotFoundErrorMeta
});

export const zWasteInquiryDocumentNotFoundErrorMeta = z.object({
    wasteInquiryId: z.string()
});

export const zWasteInquiryDocumentNotFoundError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '404'
    ]),
    code: z.enum([
        'waste_inquiry_document_not_found'
    ]),
    meta: zWasteInquiryDocumentNotFoundErrorMeta
});

export const zSapFileNotFoundErrorMeta = z.object({
    sapFileUuid: z.string().uuid()
});

export const zSapFileNotFoundError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '404'
    ]),
    code: z.enum([
        'sap_file_not_found'
    ]),
    meta: zSapFileNotFoundErrorMeta
});

export const zWasteInquiryStatus = z.enum([
    'draft',
    'new',
    'in_progress',
    'conformity_confirmed',
    'solution_defined',
    'offer_sent',
    'offer_approved',
    'completed',
    'rejected'
]);

export const zStateOfMatter = z.enum([
    'gaseous',
    'powder',
    'sludgy',
    'solid',
    'liquid',
    'viscous',
    'liquid-with-solids',
    'no-data-available'
]);

export const zWastePackagingType = z.enum([
    'bulk',
    'packaged'
]);

export const zWasteFlashpointOption = z.enum([
    '< 23°',
    '23° - 60°',
    '> 60°'
]);

export const zWastePhOption = z.enum([
    '< 2',
    '2 - 4',
    '4 - 10',
    '> 10'
]);

export const zStableTemperatureType = z.enum([
    'ambient',
    'other'
]);

export const zWasteCompositionResponse = z.object({
    name: z.union([
        z.string(),
        z.null()
    ]),
    minWeight: z.union([
        z.number().gte(0).lte(100),
        z.null()
    ]),
    maxWeight: z.union([
        z.number().gte(0).lte(100),
        z.null()
    ])
});

export const zWasteLegislationOption = z.enum([
    'none',
    'radioactive',
    'cwc',
    'controlled-drugs',
    'drug-precursor',
    'hg-containing',
    'ozon-depleting-substance',
    'animal-byproduct',
    'infectious-waste',
    'svhc'
]);

export const zSvhcExtraOption = z.enum([
    'other',
    '< 1 mg/kg',
    '> 1 mg/kg'
]);

export const zWastePropertyOption = z.enum([
    'none',
    'explosive',
    'gaseous',
    'peroxide',
    'polymerisation-sensitive',
    'pyrophoric',
    'strong-oxidizing',
    'reactive-with-t-gas',
    'reactive-with-f-gas',
    'high-acute-toxic',
    'thermal-unstable'
]);

export const zWasteDischargeFrequency = z.enum([
    'once-off-stream',
    'regular-stream',
    'once-off-campaign',
    'regular-campaign'
]);

export const zRegulatedTransportOption = z.enum([
    'yes',
    'no',
    'unknown'
]);

export const zWasteInquiryUnNumberResponse = z.object({
    unNumber: z.union([
        z.string(),
        z.null()
    ]),
    packingGroup: z.union([
        zPackingGroup,
        z.null()
    ]),
    isHazardous: z.boolean()
});

export const zWastePackagingOption = z.enum([
    'asf',
    'asp',
    'big-bag',
    'cardboard-box',
    'ibc',
    'metal-drum',
    'oversized-drum',
    'plastic-drum',
    'other'
]);

export const zWeightUnit = z.enum([
    'kg'
]);

export const zWastePackagingResponse = z.object({
    type: z.union([
        zWastePackagingOption,
        z.null()
    ]),
    size: z.union([
        z.string(),
        z.null()
    ]),
    weightPerPieceValue: z.union([
        z.number(),
        z.null()
    ]),
    weightPerPieceUnit: z.union([
        zWeightUnit,
        z.null()
    ]),
    hasInnerPackaging: z.union([
        z.boolean(),
        z.null()
    ]),
    remarks: z.union([
        z.string(),
        z.null()
    ])
});

export const zWasteTransportType = z.enum([
    'container',
    'skip',
    'tipper-truck',
    'rel-truck',
    'other'
]);

export const zContainerLoadingType = z.enum([
    'hook',
    'chain'
]);

export const zWasteLoadingType = z.enum([
    'on-waste-collection',
    'before-waste-collection'
]);

export const zWasteTransportInOption = z.enum([
    'tank-trailer',
    'tank-container',
    'no-preference',
    'other'
]);

export const zWasteLoadingMethod = z.enum([
    'gravitational',
    'pump-from-customer',
    'pump-from-haulier'
]);

export const zWasteStoredInOption = z.enum([
    'storage-tank',
    'tank-container',
    'ibcs',
    'drums',
    'other'
]);

export const zCollectionRequirementOption = z.enum([
    'tractor',
    'tractor-trailer',
    'tractor-trailer-tank'
]);

export const zViewWasteInquiryResponse = z.object({
    status: zWasteInquiryStatus,
    customer: z.union([
        zCustomerResponse,
        z.null()
    ]),
    wasteProducer: z.union([
        zWasteProducerResponse,
        z.null()
    ]),
    isUnknownWasteProducer: z.boolean(),
    pickUpAddress: z.union([
        zPickUpAddressResponse,
        z.null()
    ]),
    isUnknownPickUpAddress: z.boolean(),
    wasteStreamName: z.union([
        z.string(),
        z.null()
    ]),
    wasteStreamDescription: z.union([
        z.string(),
        z.null()
    ]),
    ewcLevel1Name: z.union([
        z.string(),
        z.null()
    ]),
    ewcLevel2Name: z.union([
        z.string(),
        z.null()
    ]),
    ewcLevel3Name: z.union([
        z.string(),
        z.null()
    ]),
    stateOfMatter: z.union([
        zStateOfMatter,
        z.null()
    ]),
    packagingType: z.union([
        zWastePackagingType,
        z.null()
    ]),
    flashpoint: z.union([
        zWasteFlashpointOption,
        z.null()
    ]),
    ph: z.union([
        zWastePhOption,
        z.null()
    ]),
    specificGravity: z.union([
        z.number(),
        z.null()
    ]),
    stableTemperatureType: z.union([
        zStableTemperatureType,
        z.null()
    ]),
    minStableTemperature: z.union([
        z.number(),
        z.null()
    ]),
    maxStableTemperature: z.union([
        z.number(),
        z.null()
    ]),
    averageStableTemperature: z.union([
        z.number(),
        z.null()
    ]),
    sdsFiles: z.array(zFileLinkResponse),
    noSds: z.boolean(),
    analysisReportFiles: z.array(zFileLinkResponse),
    noAnalysisReport: z.boolean(),
    composition: z.array(zWasteCompositionResponse),
    isSampleAvailable: z.union([
        z.boolean(),
        z.null()
    ]),
    selectedLegislationOptions: z.array(zWasteLegislationOption),
    svhcExtra: z.union([
        zSvhcExtraOption,
        z.null()
    ]),
    legislationRemarks: z.union([
        z.string(),
        z.null()
    ]),
    selectedPropertyOptions: z.array(zWastePropertyOption),
    propertyRemarks: z.union([
        z.string(),
        z.null()
    ]),
    expectedYearlyVolumeAmount: z.union([
        z.number(),
        z.null()
    ]),
    expectedYearlyVolumeUnit: z.union([
        zWasteMeasurementUnit,
        z.null()
    ]),
    expectedPerCollectionQuantity: z.union([
        z.number(),
        z.null()
    ]),
    expectedPerCollectionUnit: z.union([
        zWasteMeasurementUnit,
        z.null()
    ]),
    dischargeFrequency: z.union([
        zWasteDischargeFrequency,
        z.null()
    ]),
    firstCollectionDate: z.union([
        z.string().date(),
        z.null()
    ]),
    expectedEndDate: z.union([
        z.string().date(),
        z.null()
    ]),
    collectionRemarks: z.union([
        z.string(),
        z.null()
    ]),
    isTransportByIndaver: z.union([
        z.boolean(),
        z.null()
    ]),
    isLoadingByIndaver: z.union([
        z.boolean(),
        z.null()
    ]),
    isRegulatedTransport: z.union([
        zRegulatedTransportOption,
        z.null()
    ]),
    unNumbers: z.array(zWasteInquiryUnNumberResponse),
    packaging: z.array(zWastePackagingResponse),
    transportType: z.union([
        zWasteTransportType,
        z.null()
    ]),
    containerLoadingType: z.union([
        zContainerLoadingType,
        z.null()
    ]),
    loadingType: z.union([
        zWasteLoadingType,
        z.null()
    ]),
    transportIn: z.union([
        zWasteTransportInOption,
        z.null()
    ]),
    loadingMethod: z.union([
        zWasteLoadingMethod,
        z.null()
    ]),
    storedIn: z.union([
        zWasteStoredInOption,
        z.null()
    ]),
    transportVolumeAmount: z.union([
        z.number(),
        z.null()
    ]),
    transportVolumeUnit: z.union([
        zWasteMeasurementUnit,
        z.null()
    ]),
    isTankOwnedByCustomer: z.union([
        z.boolean(),
        z.null()
    ]),
    collectionRequirements: z.union([
        zCollectionRequirementOption,
        z.null()
    ]),
    remarks: z.union([
        z.string(),
        z.null()
    ]),
    sendCopyToContacts: z.array(zContactTypeResponse),
    additionalFiles: z.array(zFileLinkResponse),
    submittedOn: z.union([
        z.string().datetime(),
        z.null()
    ]),
    hazardInducer1: z.union([
        z.string(),
        z.null()
    ]),
    hazardInducer2: z.union([
        z.string(),
        z.null()
    ]),
    hazardInducer3: z.union([
        z.string(),
        z.null()
    ]),
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime()
});

export const zEwcCodeNotFound = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'ewc_code_not_found'
    ])
});

export const zInvalidStableTemperatureError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'invalid_stable_temperature'
    ])
});

export const zFileNotAccessibleError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'file_not_accessible'
    ])
});

export const zNoSdsFilesExpected = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'no_sds_files_expected'
    ])
});

export const zNoAnalysisReportFilesExpected = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'no_analysis_report_files_expected'
    ])
});

export const zNoOptionExpectedWhenNoneSelected = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'no_option_expected_when_none_selected'
    ])
});

export const zNoSvhcExtraExpected = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'no_svhc_extra_expected'
    ])
});

export const zWasteComposition = z.object({
    name: z.union([
        z.string().max(132),
        z.null()
    ]),
    minWeight: z.union([
        z.number().gte(0).lte(100),
        z.null()
    ]),
    maxWeight: z.union([
        z.number().gte(0).lte(100),
        z.null()
    ])
});

export const zUnNumber = z.object({
    unNumber: z.union([
        z.string().max(20),
        z.null()
    ]),
    packingGroup: z.union([
        zPackingGroup,
        z.null()
    ]),
    isHazardous: z.boolean()
});

export const zWastePackaging = z.object({
    type: z.union([
        zWastePackagingOption,
        z.null()
    ]),
    size: z.union([
        z.string().max(10),
        z.null()
    ]),
    weightPerPieceValue: z.union([
        z.number().gte(0).lte(99999999999.99),
        z.null()
    ]),
    weightPerPieceUnit: z.union([
        zWeightUnit,
        z.null()
    ]),
    hasInnerPackaging: z.union([
        z.boolean(),
        z.null()
    ]),
    remarks: z.union([
        z.string().max(1333),
        z.null()
    ])
});

export const zUpdateWasteInquiryCommand = z.object({
    customerId: z.union([
        z.string(),
        z.null()
    ]).optional(),
    customerName: z.union([
        z.string(),
        z.null()
    ]).optional(),
    wasteProducerId: z.union([
        z.string(),
        z.null()
    ]).optional(),
    wasteProducerName: z.union([
        z.string(),
        z.null()
    ]).optional(),
    isUnknownWasteProducer: z.boolean().optional(),
    pickUpAddressId: z.union([
        z.string(),
        z.null()
    ]).optional(),
    pickUpAddressName: z.union([
        z.string(),
        z.null()
    ]).optional(),
    isUnknownPickUpAddress: z.boolean().optional(),
    wasteStreamName: z.union([
        z.string().max(40),
        z.null()
    ]).optional(),
    wasteStreamDescription: z.union([
        z.string().max(1333),
        z.null()
    ]).optional(),
    ewcLevel1Name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    ewcLevel2Name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    ewcLevel3Name: z.union([
        z.string(),
        z.null()
    ]).optional(),
    stateOfMatter: z.union([
        zStateOfMatter,
        z.null()
    ]).optional(),
    packagingType: z.union([
        zWastePackagingType,
        z.null()
    ]).optional(),
    flashpoint: z.union([
        zWasteFlashpointOption,
        z.null()
    ]).optional(),
    ph: z.union([
        zWastePhOption,
        z.null()
    ]).optional(),
    specificGravity: z.union([
        z.number().gte(-9999999.9).lte(9999999.9),
        z.null()
    ]).optional(),
    stableTemperatureType: z.union([
        zStableTemperatureType,
        z.null()
    ]).optional(),
    minStableTemperature: z.union([
        z.number().gte(-99999).lte(99999),
        z.null()
    ]).optional(),
    maxStableTemperature: z.union([
        z.number().gte(-99999).lte(99999),
        z.null()
    ]).optional(),
    averageStableTemperature: z.union([
        z.number().gte(-99999).lte(99999),
        z.null()
    ]).optional(),
    sdsFiles: z.array(zCreateFileLinkCommand).optional(),
    noSds: z.boolean().optional(),
    analysisReportFiles: z.array(zCreateFileLinkCommand).optional(),
    noAnalysisReport: z.boolean().optional(),
    composition: z.array(zWasteComposition).optional(),
    isSampleAvailable: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    selectedLegislationOptions: z.array(zWasteLegislationOption).optional(),
    svhcExtra: z.union([
        zSvhcExtraOption,
        z.null()
    ]).optional(),
    legislationRemarks: z.union([
        z.string(),
        z.null()
    ]).optional(),
    selectedPropertyOptions: z.array(zWastePropertyOption).optional(),
    propertyRemarks: z.union([
        z.string(),
        z.null()
    ]).optional(),
    expectedYearlyVolumeAmount: z.union([
        z.number().gte(0.01).lte(99999999999.99),
        z.null()
    ]).optional(),
    expectedYearlyVolumeUnit: z.union([
        zWasteMeasurementUnit,
        z.null()
    ]).optional(),
    expectedPerCollectionQuantity: z.union([
        z.number().gte(0.01).lte(99999999999.99),
        z.null()
    ]).optional(),
    expectedPerCollectionUnit: z.union([
        zWasteMeasurementUnit,
        z.null()
    ]).optional(),
    dischargeFrequency: z.union([
        zWasteDischargeFrequency,
        z.null()
    ]).optional(),
    firstCollectionDate: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    expectedEndDate: z.union([
        z.string().date(),
        z.null()
    ]).optional(),
    collectionRemarks: z.union([
        z.string().max(1333),
        z.null()
    ]).optional(),
    isTransportByIndaver: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    isLoadingByIndaver: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    isRegulatedTransport: z.union([
        zRegulatedTransportOption,
        z.null()
    ]).optional(),
    unNumbers: z.array(zUnNumber).optional(),
    packaging: z.array(zWastePackaging).optional(),
    transportType: z.union([
        zWasteTransportType,
        z.null()
    ]).optional(),
    loadingType: z.union([
        zWasteLoadingType,
        z.null()
    ]).optional(),
    transportIn: z.union([
        zWasteTransportInOption,
        z.null()
    ]).optional(),
    loadingMethod: z.union([
        zWasteLoadingMethod,
        z.null()
    ]).optional(),
    storedIn: z.union([
        zWasteStoredInOption,
        z.null()
    ]).optional(),
    containerLoadingType: z.union([
        zContainerLoadingType,
        z.null()
    ]).optional(),
    transportVolumeAmount: z.union([
        z.number().gte(0.01).lte(99999999999.99),
        z.null()
    ]).optional(),
    transportVolumeUnit: z.union([
        zWasteMeasurementUnit,
        z.null()
    ]).optional(),
    isTankOwnedByCustomer: z.union([
        z.boolean(),
        z.null()
    ]).optional(),
    collectionRequirements: z.union([
        zCollectionRequirementOption,
        z.null()
    ]).optional(),
    remarks: z.union([
        z.string().max(1333),
        z.null()
    ]).optional(),
    sendCopyToContacts: z.array(zContact).optional(),
    additionalFiles: z.array(zCreateFileLinkCommand).optional(),
    hazardInducer1: z.union([
        z.string(),
        z.null()
    ]).optional(),
    hazardInducer2: z.union([
        z.string(),
        z.null()
    ]).optional(),
    hazardInducer3: z.union([
        z.string(),
        z.null()
    ]).optional()
});

export const zUpdateWasteInquiryResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime()
});

export const zWasteInquiryAlreadySubmitted = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '409'
    ]),
    code: z.enum([
        'waste_inquiry_already_submitted'
    ])
});

export const zSubmitWasteInquiryResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    submittedOn: z.string().datetime(),
    inquiryNumber: z.string()
});

export const zViewWasteInquiryIndexSortQueryKey = z.enum([
    'contractId',
    'contractItem',
    'date',
    'customerName',
    'pickUpAddressId',
    'inquiryNumber',
    'salesOrganisationId',
    'wasteStreamName',
    'wasteProducerId',
    'wasteProducerName',
    'conformityCheck'
]);

export const zViewWasteInquiryIndexSortQuery = z.object({
    key: zViewWasteInquiryIndexSortQueryKey,
    order: zSortDirection
});

export const zViewWasteInquiryIndexFilterQuery = z.object({
    statuses: z.array(zWasteInquiryStatus),
    contractId: z.string().optional(),
    contractItem: z.string().optional(),
    customerId: z.string().optional(),
    salesOrganisationId: z.string().optional(),
    wasteProducerId: z.string().optional(),
    pickUpAddressId: z.string().optional(),
    wasteStreamName: z.string().optional(),
    ewcCode: z.string().optional(),
    inquiryNumber: z.string().optional(),
    date: zDateRange.optional(),
    conformityCheck: z.boolean().optional(),
    requestorName: z.string().optional()
});

export const zWasteInquiryResponse = z.object({
    uuid: z.union([
        z.string(),
        z.null()
    ]),
    inquiryNumber: z.union([
        z.string(),
        z.null()
    ]),
    wasteStreamName: z.union([
        z.string(),
        z.null()
    ]),
    date: z.union([
        z.string().date(),
        z.null()
    ]),
    contractId: z.union([
        z.string(),
        z.null()
    ]),
    contractItem: z.union([
        z.string(),
        z.null()
    ]),
    customerId: z.union([
        z.string(),
        z.null()
    ]),
    customerName: z.union([
        z.string(),
        z.null()
    ]),
    salesOrganisationId: z.union([
        z.string(),
        z.null()
    ]),
    salesOrganisationName: z.union([
        z.string(),
        z.null()
    ]),
    wasteProducerId: z.union([
        z.string(),
        z.null()
    ]),
    wasteProducerName: z.union([
        z.string(),
        z.null()
    ]),
    pickUpAddressId: z.union([
        z.string(),
        z.null()
    ]),
    pickUpAddressName: z.union([
        z.string(),
        z.null()
    ]),
    requestorName: z.union([
        z.string(),
        z.null()
    ]),
    status: zWasteInquiryStatus,
    ewcLevel1: z.union([
        z.string(),
        z.null()
    ]),
    ewcLevel2: z.union([
        z.string(),
        z.null()
    ]),
    ewcLevel3: z.union([
        z.string(),
        z.null()
    ]),
    conformityCheck: z.boolean()
});

export const zViewWasteInquiryIndexResponse = z.object({
    items: z.array(zWasteInquiryResponse),
    meta: zPaginatedOffsetResponseMeta
});

export const zViewWasteInquirySapResponse = z.object({
    status: zWasteInquiryStatus,
    customer: z.union([
        zCustomerResponse,
        z.null()
    ]),
    wasteProducer: z.union([
        zWasteProducerResponse,
        z.null()
    ]),
    isUnknownWasteProducer: z.boolean(),
    pickUpAddress: z.union([
        zPickUpAddressResponse,
        z.null()
    ]),
    isUnknownPickUpAddress: z.boolean(),
    wasteStreamName: z.union([
        z.string(),
        z.null()
    ]),
    wasteStreamDescription: z.union([
        z.string(),
        z.null()
    ]),
    ewcLevel1Name: z.union([
        z.string(),
        z.null()
    ]),
    ewcLevel2Name: z.union([
        z.string(),
        z.null()
    ]),
    ewcLevel3Name: z.union([
        z.string(),
        z.null()
    ]),
    stateOfMatter: z.union([
        zStateOfMatter,
        z.null()
    ]),
    packagingType: z.union([
        zWastePackagingType,
        z.null()
    ]),
    flashpoint: z.union([
        zWasteFlashpointOption,
        z.null()
    ]),
    ph: z.union([
        zWastePhOption,
        z.null()
    ]),
    specificGravity: z.union([
        z.number(),
        z.null()
    ]),
    stableTemperatureType: z.union([
        zStableTemperatureType,
        z.null()
    ]),
    minStableTemperature: z.union([
        z.number(),
        z.null()
    ]),
    maxStableTemperature: z.union([
        z.number(),
        z.null()
    ]),
    averageStableTemperature: z.union([
        z.number(),
        z.null()
    ]),
    sdsFiles: z.array(zFileLinkResponse),
    noSds: z.boolean(),
    analysisReportFiles: z.array(zFileLinkResponse),
    noAnalysisReport: z.boolean(),
    composition: z.array(zWasteCompositionResponse),
    isSampleAvailable: z.union([
        z.boolean(),
        z.null()
    ]),
    selectedLegislationOptions: z.array(zWasteLegislationOption),
    svhcExtra: z.union([
        zSvhcExtraOption,
        z.null()
    ]),
    legislationRemarks: z.union([
        z.string(),
        z.null()
    ]),
    selectedPropertyOptions: z.array(zWastePropertyOption),
    propertyRemarks: z.union([
        z.string(),
        z.null()
    ]),
    expectedYearlyVolumeAmount: z.union([
        z.number(),
        z.null()
    ]),
    expectedYearlyVolumeUnit: z.union([
        zWasteMeasurementUnit,
        z.null()
    ]),
    expectedPerCollectionQuantity: z.union([
        z.number(),
        z.null()
    ]),
    expectedPerCollectionUnit: z.union([
        zWasteMeasurementUnit,
        z.null()
    ]),
    dischargeFrequency: z.union([
        zWasteDischargeFrequency,
        z.null()
    ]),
    firstCollectionDate: z.union([
        z.string().date(),
        z.null()
    ]),
    expectedEndDate: z.union([
        z.string().date(),
        z.null()
    ]),
    collectionRemarks: z.union([
        z.string(),
        z.null()
    ]),
    isTransportByIndaver: z.union([
        z.boolean(),
        z.null()
    ]),
    isLoadingByIndaver: z.union([
        z.boolean(),
        z.null()
    ]),
    isRegulatedTransport: z.union([
        zRegulatedTransportOption,
        z.null()
    ]),
    unNumbers: z.array(zWasteInquiryUnNumberResponse),
    packaging: z.array(zWastePackagingResponse),
    transportType: z.union([
        zWasteTransportType,
        z.null()
    ]),
    containerLoadingType: z.union([
        zContainerLoadingType,
        z.null()
    ]),
    loadingType: z.union([
        zWasteLoadingType,
        z.null()
    ]),
    transportIn: z.union([
        zWasteTransportInOption,
        z.null()
    ]),
    loadingMethod: z.union([
        zWasteLoadingMethod,
        z.null()
    ]),
    storedIn: z.union([
        zWasteStoredInOption,
        z.null()
    ]),
    transportVolumeAmount: z.union([
        z.number(),
        z.null()
    ]),
    transportVolumeUnit: z.union([
        zWasteMeasurementUnit,
        z.null()
    ]),
    isTankOwnedByCustomer: z.union([
        z.boolean(),
        z.null()
    ]),
    collectionRequirements: z.union([
        zCollectionRequirementOption,
        z.null()
    ]),
    remarks: z.union([
        z.string(),
        z.null()
    ]),
    sendCopyToContacts: z.array(zContactTypeResponse),
    additionalFiles: z.array(zFileLinkResponse),
    submittedOn: z.union([
        z.string().datetime(),
        z.null()
    ]),
    hazardInducer1: z.union([
        z.string(),
        z.null()
    ]),
    hazardInducer2: z.union([
        z.string(),
        z.null()
    ]),
    hazardInducer3: z.union([
        z.string(),
        z.null()
    ]),
    inquiryNumber: z.union([
        z.string(),
        z.null()
    ]),
    createdBy: z.union([
        z.string(),
        z.null()
    ]),
    contractNumber: z.union([
        z.string(),
        z.null()
    ]),
    contractItem: z.union([
        z.string(),
        z.null()
    ])
});

export const zEntityPart = z.enum([
    'sds',
    'analysis-report',
    'additional',
    'news-image'
]);

export const zUploadDocumentWasteInquiryCommand = z.object({
    fileUuid: z.string().uuid(),
    entityPart: zEntityPart
});

export const zUploadDocumentSubmittedWasteInquiryCommand = z.object({
    documents: z.array(zUploadDocumentWasteInquiryCommand)
});

export const zBulkDeleteWasteInquiryCommand = z.object({
    wasteInquiryUuids: z.array(z.string())
});

export const zCopyWasteInquirySapResponse = z.object({
    uuid: z.string().uuid()
});

export const zCreateWeeklyPlanningRequestCommand = z.record(z.unknown());

export const zCreateWeeklyPlanningRequestResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime()
});

export const zCustomerNotProvidedError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'customer_not_provided'
    ])
});

export const zUpdateWeeklyPlanningRequestCommand = z.object({
    customerId: z.union([
        z.string(),
        z.null()
    ]).optional(),
    wasteProducerId: z.union([
        z.string(),
        z.null()
    ]).optional(),
    pickUpAddressIds: z.array(z.string()).optional(),
    remarks: z.union([
        z.string(),
        z.null()
    ]).optional(),
    additionalFiles: z.array(zCreateFileLinkCommand).optional(),
    sendCopyToContacts: z.array(zContact).optional()
});

export const zUpdateWeeklyPlanningRequestResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime()
});

export const zAddWprPickUpRequestCommand = z.object({
    pickUpRequestUuid: z.string().uuid()
});

export const zAddWprPickUpRequestResponse = z.object({
    uuid: z.string().uuid()
});

export const zViewWprPickUpRequestResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    contractLineId: z.string(),
    contractNumber: z.string(),
    contractItem: z.string(),
    customerReference: z.union([
        z.string(),
        z.null()
    ]),
    wasteMaterial: z.union([
        z.string(),
        z.null()
    ]),
    materialNumber: z.union([
        z.string(),
        z.null()
    ]),
    treatmentCenterName: z.union([
        z.string(),
        z.null()
    ]),
    installationName: z.union([
        z.string(),
        z.null()
    ]),
    customerId: z.union([
        z.string(),
        z.null()
    ]),
    customerName: z.union([
        z.string(),
        z.null()
    ]),
    wasteProducerId: z.union([
        z.string(),
        z.null()
    ]),
    wasteProducerName: z.union([
        z.string(),
        z.null()
    ]),
    pickUpAddressId: z.union([
        z.string(),
        z.null()
    ]),
    pickUpAddressName: z.union([
        z.string(),
        z.null()
    ]),
    asn: z.union([
        z.string(),
        z.null()
    ]),
    tfs: z.union([
        z.boolean(),
        z.null()
    ]),
    isHazardous: z.union([
        z.boolean(),
        z.null()
    ]),
    packaged: z.union([
        zContractLinePackagingType,
        z.null()
    ]),
    tcNumber: z.union([
        z.string(),
        z.null()
    ]),
    materialAnalysis: z.union([
        z.string(),
        z.null()
    ]),
    ewcCode: z.union([
        z.string(),
        z.null()
    ]),
    endTreatmentCenterId: z.union([
        z.string(),
        z.null()
    ]),
    endTreatmentCenterName: z.union([
        z.string(),
        z.null()
    ]),
    remarks: z.union([
        z.string(),
        z.null()
    ]),
    processCode: z.union([
        z.string(),
        z.null()
    ]),
    esnNumber: z.union([
        z.string(),
        z.null()
    ]),
    deliveryInfo: z.union([
        z.string(),
        z.null()
    ]),
    materialType: z.union([
        z.string(),
        z.null()
    ]),
    packagingIndicator: z.union([
        z.string(),
        z.null()
    ]),
    startDate: z.union([
        z.string().date(),
        z.null()
    ]),
    startTime: z.union([
        z.string().time(),
        z.null()
    ])
});

export const zViewWeeklyPlanningRequestResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    customer: z.union([
        zCustomerResponse,
        z.null()
    ]),
    wasteProducer: z.union([
        zWasteProducerResponse,
        z.null()
    ]),
    pickUpAddresses: z.array(zPickUpAddressResponse),
    remarks: z.union([
        z.string(),
        z.null()
    ]),
    sendCopyToContacts: z.array(zContactTypeResponse),
    additionalFiles: z.array(zFileLinkResponse),
    pickUpRequests: z.array(zViewWprPickUpRequestResponse)
});

export const zWeeklyPlanningRequestAlreadySubmittedError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '400'
    ]),
    source: zErrorSource.optional(),
    code: z.enum([
        'weekly_planning_request_already_submitted_error'
    ])
});

export const zSubmitWeeklyPlanningRequestResponse = z.object({
    uuid: z.string().uuid(),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    submittedOn: z.string().datetime(),
    inquiryNumber: z.string()
});

export const zInternalServerApiError = z.object({
    detail: z.string().optional(),
    status: z.enum([
        '500'
    ]),
    code: z.enum([
        'internal_server_error'
    ])
});

export const zMigrateCollectionsV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        collections: z.array(z.enum([
            'contact',
            'pick-up-request-template',
            'user'
        ])).optional(),
        fresh: z.boolean().optional()
    }).optional()
});

export const zImportCollectionsV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        collections: z.array(z.enum([
            'contact',
            'pick-up-request-template',
            'user'
        ])).optional()
    }).optional()
});

export const zViewCollectionsV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.never().optional()
});

export const zViewMeV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * User details retrieved
 */
export const zViewMeV1Response = zViewMeResponse;

export const zViewUserDetailV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        uuid: z.string()
    }),
    query: z.never().optional()
});

/**
 * User details retrieved
 */
export const zViewUserDetailV1Response = zViewUserDetailResponse;

export const zViewUserIndexV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        pagination: zPaginatedOffsetQuery.optional(),
        search: z.string().optional()
    }).optional()
});

/**
 * Users retrieved
 */
export const zViewUserIndexV1Response = zViewUserIndexResponse;

export const zSyncEntraUsersV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.never().optional()
});

export const zStartUserImpersonationV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        uuid: z.string()
    }),
    query: z.never().optional()
});

/**
 * User impersonation started
 */
export const zStartUserImpersonationV1Response = zStartUserImpersonationResponse;

export const zViewPermissionIndexV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.never().optional()
});

export const zViewPermissionIndexV1Response = zViewPermissionIndexResponse;

export const zViewCustomerIndexV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        pagination: zPaginatedOffsetQuery.optional(),
        search: z.string().optional()
    }).optional()
});

export const zViewCustomerIndexV1Response = zViewCustomerIndexResponse;

export const zCreateFileV1Data = z.object({
    body: zCreateFileCommand,
    path: z.never().optional(),
    query: z.never().optional()
});

export const zCreateFileV1Response = zCreateFileResponse;

export const zConfirmFileUploadV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        file: z.string()
    }),
    query: z.never().optional()
});

export const zDownloadFileV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        file: z.string()
    }),
    query: z.never().optional()
});

export const zProxyExternalFileV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        url: z.string()
    })
});

export const zViewSuggestedCustomersV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        filter: zViewSuggestedCustomersFilterQuery
    })
});

export const zViewSuggestedCustomersV1Response = zViewSuggestedCustomersResponse;

export const zViewCustomerCountryV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        id: z.string()
    }),
    query: z.never().optional()
});

export const zViewCustomerCountryV1Response = zViewCustomerCountryResponse;

export const zViewDomainEventLogIndexV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        filter: zViewDomainEventLogIndexFilterQuery.optional(),
        pagination: zViewDomainEventLogIndexPaginationQuery.optional()
    }).optional()
});

export const zViewDomainEventLogIndexV1Response = zViewDomainEventLogIndexResponse;

export const zSearchCollectionsV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        filter: zSearchCollectionsFilterQuery,
        search: z.string()
    })
});

export const zSearchCollectionsV1Response = zSearchCollectionsResponse;

export const zViewJobsIndexV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        sort: z.array(zViewJobsIndexSortQuery).optional(),
        filter: zViewJobsIndexFilterQuery.optional(),
        pagination: zViewJobsIndexPaginationQuery.optional()
    }).optional()
});

export const zViewJobsIndexV1Response = zViewJobsIndexResponse;

export const zViewJobDetailV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        jobId: z.string()
    }),
    query: z.object({
        isArchived: z.boolean()
    })
});

export const zViewJobDetailV1Response = zViewJobDetailResponse;

export const zGetMyNotificationPreferencesV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.never().optional()
});

export const zGetMyNotificationPreferencesV1Response = zGetMyNotificationPreferencesResponse;

export const zGetNotificationTypesConfigV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.never().optional()
});

export const zGetNotificationTypesConfigV1Response = zGetNotificationTypesConfigResponse;

export const zUpdateMyChannelNotificationPreferenceV1Data = z.object({
    body: zUpdateMyChannelNotificationPreferenceCommand,
    path: z.never().optional(),
    query: z.never().optional()
});

export const zUpdateMyChannelNotificationPreferenceV1Response = z.void();

export const zSendTestNotificationV1Data = z.object({
    body: zSendTestNotificationCommand,
    path: z.never().optional(),
    query: z.never().optional()
});

export const zSendTestNotificationV1Response = z.void();

export const zGetMyNotificationsV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        filter: zGetMyNotificationsFilterQuery.optional(),
        pagination: zGetMyNotificationsPaginationQuery.optional()
    }).optional()
});

export const zGetMyNotificationsV1Response = zGetMyNotificationsResponse;

export const zViewUnreadNotificationsCountV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.never().optional()
});

export const zViewUnreadNotificationsCountV1Response = zViewUnreadNotificationsCountResponse;

export const zViewUserNotificationDetailV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        notificationUuid: z.string()
    }),
    query: z.never().optional()
});

export const zViewUserNotificationDetailV1Response = zTestNotificationNotification;

export const zMarkAllNotificationAsReadV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.never().optional()
});

export const zMarkAllNotificationAsReadV1Response = z.void();

export const zUpdateMyNotificationTypePreferenceV1Data = z.object({
    body: zUpdateMyNotificationTypePreferenceCommand,
    path: z.never().optional(),
    query: z.never().optional()
});

export const zUpdateMyNotificationTypePreferenceV1Response = z.void();

export const zMarkNotificationAsReadV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        notificationUuid: z.string()
    }),
    query: z.never().optional()
});

export const zMarkNotificationAsReadV1Response = z.void();

export const zMarkNotificationAsUnreadV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        notificationUuid: z.string()
    }),
    query: z.never().optional()
});

export const zMarkNotificationAsUnreadV1Response = z.void();

export const zUpdateMyNotificationPreferencePresetV1Data = z.object({
    body: zUpdateMyNotificationPreferencePresetCommand,
    path: z.never().optional(),
    query: z.never().optional()
});

export const zUpdateMyNotificationPreferencePresetV1Response = z.void();

export const zMigrateNotificationTypesV1Data = z.object({
    body: zMigrateNotificationTypesCommand,
    path: z.never().optional(),
    query: z.never().optional()
});

export const zMigrateNotificationTypesV1Response = z.void();

export const zViewRoleIndexV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * The roles has been successfully received.
 */
export const zViewRoleIndexV1Response = zViewRoleIndexResponse;

export const zUpdateRolesPermissionsV1Data = z.object({
    body: zUpdateRolesPermissionsCommand,
    path: z.never().optional(),
    query: z.never().optional()
});

export const zUpdateRolesPermissionsV1Response = z.void();

export const zCreateRoleV1Data = z.object({
    body: zCreateRoleCommand,
    path: z.never().optional(),
    query: z.never().optional()
});

export const zCreateRoleV1Response = zCreateRoleResponse;

export const zClearRolePermissionsCacheV1Data = z.object({
    body: zClearRolePermissionsCacheCommand,
    path: z.never().optional(),
    query: z.never().optional()
});

export const zClearRolePermissionsCacheV1Response = z.void();

export const zDeleteRoleV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        role: z.string()
    }),
    query: z.never().optional()
});

export const zDeleteRoleV1Response = z.void();

export const zViewRoleDetailV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        role: z.string()
    }),
    query: z.never().optional()
});

/**
 * The role has been successfully received.
 */
export const zViewRoleDetailV1Response = zViewRoleDetailResponse;

export const zUpdateRoleV1Data = z.object({
    body: zUpdateRoleCommand,
    path: z.object({
        uuid: z.string()
    }),
    query: z.never().optional()
});

export const zGetApiInfoData = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.never().optional()
});

/**
 * API info retrieved
 */
export const zGetApiInfoResponse2 = zGetApiInfoResponse;

export const zSwaggerData = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.never().optional()
});

export const zViewUiPreferencesV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.never().optional()
});

export const zViewUiPreferencesV1Response = zViewUiPreferencesResponse;

export const zUpdateUiPreferencesV1Data = z.object({
    body: zUpdateUiPreferencesCommand,
    path: z.never().optional(),
    query: z.never().optional()
});

export const zViewAnnouncementIndexV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        pagination: zPaginatedOffsetQuery.optional()
    }).optional()
});

export const zViewAnnouncementIndexV1Response = zViewAnnouncementIndexResponse;

export const zCreateAnnouncementV1Data = z.object({
    body: zCreateAnnouncementCommand,
    path: z.never().optional(),
    query: z.never().optional()
});

export const zCreateAnnouncementV1Response = zCreateAnnouncementResponse;

export const zDeleteAnnouncementV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        uuid: z.string()
    }),
    query: z.never().optional()
});

export const zViewAnnouncementV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        uuid: z.string()
    }),
    query: z.never().optional()
});

export const zViewAnnouncementV1Response = zViewAnnouncementResponse;

export const zUpdateAnnouncementV1Data = z.object({
    body: zUpdateAnnouncementCommand,
    path: z.object({
        uuid: z.string()
    }),
    query: z.never().optional()
});

export const zUpdateAnnouncementV1Response = zUpdateAnnouncementResponse;

export const zViewDashboardAnnouncementIndexV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        pagination: zPaginatedOffsetQuery.optional()
    }).optional()
});

export const zViewDashboardAnnouncementIndexV1Response = zViewDashboardAnnouncementIndexResponse;

export const zViewDashboardAnnouncementV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        uuid: z.string()
    }),
    query: z.never().optional()
});

export const zViewDashboardAnnouncementV1Response = zViewDashboardAnnouncementResponse;

export const zDownloadCertificateV1Data = z.object({
    body: zDownloadCertificateCommand,
    path: z.never().optional(),
    query: z.never().optional()
});

export const zViewCertificateIndexV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        pagination: zPaginatedOffsetQuery.optional(),
        sort: z.array(zViewCertificateIndexSortQuery),
        filter: zViewCertificateIndexFilterQuery.optional()
    })
});

export const zViewCertificateIndexV1Response = zViewCertificateIndexResponse;

export const zViewContactIndexV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        pagination: zPaginatedOffsetQuery.optional(),
        search: z.string().optional()
    }).optional()
});

export const zViewContactIndexV1Response = zViewContactIndexResponse;

export const zCreateContactV1Data = z.object({
    body: zCreateContactCommand,
    path: z.never().optional(),
    query: z.never().optional()
});

export const zCreateContactV1Response = zCreateContactResponse;

export const zDeleteContactV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        uuid: z.string()
    }),
    query: z.never().optional()
});

export const zDeleteContactV1Response = z.void();

export const zUpdateContactV1Data = z.object({
    body: zUpdateContactCommand,
    path: z.object({
        uuid: z.string()
    }),
    query: z.never().optional()
});

export const zUpdateContactV1Response = z.void();

export const zViewContainerTypeIndexV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        filter: zViewContainerTypeIndexFilterQuery,
        search: z.string().optional()
    })
});

export const zViewContainerTypeIndexV1Response = zViewContainerTypeIndexResponse;

export const zViewContractLineIndexV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        pagination: zPaginatedOffsetQuery.optional(),
        sort: z.array(zViewContractLineIndexSortQuery).optional(),
        filter: zViewContractLineIndexFilterQuery,
        search: z.string().optional()
    })
});

export const zViewContractLineIndexV1Response = zViewContractLineIndexResponse;

export const zViewWprContractLineIndexV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        pagination: zPaginatedOffsetQuery.optional(),
        filter: zViewWprContractLineIndexFilterQuery
    })
});

export const zViewWprContractLineIndexV1Response = zViewWprContractLineIndexResponse;

export const zViewPackagingRequestContractLineIndexV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        pagination: zPaginatedOffsetQuery.optional(),
        filter: zViewPackagingRequestContractLineIndexFilterQuery
    })
});

export const zViewPackagingRequestContractLineIndexV1Response = zViewPackagingRequestContractLineIndexResponse;

export const zGenerateContractLinesPdfV1Data = z.object({
    body: zGenerateContractLinesPdfCommand,
    path: z.never().optional(),
    query: z.never().optional()
});

export const zGenerateContractLinesPdfV1Response = zGenerateContractLinesPdfResponse;

export const zDownloadDocumentV1Data = z.object({
    body: zDownloadDocumentCommand,
    path: z.never().optional(),
    query: z.never().optional()
});

export const zGetDocumentFiltersV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        filter: zGetDocumentFiltersFilterQuery
    })
});

export const zGetDocumentFiltersV1Response = zGetDocumentFiltersResponse;

export const zViewDocumentIndexV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        pagination: zViewDocumentIndexPaginationQuery.optional(),
        filter: zViewDocumentIndexFilterQuery,
        search: z.string().optional()
    })
});

export const zViewDocumentIndexV1Response = zViewDocumentIndexResponse;

export const zViewUserSiteIndexV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.never().optional()
});

export const zViewUserSiteIndexV1Response = z.array(zViewUserSiteIndexResponse);

export const zViewDynamicTableColumnIndexV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        name: z.string()
    }),
    query: z.never().optional()
});

export const zViewDynamicTableColumnIndexV1Response = zDynamicTableIndexColumnResponse;

export const zViewDynamicTableViewIndexV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        name: z.string()
    }),
    query: z.object({
        pagination: zPaginatedOffsetQuery.optional()
    }).optional()
});

export const zViewDynamicTableViewIndexV1Response = zDynamicTableViewIndexResponse;

export const zCreateDynamicTableViewV1Data = z.object({
    body: zCreateDynamicTableViewCommand,
    path: z.object({
        name: z.string()
    }),
    query: z.never().optional()
});

export const zCreateDynamicTableViewV1Response = zCreateDynamicTableViewResponse;

export const zViewDefaultDynamicTableViewV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        name: z.string()
    }),
    query: z.never().optional()
});

export const zViewDefaultDynamicTableViewV1Response = zViewDefaultDynamicTableViewResponse;

export const zDeleteDynamicTableViewV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        name: z.string(),
        uuid: z.string()
    }),
    query: z.never().optional()
});

export const zUpdateDynamicTableViewV1Data = z.object({
    body: zUpdateDynamicTableViewCommand,
    path: z.object({
        name: z.string(),
        uuid: z.string()
    }),
    query: z.never().optional()
});

export const zUpdateDynamicTableViewV1Response = zUpdateDynamicTableViewResponse;

export const zViewEwcCodeIndexV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        search: z.string().optional()
    }).optional()
});

export const zViewEwcCodeIndexV1Response = zViewEwcCodeIndexResponse;

export const zDownloadGuidanceLetterSapV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        shipmentId: z.string()
    }),
    query: z.object({
        type: zDownloadGuidanceLetterType
    })
});

export const zViewGuidanceLetterIndexV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        pagination: zPaginatedOffsetQuery.optional(),
        sort: z.array(zViewGuidanceLetterIndexSortQuery).optional(),
        filter: zViewGuidanceLetterIndexFilterQuery.optional()
    }).optional()
});

export const zViewGuidanceLetterIndexV1Response = zViewGuidanceLetterIndexResponse;

export const zDownloadInvoiceCertificateV1Data = z.object({
    body: zDownloadInvoiceCertificateCommand,
    path: z.never().optional(),
    query: z.never().optional()
});

export const zDownloadInvoiceV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        invoiceNumber: z.string()
    }),
    query: z.never().optional()
});

export const zExportInvoicesExcelV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        pagination: zPaginatedOffsetQuery.optional(),
        sort: z.array(zViewInvoiceIndexSortQuery).optional(),
        filter: zExportInvoicesExcelFilterQuery,
        search: z.string().optional()
    })
});

export const zViewInvoiceIndexV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        pagination: zPaginatedOffsetQuery.optional(),
        sort: z.array(zViewInvoiceIndexSortQuery).optional(),
        filter: zViewInvoiceIndexFilterQuery,
        search: z.string().optional()
    })
});

export const zViewInvoiceIndexV1Response = zViewInvoiceIndexResponse;

export const zViewInvoiceDetailV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        requestNumber: z.string()
    }),
    query: z.never().optional()
});

export const zViewInvoiceDetailV1Response = zViewInvoiceResponse;

export const zViewDraftInvoiceDetailV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        invoiceNumber: z.string()
    }),
    query: z.never().optional()
});

export const zViewDraftInvoiceDetailV1Response = zViewDraftInvoiceDetailResponse;

export const zViewDraftInvoiceIndexV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        pagination: zPaginatedOffsetQuery.optional(),
        sort: z.array(zViewDraftInvoiceIndexSortQuery).optional(),
        filter: zViewDraftInvoiceIndexFilterQuery
    })
});

export const zViewDraftInvoiceIndexV1Response = zViewDraftInvoiceIndexResponse;

export const zExportDraftInvoicesExcelV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        pagination: zPaginatedOffsetQuery.optional(),
        sort: z.array(zViewDraftInvoiceIndexSortQuery).optional(),
        filter: zExportDraftInvoicesExcelFilterQuery
    })
});

export const zApproveDraftInvoiceV1Data = z.object({
    body: zApproveDraftInvoiceCommand,
    path: z.object({
        invoiceNumber: z.string()
    }),
    query: z.never().optional()
});

export const zRejectDraftInvoiceV1Data = z.object({
    body: zRejectDraftInvoiceCommand,
    path: z.object({
        invoiceNumber: z.string()
    }),
    query: z.never().optional()
});

export const zSubscribeToNewsletterV1Data = z.object({
    body: zSubscribeToNewsletterCommand,
    path: z.never().optional(),
    query: z.never().optional()
});

export const zSubscribeToNewsletterV1Response = z.void();

export const zViewNewsItemIndexV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        pagination: zPaginatedOffsetQuery.optional()
    }).optional()
});

export const zViewNewsItemIndexV1Response = zViewNewsIndexResponse;

export const zCreateNewsItemV1Data = z.object({
    body: zCreateNewsItemCommand,
    path: z.never().optional(),
    query: z.never().optional()
});

export const zCreateNewsItemV1Response = zCreateNewsItemResponse;

export const zDeleteNewsItemV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        uuid: z.string()
    }),
    query: z.never().optional()
});

export const zViewNewsItemV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        uuid: z.string()
    }),
    query: z.never().optional()
});

export const zViewNewsItemV1Response = zViewNewsItemResponse;

export const zUpdateNewsItemV1Data = z.object({
    body: zUpdateNewsItemCommand,
    path: z.object({
        uuid: z.string()
    }),
    query: z.never().optional()
});

export const zUpdateNewsItemV1Response = zUpdateNewsItemResponse;

export const zViewDashboardNewsItemIndexV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        pagination: zPaginatedOffsetQuery.optional(),
        filter: zViewDashboardNewItemFilterQuery.optional()
    }).optional()
});

export const zViewDashboardNewsItemIndexV1Response = zViewDashboardNewsIndexResponse;

export const zViewDashboardNewsItemV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        uuid: z.string()
    }),
    query: z.never().optional()
});

export const zViewDashboardNewsItemV1Response = zViewDashboardNewsItemResponse;

export const zCreatePackagingRequestV1Data = z.object({
    body: zCreatePackagingRequestCommand,
    path: z.never().optional(),
    query: z.never().optional()
});

export const zCreatePackagingRequestV1Response = zCreatePackagingRequestResponse;

export const zViewPackagingRequestV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        uuid: z.string()
    }),
    query: z.never().optional()
});

export const zViewPackagingRequestV1Response = zViewPackagingRequestResponse;

export const zUpdatePackagingRequestV1Data = z.object({
    body: zUpdatePackagingRequestCommand,
    path: z.object({
        uuid: z.string()
    }),
    query: z.never().optional()
});

export const zUpdatePackagingRequestV1Response = zUpdatePackagingRequestResponse;

export const zSubmitPackagingRequestV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        uuid: z.string()
    }),
    query: z.never().optional()
});

/**
 * Packaging request submitted
 */
export const zSubmitPackagingRequestV1Response = zSubmitPackagingRequestResponse;

export const zBulkDeletePackagingRequestV1Data = z.object({
    body: zBulkDeletePackagingRequestCommand,
    path: z.never().optional(),
    query: z.never().optional()
});

export const zCopyPackagingRequestSapV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        requestNumber: z.string()
    }),
    query: z.never().optional()
});

export const zCopyPackagingRequestSapV1Response = zCopyPackagingRequestSapResponse;

export const zViewPackagingTypeIndexV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        filter: zViewPackagingTypeIndexFilterQuery,
        search: z.string().optional()
    })
});

export const zViewPackagingTypeIndexV1Response = zViewPackagingTypeIndexResponse;

export const zViewPayerIndexV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        filter: zViewPayerIndexFilterQuery
    })
});

export const zViewPayerIndexV1Response = zViewPayerIndexResponse;

export const zViewPickUpAddressIndexV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        pagination: zPaginatedOffsetQuery.optional(),
        filter: zViewPickUpAddressIndexFilterQuery.optional(),
        search: z.string().optional()
    }).optional()
});

export const zViewPickUpAddressIndexV1Response = zViewPickUpAddressIndexResponse;

export const zViewSuggestedPickUpAddressesV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        filter: zViewSuggestedPickUpAddressesFilterQuery.optional()
    }).optional()
});

export const zViewSuggestedPickUpAddressesV1Response = zViewSuggestedPickUpAddressesResponse;

export const zGetIsPoNumberAndCostCenterRequiredV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        filter: zGetIsPoNumberAndCostCenterRequiredFilterQuery
    })
});

/**
 * Returns whether PO number and cost center are required
 */
export const zGetIsPoNumberAndCostCenterRequiredV1Response = zGetIsPoNumberAndCostCenterRequiredResponse;

export const zViewPickUpRequestIndexV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        pagination: zPaginatedOffsetQuery.optional(),
        sort: z.array(zViewPickUpRequestIndexSortQuery).optional(),
        filter: zViewPickUpRequestIndexFilterQuery
    })
});

export const zViewPickUpRequestIndexV1Response = zViewPickUpRequestIndexResponse;

export const zCreatePickUpRequestV1Data = z.object({
    body: zCreatePickUpRequestCommand,
    path: z.never().optional(),
    query: z.never().optional()
});

export const zCreatePickUpRequestV1Response = zCreatePickUpRequestResponse;

export const zViewPickUpRequestV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        uuid: z.string()
    }),
    query: z.never().optional()
});

export const zViewPickUpRequestV1Response = zViewPickUpRequestResponse;

export const zUpdatePickUpRequestV1Data = z.object({
    body: zUpdatePickUpRequestCommand,
    path: z.object({
        uuid: z.string()
    }),
    query: z.never().optional()
});

export const zUpdatePickUpRequestV1Response = zUpdatePickUpRequestResponse;

export const zViewWasteProducerIndexV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        pagination: zPaginatedOffsetQuery.optional(),
        filter: zViewWasteProducerIndexFilterQuery.optional(),
        search: z.string().optional()
    }).optional()
});

export const zViewWasteProducerIndexV1Response = zViewWasteProducerIndexResponse;

export const zViewSuggestedWasteProducersV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        filter: zViewSuggestedWasteProducersFilterQuery.optional()
    }).optional()
});

export const zViewSuggestedWasteProducersV1Response = zViewSuggestedWasteProducersResponse;

export const zSubmitPickUpRequestV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        uuid: z.string()
    }),
    query: z.never().optional()
});

/**
 * Pick up request submitted
 */
export const zSubmitPickUpRequestV1Response = zSubmitPickUpRequestResponse;

export const zBulkDeletePickUpRequestV1Data = z.object({
    body: zBulkDeletePickUpRequestCommand,
    path: z.never().optional(),
    query: z.never().optional()
});

export const zViewPickUpRequestSapV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        requestNumber: z.string()
    }),
    query: z.never().optional()
});

export const zViewPickUpRequestSapV1Response = zViewPickUpRequestSapResponse;

export const zUpdatePickUpRequestSapV1Data = z.object({
    body: zUpdatePickUpRequestSapCommand,
    path: z.object({
        requestNumber: z.string()
    }),
    query: z.never().optional()
});

export const zViewPickUpRequestTemplateIndexV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        pagination: zPaginatedOffsetQuery.optional(),
        sort: z.array(zViewPickUpRequestTemplateIndexSortQuery).optional(),
        search: z.string().optional()
    }).optional()
});

export const zViewPickUpRequestTemplateIndexV1Response = zViewPickUpRequestTemplateIndexResponse;

export const zCreatePickUpRequestTemplateV1Data = z.object({
    body: zCreatePickUpRequestTemplateCommand,
    path: z.never().optional(),
    query: z.never().optional()
});

export const zCreatePickUpRequestTemplateV1Response = zCreatePickUpRequestTemplateResponse;

export const zViewPickUpRequestTemplateV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        uuid: z.string()
    }),
    query: z.never().optional()
});

export const zViewPickUpRequestTemplateV1Response = zViewPickUpRequestTemplateResponse;

export const zUpdatePickUpRequestTemplateV1Data = z.object({
    body: zUpdatePickUpRequestTemplateCommand,
    path: z.object({
        uuid: z.string()
    }),
    query: z.never().optional()
});

export const zUpdatePickUpRequestTemplateV1Response = zUpdatePickUpRequestTemplateResponse;

export const zBulkDeletePickUpRequestTemplatesV1Data = z.object({
    body: zBulkDeletePickUpRequestTemplatesCommand,
    path: z.never().optional(),
    query: z.never().optional()
});

export const zCreatePickUpRequestFromTemplateV1Data = z.object({
    body: zCreatePickUpRequestFromTemplateCommand,
    path: z.object({
        uuid: z.string()
    }),
    query: z.never().optional()
});

export const zCreatePickUpRequestFromTemplateV1Response = zCreatePickUpRequestFromTemplateResponse;

export const zUploadDocumentSubmittedPickUpRequestV1Data = z.object({
    body: zUploadDocumentSubmittedPickUpRequestCommand,
    path: z.object({
        requestNumber: z.string()
    }),
    query: z.never().optional()
});

export const zDownloadDocumentSubmittedPickUpRequestV1Data = z.object({
    body: zDownloadDocumentSubmittedPickUpRequestCommand,
    path: z.object({
        requestNumber: z.string()
    }),
    query: z.never().optional()
});

export const zCopyPickUpRequestSapV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        requestNumber: z.string()
    }),
    query: z.never().optional()
});

export const zCopyPickUpRequestSapV1Response = zCopyPickUpRequestSapResponse;

export const zSubmitPickUpRequestSapV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        requestNumber: z.string()
    }),
    query: z.never().optional()
});

export const zViewSalesOrganisationIndexV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        pagination: zPaginatedOffsetQuery.optional(),
        search: z.string().optional()
    }).optional()
});

export const zViewSalesOrganisationIndexV1Response = zViewSalesOrganisationIndexResponse;

export const zViewTankerTypeIndexV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.never().optional()
});

export const zViewTankerTypeIndexV1Response = zViewTankerTypeIndexResponse;

export const zViewTransportTypeIndexV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.never().optional()
});

export const zViewTransportTypeIndexV1Response = zViewTransportTypeIndexResponse;

export const zViewUnNumberIndexV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        filter: zViewUnNumberFilterQuery.optional(),
        search: z.string().optional(),
        pagination: zViewUnNumberIndexPaginationQuery.optional()
    }).optional()
});

export const zViewUnNumberIndexV1Response = zViewUnNumberIndexResponse;

export const zViewUnNumberIndexForPickUpRequestV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        filter: zViewUnNumberIndexForPickUpRequestFilterQuery,
        search: z.string().optional()
    })
});

export const zViewUnNumberIndexForPickUpRequestV1Response = zViewUnNumberIndexForPickUpRequestResponse;

export const zViewWasteInquiryIndexV1Data = z.object({
    body: z.never().optional(),
    path: z.never().optional(),
    query: z.object({
        pagination: zPaginatedOffsetQuery.optional(),
        sort: z.array(zViewWasteInquiryIndexSortQuery).optional(),
        filter: zViewWasteInquiryIndexFilterQuery
    })
});

export const zViewWasteInquiryIndexV1Response = zViewWasteInquiryIndexResponse;

export const zCreateWasteInquiryV1Data = z.object({
    body: zCreateWasteInquiryCommand,
    path: z.never().optional(),
    query: z.never().optional()
});

export const zCreateWasteInquiryV1Response = zCreateWasteInquiryResponse;

export const zDownloadWasteInquirySummarySapV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        inquiryNumber: z.string()
    }),
    query: z.never().optional()
});

export const zDownloadWasteInquiryDocumentSapV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        inquiryNumber: z.string(),
        sapFileUuid: z.string()
    }),
    query: z.never().optional()
});

export const zViewWasteInquiryV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        uuid: z.string()
    }),
    query: z.never().optional()
});

export const zViewWasteInquiryV1Response = zViewWasteInquiryResponse;

export const zUpdateWasteInquiryV1Data = z.object({
    body: zUpdateWasteInquiryCommand,
    path: z.object({
        uuid: z.string()
    }),
    query: z.never().optional()
});

export const zUpdateWasteInquiryV1Response = zUpdateWasteInquiryResponse;

export const zSubmitWasteInquiryV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        uuid: z.string()
    }),
    query: z.never().optional()
});

/**
 * Waste inquiry submitted
 */
export const zSubmitWasteInquiryV1Response = zSubmitWasteInquiryResponse;

export const zViewWasteInquirySapV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        inquiryNumber: z.string()
    }),
    query: z.never().optional()
});

export const zViewWasteInquirySapV1Response = zViewWasteInquirySapResponse;

export const zUploadDocumentSubmittedWasteInquiryV1Data = z.object({
    body: zUploadDocumentSubmittedWasteInquiryCommand,
    path: z.object({
        inquiryNumber: z.string()
    }),
    query: z.never().optional()
});

export const zBulkDeleteWasteInquiryV1Data = z.object({
    body: zBulkDeleteWasteInquiryCommand,
    path: z.never().optional(),
    query: z.never().optional()
});

export const zCopyWasteInquirySapV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        inquiryNumber: z.string()
    }),
    query: z.never().optional()
});

export const zCopyWasteInquirySapV1Response = zCopyWasteInquirySapResponse;

export const zCreateWeeklyPlanningRequestV1Data = z.object({
    body: zCreateWeeklyPlanningRequestCommand,
    path: z.never().optional(),
    query: z.never().optional()
});

export const zCreateWeeklyPlanningRequestV1Response = zCreateWeeklyPlanningRequestResponse;

export const zViewWeeklyPlanningRequestV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        uuid: z.string()
    }),
    query: z.never().optional()
});

export const zViewWeeklyPlanningRequestV1Response = zViewWeeklyPlanningRequestResponse;

export const zUpdateWeeklyPlanningRequestV1Data = z.object({
    body: zUpdateWeeklyPlanningRequestCommand,
    path: z.object({
        uuid: z.string()
    }),
    query: z.never().optional()
});

export const zUpdateWeeklyPlanningRequestV1Response = zUpdateWeeklyPlanningRequestResponse;

export const zAddWprPickUpRequestV1Data = z.object({
    body: zAddWprPickUpRequestCommand,
    path: z.object({
        uuid: z.string()
    }),
    query: z.never().optional()
});

export const zAddWprPickUpRequestV1Response = zAddWprPickUpRequestResponse;

export const zSubmitWeeklyPlanningRequestV1Data = z.object({
    body: z.never().optional(),
    path: z.object({
        uuid: z.string()
    }),
    query: z.never().optional()
});

/**
 * Weekly planning request submitted
 */
export const zSubmitWeeklyPlanningRequestV1Response = zSubmitWeeklyPlanningRequestResponse;
