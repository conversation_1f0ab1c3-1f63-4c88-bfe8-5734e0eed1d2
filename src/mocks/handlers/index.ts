import { authHandlers } from '@/mocks/handlers/auth.mock.ts'
import { dashboardHandlers } from '@/mocks/handlers/dashboard.mock.ts'
import { invoiceHandlers } from '@/mocks/handlers/invoice.mock'
import { pickupRequestHandlers } from '@/mocks/handlers/pickupRequest.mock'
import { wasteInquiryHandlers } from '@/mocks/handlers/wasteInquiry.mock'

export const handlers = [
  ...authHandlers,
  ...dashboardHandlers,
  ...invoiceHandlers,
  ...wasteInquiryHandlers,
  ...pickupRequestHandlers,
]
