import {
  http,
  HttpResponse,
} from 'msw'

import { CalendarDateTimeTransformer } from '@/models/date/calendarDateTime.transformer.ts'
import { PickupRequestDetailDtoBuilder } from '@/models/pickup-request/detail/pickupRequestDetailDto.builder'
import { PickupRequestSapDetailDtoBuilder } from '@/models/pickup-request/sap-detail/pickupRequestSapDetailDto.builder'
import { PickupRequestSubmitResponseDtoBuilder } from '@/models/pickup-request/update/submit/pickupRequestSubmitResponseDto.builder'
import { UnNumberIndexDtoBuilder } from '@/models/un-number/index/unNumberIndexDto.builder'
import { PaginationUtil } from '@/utils/pagination.util.ts'
import { UuidUtil } from '@/utils/uuid.util.ts'

export const pickupRequestHandlers = [
  http.get('*/api/v1/pick-up-requests', () => {
    return HttpResponse.json(PaginationUtil.toDto([]))
  }),

  http.get('*/api/v1/pick-up-requests/is-po-number-and-cost-center-required', () => {
    return HttpResponse.json({
      isCostCenterRequired: false,
      isPoNumberRequired: false,
    })
  }),

  http.post('*/api/v1/pick-up-requests', () => {
    return HttpResponse.json({
      uuid: UuidUtil.getRandom(),
      createdAt: CalendarDateTimeTransformer.toDto(new Date()),
      updatedAt: CalendarDateTimeTransformer.toDto(new Date()),
    })
  }),

  http.get('*/api/v1/pick-up-requests/:uuid', ({ params }) => {
    const { uuid } = params as { uuid: string }
    const data = new PickupRequestDetailDtoBuilder().withData().build()

    return HttpResponse.json({
      ...data,
      uuid,
    })
  }),

  http.put('*/api/v1/pick-up-requests/:uuid', ({ params }) => {
    const { uuid } = params as { uuid: string }

    return HttpResponse.json({
      uuid,
      createdAt: CalendarDateTimeTransformer.toDto(new Date()),
      updatedAt: CalendarDateTimeTransformer.toDto(new Date()),
      isWicConfirmed: false,
      needsWicConfirmation: false,
    })
  }),

  http.post('*/api/v1/pick-up-requests/:uuid/submit', ({ params }) => {
    const { uuid } = params as { uuid: string }
    const data = new PickupRequestSubmitResponseDtoBuilder().withUuid(uuid).build()

    return HttpResponse.json(data)
  }),

  http.post('*/api/v1/pick-up-requests/bulk', () => {
    return HttpResponse.json({})
  }),

  http.get('*/api/v1/pick-up-requests/sap/:requestNumber', () => {
    const data = new PickupRequestSapDetailDtoBuilder().build()

    return HttpResponse.json(data)
  }),

  http.put('*/api/v1/pick-up-requests/sap/:requestNumber', () => {
    return HttpResponse.json({})
  }),

  http.post('*/api/v1/pick-up-requests/sap/:requestNumber/copy', () => {
    return HttpResponse.json({ uuid: UuidUtil.getRandom() })
  }),

  http.post('*/api/v1/pick-up-requests/sap/:requestNumber/submit', () => {
    return HttpResponse.json({})
  }),

  http.post('*/api/v1/pick-up-requests/:requestNumber/upload-documents', () => {
    return HttpResponse.json({})
  }),

  http.get('*/api/v1/un-numbers-pick-up-request', () => {
    const items = [
      new UnNumberIndexDtoBuilder().withNumber('1202').build(),
      new UnNumberIndexDtoBuilder().withNumber('1993').build(),
    ]

    return HttpResponse.json({ items })
  }),
]
