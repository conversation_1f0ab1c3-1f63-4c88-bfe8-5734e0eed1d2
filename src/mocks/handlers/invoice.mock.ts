import {
  http,
  HttpResponse,
} from 'msw'

import { DynamicTableViewIndexDtoBuilder } from '@/models/dynamic-table/view/dynamicTableViewIndexDto.builder'
import { InvoiceDraftDetailDtoBuilder } from '@/models/invoice/draft-detail/invoiceDraftDetailDto.builder'
import { PaginationUtil } from '@/utils/pagination.util'

export const invoiceHandlers = [
  // Index endpoints
  http.get('*/api/v1/invoices', () => {
    return HttpResponse.json(PaginationUtil.toDto([]))
  }),
  http.get('*/api/v1/draft-invoices', () => {
    return HttpResponse.json(PaginationUtil.toDto([]))
  }),

  http.get('*/api/v1/draft-invoices/:invoiceNumber', ({ params }) => {
    const { invoiceNumber } = params as { invoiceNumber: string }
    const detail = new InvoiceDraftDetailDtoBuilder().withInvoiceNumber(invoiceNumber).build()

    return HttpResponse.json(detail)
  }),

  // Dynamic table endpoints default to empty view/columns
  http.get('*/api/v1/dynamic-tables/invoice/views', () => {
    const view = new DynamicTableViewIndexDtoBuilder().build()

    return HttpResponse.json(PaginationUtil.toDto([
      view,
    ]))
  }),
  http.get('*/api/v1/dynamic-tables/invoice/default-view', () => {
    const view = new DynamicTableViewIndexDtoBuilder().build()

    return HttpResponse.json(view)
  }),
  http.get('*/api/v1/dynamic-tables/invoice/columns', () => {
    return HttpResponse.json({ items: [] })
  }),

  http.get('*/api/v1/dynamic-tables/draft-invoice/views', () => {
    const view = new DynamicTableViewIndexDtoBuilder().build()

    return HttpResponse.json(PaginationUtil.toDto([
      view,
    ]))
  }),
  http.get('*/api/v1/dynamic-tables/draft-invoice/default-view', () => {
    const view = new DynamicTableViewIndexDtoBuilder().build()

    return HttpResponse.json(view)
  }),
  http.get('*/api/v1/dynamic-tables/draft-invoice/columns', () => {
    return HttpResponse.json({ items: [] })
  }),
]
