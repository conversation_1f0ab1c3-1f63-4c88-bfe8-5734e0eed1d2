@import 'tailwindcss' layer(base);

@plugin "@tailwindcss/typography";

@variant motion-reduce (&:where(.reduce-motion, .reduce-motion *));
@variant dark (&:where(.dark, .dark *));

@theme inline {
  --color-*: initial;
  --color-black: #000000;
  --color-white: #ffffff;

  --color-brand-25: var(--brand-25);
  --color-brand-50: var(--brand-50);
  --color-brand-100: var(--brand-100);
  --color-brand-200: var(--brand-200);
  --color-brand-300: var(--brand-300);
  --color-brand-400: var(--brand-400);
  --color-brand-500: var(--brand-500);
  --color-brand-600: var(--brand-600);
  --color-brand-700: var(--brand-700);
  --color-brand-800: var(--brand-800);
  --color-brand-900: var(--brand-900);
  --color-brand-950: var(--brand-950);

  --color-error-25: var(--error-25);
  --color-error-50: var(--error-50);
  --color-error-100: var(--error-100);
  --color-error-200: var(--error-200);
  --color-error-300: var(--error-300);
  --color-error-400: var(--error-400);
  --color-error-500: var(--error-500);
  --color-error-600: var(--error-600);
  --color-error-700: var(--error-700);
  --color-error-800: var(--error-800);
  --color-error-900: var(--error-900);
  --color-error-950: var(--error-950);

  --color-warning-25: var(--warning-25);
  --color-warning-50: var(--warning-50);
  --color-warning-100: var(--warning-100);
  --color-warning-200: var(--warning-200);
  --color-warning-300: var(--warning-300);
  --color-warning-400: var(--warning-400);
  --color-warning-500: var(--warning-500);
  --color-warning-600: var(--warning-600);
  --color-warning-700: var(--warning-700);
  --color-warning-800: var(--warning-800);
  --color-warning-900: var(--warning-900);
  --color-warning-950: var(--warning-950);

  --color-success-25: var(--success-25);
  --color-success-50: var(--success-50);
  --color-success-100: var(--success-100);
  --color-success-200: var(--success-200);
  --color-success-300: var(--success-300);
  --color-success-400: var(--success-400);
  --color-success-500: var(--success-500);
  --color-success-600: var(--success-600);
  --color-success-700: var(--success-700);
  --color-success-800: var(--success-800);
  --color-success-900: var(--success-900);
  --color-success-950: var(--success-950);

  --color-gray-25: var(--gray-25);
  --color-gray-50: var(--gray-50);
  --color-gray-100: var(--gray-100);
  --color-gray-200: var(--gray-200);
  --color-gray-300: var(--gray-300);
  --color-gray-400: var(--gray-400);
  --color-gray-500: var(--gray-500);
  --color-gray-600: var(--gray-600);
  --color-gray-700: var(--gray-700);
  --color-gray-800: var(--gray-800);
  --color-gray-900: var(--gray-900);
  --color-gray-950: var(--gray-950);

  --color-moss-25: var(--moss-25);
  --color-moss-50: var(--moss-50);
  --color-moss-100: var(--moss-100);
  --color-moss-200: var(--moss-200);
  --color-moss-300: var(--moss-300);
  --color-moss-400: var(--moss-400);
  --color-moss-500: var(--moss-500);
  --color-moss-600: var(--moss-600);
  --color-moss-700: var(--moss-700);
  --color-moss-800: var(--moss-800);
  --color-moss-900: var(--moss-900);
  --color-moss-950: var(--moss-950);

  --color-teal-25: var(--teal-25);
  --color-teal-50: var(--teal-50);
  --color-teal-100: var(--teal-100);
  --color-teal-200: var(--teal-200);
  --color-teal-300: var(--teal-300);
  --color-teal-400: var(--teal-400);
  --color-teal-500: var(--teal-500);
  --color-teal-600: var(--teal-600);
  --color-teal-700: var(--teal-700);
  --color-teal-800: var(--teal-800);
  --color-teal-900: var(--teal-900);
  --color-teal-950: var(--teal-950);

  --color-light-blue-25: var(--light-blue-25);
  --color-light-blue-50: var(--light-blue-50);
  --color-light-blue-100: var(--light-blue-100);
  --color-light-blue-200: var(--light-blue-200);
  --color-light-blue-300: var(--light-blue-300);
  --color-light-blue-400: var(--light-blue-400);
  --color-light-blue-500: var(--light-blue-500);
  --color-light-blue-600: var(--light-blue-600);
  --color-light-blue-700: var(--light-blue-700);
  --color-light-blue-800: var(--light-blue-800);
  --color-light-blue-900: var(--light-blue-900);
  --color-light-blue-950: var(--light-blue-950);

  --color-purple-25: var(--purple-25);
  --color-purple-50: var(--purple-50);
  --color-purple-100: var(--purple-100);
  --color-purple-200: var(--purple-200);
  --color-purple-300: var(--purple-300);
  --color-purple-400: var(--purple-400);
  --color-purple-500: var(--purple-500);
  --color-purple-600: var(--purple-600);
  --color-purple-700: var(--purple-700);
  --color-purple-800: var(--purple-800);
  --color-purple-900: var(--purple-900);
  --color-purple-950: var(--purple-950);

  --color-accent-primary-25: var(--accent-primary-25);
  --color-accent-primary-50: var(--accent-primary-50);
  --color-accent-primary-100: var(--accent-primary-100);
  --color-accent-primary-200: var(--accent-primary-200);
  --color-accent-primary-300: var(--accent-primary-300);
  --color-accent-primary-400: var(--accent-primary-400);
  --color-accent-primary-500: var(--accent-primary-500);
  --color-accent-primary-600: var(--accent-primary-600);
  --color-accent-primary-700: var(--accent-primary-700);
  --color-accent-primary-800: var(--accent-primary-800);
  --color-accent-primary-900: var(--accent-primary-900);
  --color-accent-primary-950: var(--accent-primary-950);

  --color-fg-primary: var(--fg-primary);
  --color-fg-secondary: var(--fg-secondary);
  --color-fg-secondary-hover: var(--fg-secondary-hover);
  --color-fg-tertiary: var(--fg-tertiary);
  --color-fg-tertiary-hover: var(--fg-tertiary-hover);
  --color-fg-quaternary: var(--fg-quaternary);
  --color-fg-quaternary-hover: var(--fg-quaternary-hover);
  --color-fg-quinary: var(--fg-quinary);
  --color-fg-quinary-hover: var(--fg-quinary-hover);
  --color-fg-senary: var(--fg-senary);
  --color-fg-white: var(--fg-white);
  --color-fg-disabled: var(--fg-disabled);
  --color-fg-disabled-subtle: var(--fg-disabled-subtle);
  --color-fg-brand-primary: var(--fg-brand-primary);
  --color-fg-brand-primary-alt: var(--fg-brand-primary-alt);
  --color-fg-brand-secondary: var(--fg-brand-secondary);
  --color-fg-error-primary: var(--fg-error-primary);
  --color-fg-error-secondary: var(--fg-error-secondary);
  --color-fg-warning-primary: var(--fg-warning-primary);
  --color-fg-warning-secondary: var(--fg-warning-secondary);
  --color-fg-success-primary: var(--fg-success-primary);
  --color-fg-success-secondary: var(--fg-success-secondary);

  --text-color-primary: var(--text-primary);
  --text-color-primary-on-brand: var(--text-primary-on-brand);
  --text-color-secondary: var(--text-secondary);
  --text-color-secondary-hover: var(--text-secondary-hover);
  --text-color-secondary-on-brand: var(--text-secondary-on-brand);
  --text-color-tertiary: var(--text-tertiary);
  --text-color-teriary-on-brand: var(--text-teriary-on-brand);
  --text-color-quaternary: var(--text-quaternary);
  --text-color-quaternary-on-brand: var(--text-quaternary-on-brand);
  --text-color-white: var(--text-white);
  --text-color-disabled: var(--text-disabled);
  --text-color-placeholder: var(--text-placeholder);
  --text-color-placeholder-subtle: var(--text-placeholder-subtle);
  --text-color-brand: var(--text-brand-primary);
  --text-color-brand-secondary: var(--text-brand-secondary);
  --text-color-brand-tertiary: var(--text-brand-tertiary);
  --text-color-brand-tertiary-alt: var(--text-brand-tertiary-alt);
  --text-color-brand-quaternary: var(--text-brand-quaternary);
  --text-color-error-primary: var(--text-error-primary);
  --text-color-warning-primary: var(--text-warning-primary);
  --text-color-success-primary: var(--text-success-primary);

  --background-color-primary: var(--bg-primary);
  --background-color-primary-alt: var(--bg-primary-alt);
  --background-color-primary-hover: var(--bg-primary-hover);
  --background-color-primary-solid: var(--bg-primary-solid);
  --background-color-secondary: var(--bg-secondary);
  --background-color-secondary-alt: var(--bg-secondary-alt);
  --background-color-secondary-hover: var(--bg-secondary-hover);
  --background-color-secondary-subtle: var(--bg-secondary-subtle);
  --background-color-secondary-solid: var(--bg-secondary-solid);
  --background-color-tertiary: var(--bg-tertiary);
  --background-color-quaternary: var(--bg-quaternary);
  --background-color-active: var(--bg-active);
  --background-color-disabled: var(--bg-disabled);
  --background-color-disabled-subtle: var(--bg-disabled-subtle);
  --background-color-overlay: var(--bg-overlay);
  --background-color-brand-primary: var(--bg-brand-primary);
  --background-color-brand-primary-alt: var(--bg-brand-primary-alt);
  --background-color-brand-secondary: var(--bg-brand-secondary);
  --background-color-brand-solid: var(--bg-brand-solid);
  --background-color-brand-solid-hover: var(--bg-brand-solid-hover);
  --background-color-brand-section: var(--bg-brand-section);
  --background-color-brand-section-subtle: var(--bg-brand-section-subtle);
  --background-color-error-primary: var(--bg-error-primary);
  --background-color-error-secondary: var(--bg-error-secondary);
  --background-color-error-solid: var(--bg-error-solid);
  --background-color-warning-primary: var(--bg-warning-primary);
  --background-color-warning-secondary: var(--bg-warning-secondary);
  --background-color-warning-solid: var(--bg-warning-solid);
  --background-color-success-primary: var(--bg-success-primary);
  --background-color-success-secondary: var(--bg-success-secondary);
  --background-color-success-solid: var(--bg-success-solid);

  --border-color-primary: var(--border-primary);
  --border-color-secondary: var(--border-secondary);
  --border-color-tertiary: var(--border-tertiary);
  --border-color-disabled: var(--border-disabled);
  --border-color-disabled-subtle: var(--border-disabled-subtle);
  --border-color-brand: var(--border-brand);
  --border-color-brand-alt: var(--border-brand-alt);
  --border-color-error: var(--border-error);
  --border-color-error-subtle: var(--border-error-subtle);
}

@theme {
  --spacing-none: 0rem;     /* 0px */
  --spacing-xxs: 0.125rem;  /* 2px */
  --spacing-xs: 0.25rem;    /* 4px */
  --spacing-sm: 0.375rem;   /* 6px */
  --spacing-md: 0.5rem;     /* 8px */
  --spacing-lg: 0.75rem;    /* 12px */
  --spacing-xl: 1rem;       /* 16px */
  --spacing-2xl: 1.25rem;   /* 20px */
  --spacing-3xl: 1.5rem;    /* 24px */
  --spacing-4xl: 2rem;      /* 32px */
  --spacing-5xl: 2.5rem;    /* 40px */
  --spacing-6xl: 3rem;      /* 48px */
  --spacing-7xl: 4rem;      /* 64px */
  --spacing-8xl: 5rem;      /* 80px */
  --spacing-9xl: 6rem;      /* 96px */
  --spacing-10xl: 8rem;     /* 128px */
  --spacing-11xl: 10rem;    /* 160px */

  /* Since we don't want the width & height to be overridden by `--spacing-*`, we redefine it here */
  --width-xs: var(--container-xs);
  --width-sm: var(--container-sm);
  --width-md: var(--container-md);
  --width-lg: var(--container-lg);
  --width-xl: var(--container-xl);
  --width-2xl: var(--container-2xl);
  --width-3xl: var(--container-3xl);
  --width-4xl: var(--container-4xl);
  --width-5xl: var(--container-5xl);
  --width-6xl: var(--container-6xl);
  --width-7xl: var(--container-7xl);

  --max-width-xs: var(--container-xs);
  --max-width-sm: var(--container-sm);
  --max-width-md: var(--container-md);
  --max-width-lg: var(--container-lg);
  --max-width-xl: var(--container-xl);
  --max-width-2xl: var(--container-2xl);
  --max-width-3xl: var(--container-3xl);
  --max-width-4xl: var(--container-4xl);
  --max-width-5xl: var(--container-5xl);
  --max-width-6xl: var(--container-6xl);
  --max-width-7xl: var(--container-7xl);

  --height-xs: var(--container-xs);
  --height-sm: var(--container-sm);
  --height-md: var(--container-md);
  --height-lg: var(--container-lg);
  --height-xl: var(--container-xl);
  --height-2xl: var(--container-2xl);
  --height-3xl: var(--container-3xl);
  --height-4xl: var(--container-4xl);
  --height-5xl: var(--container-5xl);
  --height-6xl: var(--container-6xl);
  --height-7xl: var(--container-7xl);

  --max-height-xs: var(--container-xs);
  --max-height-sm: var(--container-sm);
  --max-height-md: var(--container-md);
  --max-height-lg: var(--container-lg);
  --max-height-xl: var(--container-xl);
  --max-height-2xl: var(--container-2xl);
  --max-height-3xl: var(--container-3xl);
  --max-height-4xl: var(--container-4xl);
  --max-height-5xl: var(--container-5xl);
  --max-height-6xl: var(--container-6xl);
  --max-height-7xl: var(--container-7xl);
  
  --radius-*: initial;
  --radius-none: 0rem;      /* 0px */
  --radius-xxs: 0.125rem;   /* 2px */
  --radius-xs: 0.25rem;     /* 4px */
  --radius-sm: 0.375rem;    /* 6px */
  --radius-md: 0.5rem;      /* 8px */
  --radius-lg: 0.625rem;    /* 10px */
  --radius-xl: 0.75rem;     /* 12px */
  --radius-2xl: 1rem;       /* 16px */
  --radius-4xl: 1.5rem;     /* 24px */
  --radius-full: 62.5rem;   /* 9999px */

  --leading-*: initial;
  --leading-xs: 1.125rem;          /* 18px */
  --leading-sm: 1.25rem;           /* 20px */
  --leading-md: 1.5rem;            /* 24px */
  --leading-lg: 1.75rem;           /* 28px */
  --leading-xl: 2rem;              /* 32px */  
  --leading-display-xs: 2rem;      /* 32px */
  --leading-display-sm: 2.375rem;  /* 38px */
  --leading-4xl: 2.75rem;          /* 44px */

  --text-*: initial;
  --text-xs: 0.75rem;               /* 12px */
  --text-xs--line-height: 
    var(--line-height-xs);
  --text-sm: 0.875rem;              /* 14px */
  --text-sm--line-height: 
    var(--line-height-sm);
  --text-md: 1rem;                  /* 16px */
  --text-md--line-height: 
    var(--line-height-md);
  --text-lg: 1.125rem;              /* 18px */
  --text-lg--line-height: 
    var(--line-height-lg);
  --text-xl: 1.25rem;               /* 20px */
  --text-xl--line-height: 
    var(--line-height-xl);
  --text-display-xs: 1.5rem;        /* 24px */
  --text-display-xs--line-height: 
    var(--line-height-display-xs);
  --text-display-sm: 1.875rem;      /* 30px */
  --text-display-sm--line-height: 
    var(--line-height-display-sm);
  --text-4xl: 2.25rem;              /* 36px */
  --text-4xl--line-height: 
    var(--line-height-4xl);

    --margin-*: initial;
  --margin-none: var(--spacing-none);
  --margin-xxs: var(--spacing-xxs);
  --margin-xs: var(--spacing-xs);
  --margin-sm: var(--spacing-sm);
  --margin-md: var(--spacing-md);
  --margin-lg: var(--spacing-lg);
  --margin-xl: var(--spacing-xl);
  --margin-2xl: var(--spacing-2xl);
  --margin-3xl: var(--spacing-3xl);
  --margin-4xl: var(--spacing-4xl);
  --margin-5xl: var(--spacing-5xl);
  --margin-6xl: var(--spacing-6xl);
  --margin-7xl: var(--spacing-7xl);
  --margin-8xl: var(--spacing-8xl);
  --margin-9xl: var(--spacing-9xl);
  --margin-10xl: var(--spacing-10xl);
  --margin-11xl: var(--spacing-11xl);

  --padding-*: initial;
  --padding-none: var(--spacing-none);
  --padding-xxs: var(--spacing-xxs);
  --padding-xs: var(--spacing-xs);
  --padding-sm: var(--spacing-sm);
  --padding-md: var(--spacing-md);
  --padding-lg: var(--spacing-lg);
  --padding-xl: var(--spacing-xl);
  --padding-2xl: var(--spacing-2xl);
  --padding-3xl: var(--spacing-3xl);
  --padding-4xl: var(--spacing-4xl);
  --padding-5xl: var(--spacing-5xl);
  --padding-6xl: var(--spacing-6xl);
  --padding-7xl: var(--spacing-7xl);
  --padding-8xl: var(--spacing-8xl);
  --padding-9xl: var(--spacing-9xl);
  --padding-10xl: var(--spacing-10xl);
  --padding-11xl: var(--spacing-11xl);

  --gap-*: initial;
  --gap-none: var(--spacing-none);
  --gap-xxs: var(--spacing-xxs);
  --gap-xs: var(--spacing-xs);
  --gap-sm: var(--spacing-sm);
  --gap-md: var(--spacing-md);
  --gap-lg: var(--spacing-lg);
  --gap-xl: var(--spacing-xl);
  --gap-2xl: var(--spacing-2xl);
  --gap-3xl: var(--spacing-3xl);
  --gap-4xl: var(--spacing-4xl);
  --gap-5xl: var(--spacing-5xl);
  --gap-6xl: var(--spacing-6xl);
  --gap-7xl: var(--spacing-7xl);
  --gap-8xl: var(--spacing-8xl);
  --gap-9xl: var(--spacing-9xl);
  --gap-10xl: var(--spacing-10xl);
  --gap-11xl: var(--spacing-11xl);

  --font-weight-*: initial;
  --font-weight-thin: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  --Colors-Effects-Shadows-shadow-skeumorphic-inner-border: #0A0D1218;
  --Colors-Effects-Shadows-shadow-skeumorphic-inner: #0A0D1210;
  --Colors-Effects-Shadows-shadow-xs: #0A0D1210;

  --shadow-*: initial;
  --shadow-xs: 0px 1px 2px 0px #00000010;
  --shadow-md: 0px 2px 4px -2px rgba(10,13,18,0.06), 0px 4px 6px -1px rgba(10,13,18,0.10);
  --shadow-lg: 0 12px 16px -4px rgba(16, 24, 40, 0.08), 0px 12px 16px -4px rgba(16, 24, 40, 0.03);
  --shadow-card: 0px 2px 12px -1px #0A0D120A, 0px 20px 32px -4px #0A0D1214;
  --shadow-keyboard-key: 0px 0px 0px 0.909px var(--Colors-Effects-Shadows-shadow-skeumorphic-inner-border, rgba(10, 13, 18, 0.18)) inset, 0px -1.818px 0px 0px var(--Colors-Effects-Shadows-shadow-skeumorphic-inner, rgba(10, 13, 18, 0.05)) inset, 0px 0.909px 1.818px 0px var(--Colors-Effects-Shadows-shadow-xs, rgba(10, 13, 18, 0.05));

  --font-sans: 'Montserrat Variable', sans-serif;

  --ease-sidebar-collapse: cubic-bezier(0.25, 0.8, 0.25, 1);
  --ease-bounce: linear(0.00, -0.0145, 0.0307, 0.111, 0.227, 0.358, 0.492, 0.622, 0.742, 0.846, 0.934, 1.00, 1.06, 1.10, 1.12, 1.13, 1.13, 1.12, 1.11, 1.10, 1.08, 1.06, 1.05, 1.03, 1.02, 1.01, 0.999, 0.992, 0.987, 0.985, 0.983, 0.983, 0.984, 0.986, 0.988, 0.990, 0.992, 0.994, 0.996, 0.998, 0.999, 1.00, 1.00);
  
  --width-dialog-sm: 26rem;
  --width-dialog-md: 40rem;
  --width-dialog-lg: 50rem;
  --width-drawer-lg: 40rem;
  --width-drawer-md: 30rem;
}