/**
 * This file should not be edited directly. If you want to customize the theme, 
 * edit the `default.scss` (or a custuom theme) file instead.
 */

.light {
  --black: #000000;
  --white: #ffffff;

  --fg-primary: var(--gray-900);
  --fg-secondary: var(--gray-700);
  --fg-secondary-hover: var(--gray-800);
  --fg-tertiary: var(--gray-600);
  --fg-tertiary-hover: var(--gray-700);
  --fg-quaternary: var(--gray-500);
  --fg-quaternary-hover: var(--gray-600);
  --fg-quinary: var(--gray-400);
  --fg-quinary-hover: var(--gray-500);
  --fg-senary: var(--gray-300);
  --fg-white: var(--white);
  --fg-disabled: var(--gray-400);
  --fg-disabled-subtle: var(--gray-300);
  --fg-brand-primary: var(--brand-600);
  --fg-brand-primary-alt: var(--brand-600);
  --fg-brand-secondary: var(--brand-500);
  --fg-error-primary: var(--error-600);
  --fg-error-secondary: var(--error-500);
  --fg-warning-primary: var(--warning-600);
  --fg-warning-secondary: var(--warning-500);
  --fg-success-primary: var(--success-600);
  --fg-success-secondary: var(--success-500);

  --text-primary: var(--gray-900);
  --text-primary-on-brand: var(--white);
  --text-secondary: var(--gray-700);
  --text-secondary-hover: var(--gray-800);
  --text-secondary-on-brand: var(--brand-200);
  --text-tertiary: var(--gray-600);
  --text-teriary-on-brand: var(--brand-200);
  --text-quaternary: var(--gray-500);
  --text-quaternary-on-brand: var(--brand-300);
  --text-white: var(--white);
  --text-disabled: var(--gray-500);
  --text-placeholder: var(--gray-500);
  --text-placeholder-subtle: var(--gray-300);
  --text-brand-primary: var(--brand-900);
  --text-brand-secondary: var(--brand-700);
  --text-brand-tertiary: var(--brand-600);
  --text-brand-tertiary-alt: var(--brand-600);
  --text-brand-quaternary: var(--brand-500);
  --text-error-primary: var(--error-600);
  --text-warning-primary: var(--warning-600);
  --text-success-primary: var(--success-600);

  --bg-primary: var(--white);
  --bg-primary-alt: var(--white);
  --bg-primary-hover: var(--gray-25);
  --bg-primary-solid: var(--gray-950);
  --bg-secondary: var(--gray-25);
  --bg-secondary-alt: var(--gray-50);
  --bg-secondary-hover: var(--gray-100);
  --bg-secondary-subtle: var(--gray-25);
  --bg-secondary-solid: var(--gray-600);
  --bg-tertiary: var(--gray-50);
  --bg-quaternary: var(--gray-100);
  --bg-active: var(--gray-50);
  --bg-disabled: var(--gray-25);
  --bg-disabled-subtle: var(--gray-25);
  --bg-overlay: var(--gray-950);
  --bg-brand-primary: var(--brand-50);
  --bg-brand-primary-alt: var(--brand-25);
  --bg-brand-secondary: var(--brand-100);
  --bg-brand-solid: var(--brand-600);
  --bg-brand-solid-hover: var(--brand-700);
  --bg-brand-section: var(--brand-800);
  --bg-brand-section-subtle: var(--brand-700);
  --bg-error-primary: var(--error-50);
  --bg-error-secondary: var(--error-100);
  --bg-error-solid: var(--error-600);
  --bg-warning-primary: var(--warning-50);
  --bg-warning-secondary: var(--warning-100);
  --bg-warning-solid: var(--warning-600);
  --bg-success-primary: var(--success-50);
  --bg-success-secondary: var(--success-100);
  --bg-success-solid: var(--success-600);

  --border-primary: var(--gray-100);
  --border-secondary: var(--gray-50);
  --border-tertiary: var(--gray-25);
  --border-disabled: var(--gray-50);
  --border-disabled-subtle: var(--gray-25);
  --border-brand: var(--brand-500);
  --border-brand-alt: var(--brand-300);
  --border-error: var(--error-500);
  --border-error-subtle: var(--error-300);
}

.dark {
  --black: #000000;
  --white: #ffffff;

  --fg-primary: var(--white);
  --fg-secondary: var(--gray-300);
  --fg-secondary-hover: var(--gray-200);
  --fg-tertiary: var(--gray-400);
  --fg-tertiary-hover: var(--gray-300);
  --fg-quaternary: var(--gray-400);
  --fg-quaternary-hover: var(--gray-300);
  --fg-quinary: var(--gray-500);
  --fg-quinary-hover: var(--gray-400);
  --fg-senary: var(--gray-600);
  --fg-white: var(--white);
  --fg-disabled: var(--gray-500);
  --fg-disabled-subtle: var(--gray-600);
  --fg-brand-primary: var(--brand-500);
  --fg-brand-primary-alt: var(--gray-300);
  --fg-brand-secondary: var(--brand-500);
  --fg-error-primary: var(--error-500);
  --fg-error-secondary: var(--error-400);
  --fg-warning-primary: var(--warning-500);
  --fg-warning-secondary: var(--warning-400);
  --fg-success-primary: var(--success-500);
  --fg-success-secondary: var(--success-400);

  --text-primary: var(--gray-50);
  --text-primary-on-brand: var(--gray-50);
  --text-secondary: var(--gray-300);
  --text-secondary-hover: var(--gray-200);
  --text-secondary-on-brand: var(--gray-300);
  --text-tertiary: var(--gray-400);
  --text-tertiary-hover: var(--gray-300);
  --text-tertiary-on-brand: var(--gray-400);
  --text-quaternary: var(--gray-400);
  --text-quaternary-on-brand: var(--gray-400);
  --text-white: var(--gray-50);
  --text-disabled: var(--gray-500);
  --text-placeholder: var(--gray-500);
  --text-placeholder-subtle: var(--gray-700);
  --text-brand-primary: var(--gray-50);
  --text-brand-secondary: var(--gray-300);
  --text-brand-tertiary: var(--gray-400);
  --text-brand-tertiary-alt: var(--gray-50);
  --text-error-primary: var(--error-400);
  --text-warning-primary: var(--warning-400);
  --text-success-primary: var(--success-400);

  --bg-primary: var(--gray-950);
  --bg-primary-alt: var(--bg-secondary);
  --bg-primary-hover: var(--gray-800);
  --bg-primary-solid: var(--bg-secondary);
  --bg-secondary: var(--gray-900);
  --bg-secondary-alt: var(--bg-primary);
  --bg-secondary-hover: var(--gray-800);
  --bg-secondary-subtle: var(--gray-900);
  --bg-secondary-solid: var(--gray-600);
  --bg-tertiary: var(--gray-800);
  --bg-quaternary: var(--gray-700);
  --bg-active: var(--gray-800);
  --bg-disabled: var(--gray-800);
  --bg-disabled-subtle: var(--gray-900);
  --bg-overlay: var(--gray-800);
  --bg-brand-primary: var(--brand-500);
  --bg-brand-primary-alt: var(--bg-secondary);
  --bg-brand-secondary: var(--brand-600);
  --bg-brand-solid: var(--brand-600);
  --bg-brand-solid-hover: var(--brand-500);
  --bg-brand-section: var(--bg-secondary);
  --bg-brand-section-subtle: var(--bg-primary);
  --bg-error-primary: var(--error-500);
  --bg-error-secondary: var(--error-600);
  --bg-error-solid: var(--error-600);
  --bg-warning-primary: var(--warning-500);
  --bg-warning-secondary: var(--warning-600);
  --bg-warning-solid: var(--warning-600);
  --bg-success-primary: var(--success-500);
  --bg-success-secondary: var(--success-600);
  --bg-success-solid: var(--success-600);

  --border-primary: var(--gray-700);
  --border-secondary: var(--gray-800);
  --border-tertiary: var(--gray-800);
  --border-disabled: var(--gray-700);
  --border-disabled-subtle: var(--gray-800);
  --border-brand: var(--brand-400);
  --border-brand-alt: var(--gray-700);
  --border-error: var(--error-400);
  --border-error-subtle: var(--error-400); 
}

/* .system {
  --fg-primary: var(--gray-900);
  --fg-secondary: var(--gray-700);
  --fg-secondary-hover: var(--gray-800);
  --fg-tertiary: var(--gray-600);
  --fg-tertiary-hover: var(--gray-700);
  --fg-quaternary: var(--gray-500);
  --fg-quaternary-hover: var(--gray-600);
  --fg-quinary: var(--gray-400);
  --fg-quinary-hover: var(--gray-500);
  --fg-senary: var(--gray-300);
  --fg-white: var(--white);
  --fg-disabled: var(--gray-400);
  --fg-disabled-subtle: var(--gray-300);
  --fg-brand-primary: var(--brand-600);
  --fg-brand-primary-alt: var(--brand-600);
  --fg-brand-secondary: var(--brand-500);
  --fg-error-primary: var(--error-600);
  --fg-error-secondary: var(--error-500);
  --fg-warning-primary: var(--warning-600);
  --fg-warning-secondary: var(--warning-500);
  --fg-success-primary: var(--success-600);
  --fg-success-secondary: var(--success-500);

  --text-primary: var(--gray-900);
  --text-primary-on-brand: var(--white);
  --text-secondary: var(--gray-700);
  --text-secondary-hover: var(--gray-800);
  --text-secondary-on-brand: var(--brand-200);
  --text-tertiary: var(--gray-600);
  --text-teriary-on-brand: var(--brand-200);
  --text-quaternary: var(--gray-500);
  --text-quaternary-on-brand: var(--brand-300);
  --text-white: var(--white);
  --text-disabled: var(--gray-500);
  --text-placeholder: var(--gray-500);
  --text-placeholder-subtle: var(--gray-300);
  --text-brand-primary: var(--brand-900);
  --text-brand-secondary: var(--brand-700);
  --text-brand-tertiary: var(--brand-600);
  --text-brand-tertiary-alt: var(--brand-600);
  --text-error-primary: var(--error-600);
  --text-warning-primary: var(--warning-600);
  --text-success-primary: var(--success-600);

  --bg-primary: var(--white);
  --bg-primary-alt: var(--white);
  --bg-primary-hover: var(--gray-50);
  --bg-primary-solid: var(--gray-950);
  --bg-secondary: var(--gray-50);
  --bg-secondary-alt: var(--gray-50);
  --bg-secondary-hover: var(--gray-100);
  --bg-secondary-subtle: var(--gray-25);
  --bg-secondary-solid: var(--gray-600);
  --bg-tertiary: var(--gray-100);
  --bg-quaternary: var(--gray-200);
  --bg-active: var(--gray-50);
  --bg-disabled: var(--gray-100);
  --bg-disabled-subtle: var(--gray-50);
  --bg-overlay: var(--gray-950);
  --bg-brand-primary: var(--brand-50);
  --bg-brand-primary-alt: var(--brand-25);
  --bg-brand-secondary: var(--brand-100);
  --bg-brand-solid: var(--brand-600);
  --bg-brand-solid-hover: var(--brand-700);
  --bg-brand-section: var(--brand-800);
  --bg-brand-section-subtle: var(--brand-700);
  --bg-error-primary: var(--error-50);
  --bg-error-secondary: var(--error-100);
  --bg-error-solid: var(--error-600);
  --bg-warning-primary: var(--warning-50);
  --bg-warning-secondary: var(--warning-100);
  --bg-warning-solid: var(--warning-600);
  --bg-success-primary: var(--success-50);
  --bg-success-secondary: var(--success-100);
  --bg-success-solid: var(--success-600);

  --border-primary: var(--gray-300);
  --border-secondary: var(--gray-200);
  --border-tertiary: var(--gray-100);
  --border-disabled: var(--gray-300);
  --border-disabled-subtle: var(--gray-200);
  --border-brand: var(--brand-500);
  --border-brand-alt: var(--brand-600);
  --border-error: var(--error-500);
  --border-error-subtle: var(--error-300);

  @media (prefers-color-scheme: dark) {
    --fg-primary: var(--white);
    --fg-secondary: var(--gray-300);
    --fg-secondary-hover: var(--gray-200);
    --fg-tertiary: var(--gray-400);
    --fg-tertiary-hover: var(--gray-300);
    --fg-quaternary: var(--gray-400);
    --fg-quaternary-hover: var(--gray-300);
    --fg-quinary: var(--gray-500);
    --fg-quinary-hover: var(--gray-400);
    --fg-senary: var(--gray-600);
    --fg-white: var(--white);
    --fg-disabled: var(--gray-500);
    --fg-disabled-subtle: var(--gray-600);
    --fg-brand-primary: var(--brand-500);
    --fg-brand-primary-alt: var(--gray-300);
    --fg-brand-secondary: var(--brand-500);
    --fg-error-primary: var(--error-500);
    --fg-error-secondary: var(--error-400);
    --fg-warning-primary: var(--warning-500);
    --fg-warning-secondary: var(--warning-400);
    --fg-success-primary: var(--success-500);
    --fg-success-secondary: var(--success-400);

    --text-primary: var(--gray-50);
    --text-primary-on-brand: var(--gray-50);
    --text-secondary: var(--gray-300);
    --text-secondary-hover: var(--gray-200);
    --text-secondary-on-brand: var(--gray-300);
    --text-tertiary: var(--gray-400);
    --text-tertiary-hover: var(--gray-300);
    --text-tertiary-on-brand: var(--gray-400);
    --text-quaternary: var(--gray-400);
    --text-quaternary-on-brand: var(--gray-400);
    --text-white: var(--gray-50);
    --text-disabled: var(--gray-500);
    --text-placeholder: var(--gray-500);
    --text-placeholder-subtle: var(--gray-700);
    --text-brand-primary: var(--gray-50);
    --text-brand-secondary: var(--gray-300);
    --text-brand-tertiary: var(--gray-400);
    --text-brand-tertiary-alt: var(--gray-50);
    --text-error-primary: var(--error-400);
    --text-warning-primary: var(--warning-400);
    --text-success-primary: var(--success-400);

    --bg-primary: var(--gray-950);
    --bg-primary-alt: var(--bg-secondary);
    --bg-primary-hover: var(--gray-800);
    --bg-primary-solid: var(--bg-secondary);
    --bg-secondary: var(--gray-900);
    --bg-secondary-alt: var(--bg-primary);
    --bg-secondary-hover: var(--gray-800);
    --bg-secondary-subtle: var(--gray-900);
    --bg-secondary-solid: var(--gray-600);
    --bg-tertiary: var(--gray-800);
    --bg-quaternary: var(--gray-700);
    --bg-active: var(--gray-800);
    --bg-disabled: var(--gray-800);
    --bg-disabled-subtle: var(--gray-900);
    --bg-overlay: var(--gray-800);
    --bg-brand-primary: var(--brand-500);
    --bg-brand-primary-alt: var(--bg-secondary);
    --bg-brand-secondary: var(--brand-600);
    --bg-brand-solid: var(--brand-600);
    --bg-brand-solid-hover: var(--brand-500);
    --bg-brand-section: var(--bg-secondary);
    --bg-brand-section-subtle: var(--bg-primary);
    --bg-error-primary: var(--error-500);
    --bg-error-secondary: var(--error-600);
    --bg-error-solid: var(--error-600);
    --bg-warning-primary: var(--warning-500);
    --bg-warning-secondary: var(--warning-600);
    --bg-warning-solid: var(--warning-600);
    --bg-success-primary: var(--success-500);
    --bg-success-secondary: var(--success-600);
    --bg-success-solid: var(--success-600);

    --border-primary: var(--gray-700);
    --border-secondary: var(--gray-800);
    --border-tertiary: var(--gray-800);
    --border-disabled: var(--gray-700);
    --border-disabled-subtle: var(--gray-800);
    --border-brand: var(--brand-400);
    --border-brand-alt: var(--gray-700);
    --border-error: var(--error-400);
    --border-error-subtle: var(--error-400);
  }
} */