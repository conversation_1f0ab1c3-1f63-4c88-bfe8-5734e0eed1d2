{"auth.legal_links.company_info": "Company info", "auth.legal_links.disclaimer": "Disclaimer", "auth.legal_links.privacy_&_cookies": "Privacy & cookies", "component.contact_autocomplete.placeholder": "Rechercher un contact", "component.customer_autocomplete.placeholder": "Rechercher un client", "component.editor.action.bold": "Gras", "component.editor.action.h2": "H2", "component.editor.action.h3": "H3", "component.editor.action.italic": "Italique", "component.editor.action.link": "<PERSON><PERSON>", "component.editor.action.underline": "<PERSON><PERSON><PERSON>", "component.editor.add_link": "Ajouter un lien", "component.editor.link_begin_with_https": "Votre lien doit commencer par https", "component.editor.link_label": "<PERSON><PERSON>", "component.editor.link_placeholder": "https://", "component.ewc_code.ewc_code_not_found.description": "Le code EWC que vous avez saisi n'existe pas. Veuillez vérifier le code et réessayer.", "component.ewc_code.ewc_code_not_found.title": "Code EWC introuvable.", "component.ewc_code.invalid_format.description": "Veuillez saisir le code EWC au format correct : six chiffres (par exemple, 010101).", "component.ewc_code.invalid_format.title": "Format de code EWC invalide.", "component.file_upload.allowed_file_types": "Types de fichiers autorisés", "component.file_upload.click_to_upload": "Cliquer pour télécharger", "component.file_upload.status.failed": "Échec du téléchargement", "component.file_upload.status.finished": "<PERSON><PERSON><PERSON><PERSON>", "component.file_upload.status.pending": "Préparation", "component.filters.clear_all": "Effacer tous les filtres", "component.filters.date_dialog.description": "Sélectionnez une date pour le filtre '{label}'.", "component.filters.date_range_dialog.description": "Sélectionnez une plage de dates pour le filtre '{label}'.", "component.filters.listbox.no_results": "Aucune option correspondante", "component.filters.listbox.search_placeholder": "Filtrer...", "component.filters.selected": "sélectionné", "component.form.option.no_suggestions": "<PERSON><PERSON><PERSON> trouvée", "component.keyboard_shortcut.then": "puis", "component.number_field.decrement": "Décrémenter", "component.number_field.increment": "Incrémenter", "component.password_input.hide_password": "Masquer le mot de passe", "component.password_input.show_password": "Afficher le mot de passe", "component.pick_up_address_autocomplete.placeholder": "Rechercher une adresse de ramassage", "component.refresh_prompt.new_version.action": "Actualiser", "component.refresh_prompt.new_version.description": "Nouvelle version disponible ! Rechargez pour bénéficier des dernières fonctionnalités.", "component.search_input.clear": "<PERSON><PERSON><PERSON><PERSON>", "component.search_input.placeholder": "Rechercher...", "component.select.empty_text": "Aucun résultat trouvé pour '{searchTerm}'.", "component.select.search_input_placeholder": "<PERSON><PERSON><PERSON>", "component.select.search_placeholder": "Rechercher...", "component.sidebar.close_sidebar": "<PERSON><PERSON><PERSON> la barre latérale", "component.sidebar.footer.environment": "Environnement", "component.sidebar.footer.sign_out": "Se déconnecter", "component.sidebar.footer.stop_impersonation": "<PERSON><PERSON><PERSON><PERSON> l'Usurpation", "component.sidebar.footer.user_profile": "Profil utilisateur", "component.sidebar.footer.version": "Version", "component.sidebar.group.administration": "Administration", "component.sidebar.group.system_administration": "Administration système", "component.sidebar.group.waste_management": "Gestion des déchets", "component.sidebar.open_sidebar": "<PERSON>u<PERSON><PERSON>r la barre latérale", "component.sidebar.settings": "Paramètres", "component.table.clear_filter": "Effacer les filtres", "component.table.dynamic_view.add_view": "Ajouter une vue", "component.table.dynamic_view.cannot_delete_global_default_view": "Vous ne pouvez pas supprimer une vue globale par défaut", "component.table.dynamic_view.cannot_delete_global_view_if_not_admin": "Les vues globales ne peuvent être supprimées que par les administrateurs système", "component.table.dynamic_view.cannot_delete_last_view": "Vous ne pouvez pas supprimer la dernière vue de tableau", "component.table.dynamic_view.change_order": "Changer l'ordre", "component.table.dynamic_view.change_sorting": "Changer le tri", "component.table.dynamic_view.default_global_view": "Vue globale par défaut", "component.table.dynamic_view.default_view": "Votre vue par défaut", "component.table.dynamic_view.delete_view": "Supprimer la vue", "component.table.dynamic_view.delete_view_description": "Voulez-vous vraiment supprimer la vue '{name}' ? Cette action est irréversible.", "component.table.dynamic_view.edit_view": "Modifier la vue", "component.table.dynamic_view.manage_views": "<PERSON><PERSON><PERSON> les vues", "component.table.dynamic_view.reset_all_changes": "Réinitialiser tous les changements", "component.table.dynamic_view.save": "Enregistrer la vue", "component.table.dynamic_view.save_as_default_view_for_me": "Enregistrer comme vue par défaut pour moi", "component.table.dynamic_view.save_as_default_view_globally": "En tant qu'administrateur, définissez-la comme vue par défaut globalement", "component.table.dynamic_view.save_as_global_view": "Enregistrer comme vue globale pour que tout le monde puisse l'utiliser", "component.table.dynamic_view.save_as_new": "Enregistrer sous un nouveau nom", "component.table.dynamic_view.save_as_new_disabled": "Apportez des modifications à la vue du tableau avant d'enregistrer comme nouvelle vue", "component.table.dynamic_view.save_filter_view": "Enregistrer la vue filtrée", "component.table.dynamic_view.settings": "Paramètres du tableau", "component.table.dynamic_view.shared": "Partagé", "component.table.dynamic_view.toggle_columns": "Afficher/Masquer les colonnes", "component.table.dynamic_view.update": "Mettre à jour", "component.table.dynamic_view.update_filter_view": "Mettre à jour la vue filtrée", "component.table.dynamic_view.view_deleted": "Vue supprimée", "component.table.dynamic_view.view_deleted_description": "Vous avez supprimé la vue '{name}'", "component.table.dynamic_view.view_name": "Nom de la vue filtrée", "component.table.dynamic_view.view_name_placeholder": "Exemple de vue", "component.table.dynamic_view.view_saved": "Vue enregistrée avec succès", "component.table.dynamic_view.view_saved_description": "Vous avez enregistré la vue '{name}'", "component.table.dynamic_view.view_updated_description": "Vous avez mis à jour la vue '{name}'", "component.table.dynamic_view.views": "<PERSON><PERSON>", "component.table.next_page": "Suivant", "component.table.no_active_filters": "Aucun filtre actif", "component.table.no_data.description": "Aucune donnée à afficher actuellement.", "component.table.no_data.title": "Pas de don<PERSON>", "component.table.no_results.description": "Aucun résultat ne correspond à vos critères de recherche.", "component.table.no_results.title": "Aucun résultat", "component.table.page_count": "{startIndex} - {endIndex} sur {totalItems}", "component.table.previous_page": "Précédent", "component.table.results_may_be_hidden": "Certains résultats peuvent être masqués", "component.unsaved_changes_dialog.cancel": "Continuer l'édition", "component.unsaved_changes_dialog.confirm": "<PERSON><PERSON>, j'en suis sûr", "component.unsaved_changes_dialog.description": "Toutes les modifications non enregistrées seront perdues. Êtes-vous sûr de vouloir continuer ?", "component.unsaved_changes_dialog.title": "Modifications non enregistrées", "component.waste_producer_autocomplete.placeholder": "Rechercher un producteur de déchets", "components.form.file_upload.max_file_size_exceeded.description": "Le fichier \"{name}\" dépasse la taille maximale de {max} Mo.", "components.form.file_upload.max_file_size_exceeded.title": "<PERSON><PERSON> du fi<PERSON>er dé<PERSON>", "components.waste_inquiry.composition.components.add": "Ajouter un composant", "components.waste_inquiry.composition.components.add_packaging": "Ajouter un emballage", "components.waste_inquiry.composition.components.component.max_weight.label": "Poids max.", "components.waste_inquiry.composition.components.component.min_weight.label": "Poids min.", "components.waste_inquiry.composition.components.component_name.label": "Composant", "components.waste_inquiry.composition.components.component_name.placeholder": "Nom du composant", "components.waste_inquiry.composition.components.has_inner_packaging.label": "Emballage intérieur", "components.waste_inquiry.composition.components.loading_method.label": "Méthode de chargement", "components.waste_inquiry.composition.components.packaging.label": "Emballage", "components.waste_inquiry.composition.components.packaging.placeholder": "Sélectionner un type d'emballage", "components.waste_inquiry.composition.components.packaging_group.label": "Groupe d'emballage", "components.waste_inquiry.composition.components.packaging_size.label": "<PERSON><PERSON>", "components.waste_inquiry.composition.components.packaging_type.label": "Type d'emballage", "components.waste_inquiry.composition.components.remarks.placeholder": "<PERSON><PERSON><PERSON>", "components.waste_inquiry.composition.components.stored_in.label": "<PERSON><PERSON>", "components.waste_inquiry.composition.components.transport_volume.label": "Volume de transport", "components.waste_inquiry.composition.components.un_number.label": "Numéro UN", "components.waste_inquiry.composition.components.un_number.placeholder": "Numéro UN", "components.waste_inquiry.composition.components.weight_per_piece.label": "Poids/Pièce", "enum.announcement_type.informational": "Informationnel", "enum.announcement_type.urgent": "<PERSON><PERSON>", "enum.collection_requirement_option.tractor": "Unité de tracteur seulement", "enum.collection_requirement_option.tractor_trailer": "Unité de tracteur et remorque seulement", "enum.collection_requirement_option.tractor_trailer_tank": "Tracteur, remorque et conteneur-citerne", "enum.container_loading_type.chain.description": "Une chaîne pour le chargement des déchets.", "enum.container_loading_type.chain.label": "<PERSON><PERSON><PERSON>", "enum.container_loading_type.hook.description": "Un crochet pour le chargement des déchets.", "enum.container_loading_type.hook.label": "C<PERSON>chet", "enum.container_transport_type.change_compaction_container": "Changer le conteneur de compactage", "enum.container_transport_type.direct_loading": "Chargement direct", "enum.container_transport_type.emptying_glass_bulb": "Vidange de l'ampoule en verre", "enum.container_transport_type.emptying_wheelie_bin": "Vidange du conteneur à roulettes", "enum.container_transport_type.exchange": "Échange", "enum.container_transport_type.final_collection": "Collecte finale", "enum.container_transport_type.first_placement": "Premier placement", "enum.container_transport_type.internal_movement": "Mouvement interne", "enum.container_transport_type.return": "Retour", "enum.draft_invoice_status.approved_by_customer": "Approuvé par le client", "enum.draft_invoice_status.auto_approved": "Auto approuvé", "enum.draft_invoice_status.internal_approved": "Approuvé en interne", "enum.draft_invoice_status.rejected_by_customer": "Rejeté par le client", "enum.draft_invoice_status.rejected_by_indaver": "Rejeté par Indaver", "enum.draft_invoice_status.to_be_approved_by_customer": "Être approuvé par le client", "enum.draft_invoice_status.to_be_approved_by_indaver": "Être approuvé par Indaver", "enum.draft_invoice_status_short.approved": "Approu<PERSON><PERSON>", "enum.draft_invoice_status_short.rejected": "<PERSON><PERSON><PERSON>", "enum.draft_invoice_status_short.to_be_approved": "Être approuvé", "enum.dynamic_table_column_name.account_document_number": "Document de compte N °", "enum.dynamic_table_column_name.account_manager": "Gestionnaire de compte", "enum.dynamic_table_column_name.account_manager_name": "Nom du gestionnaire de compte", "enum.dynamic_table_column_name.amount": "<PERSON><PERSON>", "enum.dynamic_table_column_name.asn": "Asn n °", "enum.dynamic_table_column_name.auto_approved_on": "Auto approuvé sur", "enum.dynamic_table_column_name.company_name": "Nom de l'entreprise", "enum.dynamic_table_column_name.confirmed_collection_date": "Date de collecte confirmée", "enum.dynamic_table_column_name.confirmed_transport_date": "Date de transport confirmée", "enum.dynamic_table_column_name.container_number": "N° de conteneur", "enum.dynamic_table_column_name.container_transport_type": "Type de transport des conteneurs", "enum.dynamic_table_column_name.container_type": "Type de conteneur", "enum.dynamic_table_column_name.container_volume_size": "Volume / taille des conteneurs", "enum.dynamic_table_column_name.contract_id": "ID du contrat", "enum.dynamic_table_column_name.contract_item": "Article du contrat", "enum.dynamic_table_column_name.contract_number": "N° de contrat", "enum.dynamic_table_column_name.cost_center": "Centre de coûts", "enum.dynamic_table_column_name.currency": "<PERSON><PERSON>", "enum.dynamic_table_column_name.customer_id": "ID client", "enum.dynamic_table_column_name.customer_name": "Nom du client", "enum.dynamic_table_column_name.customer_reference": "Référence client", "enum.dynamic_table_column_name.customer_approval_by": "Customer approval by", "enum.dynamic_table_column_name.customer_approval_date": "Customer approval date", "enum.dynamic_table_column_name.danger_label1": "Danger lbl 1", "enum.dynamic_table_column_name.danger_label2": "Danger lbl 2", "enum.dynamic_table_column_name.danger_label3": "Danger lbl 3", "enum.dynamic_table_column_name.date": "Date", "enum.dynamic_table_column_name.date_of_request": "Date de la demande", "enum.dynamic_table_column_name.delivery_info": "<PERSON><PERSON><PERSON> l<PERSON>", "enum.dynamic_table_column_name.disposal_certificate_number": "N° de certificat d'élimination", "enum.dynamic_table_column_name.due_on": "Dû sur", "enum.dynamic_table_column_name.end_treatment_center_id": "ID du centre du traitement final", "enum.dynamic_table_column_name.end_treatment_center_name": "Nom du centre de traitement final", "enum.dynamic_table_column_name.esn_number": "ESN N °", "enum.dynamic_table_column_name.estimated_weight_or_volume_unit": "Unité de poids / volume estimé", "enum.dynamic_table_column_name.estimated_weight_or_volume_value": "Valeur de poids / volume estimé", "enum.dynamic_table_column_name.ewc_code": "Code EWC", "enum.dynamic_table_column_name.first_reminder_mail_status": "1er rappel d'état du courrier", "enum.dynamic_table_column_name.first_reminder_on": "1er rappel sur", "enum.dynamic_table_column_name.hazard_inducers": "Inducteurs de danger", "enum.dynamic_table_column_name.inquiry_number": "Numéro de demande", "enum.dynamic_table_column_name.installation_name": "Nom d'installation", "enum.dynamic_table_column_name.invoice": "Facture", "enum.dynamic_table_column_name.invoice_number": "Facture n °", "enum.dynamic_table_column_name.is_container_covered": "Récipient couvert", "enum.dynamic_table_column_name.is_hazardous": "Dangereux", "enum.dynamic_table_column_name.is_return_packaging": "Return emballage", "enum.dynamic_table_column_name.is_transport_by_indaver": "Transport par Indaver", "enum.dynamic_table_column_name.issued_on": "Délivré sur", "enum.dynamic_table_column_name.material_analysis": "Analyse des matériaux", "enum.dynamic_table_column_name.material_number": "Mat<PERSON><PERSON><PERSON> n °", "enum.dynamic_table_column_name.material_type": "Type de matériau", "enum.dynamic_table_column_name.name_installation": "Nom de l'installation", "enum.dynamic_table_column_name.name_of_applicant": "Nom du demandeur", "enum.dynamic_table_column_name.net_amount": "<PERSON><PERSON> net", "enum.dynamic_table_column_name.number": "N°", "enum.dynamic_table_column_name.order_number": "N° de commande", "enum.dynamic_table_column_name.packaged": "Emballé", "enum.dynamic_table_column_name.packaging_indicator": "Indicateur d'emballage", "enum.dynamic_table_column_name.packaging_remark": "Remarque d'emballage", "enum.dynamic_table_column_name.packaging_type": "Type d'emballage", "enum.dynamic_table_column_name.packing_group": "Paquet. \ngroupe", "enum.dynamic_table_column_name.payer_id": "Identifiant", "enum.dynamic_table_column_name.payer_name": "Nom du payeur", "enum.dynamic_table_column_name.pick_up_address_id": "ID d'adresse de ramassage", "enum.dynamic_table_column_name.pick_up_address_name": "Nom de l'adresse de ramassage", "enum.dynamic_table_column_name.pickup_address": "<PERSON><PERSON><PERSON>", "enum.dynamic_table_column_name.po_number": "Po n °", "enum.dynamic_table_column_name.process_code": "Code de processus", "enum.dynamic_table_column_name.quantity_containers": "N ° des conteneurs", "enum.dynamic_table_column_name.quantity_labels": "Étiquettes de quantité", "enum.dynamic_table_column_name.quantity_packages": "Packages de quantité", "enum.dynamic_table_column_name.quantity_pallets": "Palettes de quantité", "enum.dynamic_table_column_name.reconciliation_number": "Réconciliation n °", "enum.dynamic_table_column_name.remarks": "<PERSON><PERSON><PERSON>", "enum.dynamic_table_column_name.request_number": "Numéro de demande", "enum.dynamic_table_column_name.requested_by": "Demandé par", "enum.dynamic_table_column_name.requested_end_date": "Date de fin demandée", "enum.dynamic_table_column_name.requested_start_date": "Date de début demandée", "enum.dynamic_table_column_name.sales_order": "Commande client", "enum.dynamic_table_column_name.sales_organisation_id": "ID de l'organisation commerciale", "enum.dynamic_table_column_name.sales_organisation_name": "Nom de l'organisation commerciale", "enum.dynamic_table_column_name.second_reminder_mail_status": "2ème rappel de l'état du courrier", "enum.dynamic_table_column_name.second_reminder_on": "2ème rappel sur", "enum.dynamic_table_column_name.serial_number": "N ° N °", "enum.dynamic_table_column_name.status": "Statut", "enum.dynamic_table_column_name.tanker_type": "Type de pétrolier", "enum.dynamic_table_column_name.tc_number": "TC N °", "enum.dynamic_table_column_name.tfs_number": "N° TFS", "enum.dynamic_table_column_name.third_reminder_mail_status": "Statut de rappel du 3e", "enum.dynamic_table_column_name.total_quantity_pallets": "Palettes de quantité totale", "enum.dynamic_table_column_name.transport_mode": "Mode de transport", "enum.dynamic_table_column_name.treatment_center_name": "Nom du centre de traitement", "enum.dynamic_table_column_name.type": "Taper", "enum.dynamic_table_column_name.un_number": "Numéro UN", "enum.dynamic_table_column_name.vat_amount": "Montant de la cuve", "enum.dynamic_table_column_name.waste_item": "Article de déchet", "enum.dynamic_table_column_name.waste_material": "<PERSON><PERSON><PERSON><PERSON>", "enum.dynamic_table_column_name.waste_producer": "Producteur de déchets", "enum.dynamic_table_column_name.waste_producer_id": "ID du producteur de déchets", "enum.flashpoint.high": "> 60°", "enum.flashpoint.low": "< 23°", "enum.flashpoint.medium": "23° - 60°", "enum.flashpoint.unknown": "Inconnu / Non applicable", "enum.invoice_status.cleared": "Dégagé", "enum.invoice_status.draft": "Brouillon", "enum.invoice_status.outstanding": "Remarquable", "enum.invoice_status.overdue": "En retard", "enum.invoice_type.cancellation": "Annulation", "enum.invoice_type.invoice": "Facture", "enum.invoice_type.unknown": "Inconnu", "enum.language.de": "Allemand", "enum.language.en": "<PERSON><PERSON><PERSON>", "enum.language.es": "Espagnol", "enum.language.fr": "Français", "enum.language.nl": "Néerlandais", "enum.legislation_and_properties.more_info_table.header.adr": "ADR", "enum.legislation_and_properties.more_info_table.header.clp": "CLP", "enum.legislation_and_properties.more_info_table.header.examples": "Exemples", "enum.legislation_and_properties.more_info_table.header.waste_classified_as": "Déchet classé comme", "enum.legislation_and_properties.more_info_table.header.waste_classified_as_or_substances": "Déchet classé comme OU contenant des substances classées comme", "enum.legislation_and_properties.more_info_table.header.waste_containing": "<PERSON><PERSON>chet contenant", "enum.legislation_and_properties.more_info_table.header.waste_subject_to": "<PERSON><PERSON><PERSON> soumis à", "enum.legislation_and_properties.more_info_table.header.waste_substances_classified_as": "Déchet contenant des substances classées comme", "enum.legislation_and_properties.more_info_table.header.waste_substances_subject_to": "Déchet contenant des substances soumises à", "enum.mail_status.not_sent": "Non envoyé", "enum.mail_status.sent": "<PERSON><PERSON><PERSON>", "enum.packaging.bulk.description": "Déchets non emballés transportés en grandes quantités, généralement dans des citernes, des silos ou d'autres conteneurs en vrac.", "enum.packaging.bulk.label": "Vrac", "enum.packaging.packaged.description": "Déchets contenus dans des unités scellées plus petites telles que des cylindres, des fûts ou des bouteilles.", "enum.packaging.packaged.label": "Emballé", "enum.packaging_request_type.rental": "De location", "enum.packaging_request_type.sales": "<PERSON><PERSON><PERSON>", "enum.pickup_request_status.cancelled": "<PERSON><PERSON><PERSON>", "enum.pickup_request_status.completed": "<PERSON><PERSON><PERSON><PERSON>", "enum.pickup_request_status.confirmed": "<PERSON><PERSON><PERSON><PERSON>", "enum.pickup_request_status.draft": "Brouillon", "enum.pickup_request_status.indascan_draft": "Projet indascan", "enum.pickup_request_status.pending": "En attente", "enum.pickup_transport_mode.bulk_iso_tank.description": "Indiquer au maximum 1 position contractuelle.", "enum.pickup_transport_mode.bulk_iso_tank.label": "Conteneur-citerne en vrac ou citerne ISO", "enum.pickup_transport_mode.bulk_skips_container.description": "Indiquer au maximum 3 positions contractuelles.", "enum.pickup_transport_mode.bulk_skips_container.label": "Déchets en vrac dans des bennes ou conteneurs", "enum.pickup_transport_mode.bulk_vacuum_tankers_road_tankers.description": "Indiquer au maximum 1 position contractuelle.", "enum.pickup_transport_mode.bulk_vacuum_tankers_road_tankers.label": "Déchets en vrac dans des camions-citernes à vide ou des camions-citernes routiers (avec pompe)", "enum.pickup_transport_mode.packaged_curtain_sider_truck.description": "Indiquer un nombre quelconque de positions contractuelles", "enum.pickup_transport_mode.packaged_curtain_sider_truck.label": "Déchets emballés dans une semi-remorque à rideaux ou un camion", "enum.pickup_transport_mode.packaging_request_order.label": "Commande de demande d'emballage", "enum.publish_status.archived": "Archivé", "enum.publish_status.published": "<PERSON><PERSON><PERSON>", "enum.publish_status.scheduled": "Programmé", "enum.regulated_transport.no": "Non", "enum.regulated_transport.unknown": "Inconnu", "enum.regulated_transport.yes": "O<PERSON>", "enum.role.admin": "Administrateur", "enum.role.user": "Utilisa<PERSON>ur", "enum.sharepoint_document_status.active": "Actif", "enum.sharepoint_document_status.archived": "Archivé", "enum.sharepoint_document_view_name.bsc": "Carte de score équilibrée", "enum.sharepoint_document_view_name.contract": "Contractes", "enum.sharepoint_document_view_name.manual": "Manuel <PERSON>", "enum.sharepoint_document_view_name.mastertable": "<PERSON><PERSON><PERSON>", "enum.sharepoint_document_view_name.meetings": "Minutes", "enum.sharepoint_document_view_name.quotation": "Citations", "enum.sharepoint_document_view_name.tfs": "Fichiers TFS", "enum.sharepoint_document_view_name.transport": "Documents de transport", "enum.stable_temperature.ambient": "Ambiante", "enum.stable_temperature.average": "Fournir une moyenne", "enum.stable_temperature.min_max": "Fournir un minimum et un maximum", "enum.state_of_matter.gaseous": "Gazeux", "enum.state_of_matter.liquid": "Liquide", "enum.state_of_matter.liquid_with_solids": "Liquide avec solides", "enum.state_of_matter.no_data_available": "<PERSON><PERSON><PERSON> donnée disponible", "enum.state_of_matter.powder": "<PERSON><PERSON><PERSON><PERSON>", "enum.state_of_matter.sludgy": "<PERSON><PERSON><PERSON>", "enum.state_of_matter.solid": "Solide", "enum.state_of_matter.viscous": "<PERSON><PERSON><PERSON><PERSON>", "enum.svhc_extra_option.<_1_mg_kg": "< 1 mg/kg de substances persistantes", "enum.svhc_extra_option.>_1_mg_kg": "> 1 mg/kg de substances persistantes", "enum.svhc_extra_option.other": "<PERSON><PERSON>", "enum.tanker_type.road_tanker": "Camion-citerne routier", "enum.tanker_type.road_tanker_pump": "Camion-citerne routier avec pompe", "enum.tanker_type.vacuum_tanker": "Camion-citerne à vide", "enum.waste_discharge_frequency.once_off_campaign.description": "Une collecte unique dans le cadre d'une campagne spécifique.", "enum.waste_discharge_frequency.once_off_campaign.label": "Campagne unique", "enum.waste_discharge_frequency.once_off_stream.description": "Un événement de décharge unique et non récurrent.", "enum.waste_discharge_frequency.once_off_stream.label": "Flux unique", "enum.waste_discharge_frequency.regular_campaign.description": "Collectes répétées ayant lieu dans le cadre de campagnes planifiées.", "enum.waste_discharge_frequency.regular_campaign.label": "<PERSON><PERSON><PERSON>", "enum.waste_discharge_frequency.regular_stream.description": "Dé<PERSON>rge continue et constante à intervalles réguliers.", "enum.waste_discharge_frequency.regular_stream.label": "Flux régulier", "enum.waste_inquiry_status.completed": "<PERSON><PERSON><PERSON><PERSON>", "enum.waste_inquiry_status.conformity_confirmed": "Conformité confirmée", "enum.waste_inquiry_status.draft": "Brouillon", "enum.waste_inquiry_status.in_progress": "En cours", "enum.waste_inquiry_status.new": "Nouveau", "enum.waste_inquiry_status.not_relevant": "Non pertinent", "enum.waste_inquiry_status.offer_approved": "Offre approuvée", "enum.waste_inquiry_status.offer_sent": "Offre envoyée", "enum.waste_inquiry_status.rejected": "<PERSON><PERSON><PERSON>", "enum.waste_inquiry_status.solution_defined": "Solution définie", "enum.waste_legislation_option.animal_byproduct": "Sous-produits animaux", "enum.waste_legislation_option.animal_byproduct.more_info_table.classified_as": "Sous-produits animaux (SPA)", "enum.waste_legislation_option.animal_byproduct.more_info_table.examples": "<div><strong>Sous-produits animaux et produits dérivés exclus de la consommation humaine :</strong><ul><li>Aliments pour animaux (ex. à base de farine de poisson et de protéines animales transformées, ...)</li><li>Engrais organiques et amendements du sol (ex. fumier, guano, OF/SI transformés à base de protéines animales transformées, ...)</li><li>Produits techniques (ex. aliments pour animaux de compagnie, cuirs et peaux pour le cuir, laine, sang pour la production d'outils de diagnostic, ...)</li></ul></div>", "enum.waste_legislation_option.animal_byproduct.more_info_table.subject_to": "<div><ul><li>Règlement (UE) 1069/2009</li></ul></div>", "enum.waste_legislation_option.controlled_drugs": "Drogues contrôlées", "enum.waste_legislation_option.controlled_drugs.more_info_table.classified_as": "Drogues contrôlées", "enum.waste_legislation_option.controlled_drugs.more_info_table.examples": "<div><strong>Stupéfiants</strong><ul><li>Morphine</li><li>Fentanyl</li><li>Codéine</li><li>Cocaïne</li><br></ul><strong>Psychotropes</strong><ul><li>Benzodiazépines</li><li>Alprozalam</li><li>Xanax</li></ul></div>", "enum.waste_legislation_option.controlled_drugs.more_info_table.subject_to": "<div><ul><li>Convention unique sur les stupéfiants de 1961</li><li>Convention sur les substances psychotropes de 1971</li><li>Ou soumis à la législation locale sur les drogues contrôlées.</li></ul></div>", "enum.waste_legislation_option.cwc": "CWC (Convention sur les armes chimiques)", "enum.waste_legislation_option.cwc.more_info_table.classified_as": "Armes chimiques", "enum.waste_legislation_option.cwc.more_info_table.examples": "<div><strong>Tableau 1</strong><ul><li>Gaz moutarde au soufre</li><li><PERSON><PERSON></li><li><PERSON><PERSON></li><li>VX</li><br></ul><strong>Tableau 2</strong><ul><li>Dichlorure de méthylphosphonyle</li><li>Trichlorure d'arsenic</li><br></ul><strong>Tableau 3</strong><ul><li>Oxychlorure de phosphore</li><li>Trichlorure de phosphore</li><li>Cyanure d'hydrogène</li><li>Triéthanolamine</li><li>Chlorure de thionyle</li><br></ul></div>", "enum.waste_legislation_option.cwc.more_info_table.subject_to": "Convention sur les armes chimiques (CWC) et mentionnées dans : Tableau 1, ou Tableau 2, ou Tableau 3", "enum.waste_legislation_option.drug_precursor": "Précurseur de drogue", "enum.waste_legislation_option.drug_precursor.more_info_table.classified_as": "Précurseurs de drogues", "enum.waste_legislation_option.drug_precursor.more_info_table.examples": "<div><strong>Tableau 1</strong><ul><li>Ergotamine</li><li>Éphédrine</li><li>Safrole</li></ul><br><strong>Tableau 2</strong><ul><li>Permanganate de potassium</li><li>Acide phénylacétique</li><li>Anhydride acétique</li><li>Acide anthranilique</li><li>Pipéridine</li></ul></div>", "enum.waste_legislation_option.drug_precursor.more_info_table.subject_to": "<div><ul><li>Règlement (UE) 273/2004</li></ul><p>Avec un accent sur les substances des tableaux 1 et 2 (substances actives).</p></div>", "enum.waste_legislation_option.hg_containing": "Déchets contenant du Hg", "enum.waste_legislation_option.hg_containing.more_info_table.containing": "Mercure", "enum.waste_legislation_option.hg_containing.more_info_table.examples": "<div><ul><li>Déchets contenant des concentrations de Hg pertinentes</li><li>Composés de mercure purs</li><li>Articles avec ajout de mercure</li><li>Mercure métallique</li></ul></div>", "enum.waste_legislation_option.hg_containing.more_info_table.substances_subject_to": "<div><ul><li>Règlement (UE) 1102/2008</li><li>Convention de Minamata</li></ul><p>Déchets contenant du mercure à des concentrations nécessitant une attention particulière lors du traitement pour éviter l'émission de mercure dans l'environnement.</p></div>", "enum.waste_legislation_option.infectious_waste": "Déchets infectieux", "enum.waste_legislation_option.infectious_waste.more_info_table.adr": "Classe 6.2, <PERSON><PERSON><PERSON>x", "enum.waste_legislation_option.infectious_waste.more_info_table.classified_as": "Infectieux", "enum.waste_legislation_option.infectious_waste.more_info_table.clp": "Pas de classification spécifique", "enum.waste_legislation_option.infectious_waste.more_info_table.examples": "<div><strong>Déchets potentiellement contaminés par des virus, bactéries, champignons pouvant provoquer une maladie humaine/animale, ex.</strong><ul><li>Botulisme (clostridium botulinum)</li><li>Anthrax (bacillus anthracis)</li><li>Corona COVID-19</li><li>Legionella</li><li><PERSON><PERSON><PERSON></li><li>Ebola</li></ul></div>", "enum.waste_legislation_option.none": "Aucun", "enum.waste_legislation_option.ozon_depleting_substance": "Substances appauvrissant la couche d'ozone et gaz à effet de serre fluorés", "enum.waste_legislation_option.ozon_depleting_substance.more_info_table.clp": "H420 (substances ODS)", "enum.waste_legislation_option.ozon_depleting_substance.more_info_table.examples": "<div><strong>Règlement (UE) 1005/2009</strong><ul><li>Chlorofluorocarbones (CFC)</li><li>Hydrochlorofluorocarbones (HCFC)</li><li>Tétrachlorure de carbone</li><li>Bromure de méthyle</li><li>Halons</li></ul><br><strong>Règlement (UE) 517/2014</strong><ul><li>Hydrofluorocarbones, perfluorocarbones</li><li>Hexafluorure de soufre</li><li>Trifluorure d'azote</li></ul></div>", "enum.waste_legislation_option.ozon_depleting_substance.more_info_table.substances_classified_as": "Substances appauvrissant la couche d'ozone et/ou gaz à effet de serre fluorés", "enum.waste_legislation_option.ozon_depleting_substance.more_info_table.substances_subject_to": "<div><ul><li>Règlement (UE) 1005/2009</li><li>Règlement (UE) 517/2014</li></ul></div>", "enum.waste_legislation_option.radioactive": "Déchets radioactifs et NORM (matériaux radioactifs naturels)", "enum.waste_legislation_option.radioactive.more_info_table.adr": "Pas toujours applicable", "enum.waste_legislation_option.radioactive.more_info_table.classified_or_substances": "Radioactif et/ou Matériau radioactif naturel (NORM)", "enum.waste_legislation_option.radioactive.more_info_table.examples": "<div><strong>Radioactif</strong><ul><li>Nitrate de thorium</li><li>Acétate d'uranyle</li><li>Césium 131</li><li>Tritium</li></ul><br><strong>NORM</strong><ul><li>Oxyde de zirconium</li><li>Plomb-121</li></ul></div>", "enum.waste_legislation_option.radioactive.more_info_table.subject_to": "Directive 2013/59/Euratom (ou définie par la législation locale) : déchets dont l'activité de rayonnement est supérieure aux valeurs d'exonération mentionnées à l'annexe VII.", "enum.waste_legislation_option.svhc": "SVHC (Substances extrêmement préoccupantes)", "enum.waste_legislation_option.svhc.more_info_table.examples": "<div><strong>Substances mentionnées dans REACH (règlement (UE) 1907/2006) :</strong><ul><li>Autorisation (annexe XIV)</li><li>Restriction (annexe XVII)</li><li>Liste des substances candidates</li><li>Hexabromocyclododécane</li><li>Composés de Cd/As/Pb</li><li>Dichromate de sodium</li><li>1,2 dichloroéthane</li><li>Phtalate de dibutyle</li><li>Amiante</li><li>Benzène</li><li>GEN-X</li><li>APFO</li><li>HAP</li></ul></div>", "enum.waste_legislation_option.svhc.more_info_table.substances_classified_as": "SVHC (Substances extrêmement préoccupantes)", "enum.waste_legislation_option.svhc.more_info_table.substances_subject_to": "<div><strong>Critères de l'article 57 de REACH* :</strong><ul><li>Cancérogène cat. 1A ou 1B</li><li>Mutagène sur les cellules germinales cat. 1A ou 1B</li><li>Toxicité pour la reproduction cat. 1A ou 1B</li><li>PBT (persistantes, bioaccumulables et toxiques)</li><li>VPVB (très persistantes et très bioaccumulables)</li><li>Substances pour lesquelles il existe des preuves scientifiques d'effets graves probables sur la santé humaine ou l'environnement qui suscitent un niveau de préoccupation équivalent à celui des autres substances énumérées aux points (a) à (e). (ex. propriétés perturbatrices endocriniennes)</li></ul></div>", "enum.waste_legislation_option.svhc.more_info_table.substances_subject_to_note": "* Pays-Bas : règlement ZZS", "enum.waste_loading_method.gravitational.description": "Les déchets sont chargés par gravité.", "enum.waste_loading_method.gravitational.label": "Gravitationnel", "enum.waste_loading_method.pump_from_customer.description": "Les déchets sont pompés du client.", "enum.waste_loading_method.pump_from_customer.label": "Pompage depuis le client", "enum.waste_loading_method.pump_from_haulier.description": "Les déchets sont pompés du transporteur.", "enum.waste_loading_method.pump_from_haulier.label": "Pompage depuis le transporteur", "enum.waste_loading_type.before_waste_collection.description": "Les déchets sont chargés avant la collecte des déchets.", "enum.waste_loading_type.before_waste_collection.label": "Avant la collecte des déchets", "enum.waste_loading_type.on_waste_collection.description": "Les déchets sont chargés lors de la collecte des déchets.", "enum.waste_loading_type.on_waste_collection.label": "Lors de la collecte des déchets", "enum.waste_measurement_unit.kg": "KG", "enum.waste_measurement_unit.m3": "M3", "enum.waste_measurement_unit.pc": "PC", "enum.waste_measurement_unit.to": "TO", "enum.waste_measurement_unit.yd3": "Yd3", "enum.waste_packaging.asf": "Asf", "enum.waste_packaging.asp": "<PERSON><PERSON>", "enum.waste_packaging.big_bag": "Big bag", "enum.waste_packaging.cardboard_box": "Boîte en carton", "enum.waste_packaging.ibc": "Ibc", "enum.waste_packaging.metal_drum": "Fût métallique", "enum.waste_packaging.other": "<PERSON><PERSON>", "enum.waste_packaging.oversized_drum": "Fût surdimensionné", "enum.waste_packaging.plastic_drum": "Fût en plastique", "enum.waste_packaging_size.not_applicable": "N/A", "enum.waste_packaging_size.one": "I", "enum.waste_packaging_size.three": "III", "enum.waste_packaging_size.two": "II", "enum.waste_ph.high": "4 - 10", "enum.waste_ph.low": "< 2", "enum.waste_ph.medium": "2 - 4", "enum.waste_ph.very_high": "> 10", "enum.waste_property_option.explosive": "Déchets explosifs (ADR classe 1) et explosifs désensibilisés", "enum.waste_property_option.explosive.more_info_table.adr_2": "Classe 3 et 4.1 avec code de classification D et DT", "enum.waste_property_option.explosive.more_info_table.classified_as_1": "Explosifs", "enum.waste_property_option.explosive.more_info_table.classified_as_2": "Explosifs désensibilisés", "enum.waste_property_option.explosive.more_info_table.clp_1": "<div>H200<br>H201<br>H202<br>H203<br>H204<br>H205</div>", "enum.waste_property_option.explosive.more_info_table.clp_2": "<div>H206<br>H207<br>H208</div>", "enum.waste_property_option.explosive.more_info_table.examples_1": "<div><ul><li>Nitrate d'ammonium (explosif)</li><li>Trinitrotoluène</li><li>Nitrocellulose</li><li>Nitroglycérine</li><li>Acide picrique</li><li>Feux d'artifice</li><li>Munitions</li></ul></div>", "enum.waste_property_option.explosive.more_info_table.examples_2": "<div><ul><li>Nitrocellulose avec au moins 25% d'eau</li><li>Nitroglycérine dans l'alcool</li><li>Acide picrique dans l'eau</li><li>ISDN avec lactose</li></ul></div>", "enum.waste_property_option.gaseous": "<PERSON><PERSON>chet<PERSON> gazeux", "enum.waste_property_option.gaseous.more_info_table.adr": "Classe 2 : inflammable, toxique, comburant et corrosif)", "enum.waste_property_option.gaseous.more_info_table.classified_as": "Gazeux", "enum.waste_property_option.gaseous.more_info_table.clp": "<div>H220<br>H221<br>H270</div>", "enum.waste_property_option.gaseous.more_info_table.examples": "<div><ul><li>Déchets de type GPL</li><li>Chlorure de vinyle</li><li>Phosphine</li><li>Isobutène</li><li>Ammoniac</li><li>Chlore</li><li>Fr<PERSON>ons</li><li><PERSON>thane</li></ul></div>", "enum.waste_property_option.high_acute_toxic": "Déchets classés comme très toxiques aigus (T+)", "enum.waste_property_option.high_acute_toxic.more_info_table.adr": "<div>Classe 6.1<br>GE I et II</div>", "enum.waste_property_option.high_acute_toxic.more_info_table.classified_as": "Très toxique aigu (T+)", "enum.waste_property_option.high_acute_toxic.more_info_table.clp": "<div>H300<br>H310<br>H330</div>", "enum.waste_property_option.high_acute_toxic.more_info_table.examples": "<div><ul><li>Composés de mercure</li><li>Cyanure d'hydrogène</li><li>Fluorure d'hydrogène</li><li>Cyanure de sodium</li><li>Dinitrobenzène</li><li>Alkyles de plomb</li><li><PERSON><PERSON></li></ul></div>", "enum.waste_property_option.none": "Aucun", "enum.waste_property_option.peroxide": "Peroxyde (organique et inorganique) ou déchets auto-réactifs", "enum.waste_property_option.peroxide.more_info_table.adr_1": "Classe 5.2", "enum.waste_property_option.peroxide.more_info_table.adr_2": "Classe 4.1", "enum.waste_property_option.peroxide.more_info_table.adr_3": "Classe 5.1", "enum.waste_property_option.peroxide.more_info_table.classified_as_or_substances_1": "Peroxyde organique", "enum.waste_property_option.peroxide.more_info_table.classified_as_or_substances_2": "Auto-réactif", "enum.waste_property_option.peroxide.more_info_table.classified_as_or_substances_3": "Peroxyde d'hydrogène", "enum.waste_property_option.peroxide.more_info_table.clp_1": "<div>H240<br>H241<br>H242</div>", "enum.waste_property_option.peroxide.more_info_table.clp_2": "<div>H240<br>H241<br>H242</div>", "enum.waste_property_option.peroxide.more_info_table.clp_3": "H271", "enum.waste_property_option.peroxide.more_info_table.examples": "<div><ul><li>AIBN (azobisisobutyronitrile)</li><li>Peroxyde de di-tert-butyle</li><li>Peroxyde d'hydrogène</li><li>Peroxyde de dilauroyle</li><li>Peroxyde de benzoyle</li></ul></div>", "enum.waste_property_option.polymerisation_sensitive": "Déchets sensibles à la polymérisation", "enum.waste_property_option.polymerisation_sensitive.more_info_table.adr_1": "Pas de classification spécifique.<br>Monomères avec stabilisation chimique marqués avec la disposition spéciale 386.", "enum.waste_property_option.polymerisation_sensitive.more_info_table.adr_2": "Pas de classification spécifique", "enum.waste_property_option.polymerisation_sensitive.more_info_table.classified_as_or_substances_1": "Monomères insaturés", "enum.waste_property_option.polymerisation_sensitive.more_info_table.classified_as_or_substances_2": "Oxiranes", "enum.waste_property_option.polymerisation_sensitive.more_info_table.clp_1": "Pas de classification spécifique", "enum.waste_property_option.polymerisation_sensitive.more_info_table.clp_2": "Pas de classification spécifique", "enum.waste_property_option.polymerisation_sensitive.more_info_table.examples": "<div><ul><li>Monomères de méthacrylate</li><li>Acide acrylique</li><li>Acrylonitrile</li><li>Acrylate</li><li>Styrène</li><li>MDI</li><li>TDI</li></ul><br><ul><li>Épichlorohydrine</li></ul><br><ul><li>Oxyde de propylène</li><li>Oxyde d'éthylène</li></ul></div>", "enum.waste_property_option.pyrophoric": "Déchets pyrophoriques ou substances auto-échauffantes ou liquides très inflammables", "enum.waste_property_option.pyrophoric.more_info_table.adr_1": "Classe 4.2", "enum.waste_property_option.pyrophoric.more_info_table.adr_2": "Classe 3, GE I", "enum.waste_property_option.pyrophoric.more_info_table.classified_as_1": "Pyrophorique", "enum.waste_property_option.pyrophoric.more_info_table.classified_as_2": "Liquides très inflammables", "enum.waste_property_option.pyrophoric.more_info_table.clp_1": "<div>H250<br>H251<br>H252</div>", "enum.waste_property_option.pyrophoric.more_info_table.clp_2": "H224", "enum.waste_property_option.pyrophoric.more_info_table.examples_1": "<div><ul><li>Phosphore blanc</li><li>Trichlorosilane</li><li>Alkyles métalliques</li></ul></div>", "enum.waste_property_option.pyrophoric.more_info_table.examples_2": "<div><ul><li><PERSON><PERSON><PERSON><PERSON></li><li><PERSON><PERSON><PERSON><PERSON><PERSON></li><li>Isopentane</li></ul></div>", "enum.waste_property_option.reactive_with_f_gas": "Déchets réactifs avec l'eau/l'air/l'acide/la base avec formation de gaz F (ex. H2, éthane, ...)", "enum.waste_property_option.reactive_with_f_gas.more_info_table.adr": "Classe 4.3", "enum.waste_property_option.reactive_with_f_gas.more_info_table.classified_as_or_substances": "Réactif avec l'eau/l'air/l'acide/la base avec formation de gaz F*", "enum.waste_property_option.reactive_with_f_gas.more_info_table.clp": "<div>H260<br>H261</div>", "enum.waste_property_option.reactive_with_f_gas.more_info_table.examples": "<div><ul><li>Hydrures (borohydrure de sodium/potassium, hydrure de lithium et d'aluminium)</li><li>Métaux alcalins (Na, K,...)</li><li><PERSON><PERSON><PERSON><PERSON> (Mg, Al,...)</li><li>Alkyles métalliques</li></ul></div>", "enum.waste_property_option.reactive_with_f_gas.more_info_table.examples_note": "*Exemples de gaz F : H2, <PERSON><PERSON><PERSON>,...", "enum.waste_property_option.reactive_with_t_gas": "Déchets réactifs avec l'eau/l'air/l'acide/la base avec formation d'un gaz T (ex. HCI/CI2, HCN, H2S, NH3, NOx, PH3, ...)", "enum.waste_property_option.reactive_with_t_gas.more_info_table.adr": "Pas de classification spécifique", "enum.waste_property_option.reactive_with_t_gas.more_info_table.classified_as_or_substances": "Réactif avec l'eau/l'air/l'acide/la base avec formation de gaz T*", "enum.waste_property_option.reactive_with_t_gas.more_info_table.clp": "<div>H014<br>H029<br>H031<br>H032</div>", "enum.waste_property_option.reactive_with_t_gas.more_info_table.examples": "<div><ul><li>Composés phosphorés (phosphure d'aluminium, phosphure de calcium, phosphure de magnésium,...)</li><li>Chlorures d'acide (chlorure de thionyle, oxychlorure de phosphore, trichlorure de phosphore, tétrachlorure de titane,...)</li><li>Hypohalites (hypochlorite de sodium,...) </li><li>Composés de cyanure</li><li>Composés de sulfure</li><li>Sels d'ammonium</li></ul></div>", "enum.waste_property_option.reactive_with_t_gas.more_info_table.examples_note": "*Exemples de gaz T : HCI/CI2, HCN, H2S, NH3, NOX, PH3,...", "enum.waste_property_option.strong_oxidizing": "Fortement oxydant", "enum.waste_property_option.strong_oxidizing.more_info_table.adr": "Classe 5.1", "enum.waste_property_option.strong_oxidizing.more_info_table.classified_as_or_substances": "Fortement oxydant", "enum.waste_property_option.strong_oxidizing.more_info_table.clp": "H270<br>H271<br>H272", "enum.waste_property_option.strong_oxidizing.more_info_table.examples": "<div><ul><li>Permanganate de potassium</li><li>Trioxyde de chrome (VI)</li><li>Nitrate d'ammonium</li><li>Perchlorates</li><li>Chlorates</li></ul></div>", "enum.waste_property_option.thermal_unstable": "Déchets thermiquement instables", "enum.waste_property_option.thermal_unstable.more_info_table.adr": "Pas de classification spécifique", "enum.waste_property_option.thermal_unstable.more_info_table.classified_as_or_substances": "Thermodynamiquement instable", "enum.waste_property_option.thermal_unstable.more_info_table.clp": "Pas de classification spécifique", "enum.waste_property_option.thermal_unstable.more_info_table.examples": "<div><ul><li>Sulfoxydes organiques</li><li>Diazo/diazonium</li><li>Acide acrylique</li><li>Isocyanate</li><li>Hydrazine</li><li>N-oxydes</li><li>Alcynes</li><li>Alcènes</li><li>Nitroco</li><li>N-nitro</li></ul></div>", "enum.waste_stored_in.drums.description": "Fûts pour le stockage des déchets.", "enum.waste_stored_in.drums.label": "Fûts", "enum.waste_stored_in.ibcs.description": "GRV pour le stockage des déchets.", "enum.waste_stored_in.ibcs.label": "GRV", "enum.waste_stored_in.other.description": "Autre stockage des déchets.", "enum.waste_stored_in.other.label": "<PERSON><PERSON>", "enum.waste_stored_in.storage_tank.description": "Réservoir de stockage pour les déchets.", "enum.waste_stored_in.storage_tank.label": "Réservoir de stockage", "enum.waste_stored_in.tank_container.description": "Conteneur-citerne pour le stockage des déchets.", "enum.waste_stored_in.tank_container.label": "Conteneur-citerne", "enum.waste_transport_in.no_preference.description": "Pas de préférence pour le transport des déchets.", "enum.waste_transport_in.no_preference.label": "Pas de préférence", "enum.waste_transport_in.other.description": "Autre transport des déchets.", "enum.waste_transport_in.other.label": "<PERSON><PERSON>", "enum.waste_transport_in.tank_container.description": "Un conteneur pour le transport des déchets.", "enum.waste_transport_in.tank_container.label": "Conteneur-citerne", "enum.waste_transport_in.tank_trailer.description": "Une remorque pour le transport des déchets.", "enum.waste_transport_in.tank_trailer.label": "Remorque-citerne", "enum.waste_transport_type.container.description": "Un conteneur pour le transport des déchets.", "enum.waste_transport_type.container.label": "Conteneur", "enum.waste_transport_type.other.description": "Autre type de transport des déchets.", "enum.waste_transport_type.other.label": "<PERSON><PERSON>", "enum.waste_transport_type.rel_truck.description": "Un camion pour le transport des déchets.", "enum.waste_transport_type.rel_truck.label": "Camion REL/REF", "enum.waste_transport_type.skip.description": "Transport de déchets en benne.", "enum.waste_transport_type.skip.label": "<PERSON><PERSON>", "enum.waste_transport_type.tripper_truck.description": "Un camion pour le transport des déchets.", "enum.waste_transport_type.tripper_truck.label": "<PERSON><PERSON>", "error.bad_request.description": "Une erreur est survenue. Veuillez réessayer.", "error.bad_request.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "error.default_error.description": "Veuillez réessayer plus tard.", "error.default_error.title": "Une erreur est survenue", "error.forbidden.description": "Vous n'avez pas la permission d'accéder à cette ressource.", "error.forbidden.title": "Interdit", "error.internal_server_error.description": "Veuillez réessayer plus tard.", "error.internal_server_error.title": "<PERSON>rreur interne du serveur", "error.invalid_form_input.description": "Veuillez vérifier les champs surlignés et réessayer.", "error.invalid_form_input.title": "Formulaire invalide", "error.resource_not_found.description": "La ressource demandée n'a pas pu être trouvée.", "error.resource_not_found.title": "Ressource introuvable", "error.unauthorized.description": "Vous n'êtes pas autorisé à accéder à cette ressource.", "error.unauthorized.title": "Non autorisé", "error.validation_error.description": "Veuillez vérifier le formulaire pour les erreurs.", "error.validation_error.title": "Erreur de validation", "form.fields.email": "E-mail", "form.fields.password": "Mot de passe", "form.save_changes": "Enregistrer les modifications", "impersonation.warning.continue": "<PERSON><PERSON><PERSON>", "impersonation.warning.description": "Vous avez été inactif pendant 5 minutes en usurpant l'identité de {user}. Voulez-vous continuer ou arrêter l'usurpation?", "impersonation.warning.stop": "<PERSON>rr<PERSON><PERSON> l'usurpation", "impersonation.warning.title": "Toujours en usurpation", "module.admin.overview.title": "Administration du système", "module.auth.legal_links.disclaimer.paragraph_1": "The Indaver Portal and all subordinate systems (a.o. the Indaver Customer Zone, the Indaver Supplier Zone, the Indaver E-reporting...) and documents were developed with the best care possible. However, Indaver cannot be held responsible for the proper description of it, nor for the proper functioning of each of the functionalities.", "module.auth.legal_links.disclaimer.paragraph_2": "The Indaver Portal and the subordinate systems and documentation are property of Indaver and may not be copied or be disclosed to third parties or made available, in any way and any medium without the prior written approval of Indaver.", "module.auth.legal_links.disclaimer.paragraph_3": "Indaver is in this respect not responsible for any loss of data that were entered, nor for any incorrect processing by the software available or for the consequences arising from it.", "module.auth.legal_links.disclaimer.paragraph_4": "Indaver provides no warranties regarding the availability or functionality of the Indaver Portal and the subordinate systems and documents.", "module.auth.legal_links.disclaimer.paragraph_5": "Indaver is not responsible for any damage to the IT systems (hardware or software) of the user whether directly related or not to the use of the Indaver Portal.", "module.auth.legal_links.disclaimer.paragraph_6": "The user commits himself to use the Indaver Portal only in connection with the proposed objectives and refrain from this for any other use, either for himself or for others to use, make available, copy or modify.", "module.auth.legal_links.disclaimer.paragraph_7": "Indaver reserves at all times the right to deny access to the Indaver Portal, either individually or collectively, in particular for breaches of these provisions set as access conditions, or even as a precaution against damage to the systems of Indaver, regardless if these are related to the use of the system by the user.", "module.auth.legal_links.disclaimer.paragraph_8": "By the mere use of the Indaver Portal, the user is considered to have accepted the conditions.", "module.auth.login.customer_zone.label": "Customer Zone", "module.auth.login.description": "Connectez-vous pour accéder à l'espace client.", "module.auth.login.error": "Une erreur est survenue lors de la connexion. Réessayez plus tard.", "module.auth.login.error.description": "Une erreur est survenue lors de la connexion. Veuillez réessayer.", "module.auth.login.error.title": "Une erreur est survenue", "module.auth.login.leading_text": "Votre portail sécurisé pour gérer vos déchets", "module.auth.login.page_title": "Connexion", "module.auth.login.sign_in": "Se connecter", "module.auth.login.title": "Bienvenue !", "module.auth.login.visit_website": "Indaver is leading in sustainable", "module.auth.login.waste_management.label": "Waste management", "module.auth.roles.error.description": "Contactez votre administrateur pour vérifier vos rôles.", "module.auth.roles.error.title": "Vous n'avez aucun rôle attribué", "module.certificate.overview.title": "Certificats", "module.contract.overview.bulk.download_pdf": "Télécharger les critères d'acceptation", "module.contract.overview.bulk.plan": "Planifier la collecte", "module.contract.overview.bulk.plan_error.different_waste_producers": "Tous les contrats sélectionnés doivent avoir le même producteur de déchets pour démarrer une demande de ramassage", "module.contract.overview.bulk.plan_error.no_customer": "Il semble y avoir une erreur avec l'ID client des articles sélectionnés.", "module.contract.overview.title": "Contrats", "module.dashboard.features.news_detail.more_news": "Plus d'actualités", "module.dashboard.features.overview.announcements.title": "Annonces", "module.dashboard.features.overview.greeting.afternoon": "Bon après-midi", "module.dashboard.features.overview.greeting.evening": "Bonsoir", "module.dashboard.features.overview.greeting.morning": "Bonjour", "module.dashboard.features.overview.news.no_news": "Aucun article d'actualité disponible pour le moment", "module.dashboard.features.overview.news.title": "Actualités", "module.dashboard.features.overview.newsletter.email": "Adresse e-mail", "module.dashboard.features.overview.newsletter.subscribe": "<PERSON>'abonner", "module.dashboard.features.overview.newsletter.subscribe_success.description": "Vous êtes maintenant abonné à notre newsletter.", "module.dashboard.features.overview.newsletter.subscribe_success.title": "<PERSON><PERSON><PERSON> !", "module.dashboard.features.overview.newsletter.title": "Newsletter", "module.dashboard.overview.newsletter.title": "Restez informé ! Abonnez-vous à notre newsletter en entrant votre adresse e-mail ci-dessous.", "module.dashboard.overview.page_title": "Tableau de bord", "module.dashboard.overview.title": "Tableau de bord", "module.document.overview.columns.action_date": "Date d'action", "module.document.overview.columns.applicable_from": "Applicable de", "module.document.overview.columns.applicable_until": "Applicable jusqu'à", "module.document.overview.columns.document_name": "Nom de document", "module.document.overview.columns.status": "Statut", "module.document.overview.columns.type_tfs": "Type TFS", "module.document.overview.columns.waste_producer": "Producteur de déchets", "module.document.overview.filters.customer": "Client", "module.document.overview.filters.status": "Statut", "module.document.overview.filters.waste_producer": "Producteurs de déchets", "module.document.overview.filters.year": "<PERSON><PERSON>", "module.document.overview.title": "Mes Documents", "module.document.overview.tooltip": "Nous avons regroupé tous les documents liés à votre package TWM et fournis les services. Si vous ne trouvez pas de document spécifique, n'hésitez pas à appeler votre contact de confiance chez Indaver.", "module.guidance_letter.overview.title": "Lettres d'orientation", "module.guidance_letters.overview.table.download_attachment": "Download attachment", "module.guidance_letters.overview.table.download_preview": "Télécharger l'aperçu", "module.guidance_letters.overview.table.download_print": "Télécharger pour imprimer", "module.invoice.create": "<PERSON><PERSON>er une facture", "module.invoice.create.title": "<PERSON><PERSON>er une facture", "module.invoice.detail.amount": "<PERSON><PERSON>", "module.invoice.detail.edit_invoice": "Modifier la facture", "module.invoice.detail.files": "Fichiers", "module.invoice.detail.no_certificate_available": "Aucun certificat disponible", "module.invoice.detail.no_customer_reference": "Au<PERSON>ne référence client", "module.invoice.detail.no_document_available": "Aucun document disponible", "module.invoice.detail_title": "Détail de facture", "module.invoice.info": "Informations sur la facture", "module.invoice.label.plural": "Factures", "module.invoice.overview.status": "Statut", "module.invoice.overview.tab.all": "Tous", "module.invoice.overview.tab.approved": "Approu<PERSON><PERSON>", "module.invoice.overview.tab.cleared": "Dégagé", "module.invoice.overview.tab.draft": "Brouillons", "module.invoice.overview.tab.open": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "module.invoice.overview.tab.outstanding": "Remarquable", "module.invoice.overview.tab.overdue": "En retard", "module.invoice.overview.tab.paid": "<PERSON><PERSON>", "module.invoice.overview.tab.proforma": "Format", "module.invoice.overview.tab.rejected": "<PERSON><PERSON><PERSON>", "module.invoice.overview.tab.submitted": "<PERSON><PERSON><PERSON>", "module.invoice.overview.tab.to_be_approved": "Être approuvé", "module.invoice.overview.title": "Factures", "module.invoice.title": "Facture", "module.invoice.unknown": "Facture inconnue", "module.invoice.update.title": "Mettre à jour la facture", "module.invoice.uuid": "Facture uuid", "module.invoices.review.approve_description": "Fournissez éventuellement une remarque et / ou un numéro de PO.", "module.invoices.review.approve_subtitle": "Approuver la facture", "module.invoices.review.approve_title": "Êtes-vous sûr que vous souhaitez approuver la facture `{invoiceNumber}`?", "module.invoices.review.fields.approve_reason": "<PERSON><PERSON><PERSON> d'approbation", "module.invoices.review.fields.comment": "<PERSON><PERSON><PERSON>", "module.invoices.review.fields.po_number": "<PERSON><PERSON><PERSON><PERSON> de commande", "module.invoices.review.fields.reject_reason": "<PERSON><PERSON><PERSON>", "module.invoices.review.reject_description": "Veuillez fournir une remarque indiquant la raison du rejet", "module.invoices.review.reject_subtitle": "<PERSON><PERSON>er la facture", "module.invoices.review.reject_title": "Êtes-vous sûr de vouloir rejeter la facture `{invoiceNumber}`?", "module.invoices.review.submit_approval": "Soumettre et approuver", "module.invoices.review.submit_rejection": "Soumettre et rejeter", "module.language_management.overview.title": "Traductions", "module.news.announcement.delete.success_message.description": "Votre annonce a été supprimée avec succès et n'est plus visible.", "module.news.announcement.delete.success_message.title": "<PERSON><PERSON><PERSON> supprimée", "module.news.announcement.update.title": "Modifier l'annonce", "module.news.announcement_overview.title": "Annonces", "module.news.announcement_overview.urgent_announcement": "<PERSON><PERSON>ce urgente", "module.news.announcements.create.page_title": "Nouvelle annonce", "module.news.announcements.update.return_to_overview": "Toutes les annonces", "module.news.article.create.page_title": "Nouvel article", "module.news.article.delete.success_message.description": "Votre article a été supprimé avec succès et n'est plus visible.", "module.news.article.delete.success_message.title": "Article supprimé", "module.news.article.fields.end_date": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "module.news.article.fields.hint": "Seules les balises <iframe> sont acceptées dans ce champ. Pour intégrer du contenu, copiez le code d'intégration iframe complet à partir de plateformes telles que YouTube, Vimeo ou tout autre service que vous utilisez.", "module.news.article.fields.publishing_date": "Date de publication", "module.news.article.fields.start_date": "<PERSON>", "module.news.article.fields.video": "Vidéo", "module.news.article.fields.video_placeholder": "iframe vidéo", "module.news.article.update.page_title": "Modifier l'article", "module.news.delete.success_message.description": "Votre article a été supprimé avec succès et n'est plus visible.", "module.news.delete.success_message.title": "Article supprimé", "module.news.overview.long_title": "Articles", "module.news.overview.new_announcement": "Nouvelle annonce", "module.news.overview.new_article": "Nouvel article", "module.news.overview.no_active_filters": "Aucun filtre actif", "module.news.overview.table.author": "<PERSON><PERSON><PERSON>", "module.news.overview.table.ends_on": "Se termine le", "module.news.overview.table.published_on": "<PERSON><PERSON><PERSON> le", "module.news.overview.table.status": "Statut", "module.news.overview.table.title": "Titre", "module.news.overview.title": "Actualités", "module.news.update.created_on_date_by_user": "<PERSON><PERSON><PERSON> le {date} par {user}", "module.news.update.delete_article": "<PERSON><PERSON><PERSON><PERSON>", "module.news.update.delete_message": "Voulez-vous vraiment supprimer cet article ? Cette action est irréversible.", "module.news.update.fields.content": "Contenu", "module.news.update.fields.from": "<PERSON>", "module.news.update.fields.image": "Image", "module.news.update.fields.schedule_publishing": "Planifier la publication", "module.news.update.fields.status": "Statut", "module.news.update.fields.title": "Titre", "module.news.update.fields.title_placeholder": "Titre", "module.news.update.fields.until": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "module.news.update.publish_announcement": "Publier", "module.news.update.publish_article": "Publier", "module.news.update.publish_success_message.description": "Votre article a été publié avec succès.", "module.news.update.publish_success_message.title": "Article publié", "module.news.update.return_to_overview": "Tous les articles", "module.news.update.save_changes": "Enregistrer les modifications", "module.news.update.success_message.description": "Votre article a été enregistré avec succès.", "module.news.update.success_message.title": "Article enregistré.", "module.news.update.validation.at_least_one_translation": "Au moins 1 traduction doit avoir un titre et un contenu.", "module.news.update.validation.end_date_after_start_date": "La date de fin doit être postérieure à la date de début", "module.news.update.validation.image_required": "Une image miniature est requise.", "module.news.update.validation.start_date_today_or_future": "La date doit être aujourd'hui ou dans le futur", "module.news.update.validation.title_and_content_required": "Le titre et le contenu doivent être remplis ou laissés vides.", "module.news.update_announcement.delete_announcement": "<PERSON><PERSON><PERSON><PERSON>", "module.news.update_announcement.delete_message": "Voulez-vous vraiment supprimer cette annonce ? Cette action est irréversible.", "module.news.update_announcement.fields.type": "Type d'annonce", "module.news.update_announcement.page_title": "Modifier l'annonce", "module.news.update_announcement.publish_success_message.description": "Votre annonce a été publiée avec succès.", "module.news.update_announcement.publish_success_message.title": "<PERSON><PERSON><PERSON> publi<PERSON>", "module.news.update_announcement.return_to_overview": "Toutes les annonces", "module.news.update_announcement.success_message.description": "Votre annonce a été enregistrée avec succès.", "module.news.update_announcement.success_message.title": "<PERSON><PERSON><PERSON> enregis<PERSON><PERSON>", "module.packaging_request.overview.new_request": "Nouvelle demande d'emballage", "module.packaging_request.update.customer_and_location.customer.title": "Client", "module.packaging_request.update.customer_and_location.delivery.title": "<PERSON><PERSON><PERSON>", "module.packaging_request.update.customer_and_location.title": "Producteur de déchets et adresse de livraison", "module.packaging_request.update.customer_and_location.waste_producer.title": "Producteur de déchets", "module.packaging_request.update.delivery.add_contact": "A<PERSON>ter le contact", "module.packaging_request.update.delivery.add_existing_contact": "Ajouter le contact existant", "module.packaging_request.update.delivery.date_or_period": "Date ou période préférée", "module.packaging_request.update.delivery.remarks": "Avez-vous des remarques pour nous?", "module.packaging_request.update.delivery.remarks_placeholder": "<PERSON><PERSON><PERSON>, commentaires ou questions?", "module.packaging_request.update.delivery.send_copy_to_contacts": "V<PERSON><PERSON><PERSON>-vous envoyer une copie à d'autres contacts?", "module.packaging_request.update.delivery.title": "<PERSON><PERSON><PERSON>", "module.packaging_request.update.delivery_details": "<PERSON><PERSON><PERSON>iv<PERSON>", "module.packaging_request.update.general_info": "Informations générales", "module.packaging_request.update.packaging.label": "Sélectionnez l'emballage dont vous avez besoin", "module.packaging_request.update.packaging.table.cost_center": "Centre de coûts", "module.packaging_request.update.packaging.table.image_alt": "Image pour le numéro de matériau {number}", "module.packaging_request.update.packaging.table.po_number": "<PERSON><PERSON><PERSON><PERSON> de commande", "module.packaging_request.update.packaging.title": "Sélection d'emballage", "module.packaging_request.update.packaging.too_little_selected": "<PERSON><PERSON> devez sélectionner au moins 1", "module.packaging_request.update.page_title": "Demande d'emballage", "module.packaging_request.update.submit.email_label": "E-mail", "module.packaging_request.update.submit.email_placeholder": "exempleemail.com", "module.packaging_request.update.submit.first_name": "Prénom", "module.packaging_request.update.submit.last_name": "Nom de famille", "module.packaging_request.update.submit.request_submitted": "Votre emballage a été soumis.", "module.packaging_request.update.submit.request_submitted_description": "Votre demande a été soumise avec succès sous l'ID {id}. Nous vous contacterons bientôt.", "module.packaging_request.update.submit.return_to_overview": "Retour à l'aperçu", "module.packaging_request.update.submit.success": "Soumis!", "module.packaging_request.update.submit.success_description": "Demande d'emballage soumise avec succès avec id {id}", "module.packaging_request.update.submit.thank_you": "<PERSON><PERSON><PERSON> !", "module.permissions.overview.title": "Autorisations", "module.pickup_request.detail.confirmed": "Collecte confirmée du {start} au {end}", "module.pickup_request.detail.created_on": "<PERSON><PERSON><PERSON>", "module.pickup_request.detail.page_title": "<PERSON><PERSON><PERSON>", "module.pickup_request.detail.requested": "Collecte demandée du {start} au {end}", "module.pickup_request.detail.submitted_id": "ID {id}", "module.pickup_request.detail.title": "Collecte pour", "module.pickup_request.overview.bulk.delete_draft": "Supp<PERSON>er les brouillons", "module.pickup_request.overview.bulk.delete_draft_description": "Vos ébauches sélectionnées seront supprimées en permanence", "module.pickup_request.overview.filter.draft_status": "Statut de brouillon", "module.pickup_request.overview.filter.hazardous": "Dangereux", "module.pickup_request.overview.filter.transport_by_indaver": "Transport par Indaver", "module.pickup_request.overview.filter.transport_mode": "Mode de transport", "module.pickup_request.overview.new_pickup": "Nouvelle collecte", "module.pickup_request.overview.new_request": "Nouvelle demande", "module.pickup_request.overview.tab.awaiting_booking": "En attente de réservation", "module.pickup_request.overview.tab.booked": "Réservé", "module.pickup_request.overview.tab.cancelled": "<PERSON><PERSON><PERSON>", "module.pickup_request.overview.tab.completed": "<PERSON><PERSON><PERSON><PERSON>", "module.pickup_request.overview.tab.drafts": "Brouillons", "module.pickup_request.overview.tab.indascan_drafts": "Indascan", "module.pickup_request.overview.tab.submitted": "<PERSON><PERSON><PERSON>", "module.pickup_request.overview.title": "Demandes de collecte", "module.pickup_request.sidebar.title": "Collectes", "module.pickup_request.update.administration.fields.cost_center": "Centre de coûts", "module.pickup_request.update.administration.fields.po_number": "<PERSON><PERSON><PERSON><PERSON> de bon de commande", "module.pickup_request.update.administration.fields.serial_number": "Numéro de série", "module.pickup_request.update.administration.fields.tfs_number": "Numéro TFS", "module.pickup_request.update.administration.fields.waste": "<PERSON><PERSON><PERSON><PERSON>", "module.pickup_request.update.administration.title": "Administration", "module.pickup_request.update.container_info.title": "Infos conteneur", "module.pickup_request.update.customer_and_location.title": "Producteur de déchets et adresse de collecte", "module.pickup_request.update.details.asn": "ASN", "module.pickup_request.update.details.bulk_unit": "Unités en vrac", "module.pickup_request.update.details.bulk_unit_description": "Ces modifications seront appliquées à toutes les lignes de déchets. \nVous pourrez toujours modifier l'unité pour chaque ligne de déchets individuellement.", "module.pickup_request.update.details.bulk_unit_weight_volume": "Définir «unité de poids / volume estimé» à:", "module.pickup_request.update.details.customer_reference": "Référence du client", "module.pickup_request.update.details.delivery_info": "Informations sur la livraison", "module.pickup_request.update.details.esn_number": "ESN", "module.pickup_request.update.details.ewc_code": "Code EWC", "module.pickup_request.update.details.material_analysis": "Analyse des matériaux", "module.pickup_request.update.details.materials_error": "Vous semblez avoir une erreur sur ce champ: '{champ}'. \nNotez que ce champ peut être caché en raison de vos colonnes dynamiques actives.", "module.pickup_request.update.details.process_code": "Code de processus", "module.pickup_request.update.details.title": "Détails", "module.pickup_request.update.details.waste_material": "<PERSON><PERSON><PERSON><PERSON>", "module.pickup_request.update.general_info": "Informations générales", "module.pickup_request.update.packaging.fields.container_covered": "Cont. couvert", "module.pickup_request.update.packaging.fields.container_number": "N° cont.", "module.pickup_request.update.packaging.fields.container_size_volume": "Taille/volume cont.", "module.pickup_request.update.packaging.fields.container_type": "Type cont.", "module.pickup_request.update.packaging.fields.estimated_weight_volume": "Poids / volume estimé", "module.pickup_request.update.packaging.fields.is_return_packaging": "Emballage de retour", "module.pickup_request.update.packaging.fields.is_return_packaging_description": "Je souhaite recevoir la même quantité et le même type d'emballage en retour", "module.pickup_request.update.packaging.fields.is_return_packaging_tooltip": "Pertinent uniquement si vous souhaitez que le même emballage exact soit retourné. Applicable uniquement pour : GRV, ASF, ASP, Conteneur-citerne, Gebinde im ASP", "module.pickup_request.update.packaging.fields.number_of_containers": "Nombre de cont.", "module.pickup_request.update.packaging.fields.packaging_remark": "Remarques sur l'emballage", "module.pickup_request.update.packaging.fields.packaging_type": "Type d'emballage", "module.pickup_request.update.packaging.fields.quantity_labels": "N° étiquettes", "module.pickup_request.update.packaging.fields.quantity_packages": "N° colis", "module.pickup_request.update.packaging.fields.quantity_pallets": "N° palettes", "module.pickup_request.update.packaging.fields.reconciliation_number": "Réconciliation n °", "module.pickup_request.update.packaging.fields.tanker_type": "Type de citerne", "module.pickup_request.update.packaging.fields.total_quantity_pallets": "Nombre total de palettes", "module.pickup_request.update.packaging.fields.transport_type": "Type de transp.", "module.pickup_request.update.packaging.fields.unit": "Unité", "module.pickup_request.update.packaging.fields.waste": "<PERSON><PERSON><PERSON>", "module.pickup_request.update.packaging.placeholder.amount": "0", "module.pickup_request.update.packaging.placeholder.container_type": "Type de conteneur", "module.pickup_request.update.packaging.placeholder.container_volume_size": "4mx2mx2m", "module.pickup_request.update.packaging.placeholder.hazard_inducers": "Inducteurs de danger", "module.pickup_request.update.packaging.placeholder.long_number": "0", "module.pickup_request.update.packaging.placeholder.packaging_type": "Type d'emballage", "module.pickup_request.update.packaging.placeholder.tanker_type": "Type de citerne", "module.pickup_request.update.packaging.placeholder.transport_type": "Type de transport", "module.pickup_request.update.packaging.placeholder.un_number": "Numéro UN", "module.pickup_request.update.packaging.title": "Informations emballage", "module.pickup_request.update.packaging_request.title": "Commande d'emballage", "module.pickup_request.update.page_title": "Nouvelle collecte", "module.pickup_request.update.pickup_details": "Détails de la collecte", "module.pickup_request.update.planning.additional_files": "Des fichiers supplémentaires à télécharger ?", "module.pickup_request.update.planning.additional_files_hint": "Ex : photos, fiches produits, listes chimiques, etc.", "module.pickup_request.update.planning.date_in_future": "La date de début doit être dans le futur.", "module.pickup_request.update.planning.date_or_period": "Date ou période préférée", "module.pickup_request.update.planning.remarks": "Avez-vous des remarques pour nous ?", "module.pickup_request.update.planning.remarks_placeholder": "<PERSON><PERSON><PERSON>, commentaires ou questions ?", "module.pickup_request.update.planning.title": "Planification", "module.pickup_request.update.planning.wic_confirmation_label": "Je confirme que le déchet est conforme au document WIC signé", "module.pickup_request.update.quick_entry_mode": "Mode d'entrée rapide", "module.pickup_request.update.return_to_overview": "Toutes les demandes de collecte", "module.pickup_request.update.submit.add_contact": "Ajouter un contact", "module.pickup_request.update.submit.add_existing_contact": "Ajouter un contact existant", "module.pickup_request.update.submit.almost_done": "Presque terminé !", "module.pickup_request.update.submit.edit_pickup": "Modifier la collecte", "module.pickup_request.update.submit.email_label": "E-mail", "module.pickup_request.update.submit.email_placeholder": "exemple{at}email.com", "module.pickup_request.update.submit.first_name": "Prénom", "module.pickup_request.update.submit.last_name": "Nom de famille", "module.pickup_request.update.submit.request_submitted": "Votre demande de collecte a été soumise", "module.pickup_request.update.submit.request_submitted_description": "Votre demande a été soumise avec succès sous l'ID {id}. Nous vous recontacterons bientôt.", "module.pickup_request.update.submit.return_to_overview": "Retour à l'aperçu", "module.pickup_request.update.submit.send_copy_to_contacts": "V<PERSON><PERSON><PERSON>-vous envoyer une copie à d'autres contacts ?", "module.pickup_request.update.submit.submit_pickup": "Soumettre la collecte", "module.pickup_request.update.submit.submit_request": "Prêt à soumettre votre collecte ?", "module.pickup_request.update.submit.thank_you": "<PERSON><PERSON><PERSON> !", "module.pickup_request.update.transport.fields.adr_class": "Classe ADR", "module.pickup_request.update.transport.fields.danger_label_one": "Étiquette de danger 1", "module.pickup_request.update.transport.fields.danger_label_three": "Étiquette de danger 3", "module.pickup_request.update.transport.fields.danger_label_two": "Étiquette de danger 2", "module.pickup_request.update.transport.fields.hazard_inducers": "Inducteurs de danger", "module.pickup_request.update.transport.fields.hazard_inducers_hint": "Requis car le numéro de l'ONU sélectionné est dangereux.", "module.pickup_request.update.transport.fields.packaging_group": "Groupe d'emballage", "module.pickup_request.update.transport.fields.un_number": "Numéro UN", "module.pickup_request.update.transport.fields.waste": "<PERSON><PERSON><PERSON>", "module.pickup_request.update.transport.title": "Transport", "module.pickup_request.update.waste.contract_line.custmer_ref": "Réf. client", "module.pickup_request.update.waste.contract_line.ewc_code": "Code DDC", "module.pickup_request.update.waste.contract_line.max_selection_reached": "Sélection maximale atteinte", "module.pickup_request.update.waste.contract_line.pickup_address": "<PERSON>ress<PERSON> de collecte", "module.pickup_request.update.waste.contract_line.pickup_date": "Date de collecte préférée", "module.pickup_request.update.waste.contract_line.pickup_time": "Heure de collecte préférée", "module.pickup_request.update.waste.contract_line.too_little_selected": "V<PERSON> devez en sélectionner au moins 1", "module.pickup_request.update.waste.contract_line.too_many_selected": "Vous en avez sélectionné trop, veuil<PERSON><PERSON> désélectionner ou changer le mode de transport", "module.pickup_request.update.waste.contract_line.waste_material": "<PERSON><PERSON>", "module.pickup_request.update.waste.materials.all": "<PERSON>ut", "module.pickup_request.update.waste.materials.label": "Rechercher et sélectionner les matières déchets dans la liste", "module.pickup_request.update.waste.materials.selected": "Sélectionné", "module.pickup_request.update.waste.materials.title": "Quelles matières déchets souhaitez-vous éliminer ?", "module.pickup_request.update.waste.packaging_added": "Ajouter un emballage à cette demande", "module.pickup_request.update.waste.title": "<PERSON><PERSON><PERSON><PERSON>", "module.pickup_request.update.waste.transport_by_indaver": "Je souhaite que le transport soit organisé par Indaver.", "module.pickup_request.update.waste.transport_mode": "Mode de transport", "module.reporting.overview.title": "Rapports", "module.roles_and_permissions.add_new_role": "Nouveau rôle", "module.roles_and_permissions.create_role_dialog.description": "Créer un nouveau rôle", "module.roles_and_permissions.create_role_dialog.title": "<PERSON><PERSON>er un rôle", "module.roles_and_permissions.save_changes": "Enregistrer les modifications", "module.setting.application.title": "Application", "module.setting.contact.create": "<PERSON><PERSON><PERSON> un contact", "module.setting.contact.create_success": "Contact créé avec succès", "module.setting.contact.delete_description": "Êtes-vous sûr de vouloir supprimer ce contact ?", "module.setting.contact.delete_success": "Contact supprimé avec succès.", "module.setting.contact.edit": "Modifier le contact", "module.setting.contact.fields.email": "E-mail", "module.setting.contact.fields.first_name": "Prénom", "module.setting.contact.fields.last_name": "Nom de famille", "module.setting.contact.no_contacts": "Vous n'avez actuellement aucun contact.", "module.setting.font_size.default": "<PERSON><PERSON> <PERSON><PERSON>", "module.setting.font_size.description": "Ajuster la taille de la police.", "module.setting.font_size.large": "Grande", "module.setting.font_size.larger": "Plus grande", "module.setting.font_size.small": "Petite", "module.setting.font_size.smaller": "Plus petite", "module.setting.font_size.title": "Taille de la police", "module.setting.high_contrast.description": "Améliore la lisibilité et la visibilité en augmentant le contraste entre les éléments de l'interface utilisateur.", "module.setting.high_contrast.disabled.description": "Le mode contraste élevé est désactivé.", "module.setting.high_contrast.disabled.label": "Désactivé", "module.setting.high_contrast.enabled.description": "Le mode contraste élevé est activé.", "module.setting.high_contrast.enabled.label": "Activé", "module.setting.high_contrast.title": "Mode contraste élevé", "module.setting.interface_theme.dark": "<PERSON><PERSON><PERSON>", "module.setting.interface_theme.description": "Sélectionnez ou personnalisez votre thème d'interface utilisateur.", "module.setting.interface_theme.light": "<PERSON>", "module.setting.interface_theme.system_preference": "Préférence syst<PERSON>", "module.setting.interface_theme.title": "Thème de l'interface", "module.setting.keyboard_shortcuts.description": "Les raccourcis clavier sont toujours activés, mais vous pouvez choisir d'afficher une astuce.", "module.setting.keyboard_shortcuts.disabled.description": "Les astuces de raccourcis clavier sont masquées.", "module.setting.keyboard_shortcuts.disabled.label": "Désactivé", "module.setting.keyboard_shortcuts.enabled.description": "Les astuces de raccourcis clavier sont visibles.", "module.setting.keyboard_shortcuts.enabled.label": "Activé", "module.setting.keyboard_shortcuts.example_button": "Exemple", "module.setting.keyboard_shortcuts.title": "Astuces de racco<PERSON>cis clavier", "module.setting.language.description": "Changer votre langue.", "module.setting.language.locales.en_nl": "<PERSON><PERSON><PERSON> (Pays-Bas / Belgique)", "module.setting.language.locales.en_us": "<PERSON><PERSON><PERSON>", "module.setting.language.locales.nl_be": "Néerlandais (Belgique)", "module.setting.language.title": "<PERSON><PERSON>", "module.setting.reduce_motion.description": "Réduit la quantité et l'intensité des animations, des effets de survol et d'autres effets de mouvement dans l'application.", "module.setting.reduce_motion.disabled.description": "Toutes les animations et les effets de survol sont activés pour une expérience complète.", "module.setting.reduce_motion.disabled.label": "Désactivé", "module.setting.reduce_motion.enabled.description": "Vous verrez moins d'animations et d'effets de survol pour réduire le mouvement.", "module.setting.reduce_motion.enabled.label": "Activé", "module.setting.reduce_motion.title": "Rédu<PERSON> le mouvement", "module.setting.roles_and_permissions.add_new_role": "Ajouter un rôle", "module.setting.roles_and_permissions.create_role_dialog.description": "Créer un nouveau rôle", "module.setting.roles_and_permissions.create_role_dialog.title": "<PERSON><PERSON>er un rôle", "module.setting.roles_and_permissions.save_changes": "Enregistrer les modifications", "module.setting.roles_and_permissions.save_changes_success": "Modifications enregistrées avec succès", "module.setting.roles_and_permissions.title": "Rôles et autorisations", "module.setting.subtitle": "G<PERSON>rer les paramètres de votre application", "module.setting.title": "Paramètres", "module.settings.back.label": "Retour", "module.settings.contacts.description": "<PERSON><PERSON><PERSON> vos contacts.", "module.settings.contacts.title": "Contacts", "module.settings.disabled": "Désactivé", "module.settings.display.title": "Affichage", "module.settings.enabled": "Activé", "module.settings.forward.label": "Suivant", "module.settings.general": "Général", "module.settings.maximize.label": "Maximiser l'affichage des paramètres", "module.settings.minimize.label": "Minimiser l'affichage des paramètres", "module.settings.no_results": "Aucun résultat trouvé pour \"{searchTerm}\".", "module.settings.roles_and_permissions.add_new_role": "Ajouter un rôle", "module.settings.roles_and_permissions.create_role_dialog.description": "Créer un nouveau rôle", "module.settings.roles_and_permissions.create_role_dialog.title": "<PERSON><PERSON>er un rôle", "module.settings.roles_and_permissions.save_changes": "Enregistrer les modifications", "module.settings.roles_and_permissions.save_changes_success": "Modifications enregistrées avec succès", "module.settings.roles_and_permissions.table.delete_role": "<PERSON><PERSON><PERSON><PERSON> le rôle", "module.settings.roles_and_permissions.table.permissions": "Autorisations", "module.settings.roles_and_permissions.title": "Rôles et autorisations", "module.settings.search.clear.label": "Effacer le terme de recherche", "module.settings.search.placeholder": "Recherche rapide", "module.settings.section.appearance.description": "Personnalisez l'apparence générale de l'application.", "module.settings.section.appearance.option.dark_mode": "Mode sombre", "module.settings.section.appearance.option.light_mode": "Mode clair", "module.settings.section.appearance.option.system_preference": "Préférence syst<PERSON>", "module.settings.section.appearance.title": "Apparence", "module.settings.section.font_size.description": "<PERSON><PERSON><PERSON><PERSON> la taille du texte pour une meilleure lisibilité ou pour afficher plus de contenu sur votre écran.", "module.settings.section.font_size.option.default": "<PERSON><PERSON> <PERSON><PERSON>", "module.settings.section.font_size.option.large": "Grande", "module.settings.section.font_size.option.larger": "Plus grande", "module.settings.section.font_size.option.small": "Petite", "module.settings.section.font_size.option.smaller": "Plus petite", "module.settings.section.font_size.title": "Taille de la police", "module.settings.section.high_contrast.description": "Améliorez la visibilité et réduisez la fatigue oculaire avec le mode contraste élevé.", "module.settings.section.high_contrast.disabled.label": "Le mode contraste élevé est désactivé", "module.settings.section.high_contrast.enabled.label": "Le mode contraste élevé est activé", "module.settings.section.high_contrast.title": "Contraste élevé", "module.settings.section.keyboard_shortcut_hints.description": "Activez ou désactivez les astuces pour les raccourcis clavier afin de naviguer plus efficacement dans l'application.", "module.settings.section.keyboard_shortcut_hints.disabled.label": "Les astuces de raccourcis clavier sont masquées", "module.settings.section.keyboard_shortcut_hints.enabled.label": "Les astuces de raccourcis clavier sont visibles", "module.settings.section.keyboard_shortcut_hints.example": "Exemple", "module.settings.section.keyboard_shortcut_hints.not_available_on_mobile": "Les raccourcis clavier ne sont pas disponibles sur les appareils mobiles ou les tablettes. Pour modifier ce paramètre, ouvrez l'application sur un ordinateur de bureau.", "module.settings.section.keyboard_shortcut_hints.title": "Astuces de racco<PERSON>cis clavier", "module.settings.section.language.description": "Sélectionnez votre langue préférée pour l'application.", "module.settings.section.language.title": "<PERSON><PERSON>", "module.settings.settings_are_hidden.label": "{count} paramètre dans « {viewName} » est actuellement masqué. | {count} paramètres dans « {viewName} » sont actuellement masqués.", "module.settings.settings_are_hidden.show_all.label": "Afficher tous les paramètres", "module.user.columns.upn": "Upn", "module.user.create.create_user": "C<PERSON>er un utilisateur", "module.user.create.success_toast.message": "L'utilisateur a été créé avec succès.", "module.user.create.title": "Nouvel utilisateur", "module.user.detail.edit_user": "Modifier l'utilisateur", "module.user.detail.title": "Modifier l'utilisateur", "module.user.form.section.name.description": "Prénom et nom de l'utilisateur.", "module.user.impersonation.description": "Êtes-vous sûr de vouloir usurper l'identité de {user} ? Cela vous permettra d'agir en leur nom.", "module.user.impersonation.title": "Commencer l'Usurpation", "module.user.overview.title": "Utilisateurs", "module.user.role": "R<PERSON><PERSON>", "module.waste_inquiry.detail.additional_files": "Fichiers supplémentaires", "module.waste_inquiry.detail.analysis_report": "Rapport d'analyse", "module.waste_inquiry.detail.conformity_assessment": "Évaluation de conformité", "module.waste_inquiry.detail.contract_item": "Article de contrat {item}", "module.waste_inquiry.detail.contract_number": "Contrat n° {number}", "module.waste_inquiry.detail.download_summary": "Télécharger le résumé", "module.waste_inquiry.detail.sds": "Fiche de données de sécurité (FDS)", "module.waste_inquiry.overview.bulk.all_items_selected": "Tous les articles sélectionnés", "module.waste_inquiry.overview.bulk.delete_draft": "Supp<PERSON>er les brouillons", "module.waste_inquiry.overview.bulk.delete_draft_description": "Vos ébauches sélectionnées seront supprimées en permanence", "module.waste_inquiry.overview.bulk.items_selected": "{count} article sélectionné | {count} articles sélectionnés", "module.waste_inquiry.overview.bulk.items_unselected": "article désélectionné de tous | articles désélectionnés de tous", "module.waste_inquiry.overview.date": "Date", "module.waste_inquiry.overview.new_waste_inquiry": "Nouvelle demande de déchets", "module.waste_inquiry.overview.requested_by": "Demandé par", "module.waste_inquiry.overview.status": "Statut", "module.waste_inquiry.overview.tab.completed": "<PERSON><PERSON><PERSON><PERSON>", "module.waste_inquiry.overview.tab.drafts": "Brouillons", "module.waste_inquiry.overview.tab.offers": "Offres", "module.waste_inquiry.overview.tab.pending": "En attente", "module.waste_inquiry.overview.tab.submitted": "<PERSON><PERSON><PERSON>", "module.waste_inquiry.overview.title": "<PERSON><PERSON><PERSON>", "module.waste_inquiry.update.all_changes_saved": "Toutes les modifications enregistrées", "module.waste_inquiry.update.characteristics.flashpoint_type.label": "Point d'éclair", "module.waste_inquiry.update.characteristics.ph_type.label": "pH", "module.waste_inquiry.update.characteristics.specific_gravity.label": "Densité spécifique", "module.waste_inquiry.update.characteristics.stable_temperature.label": "Température", "module.waste_inquiry.update.characteristics.stable_temperature.placeholder": "Sélectionner un type", "module.waste_inquiry.update.characteristics.title": "Caractéristiques", "module.waste_inquiry.update.collection.campaign_name.label": "Nom de la campagne", "module.waste_inquiry.update.collection.expected_end_date.label": "Date de fin prévue", "module.waste_inquiry.update.collection.expected_per_collection_quantity.label": "Quantité prévue par collecte", "module.waste_inquiry.update.collection.expected_yearly_volume_amount.label": "Volume annuel prévu", "module.waste_inquiry.update.collection.first_collection_date.label": "Date de début prévue", "module.waste_inquiry.update.collection.frequency_of_discharge.label": "<PERSON><PERSON>quence de déchargement", "module.waste_inquiry.update.collection.remarks.label": "Informations supplémentaires", "module.waste_inquiry.update.collection.remarks.placeholder": "Fournissez des informations supplémentaires, des remarques ou des questions", "module.waste_inquiry.update.collection.title": "<PERSON><PERSON><PERSON>", "module.waste_inquiry.update.collection_and_transport": "Collecte et transport", "module.waste_inquiry.update.composition.analysis_report.label": "<PERSON><PERSON>-vous un rapport d'analyse ?", "module.waste_inquiry.update.composition.analysis_report.no_report": "Je n'ai pas de rapport d'analyse", "module.waste_inquiry.update.composition.components.label": "Spécifier la composition des déchets", "module.waste_inquiry.update.composition.sample.available": "J'ai un échantillon disponible à envoyer", "module.waste_inquiry.update.composition.sample.label": "Échantillon de déchets", "module.waste_inquiry.update.composition.sample.not_available": "Je n'ai pas d'échantillon disponible à envoyer", "module.waste_inquiry.update.composition.sds.label": "<PERSON><PERSON><PERSON>vous une fiche de données de sécurité (FDS) ?", "module.waste_inquiry.update.composition.sds.no_sds": "Je n'ai pas de FDS", "module.waste_inquiry.update.composition.title": "Composition", "module.waste_inquiry.update.created_on": "<PERSON><PERSON><PERSON>", "module.waste_inquiry.update.customer_and_location.customer.title": "Sélectionner un client", "module.waste_inquiry.update.customer_and_location.pick_up_address.title": "Sé<PERSON><PERSON>ner une adresse de collecte", "module.waste_inquiry.update.customer_and_location.pick_up_address.unknown_label": "L'adresse de collecte est inconnue", "module.waste_inquiry.update.customer_and_location.suggestions": "Suggestions", "module.waste_inquiry.update.customer_and_location.title": "Client et lieu", "module.waste_inquiry.update.customer_and_location.waste_producer.title": "Sélectionner un producteur de déchets", "module.waste_inquiry.update.customer_and_location.waste_producer.unknown_label": "Le producteur de déchets est inconnu", "module.waste_inquiry.update.failed_to_save_changes": "Échec de l'enregistrement des modifications", "module.waste_inquiry.update.general_info": "Informations générales", "module.waste_inquiry.update.legislation.title": "Législation", "module.waste_inquiry.update.legislation_and_properties.legislations.label": "Législations", "module.waste_inquiry.update.legislation_and_properties.legislations.remarks.placeholder": "Veuillez spécifier chaque législation sélectionnée", "module.waste_inquiry.update.legislation_and_properties.properties.label": "Propriétés", "module.waste_inquiry.update.legislation_and_properties.properties.remarks.placeholder": "Veuillez spécifier chaque propriété sélectionnée", "module.waste_inquiry.update.legislation_and_properties.remarks.label": "Spécifier", "module.waste_inquiry.update.legislation_and_properties.remarks.placeholder": "Veuillez spécifier chaque législation sélectionnée", "module.waste_inquiry.update.legislation_and_properties.title": "Législation et propriétés", "module.waste_inquiry.update.packaging.add_un_number": "Ajouter le numéro UN", "module.waste_inquiry.update.page_title": "Nouvelle demande de déchets", "module.waste_inquiry.update.properties.title": "Propriétés", "module.waste_inquiry.update.return_to_overview": "Tous les flux de déchets", "module.waste_inquiry.update.saving_changes": "Enregistrement des modifications", "module.waste_inquiry.update.submit.add_contact_label": "Ajouter un contact", "module.waste_inquiry.update.submit.add_existing_contact_label": "Ajouter un contact existant", "module.waste_inquiry.update.submit.additional_files_hint": "Ex : photos, fiches produits, listes chimiques, etc.", "module.waste_inquiry.update.submit.additional_files_label": "Des fichiers supplémentaires à télécharger ?", "module.waste_inquiry.update.submit.copy_id": "Copier l'ID.", "module.waste_inquiry.update.submit.edit": "Modifier la demande", "module.waste_inquiry.update.submit.remarks_label": "Avez-vous des remarques pour nous concernant votre demande ?", "module.waste_inquiry.update.submit.remarks_placeholder": "<PERSON><PERSON><PERSON>, commentaires ou questions ?", "module.waste_inquiry.update.submit.return_to_overview": "Retour à l'aperçu", "module.waste_inquiry.update.submit.send_copy_to_customer_label": "V<PERSON><PERSON><PERSON>-vous envoyer une copie à d'autres contacts ?", "module.waste_inquiry.update.submit.submit": "Soumettre la demande", "module.waste_inquiry.update.submit.success": "Votre demande a été soumise avec succès sous l'ID {id}. Nous vous recontacterons bientôt.", "module.waste_inquiry.update.submitted_by": "Par {user}", "module.waste_inquiry.update.submitted_id": "ID {id}", "module.waste_inquiry.update.submitted_on": "<PERSON><PERSON><PERSON> le", "module.waste_inquiry.update.transport.collection_requirements.label": "Exigences de collecte", "module.waste_inquiry.update.transport.container_loading_type.label": "Type de chargement du conteneur", "module.waste_inquiry.update.transport.hazard_inducer_1.label": "Inducteur de danger 1", "module.waste_inquiry.update.transport.hazard_inducer_2.label": "Inducteur de danger 2", "module.waste_inquiry.update.transport.hazard_inducer_3.label": "Inducteur de danger 3", "module.waste_inquiry.update.transport.is_tank_owned_by_customer.label": "Est-il la propriété du client ?", "module.waste_inquiry.update.transport.is_transport_arranged_by_indaver.falsy.label": "Non, par le client ou une autre entreprise", "module.waste_inquiry.update.transport.is_transport_arranged_by_indaver.label": "Est-il organisé par Indaver ?", "module.waste_inquiry.update.transport.is_transport_arranged_by_indaver.truthy.label": "O<PERSON>, par Indaver", "module.waste_inquiry.update.transport.loading_by_indaver.label": "Chargement par Indaver ?", "module.waste_inquiry.update.transport.loading_type.label": "Type de chargement de transport", "module.waste_inquiry.update.transport.regulated_transport.label": "Est-ce un transport réglementé ?", "module.waste_inquiry.update.transport.title": "Transport", "module.waste_inquiry.update.transport.transport_in.label": "Transport en", "module.waste_inquiry.update.transport.transport_type.label": "Type de transport", "module.waste_inquiry.update.transport.un_number_and_packing_group.label": "Spécifier le(s) numéro(s) UN et les groupes d'emballage", "module.waste_inquiry.update.type.description.label": "Description et origine du flux de déchets", "module.waste_inquiry.update.type.description.placeholder": "Fournir une courte description en langage courant du déchet et du processus de génération", "module.waste_inquiry.update.type.name.label": "Nom", "module.waste_inquiry.update.type.name.placeholder": "Donner un nom au flux de déchets", "module.waste_inquiry.update.type.packaging_type.label": "Type d'emballage", "module.waste_inquiry.update.type.state_of_matter.label": "État de la matière", "module.waste_inquiry.update.type.state_of_matter.placeholder": "Sélectionnez l'état de la matière", "module.waste_inquiry.update.type.title": "Type", "module.waste_inquiry.update.waste_identification": "Identification des déchets", "module.weekly_planning.overview.new_planning": "Nouvelle planification hebdomadaire", "module.weekly_planning.overview.title": "Planification hebdomadaire", "module.weekly_planning.update.customer_and_location.customer.title": "Client", "module.weekly_planning.update.customer_and_location.pick_up_address.is_unknown_label": "L'adresse de collecte est inconnue", "module.weekly_planning.update.customer_and_location.pick_up_address.title": "<PERSON>ress<PERSON> de collecte", "module.weekly_planning.update.customer_and_location.title": "Producteur de déchets et adresse de collecte", "module.weekly_planning.update.customer_and_location.waste_producer.is_unknown_label": "Le producteur de déchets est inconnu", "module.weekly_planning.update.customer_and_location.waste_producer.title": "Producteur de déchets", "module.weekly_planning.update.extra_info.title": "Informations supplémentaires", "module.weekly_planning.update.general_info": "Informations générales", "module.weekly_planning.update.page_title": "Planification hebdomadaire", "module.weekly_planning.update.pickup.save_and_close": "Enregistrer et fermer", "module.weekly_planning.update.pickup.title": "Modifier la demande de collecte", "module.weekly_planning.update.pickup_details": "Détails de la collecte", "module.weekly_planning.update.planning.additional_files": "Des fichiers supplémentaires à télécharger ?", "module.weekly_planning.update.planning.additional_files_hint": "Ex : photos, fiches produits, listes chimiques, etc.", "module.weekly_planning.update.planning.date": "Date préférée", "module.weekly_planning.update.planning.remarks": "Avez-vous des remarques pour nous ?", "module.weekly_planning.update.planning.remarks_placeholder": "<PERSON><PERSON><PERSON>, commentaires ou questions ?", "module.weekly_planning.update.planning.time": "<PERSON><PERSON> préfé<PERSON>", "module.weekly_planning.update.return_to_overview": "Retour à l'aperçu", "module.weekly_planning.update.submit.add_contact": "Ajouter un contact", "module.weekly_planning.update.submit.add_existing_contact": "Ajouter un contact existant", "module.weekly_planning.update.submit.add_existing_contact_label": "Ajouter un contact existant", "module.weekly_planning.update.submit.almost_done": "Presque terminé !", "module.weekly_planning.update.submit.edit_planning": "Modifier la planification hebdomadaire", "module.weekly_planning.update.submit.email_label": "E-mail", "module.weekly_planning.update.submit.email_placeholder": "exemple{at}indaver.com", "module.weekly_planning.update.submit.first_name": "Prénom", "module.weekly_planning.update.submit.go_to_waste_step": "Aller à l'étape Déchets", "module.weekly_planning.update.submit.last_name": "Nom de famille", "module.weekly_planning.update.submit.pickup_request_error": "Nous ne pouvons pas soumettre votre planification hebdomadaire. L'une de vos demandes de collecte semble avoir un problème sur ce champ : {field}", "module.weekly_planning.update.submit.request_submitted": "Planification hebdomadaire soumise !", "module.weekly_planning.update.submit.request_submitted_description": "Votre demande a été soumise avec succès sous l'ID {id}. Nous vous recontacterons bientôt.", "module.weekly_planning.update.submit.return_to_overview": "Retour à l'aperçu", "module.weekly_planning.update.submit.send_copy_to_contacts": "V<PERSON><PERSON><PERSON>-vous envoyer une copie à d'autres contacts ?", "module.weekly_planning.update.submit.submit_planning": "Soumettre la planification hebdomadaire", "module.weekly_planning.update.submit.submit_request": "Prêt à soumettre votre planification hebdomadaire ?", "module.weekly_planning.update.submit.thank_you": "<PERSON><PERSON><PERSON>", "module.weekly_planning.update.waste.contract_line.start_date_required": "Spécifiez votre date de collecte préférée", "module.weekly_planning.update.waste.contract_line.too_little_selected": "V<PERSON> devez en sélectionner au moins 1", "module.weekly_planning.update.waste.materials.all": "<PERSON>ut", "module.weekly_planning.update.waste.materials.selected": "Sélectionné", "module.weekly_planning.update.waste.materials.title": "Quelles matières déchets souhaitez-vous éliminer ?", "module.weekly_planning.update.waste.title": "<PERSON><PERSON><PERSON><PERSON>", "shared.actions": "Actions", "shared.add": "Ajouter", "shared.adr_labels": "Étiquettes ADR", "shared.approve": "Approuver", "shared.average_temperature": "Température moyenne", "shared.back": "Retour", "shared.cancel": "Annuler", "shared.clear": "<PERSON><PERSON><PERSON><PERSON>", "shared.close": "<PERSON><PERSON><PERSON>", "shared.copy": "<PERSON><PERSON><PERSON>", "shared.copy_success_description": "Les données ont été copiées avec succès.", "shared.copy_success_title": "Données copiées", "shared.cz_manual.open_link": "<PERSON><PERSON><PERSON><PERSON>r le lien vers le manuel", "shared.delete": "<PERSON><PERSON><PERSON><PERSON>", "shared.description": "Description", "shared.download": "Télécharger", "shared.edit": "Modifier", "shared.ewc_code": "Code DDC", "shared.expand": "Développer", "shared.export_excel": "Export Excel", "shared.filters": "Filtres", "shared.general": "Général", "shared.loading": "Chargement", "shared.max_image_size_mb": "<PERSON>. {amount} Mo", "shared.max_temperature": "Température max.", "shared.min_temperature": "Température min.", "shared.minus": "<PERSON>ins", "shared.more_info": "Plus d'infos", "shared.next": "Suivant", "shared.next_step": "Passer à l'étape suivante", "shared.no": "Non", "shared.no_roles_assigned": "Aucun rôle attribué", "shared.not_available.abbreviation": "N/D", "shared.number_short": "N°", "shared.open_useful_links": "<PERSON><PERSON><PERSON><PERSON>r les liens utiles", "shared.plus": "Plus", "shared.previous": "Précédent", "shared.previous_step": "Retour à l'étape précédente", "shared.reject": "<PERSON><PERSON><PERSON>", "shared.remove": "<PERSON><PERSON><PERSON><PERSON>", "shared.save": "<PERSON><PERSON><PERSON>", "shared.save_changes": "Enregistrer les modifications", "shared.search": "<PERSON><PERSON><PERSON>", "shared.shrink": "<PERSON><PERSON><PERSON><PERSON>", "shared.start_impersonation": "Commencer l'Usurpation", "shared.submit": "So<PERSON><PERSON><PERSON>", "shared.type": "Taper", "shared.unfinished_feature_description": "Cette fonctionnalité est en développement et n'est pas encore terminée.", "shared.unfinished_feature_title": "Fonctionnalité inachevée", "shared.unknown_address": "Adresse inconnue", "shared.update": "Mettre à jour", "shared.view_detail": "Voir le détail", "shared.useful_link.ewastra_portal_description": "Si vos déchets non dangereux sont collectés par notre filiale Indaver Logistics, vous pouvez éviter beaucoup de paperasse en accédant en ligne à vos E-CMR via cette plateforme.", "shared.useful_link.ewastra_portal_title": "Portail Ewastra", "shared.useful_link.indaver_report_description": "Notre ancien environnement de reporting avec accès à vos données historiques.", "shared.useful_link.indaver_report_title": "Indaver Reports", "shared.useful_link.waste_collectors_description": "Agréments de collecte des déchets multi-régionaux.", "shared.useful_link.waste_collectors_title": "Collecteurs de déchets", "shared.useful_link.waste_facilities_description": "Licences et agréments des installations répertoriés par entreprise.", "shared.useful_link.waste_facilities_title": "Installations de déchets", "shared.working_as": "Travaille comme", "shared.yes": "O<PERSON>", "user.birth_date": "Date de naissance", "user.email": "E-mail", "user.email_placeholder": "exemple{at}email.com", "user.first_name": "Prénom", "user.label.plural": "Utilisateurs", "user.label.singular": "Utilisa<PERSON>ur", "user.label.user_with_count": "Aucun utilisateur | {count} utilisateur | {count} utilisateurs", "user.last_name": "Nom de famille", "user.name": "Nom", "validation.invalid_date": "Date invalide", "validation.invalid_datetime": "Date et heure invalides", "validation.invalid_email": "Adresse e-mail invalide", "validation.invalid_regex": "Format invalide", "validation.invalid_string": "<PERSON><PERSON> invalide", "validation.invalid_union": "<PERSON><PERSON> invalide", "validation.invalid_url": "URL invalide", "validation.invalid_uuid": "UUID invalide", "validation.required": "Ce champ est obligatoire", "validation.too_big": "Doit être inférieur ou égal à {count}", "validation.too_big_array": "Doit contenir au maximum {count} élément | Doit contenir au maximum {count} éléments", "validation.too_big_date": "<PERSON>it être antérieur au {count}", "validation.too_big_number": "Doit être inférieur ou égal à {count}", "validation.too_big_string": "Doit contenir au maximum {count} caractère | Doit contenir au maximum {count} caractères", "validation.too_small": "Doit être supérieur ou égal à {count}", "validation.too_small_array": "Doit contenir au moins {count} élément | Doit contenir au moins {count} éléments", "validation.too_small_date": "<PERSON>it être postérieur au {count}", "validation.too_small_number": "Doit être supérieur ou égal à {count}", "validation.too_small_string": "Doit contenir au moins {count} caractère | Doit contenir au moins {count} caractères", "enum.certificate_type.cor": "Certificat de recyclage", "enum.certificate_type.cot_cob": "Certificat de traitement", "module.invoice.detail.return_to_drafts": "Retour aux brouillons"}