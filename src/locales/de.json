{"auth.legal_links.company_info": "Company info", "auth.legal_links.disclaimer": "Disclaimer", "auth.legal_links.privacy_&_cookies": "Privacy & cookies", "component.contact_autocomplete.placeholder": "Kontakt suchen", "component.customer_autocomplete.placeholder": "<PERSON><PERSON> suchen", "component.editor.action.bold": "<PERSON><PERSON>", "component.editor.action.h2": "H2", "component.editor.action.h3": "H3", "component.editor.action.italic": "<PERSON><PERSON><PERSON>", "component.editor.action.link": "Link", "component.editor.action.underline": "Unterstreichen", "component.editor.add_link": "<PERSON>", "component.editor.link_begin_with_https": "<PERSON><PERSON> <PERSON> muss mit https beginnen", "component.editor.link_label": "Link", "component.editor.link_placeholder": "https://", "component.ewc_code.ewc_code_not_found.description": "Der von Ihnen eingegebene AVV Nr. existiert nicht. Bitte überprüfen Sie den Code und versuchen Sie es erneut.", "component.ewc_code.ewc_code_not_found.title": "AVV Nr. nicht gefunden.", "component.ewc_code.invalid_format.description": "Bitte geben Sie den AVV Nr. im richtigen Format ein: <PERSON><PERSON> (z.B. 010101).", "component.ewc_code.invalid_format.title": "Ungültiges AVV Nr. Format.", "component.file_upload.allowed_file_types": "Zulässige Dateitypen", "component.file_upload.click_to_upload": "Zum Hochladen klicken", "component.file_upload.status.failed": "Hochladen fehlgeschlagen", "component.file_upload.status.finished": "Abgeschlossen", "component.file_upload.status.pending": "Wird vorbereitet", "component.filters.clear_all": "Alle Filter löschen", "component.filters.date_dialog.description": "W<PERSON>hlen Sie ein Datum für den Filter '{label}'.", "component.filters.date_range_dialog.description": "Wählen Sie einen Datumsbereich für den Filter '{label}'.", "component.filters.listbox.no_results": "<PERSON>ine übereinstimmenden Optionen", "component.filters.listbox.search_placeholder": "Filtern...", "component.filters.selected": "ausgewählt", "component.form.option.no_suggestions": "<PERSON><PERSON>läge gefunden", "component.keyboard_shortcut.then": "dann", "component.number_field.decrement": "Verringern", "component.number_field.increment": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "component.password_input.hide_password": "Passwort ausblenden", "component.password_input.show_password": "Passwort anzeigen", "component.pick_up_address_autocomplete.placeholder": "<PERSON><PERSON><PERSON><PERSON> suchen", "component.refresh_prompt.new_version.action": "Aktualisieren", "component.refresh_prompt.new_version.description": "Neue Version verfügbar! Neu laden, um die neuesten Funktionen zu erhalten.", "component.search_input.clear": "Löschen", "component.search_input.placeholder": "Suchen...", "component.select.empty_text": "<PERSON><PERSON> für '{searchTerm}' gefunden.", "component.select.search_input_placeholder": "<PERSON><PERSON>", "component.select.search_placeholder": "Suchen...", "component.sidebar.close_sidebar": "Seitenleiste schließen", "component.sidebar.footer.environment": "Umgebung", "component.sidebar.footer.sign_out": "Abmelden", "component.sidebar.footer.stop_impersonation": "Identitätswechsel Stoppen", "component.sidebar.footer.user_profile": "Benutzerprofil", "component.sidebar.footer.version": "Version", "component.sidebar.group.administration": "Verwaltung", "component.sidebar.group.system_administration": "Systemverwaltung", "component.sidebar.group.waste_management": "Abfallwirtschaft", "component.sidebar.open_sidebar": "Seitenleiste öffnen", "component.sidebar.settings": "Einstellungen", "component.table.clear_filter": "<PERSON><PERSON>", "component.table.dynamic_view.add_view": "Ansicht hinzufügen", "component.table.dynamic_view.cannot_delete_global_default_view": "Sie können keine globale Standardansicht löschen", "component.table.dynamic_view.cannot_delete_global_view_if_not_admin": "Globale Ansichten können nur von Systemadministratoren gelöscht werden", "component.table.dynamic_view.cannot_delete_last_view": "Sie können die letzte Tabellenansicht nicht löschen", "component.table.dynamic_view.change_order": "Reihenfolge ändern", "component.table.dynamic_view.change_sorting": "Sortierung ändern", "component.table.dynamic_view.default_global_view": "Globale Standardansicht", "component.table.dynamic_view.default_view": "<PERSON><PERSON><PERSON> Standard<PERSON>icht", "component.table.dynamic_view.delete_view": "Ansicht löschen", "component.table.dynamic_view.delete_view_description": "<PERSON>d <PERSON> sic<PERSON>, dass Si<PERSON> die Ansicht '{name}' löschen möchten? Diese Aktion ist unumkehrbar.", "component.table.dynamic_view.edit_view": "Ansicht bearbeiten", "component.table.dynamic_view.manage_views": "Ansichten verwalten", "component.table.dynamic_view.reset_all_changes": "Alle Änderungen zurücksetzen", "component.table.dynamic_view.save": "Ansicht s<PERSON>ichern", "component.table.dynamic_view.save_as_default_view_for_me": "Als meine Standardansicht speichern", "component.table.dynamic_view.save_as_default_view_globally": "Als Administrator global als Standard festlegen", "component.table.dynamic_view.save_as_global_view": "Als globale Ansicht speichern, damit jeder sie verwenden kann", "component.table.dynamic_view.save_as_new": "<PERSON><PERSON>", "component.table.dynamic_view.save_as_new_disabled": "Nehmen Sie Änderungen an der Tabellenansicht vor, bevor <PERSON> sie als neue Ansicht speichern", "component.table.dynamic_view.save_filter_view": "Filteransicht speichern", "component.table.dynamic_view.settings": "Tabelleneinstellungen", "component.table.dynamic_view.shared": "Get<PERSON><PERSON>", "component.table.dynamic_view.toggle_columns": "Spalten umschalten", "component.table.dynamic_view.update": "Aktualisieren", "component.table.dynamic_view.update_filter_view": "Filteransicht aktualisieren", "component.table.dynamic_view.view_deleted": "<PERSON><PERSON><PERSON>", "component.table.dynamic_view.view_deleted_description": "Sie haben die Ansicht '{name}' <PERSON><PERSON><PERSON><PERSON>", "component.table.dynamic_view.view_name": "Name der Filteransicht", "component.table.dynamic_view.view_name_placeholder": "Beispielansicht", "component.table.dynamic_view.view_saved": "Ansicht erfolgreich gespeichert", "component.table.dynamic_view.view_saved_description": "Sie haben die Ansicht '{name}' ges<PERSON><PERSON><PERSON>", "component.table.dynamic_view.view_updated_description": "Sie haben die Ansicht '{name}' aktualisiert", "component.table.dynamic_view.views": "<PERSON><PERSON><PERSON><PERSON>", "component.table.next_page": "Nächste", "component.table.no_active_filters": "<PERSON><PERSON> aktiven <PERSON>", "component.table.no_data.description": "Derzeit sind keine Daten zur Anzeige vorhanden.", "component.table.no_data.title": "<PERSON><PERSON>", "component.table.no_results.description": "<PERSON>s gibt keine Ergebnisse, die Ihren Suchkriterien entsprechen.", "component.table.no_results.title": "<PERSON><PERSON>", "component.table.page_count": "{startIndex} - {endIndex} von {totalItems}", "component.table.previous_page": "<PERSON><PERSON><PERSON><PERSON>", "component.table.results_may_be_hidden": "Einige Ergebnisse sind möglicherweise ausgeblendet", "component.unsaved_changes_dialog.cancel": "Bearbeitung fortsetzen", "component.unsaved_changes_dialog.confirm": "<PERSON><PERSON>, ich bin sicher", "component.unsaved_changes_dialog.description": "Alle nicht gespeicherten Änderungen gehen verloren. Sind Si<PERSON> sicher, dass Si<PERSON> fortfahren möchten?", "component.unsaved_changes_dialog.title": "Nicht gespeicherte Änderungen", "component.waste_producer_autocomplete.placeholder": "Abfall<PERSON><PERSON><PERSON><PERSON> <PERSON>en", "components.form.file_upload.max_file_size_exceeded.description": "Datei \"{name}\" überschreitet die maximale Größe von {max}MB.", "components.form.file_upload.max_file_size_exceeded.title": "Dateigröße überschritten", "components.waste_inquiry.composition.components.add": "Komponente hinzufügen", "components.waste_inquiry.composition.components.add_packaging": "Verpackung hinzufügen", "components.waste_inquiry.composition.components.component.max_weight.label": "<PERSON><PERSON>", "components.waste_inquiry.composition.components.component.min_weight.label": "<PERSON><PERSON>", "components.waste_inquiry.composition.components.component_name.label": "Komponente", "components.waste_inquiry.composition.components.component_name.placeholder": "Komponentenname", "components.waste_inquiry.composition.components.has_inner_packaging.label": "Innenverpackung", "components.waste_inquiry.composition.components.loading_method.label": "Verladeverfahren", "components.waste_inquiry.composition.components.packaging.label": "Verpackung", "components.waste_inquiry.composition.components.packaging.placeholder": "Verpackungsart auswählen", "components.waste_inquiry.composition.components.packaging_group.label": "Verpackungsgruppe", "components.waste_inquiry.composition.components.packaging_size.label": "Größe", "components.waste_inquiry.composition.components.packaging_type.label": "Verpackungsart", "components.waste_inquiry.composition.components.remarks.placeholder": "Bemerkungen", "components.waste_inquiry.composition.components.stored_in.label": "<PERSON><PERSON><PERSON><PERSON> in", "components.waste_inquiry.composition.components.transport_volume.label": "Transportvolumen", "components.waste_inquiry.composition.components.un_number.label": "UN-Nummer", "components.waste_inquiry.composition.components.un_number.placeholder": "UN-Nummer", "components.waste_inquiry.composition.components.weight_per_piece.label": "Gewicht/Stück", "enum.announcement_type.informational": "Informativ", "enum.announcement_type.urgent": "Dringend", "enum.collection_requirement_option.tractor": "<PERSON><PERSON>", "enum.collection_requirement_option.tractor_trailer": "Nur Zugmaschine & Anhänger", "enum.collection_requirement_option.tractor_trailer_tank": "Zugmaschine, Anhänger & Tankcontainer", "enum.container_loading_type.chain.description": "Eine Kette zum Verladen von <PERSON>.", "enum.container_loading_type.chain.label": "<PERSON><PERSON>", "enum.container_loading_type.hook.description": "Ein Haken zum Verladen von <PERSON>.", "enum.container_loading_type.hook.label": "<PERSON>ken", "enum.container_transport_type.change_compaction_container": "Presscontainer wechseln", "enum.container_transport_type.direct_loading": "Direktverladung", "enum.container_transport_type.emptying_glass_bulb": "<PERSON><PERSON><PERSON><PERSON> leeren", "enum.container_transport_type.emptying_wheelie_bin": "<PERSON><PERSON><PERSON><PERSON><PERSON> leeren", "enum.container_transport_type.exchange": "<PERSON><PERSON><PERSON><PERSON>", "enum.container_transport_type.final_collection": "<PERSON>ab<PERSON><PERSON>", "enum.container_transport_type.first_placement": "Erstplatzierung", "enum.container_transport_type.internal_movement": "Interner Transport", "enum.container_transport_type.return": "Rückgabe", "enum.draft_invoice_status.approved_by_customer": "Vom Kunden genehmigt", "enum.draft_invoice_status.auto_approved": "Automatisch zugelassen", "enum.draft_invoice_status.internal_approved": "Intern zugelassen", "enum.draft_invoice_status.rejected_by_customer": "Vom Kunden abgelehnt", "enum.draft_invoice_status.rejected_by_indaver": "<PERSON><PERSON><PERSON><PERSON><PERSON> von Indaver", "enum.draft_invoice_status.to_be_approved_by_customer": "Vom Kunden genehmigt werden", "enum.draft_invoice_status.to_be_approved_by_indaver": "Von Indaver genehmigt werden", "enum.draft_invoice_status_short.approved": "<PERSON><PERSON><PERSON><PERSON>", "enum.draft_invoice_status_short.rejected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enum.draft_invoice_status_short.to_be_approved": "Zugelassen werden", "enum.dynamic_table_column_name.account_document_number": "Kontodokument N °", "enum.dynamic_table_column_name.account_manager": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enum.dynamic_table_column_name.account_manager_name": "Account Manager Name", "enum.dynamic_table_column_name.amount": "<PERSON><PERSON>", "enum.dynamic_table_column_name.asn": "Asn N °", "enum.dynamic_table_column_name.auto_approved_on": "Auto zugelassen auf", "enum.dynamic_table_column_name.company_name": "Name der Firma", "enum.dynamic_table_column_name.confirmed_collection_date": "Bestätigtes Abholdatum", "enum.dynamic_table_column_name.confirmed_transport_date": "Bestätigtes Transportdatum", "enum.dynamic_table_column_name.container_number": "Container-Nr.", "enum.dynamic_table_column_name.container_transport_type": "Containertransporttyp", "enum.dynamic_table_column_name.container_type": "<PERSON>tain<PERSON><PERSON>", "enum.dynamic_table_column_name.container_volume_size": "Behältervolumen/Größe", "enum.dynamic_table_column_name.contract_id": "Vertrags-ID", "enum.dynamic_table_column_name.contract_item": "Vertragsposition", "enum.dynamic_table_column_name.contract_number": "Vertrags-Nr.", "enum.dynamic_table_column_name.cost_center": "Kostenstelle", "enum.dynamic_table_column_name.currency": "Währung", "enum.dynamic_table_column_name.customer_id": "Kunden-ID", "enum.dynamic_table_column_name.customer_name": "Kundenname", "enum.dynamic_table_column_name.customer_reference": "Kundenreferenz", "enum.dynamic_table_column_name.customer_approval_by": "Customer approval by", "enum.dynamic_table_column_name.customer_approval_date": "Customer approval date", "enum.dynamic_table_column_name.danger_label1": "Gefahrenauslöser LBL 1", "enum.dynamic_table_column_name.danger_label2": "Gefahrenauslöser LBL 2", "enum.dynamic_table_column_name.danger_label3": "Gefahrenauslöser LBL 3", "enum.dynamic_table_column_name.date": "Datum", "enum.dynamic_table_column_name.date_of_request": "Anfragedatum", "enum.dynamic_table_column_name.delivery_info": "Lieferinformationen", "enum.dynamic_table_column_name.disposal_certificate_number": "Entsorgungsnachweis-Nr.", "enum.dynamic_table_column_name.due_on": "<PERSON><PERSON><PERSON><PERSON> auf", "enum.dynamic_table_column_name.end_treatment_center_id": "ID für das Ende der Behandlungszentrale", "enum.dynamic_table_column_name.end_treatment_center_name": "Name des Endbehandlungszentrums", "enum.dynamic_table_column_name.esn_number": "EN/SN n °", "enum.dynamic_table_column_name.estimated_weight_or_volume_unit": "Geschätzte Gewicht/Volumeneinheit", "enum.dynamic_table_column_name.estimated_weight_or_volume_value": "Geschätztes Gewicht/Volumen", "enum.dynamic_table_column_name.ewc_code": "AVV Nr.", "enum.dynamic_table_column_name.first_reminder_mail_status": "1. Erinnerung Mail -Status", "enum.dynamic_table_column_name.first_reminder_on": "1. <PERSON><PERSON><PERSON> an", "enum.dynamic_table_column_name.hazard_inducers": "Gefahreninduktoren", "enum.dynamic_table_column_name.inquiry_number": "Anforderungsnummer", "enum.dynamic_table_column_name.installation_name": "Installationsname", "enum.dynamic_table_column_name.invoice": "<PERSON><PERSON><PERSON><PERSON>", "enum.dynamic_table_column_name.invoice_number": "Rechnung N °", "enum.dynamic_table_column_name.is_container_covered": "<PERSON><PERSON><PERSON><PERSON>", "enum.dynamic_table_column_name.is_hazardous": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enum.dynamic_table_column_name.is_return_packaging": "Rückgabeverpackung", "enum.dynamic_table_column_name.is_transport_by_indaver": "Transport durch Indaver", "enum.dynamic_table_column_name.issued_on": "Ausgestellt auf", "enum.dynamic_table_column_name.material_analysis": "Materialanalyse", "enum.dynamic_table_column_name.material_number": "Material N °", "enum.dynamic_table_column_name.material_type": "Materialtyp", "enum.dynamic_table_column_name.name_installation": "Name der <PERSON>lage", "enum.dynamic_table_column_name.name_of_applicant": "Name des Antragstellers", "enum.dynamic_table_column_name.net_amount": "Nettomenge", "enum.dynamic_table_column_name.number": "Nr.", "enum.dynamic_table_column_name.order_number": "Bestell-Nr.", "enum.dynamic_table_column_name.packaged": "Verpackt", "enum.dynamic_table_column_name.packaging_indicator": "Verpackungsindikator", "enum.dynamic_table_column_name.packaging_remark": "Verpackungs Bemerkung", "enum.dynamic_table_column_name.packaging_type": "Verpackungstyp", "enum.dynamic_table_column_name.packing_group": "Pack. \nGruppe", "enum.dynamic_table_column_name.payer_id": "Zahler -<PERSON>", "enum.dynamic_table_column_name.payer_name": "<PERSON><PERSON><PERSON><PERSON>", "enum.dynamic_table_column_name.pick_up_address_id": "<PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON>", "enum.dynamic_table_column_name.pick_up_address_name": "Name der Anfallstelle", "enum.dynamic_table_column_name.pickup_address": "<PERSON><PERSON><PERSON><PERSON>", "enum.dynamic_table_column_name.po_number": "Po n °", "enum.dynamic_table_column_name.process_code": "Prozesscode", "enum.dynamic_table_column_name.quantity_containers": "<PERSON> <PERSON>", "enum.dynamic_table_column_name.quantity_labels": "Anzahl Labels", "enum.dynamic_table_column_name.quantity_packages": "Anzahl Verpackungen", "enum.dynamic_table_column_name.quantity_pallets": "<PERSON><PERSON><PERSON>", "enum.dynamic_table_column_name.reconciliation_number": "Versöhnung N °", "enum.dynamic_table_column_name.remarks": "Anmerkungen", "enum.dynamic_table_column_name.request_number": "Anfragenummer", "enum.dynamic_table_column_name.requested_by": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "enum.dynamic_table_column_name.requested_end_date": "Gewünschtes Enddatum", "enum.dynamic_table_column_name.requested_start_date": "Gewünschtes Startdatum", "enum.dynamic_table_column_name.sales_order": "Kundenauftrag", "enum.dynamic_table_column_name.sales_organisation_id": "Verkaufsorganisation-ID", "enum.dynamic_table_column_name.sales_organisation_name": "Name der Verkaufsorganisation", "enum.dynamic_table_column_name.second_reminder_mail_status": "2. Erinnerung Mail -Status", "enum.dynamic_table_column_name.second_reminder_on": "2. <PERSON><PERSON><PERSON> an", "enum.dynamic_table_column_name.serial_number": "Serienn °", "enum.dynamic_table_column_name.status": "Status", "enum.dynamic_table_column_name.tanker_type": "Tanker -<PERSON><PERSON>", "enum.dynamic_table_column_name.tc_number": "Tc n °", "enum.dynamic_table_column_name.tfs_number": "VVA-Nr.", "enum.dynamic_table_column_name.third_reminder_mail_status": "3. Erinnerungsstatus", "enum.dynamic_table_column_name.total_quantity_pallets": "Gesamtmengepaletten", "enum.dynamic_table_column_name.transport_mode": "Transportmodus", "enum.dynamic_table_column_name.treatment_center_name": "Name des Behandlungszentrums", "enum.dynamic_table_column_name.type": "<PERSON><PERSON>", "enum.dynamic_table_column_name.un_number": "UN-Nummer", "enum.dynamic_table_column_name.vat_amount": "Mehrwertsteuerbetrag", "enum.dynamic_table_column_name.waste_item": "Abfallposition", "enum.dynamic_table_column_name.waste_material": "Abfallmaterial", "enum.dynamic_table_column_name.waste_producer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enum.dynamic_table_column_name.waste_producer_id": "Abfallerzeuger-ID", "enum.flashpoint.high": "> 60°", "enum.flashpoint.low": "< 23°", "enum.flashpoint.medium": "23° - 60°", "enum.flashpoint.unknown": "Unbekannt / Nicht zutreffend", "enum.invoice_status.cleared": "G<PERSON><PERSON><PERSON><PERSON>", "enum.invoice_status.draft": "<PERSON><PERSON><PERSON><PERSON>", "enum.invoice_status.outstanding": "<PERSON><PERSON><PERSON>nd", "enum.invoice_status.overdue": "Überfällig", "enum.invoice_type.cancellation": "Stornierung", "enum.invoice_type.invoice": "<PERSON><PERSON><PERSON><PERSON>", "enum.invoice_type.unknown": "Unbekannt", "enum.language.de": "De<PERSON>ch", "enum.language.en": "<PERSON><PERSON><PERSON>", "enum.language.es": "Spanisch", "enum.language.fr": "Franzö<PERSON><PERSON>", "enum.language.nl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enum.legislation_and_properties.more_info_table.header.adr": "ADR", "enum.legislation_and_properties.more_info_table.header.clp": "CLP", "enum.legislation_and_properties.more_info_table.header.examples": "<PERSON><PERSON><PERSON><PERSON>", "enum.legislation_and_properties.more_info_table.header.waste_classified_as": "Abfall e<PERSON><PERSON><PERSON>t als", "enum.legislation_and_properties.more_info_table.header.waste_classified_as_or_substances": "Abfall eingestuft als ODER Stoffe enthaltend, die eingestuft sind als", "enum.legislation_and_properties.more_info_table.header.waste_containing": "Abfall enthaltend", "enum.legislation_and_properties.more_info_table.header.waste_subject_to": "Abfall unterliegt", "enum.legislation_and_properties.more_info_table.header.waste_substances_classified_as": "Abfall, der als eingestufte Stoffe enthält", "enum.legislation_and_properties.more_info_table.header.waste_substances_subject_to": "<PERSON><PERSON><PERSON>, der Stoffe enthält, die unterliegen", "enum.mail_status.not_sent": "<PERSON>cht gesendet", "enum.mail_status.sent": "Gesendet", "enum.packaging.bulk.description": "Unverpackter Abfall, der in großen Mengen transportiert wird, typischerweise in Tanks, Silos oder anderen Schüttgutbehältern.", "enum.packaging.bulk.label": "Schüttgut", "enum.packaging.packaged.description": "Ab<PERSON>, der in versie<PERSON>ten, kleineren Einheiten wie Zylindern, Fässern oder Flaschen enthalten ist.", "enum.packaging.packaged.label": "Verpackt", "enum.packaging_request_type.rental": "<PERSON><PERSON>", "enum.packaging_request_type.sales": "Verkäufe", "enum.pickup_request_status.cancelled": "<PERSON><PERSON><PERSON><PERSON>", "enum.pickup_request_status.completed": "Abgeschlossen", "enum.pickup_request_status.confirmed": "Bestätigt", "enum.pickup_request_status.draft": "<PERSON><PERSON><PERSON><PERSON>", "enum.pickup_request_status.indascan_draft": "Indascaner Entwurf", "enum.pickup_request_status.pending": "<PERSON><PERSON><PERSON><PERSON>", "enum.pickup_transport_mode.bulk_iso_tank.description": "Geben Si<PERSON> nicht mehr als 1 Vertragsposition an.", "enum.pickup_transport_mode.bulk_iso_tank.label": "Schüttgut-Tankcontainer oder ISO-Tank", "enum.pickup_transport_mode.bulk_skips_container.description": "Geben Si<PERSON> nicht mehr als 3 Vertragspositionen an.", "enum.pickup_transport_mode.bulk_skips_container.label": "Schüttgutabfall in Mulden oder Containern", "enum.pickup_transport_mode.bulk_vacuum_tankers_road_tankers.description": "Geben Si<PERSON> nicht mehr als 1 Vertragsposition an.", "enum.pickup_transport_mode.bulk_vacuum_tankers_road_tankers.label": "Schüttgutabfall im Tank-, Saugwagen", "enum.pickup_transport_mode.packaged_curtain_sider_truck.description": "Geben Si<PERSON> eine beliebige Anzahl von Vertragspositionen an", "enum.pickup_transport_mode.packaged_curtain_sider_truck.label": "Stückgutabfall im Planenwagen oder Sattelzug", "enum.pickup_transport_mode.packaging_request_order.label": "Verpackungsanforderungsreihenfolge", "enum.publish_status.archived": "<PERSON><PERSON><PERSON><PERSON>", "enum.publish_status.published": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enum.publish_status.scheduled": "<PERSON><PERSON><PERSON>", "enum.regulated_transport.no": "<PERSON><PERSON>", "enum.regulated_transport.unknown": "Unbekannt", "enum.regulated_transport.yes": "<PERSON>a", "enum.role.admin": "Admin", "enum.role.user": "<PERSON><PERSON><PERSON>", "enum.sharepoint_document_status.active": "Aktiv", "enum.sharepoint_document_status.archived": "<PERSON><PERSON><PERSON><PERSON>", "enum.sharepoint_document_view_name.bsc": "Ausgeglichene Score -Karte", "enum.sharepoint_document_view_name.contract": "Verträge", "enum.sharepoint_document_view_name.manual": "TWM -Handbuch", "enum.sharepoint_document_view_name.mastertable": "Meistertable", "enum.sharepoint_document_view_name.meetings": "Minuten", "enum.sharepoint_document_view_name.quotation": "Zitate", "enum.sharepoint_document_view_name.tfs": "TFS -Dateien", "enum.sharepoint_document_view_name.transport": "Transportdokumente", "enum.stable_temperature.ambient": "Umgebungstemperatur", "enum.stable_temperature.average": "Geben Si<PERSON> einen Durchschnitt an", "enum.stable_temperature.min_max": "G<PERSON>en Sie ein Minimum und ein Maximum an", "enum.state_of_matter.gaseous": "Gasf<PERSON><PERSON><PERSON>", "enum.state_of_matter.liquid": "Flüssig", "enum.state_of_matter.liquid_with_solids": "Flüssig mit Feststoffen", "enum.state_of_matter.no_data_available": "<PERSON><PERSON> ve<PERSON>ü<PERSON>", "enum.state_of_matter.powder": "Pulver", "enum.state_of_matter.sludgy": "Schlammig", "enum.state_of_matter.solid": "Fest", "enum.state_of_matter.viscous": "<PERSON><PERSON><PERSON>", "enum.svhc_extra_option.<_1_mg_kg": "< 1 mg/kg persistente Stoffe", "enum.svhc_extra_option.>_1_mg_kg": "> 1 mg/kg persistente Stoffe", "enum.svhc_extra_option.other": "<PERSON><PERSON>", "enum.tanker_type.road_tanker": "Straßentankwagen", "enum.tanker_type.road_tanker_pump": "Straßentankwagenpumpe", "enum.tanker_type.vacuum_tanker": "Saugwagen", "enum.waste_discharge_frequency.once_off_campaign.description": "Eine einmalige Abholung im Rahmen einer bestimmten Kampagne.", "enum.waste_discharge_frequency.once_off_campaign.label": "Einmalige Kampagne", "enum.waste_discharge_frequency.once_off_stream.description": "Ein einmaliges, nicht wiederkehrendes Entsorgungsereignis.", "enum.waste_discharge_frequency.once_off_stream.label": "<PERSON><PERSON><PERSON>", "enum.waste_discharge_frequency.regular_campaign.description": "Wiederholte Abholungen im Rahmen geplanter Kampagnen.", "enum.waste_discharge_frequency.regular_campaign.label": "Regelmäßige Kampagne", "enum.waste_discharge_frequency.regular_stream.description": "<PERSON><PERSON><PERSON>, gleichmäßige Entsorgung in festgelegten Intervallen.", "enum.waste_discharge_frequency.regular_stream.label": "Regelmä<PERSON><PERSON>", "enum.waste_inquiry_status.completed": "Abgeschlossen", "enum.waste_inquiry_status.conformity_confirmed": "Konformität bestätigt", "enum.waste_inquiry_status.draft": "<PERSON><PERSON><PERSON><PERSON>", "enum.waste_inquiry_status.in_progress": "In Bearbeitung", "enum.waste_inquiry_status.new": "<PERSON>eu", "enum.waste_inquiry_status.not_relevant": "<PERSON><PERSON>", "enum.waste_inquiry_status.offer_approved": "<PERSON><PERSON><PERSON>", "enum.waste_inquiry_status.offer_sent": "Angebot gesendet", "enum.waste_inquiry_status.rejected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enum.waste_inquiry_status.solution_defined": "<PERSON><PERSON><PERSON><PERSON> definiert", "enum.waste_legislation_option.animal_byproduct": "Tierische Nebenprodukte", "enum.waste_legislation_option.animal_byproduct.more_info_table.classified_as": "Tierische Nebenprodukte (TNP)", "enum.waste_legislation_option.animal_byproduct.more_info_table.examples": "<div><strong>Tierische Nebenprodukte und Folgeprodukte, die vom menschlichen Verzehr ausgeschlossen sind:</strong><ul><li>Futtermittel (z.B. auf der Basis von Fischmehl und verarbeitetem tierischem Protein, ...)</li><li>Organische Düngemittel und Bodenverbesserungsmittel (z.B. Gülle, Guano, verarbeitete OD/BV auf der Basis von verarbeitetem tierischem Protein, ...)</li><li>Technische Produkte (z.B. <PERSON>nahrung, Häute und Felle für Leder, Wolle, Blut zur Herstellung von Diagnosewerkzeugen, ...)</li></ul></div>", "enum.waste_legislation_option.animal_byproduct.more_info_table.subject_to": "<div><ul><li>EU-Verordnung 1069/2009</li></ul></div>", "enum.waste_legislation_option.controlled_drugs": "Betäubungsmittel", "enum.waste_legislation_option.controlled_drugs.more_info_table.classified_as": "Betäubungsmittel", "enum.waste_legislation_option.controlled_drugs.more_info_table.examples": "<div><strong>Narkot<PERSON></strong><ul><li><PERSON><PERSON><PERSON></li><li>Fentanyl</li><li>Codein</li><li>Kokain</li><br></ul><strong>Psychotropika</strong><ul><li>Benzodiazepine</li><li>Alprazolam</li><li>Xanax</li></ul></div>", "enum.waste_legislation_option.controlled_drugs.more_info_table.subject_to": "<div><ul><li>Einzige Suchtstoffkonvention von 1961</li><li>Konvention über psychotrope Substanzen von 1971</li><li>Oder unterliegt der lokalen Gesetzgebung für Betäubungsmittel.</li></ul></div>", "enum.waste_legislation_option.cwc": "CWÜ (Chemiewaffenübereinkommen)", "enum.waste_legislation_option.cwc.more_info_table.classified_as": "Chemische Waffen", "enum.waste_legislation_option.cwc.more_info_table.examples": "<div><strong>Liste 1</strong><ul><li><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>f</li><li><PERSON><PERSON></li><li><PERSON>rin</li><li>VX</li><br></ul><strong>Liste 2</strong><ul><li>Methylphosphonsäuredichlorid</li><li>Arsentrichlorid</li><br></ul><strong>Liste 3</strong><ul><li>Phosphoroxychlorid</li><li>Phosphortrichlorid</li><li>Cyanwasserstoff</li><li>Triethanolamin</li><li>Thionylchlorid</li><br></ul></div>", "enum.waste_legislation_option.cwc.more_info_table.subject_to": "Chemiewaffenübereinkommen (CWÜ) und erwähnt in: Liste 1, oder Liste 2, oder Liste 3", "enum.waste_legislation_option.drug_precursor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enum.waste_legislation_option.drug_precursor.more_info_table.classified_as": "Drogenausgangsstoffe", "enum.waste_legislation_option.drug_precursor.more_info_table.examples": "<div><strong>Anhang 1</strong><ul><li>Ergotamin</li><li>Ephedrin</li><li><PERSON><PERSON><PERSON></li></ul><br><strong>Anhang 2</strong><ul><li>Kaliumpermanganat</li><li>Phenylessigsäure</li><li>Essigsäureanhydrid</li><li>Anth<PERSON>lsä<PERSON></li><li><PERSON><PERSON><PERSON></li></ul></div>", "enum.waste_legislation_option.drug_precursor.more_info_table.subject_to": "<div><ul><li>EU-Verordnung 273/2004</li></ul><p>Mi<PERSON> Schwerpunkt auf Stoffen der Anhänge 1 und 2 (Wirkstoffe).</p></div>", "enum.waste_legislation_option.hg_containing": "Hg-<PERSON><PERSON> Abfall", "enum.waste_legislation_option.hg_containing.more_info_table.containing": "<PERSON><PERSON><PERSON><PERSON>", "enum.waste_legislation_option.hg_containing.more_info_table.examples": "<div><ul><li>Abfälle mit relevanten Hg-Konzentrationen</li><li><PERSON><PERSON>dunge<PERSON></li><li>Quecksilber zugesetzte Artikel</li><li>Metallisches Quecksilber</li></ul></div>", "enum.waste_legislation_option.hg_containing.more_info_table.substances_subject_to": "<div><ul><li>EU-Verordnung 1102/2008</li><li>Minamata-Konvention</li></ul><p><PERSON><PERSON><PERSON><PERSON><PERSON>, die Quecksilber in Konzentrationen enthalten, die bei der Verarbeitung besondere Aufmerksamkeit erfordern, um die Emission von Quecksilber in die Umwelt zu vermeiden.</p></div>", "enum.waste_legislation_option.infectious_waste": "Infektiöser Abfall", "enum.waste_legislation_option.infectious_waste.more_info_table.adr": "Klasse 6.2, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enum.waste_legislation_option.infectious_waste.more_info_table.classified_as": "Infektiös", "enum.waste_legislation_option.infectious_waste.more_info_table.clp": "Keine spezifische Klassifizierung", "enum.waste_legislation_option.infectious_waste.more_info_table.examples": "<div><strong><PERSON><PERSON><PERSON><PERSON><PERSON>, die potenziell mit Viren, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> kontaminiert sind, die eine menschliche/tierische Krankheit verursachen können, z.B.</strong><ul><li><PERSON><PERSON><PERSON><PERSON> (Clostridium botulinum)</li><li><PERSON><PERSON><PERSON> (Bacillus anthracis)</li><li>Corona COVID-19</li><li>Legionellen</li><li>Influenza</li><li>Ebola</li></ul></div>", "enum.waste_legislation_option.none": "<PERSON><PERSON>", "enum.waste_legislation_option.ozon_depleting_substance": "Ozonabbauende Stoffe und fluorierte Treibhausgase", "enum.waste_legislation_option.ozon_depleting_substance.more_info_table.clp": "H420 (ODS-Stoffe)", "enum.waste_legislation_option.ozon_depleting_substance.more_info_table.examples": "<div><strong>EU-Verordnung 1005/2009</strong><ul><li>Fluorchlorkohlenwasserstoffe (FCKW)</li><li>Teilhalogenierte Fluorchlorkohlenwasserstoffe (HFCKW)</li><li>Te<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></li><li>Methylbromid</li><li>Halone</li></ul><br><strong>EU-Verordnung 517/2014</strong><ul><li>Fluoriert<PERSON> Kohlenwasserstoffe, perfluorierte Kohlenwasserstoffe</li><li>Schwefelhexafluorid</li><li>Stickstofftrifluorid</li></ul></div>", "enum.waste_legislation_option.ozon_depleting_substance.more_info_table.substances_classified_as": "Ozonabbauende Stoffe und/oder fluorierte Treibhausgase", "enum.waste_legislation_option.ozon_depleting_substance.more_info_table.substances_subject_to": "<div><ul><li>EU-Verordnung 1005/2009</li><li>EU-Verordnung 517/2014</li></ul></div>", "enum.waste_legislation_option.radioactive": "Radioaktive Abfälle und NORM (natürlich vorkommende radioaktive Materialien)", "enum.waste_legislation_option.radioactive.more_info_table.adr": "Nicht immer anwendbar", "enum.waste_legislation_option.radioactive.more_info_table.classified_or_substances": "Radioaktiv und/oder natürlich vorkommendes radioaktives Material (NORM)", "enum.waste_legislation_option.radioactive.more_info_table.examples": "<div><strong>Radioaktiv</strong><ul><li>Thoriumnitrat</li><li>Uranylacetat</li><li>Cäsium 131</li><li>Tritium</li></ul><br><strong>NORM</strong><ul><li>Zirkoniumoxid</li><li>Blei-121</li></ul></div>", "enum.waste_legislation_option.radioactive.more_info_table.subject_to": "Richtlinie 2013/59/Euratom (oder durch lokale Gesetzgebung definiert): Abfälle mit Strahlungsaktivität über den Aktivitätswerten für die Freigabe gemäß Anhang VII.", "enum.waste_legislation_option.svhc": "SVHC (Besonders besorgniserregende Stoffe)", "enum.waste_legislation_option.svhc.more_info_table.examples": "<div><strong><PERSON><PERSON><PERSON>, die in REACH (EU-Verordnung 1907/2006) erwähnt sind:</strong><ul><li><PERSON><PERSON><PERSON><PERSON> (Anhang XIV)</li><li>Beschränkung (Anhang XVII)</li><li>Kandidatenliste</li><li>Hexabromcyclododecan</li><li>Cd/As/Pb-Verbindungen</li><li>Natriumdichromat</li><li>1,2-Dichlorethan</li><li>Dibutylphthalat</li><li>Asbest</li><li>Benzol</li><li>GEN-X</li><li>PFOA</li><li>PAK</li></ul></div>", "enum.waste_legislation_option.svhc.more_info_table.substances_classified_as": "SVHC (Besonders besorgniserregende Stoffe)", "enum.waste_legislation_option.svhc.more_info_table.substances_subject_to": "<div><strong>Kriterien Art. 57 von REACH*:</strong><ul><li>Karzinogen Kat. 1A oder 1B</li><li>Keimzellmutagen Kat. 1A oder 1B</li><li>Reproduktionstoxizität Kat. 1A oder 1B</li><li>PBT (persistent, bioakkumulierbar und toxisch)</li><li>vPvB (sehr persistent und sehr bioakkumulierbar)</li><li><PERSON><PERSON><PERSON>, für die wissenschaftliche Beweise für wahrscheinliche schwerwiegende Auswirkungen auf die menschliche Gesundheit oder die Umwelt vorliegen, die zu einem gleichwertigen Besorgnisgrad wie bei den anderen unter den Punkten (a) bis (e) aufgeführten Stoffen führen. (z.B. endokrin wirksame Eigenschaften)</li></ul></div>", "enum.waste_legislation_option.svhc.more_info_table.substances_subject_to_note": "* Niederlande: ZZS-Verordnung", "enum.waste_loading_method.gravitational.description": "Abfall wird durch Schwerkraft verladen.", "enum.waste_loading_method.gravitational.label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enum.waste_loading_method.pump_from_customer.description": "Abfall wird vom Kunden gepumpt.", "enum.waste_loading_method.pump_from_customer.label": "Pumpe vom Kunden", "enum.waste_loading_method.pump_from_haulier.description": "Abfall wird vom Spediteur gepumpt.", "enum.waste_loading_method.pump_from_haulier.label": "Pumpe vom Spediteur", "enum.waste_loading_type.before_waste_collection.description": "Abfall wird vor der Abfallsammlung verladen.", "enum.waste_loading_type.before_waste_collection.label": "Vor der Abfallsammlung", "enum.waste_loading_type.on_waste_collection.description": "Abfall wird bei der Abfallsammlung verladen.", "enum.waste_loading_type.on_waste_collection.label": "Bei der Abfallsammlung", "enum.waste_measurement_unit.kg": "KG", "enum.waste_measurement_unit.m3": "M3", "enum.waste_measurement_unit.pc": "ST", "enum.waste_measurement_unit.to": "TO", "enum.waste_measurement_unit.yd3": "Yd3", "enum.waste_packaging.asf": "Asf", "enum.waste_packaging.asp": "<PERSON><PERSON>", "enum.waste_packaging.big_bag": "Big Bag", "enum.waste_packaging.cardboard_box": "<PERSON><PERSON>", "enum.waste_packaging.ibc": "IBC", "enum.waste_packaging.metal_drum": "Metallfass", "enum.waste_packaging.other": "<PERSON><PERSON>", "enum.waste_packaging.oversized_drum": "Übergrößes Fass", "enum.waste_packaging.plastic_drum": "Kunststofffass", "enum.waste_packaging_size.not_applicable": "N/A", "enum.waste_packaging_size.one": "I", "enum.waste_packaging_size.three": "III", "enum.waste_packaging_size.two": "II", "enum.waste_ph.high": "4 - 10", "enum.waste_ph.low": "< 2", "enum.waste_ph.medium": "2 - 4", "enum.waste_ph.very_high": "> 10", "enum.waste_property_option.explosive": "Explosive Abfälle (ADR-Klasse 1) und desensibilisierte Explosivstoffe", "enum.waste_property_option.explosive.more_info_table.adr_2": "Klasse 3 und 4.1 mit Klassifizierungscode D und DT", "enum.waste_property_option.explosive.more_info_table.classified_as_1": "Explosivstoffe", "enum.waste_property_option.explosive.more_info_table.classified_as_2": "Desensibilisierte Explosivstoffe", "enum.waste_property_option.explosive.more_info_table.clp_1": "<div>H200<br>H201<br>H202<br>H203<br>H204<br>H205</div>", "enum.waste_property_option.explosive.more_info_table.clp_2": "<div>H206<br>H207<br>H208</div>", "enum.waste_property_option.explosive.more_info_table.examples_1": "<div><ul><li>Ammoniumnitrat (explosiv)</li><li>Trinitrotoluol</li><li>Nitrocellulose</li><li>Nitroglycerin</li><li>Pikrinsäure</li><li>Feuerwerk</li><li>Munition</li></ul></div>", "enum.waste_property_option.explosive.more_info_table.examples_2": "<div><ul><li>Nitrocellulose mit mindestens 25% Wasser</li><li>Nitroglycerin in Alkohol</li><li>Pikrins<PERSON><PERSON> in Wasser</li><li>ISDN mit Laktose</li></ul></div>", "enum.waste_property_option.gaseous": "Gasf<PERSON><PERSON><PERSON> Abfall", "enum.waste_property_option.gaseous.more_info_table.adr": "Klasse 2: <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, oxi<PERSON><PERSON> und korrosiv)", "enum.waste_property_option.gaseous.more_info_table.classified_as": "Gasf<PERSON><PERSON><PERSON>", "enum.waste_property_option.gaseous.more_info_table.clp": "<div>H220<br>H221<br>H270</div>", "enum.waste_property_option.gaseous.more_info_table.examples": "<div><ul><li>LPG-artiger Abfall</li><li>Vinylchlorid</li><li>Phosphin</li><li>Isobuten</li><li>Ammoniak</li><li>Chlor</li><li><PERSON><PERSON><PERSON></li><li><PERSON></li></ul></div>", "enum.waste_property_option.high_acute_toxic": "<PERSON>b<PERSON>, der als hoch akut toxisch (T+) eingestuft ist", "enum.waste_property_option.high_acute_toxic.more_info_table.adr": "<div>Klasse 6.1<br>PG I und II</div>", "enum.waste_property_option.high_acute_toxic.more_info_table.classified_as": "<PERSON><PERSON> akut toxisch (T+)", "enum.waste_property_option.high_acute_toxic.more_info_table.clp": "<div>H300<br>H310<br>H330</div>", "enum.waste_property_option.high_acute_toxic.more_info_table.examples": "<div><ul><li>Quecksilberverbindungen</li><li>Cyanwasserstoff</li><li>Fluorwasserstoff</li><li>Natriumcyanid</li><li>Dinitrobenzol</li><li>Bleialkyle</li><li><PERSON><PERSON><PERSON></li></ul></div>", "enum.waste_property_option.none": "<PERSON><PERSON>", "enum.waste_property_option.peroxide": "Peroxid (organisch und anorganisch) oder selbstzersetzlicher Abfall", "enum.waste_property_option.peroxide.more_info_table.adr_1": "Klasse 5.2", "enum.waste_property_option.peroxide.more_info_table.adr_2": "Klasse 4.1", "enum.waste_property_option.peroxide.more_info_table.adr_3": "Klasse 5.1", "enum.waste_property_option.peroxide.more_info_table.classified_as_or_substances_1": "Organisches Peroxid", "enum.waste_property_option.peroxide.more_info_table.classified_as_or_substances_2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enum.waste_property_option.peroxide.more_info_table.classified_as_or_substances_3": "Wasserstoffperoxid", "enum.waste_property_option.peroxide.more_info_table.clp_1": "<div>H240<br>H241<br>H242</div>", "enum.waste_property_option.peroxide.more_info_table.clp_2": "<div>H240<br>H241<br>H242</div>", "enum.waste_property_option.peroxide.more_info_table.clp_3": "H271", "enum.waste_property_option.peroxide.more_info_table.examples": "<div><ul><li>AIBN (Azobisisobutyronitril)</li><li>Di-tert-butylperoxid</li><li>Wasserstoffperoxid</li><li>Dilauroylperoxid</li><li>Benzoylperoxid</li></ul></div>", "enum.waste_property_option.polymerisation_sensitive": "Polymerisationsempfindlicher Abfall", "enum.waste_property_option.polymerisation_sensitive.more_info_table.adr_1": "<PERSON>ine spezifische Klassifizierung.<br><PERSON><PERSON><PERSON> mit chemischer Stabilisierung sind mit Sondervorschrift 386 gekennzeichnet.", "enum.waste_property_option.polymerisation_sensitive.more_info_table.adr_2": "Keine spezifische Klassifizierung", "enum.waste_property_option.polymerisation_sensitive.more_info_table.classified_as_or_substances_1": "Ungesättigte Monomere", "enum.waste_property_option.polymerisation_sensitive.more_info_table.classified_as_or_substances_2": "Oxirane", "enum.waste_property_option.polymerisation_sensitive.more_info_table.clp_1": "Keine spezifische Klassifizierung", "enum.waste_property_option.polymerisation_sensitive.more_info_table.clp_2": "Keine spezifische Klassifizierung", "enum.waste_property_option.polymerisation_sensitive.more_info_table.examples": "<div><ul><li>Methacrylat-Monomere</li><li>Acrylsäure</li><li>Acrylnitril</li><li>Acrylat</li><li>Styrol</li><li>MDI</li><li>TDI</li></ul><br><ul><li>Epichlorhydrin</li></ul><br><ul><li>Propylenoxid</li>", "enum.waste_property_option.pyrophoric": "Pyrophorer Abfall oder selbsterhitzungsfähige Stoffe oder leicht entzündbare Flüssigkeiten", "enum.waste_property_option.pyrophoric.more_info_table.adr_1": "Klasse 4.2", "enum.waste_property_option.pyrophoric.more_info_table.adr_2": "Klasse 3, VPI", "enum.waste_property_option.pyrophoric.more_info_table.classified_as_1": "Pyrophor", "enum.waste_property_option.pyrophoric.more_info_table.classified_as_2": "Leicht entzündbare Flüssigkeiten", "enum.waste_property_option.pyrophoric.more_info_table.clp_1": "<div>H250<br>H251<br>H252</div>", "enum.waste_property_option.pyrophoric.more_info_table.clp_2": "H224", "enum.waste_property_option.pyrophoric.more_info_table.examples_1": "<div><ul><li><PERSON><PERSON>r Phosphor</li><li>Trichlorsilan</li><li>Metallalkyle</li></ul></div>", "enum.waste_property_option.pyrophoric.more_info_table.examples_2": "<div><ul><li>Slop Isopren</li><li>Diethylether</li><li>Isopentan</li></ul></div>", "enum.waste_property_option.reactive_with_f_gas": "Ab<PERSON>, der mit Wasser/Luft/Säure/Base unter Bildung von F-Gas (z.B. H2, Ethan, ...) reagiert", "enum.waste_property_option.reactive_with_f_gas.more_info_table.adr": "Klasse 4.3", "enum.waste_property_option.reactive_with_f_gas.more_info_table.classified_as_or_substances": "Reaktiv mit Wasser/Luft/Säure/Base unter Bildung von F-Gas*", "enum.waste_property_option.reactive_with_f_gas.more_info_table.clp": "<div>H260<br>H261</div>", "enum.waste_property_option.reactive_with_f_gas.more_info_table.examples": "<div><ul><li>Hydride (Natrium-/Kaliumborhydrid, Lithiumaluminiumhydrid)</li><li>Alkalimetalle (Na, K,...)</li><li>Metalle (Mg, Al,...)</li><li>Metallalkyle</li></ul></div>", "enum.waste_property_option.reactive_with_f_gas.more_info_table.examples_note": "*Beispiele für F-Gase: <PERSON>2, <PERSON>,...", "enum.waste_property_option.reactive_with_t_gas": "<PERSON>b<PERSON>, der mit Wasser/Luft/S<PERSON>ure/Base unter Bildung eines T-Gases (z.B. HCl/Cl2, HCN, H2S, NH3, NOx, PH3, ...) reagiert", "enum.waste_property_option.reactive_with_t_gas.more_info_table.adr": "Keine spezifische Klassifizierung", "enum.waste_property_option.reactive_with_t_gas.more_info_table.classified_as_or_substances": "Reaktiv mit Wasser/Luft/Säure/Base unter Bildung von T-Gas*", "enum.waste_property_option.reactive_with_t_gas.more_info_table.clp": "<div>H014<br>H029<br>H031<br>H032</div>", "enum.waste_property_option.reactive_with_t_gas.more_info_table.examples": "<div><ul><li>Phosphidverbindungen (Aluminiumphosphid, Calciumphosphid, Magnesiumphosphid,...)</li><li>Säurechloride (Thionylchlorid, Phosphoroxychlorid, Phosphortrichlorid, Titantetrachlorid,...)</li><li>Hypohalogenide (Natriumhypochlorit,...) </li><li>Cyanidverbindungen</li><li>Sulfidverbindungen</li><li>Ammoniumsalze</li></ul></div>", "enum.waste_property_option.reactive_with_t_gas.more_info_table.examples_note": "*Beispiele für T-Gase: HCl/Cl2, HCN, H2S, NH3, NOx, PH3,...", "enum.waste_property_option.strong_oxidizing": "<PERSON> oxidier<PERSON>", "enum.waste_property_option.strong_oxidizing.more_info_table.adr": "Klasse 5.1", "enum.waste_property_option.strong_oxidizing.more_info_table.classified_as_or_substances": "<PERSON> oxidier<PERSON>", "enum.waste_property_option.strong_oxidizing.more_info_table.clp": "H270<br>H271<br>H272", "enum.waste_property_option.strong_oxidizing.more_info_table.examples": "<div><ul><li>Kaliumpermanganat</li><li>Chrom(VI)-trioxid</li><li>Ammoniumnitrat</li><li>Perchlorate</li><li>Chlorate</li></ul></div>", "enum.waste_property_option.thermal_unstable": "Thermisch instabiler Abfall", "enum.waste_property_option.thermal_unstable.more_info_table.adr": "Keine spezifische Klassifizierung", "enum.waste_property_option.thermal_unstable.more_info_table.classified_as_or_substances": "Thermodynamisch instabil", "enum.waste_property_option.thermal_unstable.more_info_table.clp": "Keine spezifische Klassifizierung", "enum.waste_property_option.thermal_unstable.more_info_table.examples": "<div><ul><li>Organische Sulfoxide</li><li>Diazo/Diazonium</li><li>Acrylsäure</li><li>Isocyanat</li><li>Hydrazin</li><li>N-Oxide</li><li>Alkine</li><li>Alkene</li><li>Nitroco</li><li>N-Nitro</li></ul></div>", "enum.waste_stored_in.drums.description": "Fässer zur Lagerung von <PERSON>fall.", "enum.waste_stored_in.drums.label": "<PERSON><PERSON><PERSON>", "enum.waste_stored_in.ibcs.description": "IBCs zur Lagerung von <PERSON>.", "enum.waste_stored_in.ibcs.label": "IBCs", "enum.waste_stored_in.other.description": "Andere Abfalllagerung.", "enum.waste_stored_in.other.label": "<PERSON><PERSON>", "enum.waste_stored_in.storage_tank.description": "Lagertank zur Lagerung von <PERSON>.", "enum.waste_stored_in.storage_tank.label": "Lagertank", "enum.waste_stored_in.tank_container.description": "Tankcontainer zur Lagerung von A<PERSON>fall.", "enum.waste_stored_in.tank_container.label": "Tankcontainer", "enum.waste_transport_in.no_preference.description": "Keine Präferenz für den Abfalltransport.", "enum.waste_transport_in.no_preference.label": "<PERSON><PERSON>", "enum.waste_transport_in.other.description": "Anderer Abfalltransport.", "enum.waste_transport_in.other.label": "<PERSON><PERSON>", "enum.waste_transport_in.tank_container.description": "Ein Container für den Transport von Abfall.", "enum.waste_transport_in.tank_container.label": "Tankcontainer", "enum.waste_transport_in.tank_trailer.description": "Ein Anhänger für den Transport von Abfall.", "enum.waste_transport_in.tank_trailer.label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "enum.waste_transport_type.container.description": "Ein Container für den Abfalltransport.", "enum.waste_transport_type.container.label": "Container", "enum.waste_transport_type.other.description": "Andere Art des Abfalltransports.", "enum.waste_transport_type.other.label": "<PERSON><PERSON>", "enum.waste_transport_type.rel_truck.description": "Ein LKW für den Transport von Abfall.", "enum.waste_transport_type.rel_truck.label": "REL/REF LKW", "enum.waste_transport_type.skip.description": "Mulden-Abfalltransport.", "enum.waste_transport_type.skip.label": "<PERSON><PERSON>", "enum.waste_transport_type.tripper_truck.description": "Ein LKW für den Transport von Abfall.", "enum.waste_transport_type.tripper_truck.label": "Ki<PERSON>-LKW", "error.bad_request.description": "Etwas ist schiefgelaufen. Bitte versuchen Sie es erneut.", "error.bad_request.title": "Ungültige Anfrage", "error.default_error.description": "Bitte versuchen Sie es später erneut.", "error.default_error.title": "Etwas ist schiefgelaufen", "error.forbidden.description": "<PERSON>e haben keine Berechtigung, auf diese Ressource zuzugreifen.", "error.forbidden.title": "Verboten", "error.internal_server_error.description": "Bitte versuchen Sie es später erneut.", "error.internal_server_error.title": "<PERSON><PERSON>", "error.invalid_form_input.description": "Bitte überprüfen Sie die markierten Felder und versuchen Sie es erneut.", "error.invalid_form_input.title": "Ungültiges Formular", "error.resource_not_found.description": "Die angeforderte Ressource konnte nicht gefunden werden.", "error.resource_not_found.title": "Ressource nicht gefunden", "error.unauthorized.description": "<PERSON>e sind nicht berechtigt, auf diese Ressource zuzugreifen.", "error.unauthorized.title": "Nicht autorisiert", "error.validation_error.description": "Bitte überprüfen Sie das Formular auf Fehler.", "error.validation_error.title": "Validierungsfehler", "form.fields.email": "E-Mail", "form.fields.password": "Passwort", "form.save_changes": "Änderungen speichern", "impersonation.warning.continue": "Fortfahren", "impersonation.warning.description": "Sie waren 5 Minuten lang inaktiv, w<PERSON><PERSON><PERSON> <PERSON><PERSON> {user} verkörpert haben. Möchten Sie fortfahren oder die Verkörperung stoppen?", "impersonation.warning.stop": "Verkörperung stoppen", "impersonation.warning.title": "Immer noch verkörpernd", "module.admin.overview.title": "Systemverwaltung", "module.auth.legal_links.disclaimer.paragraph_1": "The Indaver Portal and all subordinate systems (a.o. the Indaver Customer Zone, the Indaver Supplier Zone, the Indaver E-reporting...) and documents were developed with the best care possible. However, Indaver cannot be held responsible for the proper description of it, nor for the proper functioning of each of the functionalities.", "module.auth.legal_links.disclaimer.paragraph_2": "The Indaver Portal and the subordinate systems and documentation are property of Indaver and may not be copied or be disclosed to third parties or made available, in any way and any medium without the prior written approval of Indaver.", "module.auth.legal_links.disclaimer.paragraph_3": "Indaver is in this respect not responsible for any loss of data that were entered, nor for any incorrect processing by the software available or for the consequences arising from it.", "module.auth.legal_links.disclaimer.paragraph_4": "Indaver provides no warranties regarding the availability or functionality of the Indaver Portal and the subordinate systems and documents.", "module.auth.legal_links.disclaimer.paragraph_5": "Indaver is not responsible for any damage to the IT systems (hardware or software) of the user whether directly related or not to the use of the Indaver Portal.", "module.auth.legal_links.disclaimer.paragraph_6": "The user commits himself to use the Indaver Portal only in connection with the proposed objectives and refrain from this for any other use, either for himself or for others to use, make available, copy or modify.", "module.auth.legal_links.disclaimer.paragraph_7": "Indaver reserves at all times the right to deny access to the Indaver Portal, either individually or collectively, in particular for breaches of these provisions set as access conditions, or even as a precaution against damage to the systems of Indaver, regardless if these are related to the use of the system by the user.", "module.auth.legal_links.disclaimer.paragraph_8": "By the mere use of the Indaver Portal, the user is considered to have accepted the conditions.", "module.auth.login.customer_zone.label": "Customer Zone", "module.auth.login.description": "Melden Sie sich an, um zur Kundenzone zu gelangen.", "module.auth.login.error": "<PERSON><PERSON> ist ein Fehler aufgetreten. Versuchen Sie es später erneut.", "module.auth.login.error.description": "<PERSON><PERSON> ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut.", "module.auth.login.error.title": "Etwas ist schiefgelaufen", "module.auth.login.leading_text": "Ihr sicheres Portal zur Verwaltung Ihrer Abfälle", "module.auth.login.page_title": "Anmelden", "module.auth.login.sign_in": "Anmelden", "module.auth.login.title": "Willkommen!", "module.auth.login.visit_website": "Indaver is leading in sustainable", "module.auth.login.waste_management.label": "Waste management", "module.auth.roles.error.description": "<PERSON><PERSON> sich an Ihren Administrator, um Ihre Rollen zu überprüfen.", "module.auth.roles.error.title": "Sie haben keine zugewiesene Rolle", "module.certificate.overview.title": "Zertifikate", "module.contract.overview.bulk.download_pdf": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "module.contract.overview.bulk.plan": "Abholung planen", "module.contract.overview.bulk.plan_error.different_waste_producers": "Alle ausgewählten Verträge müssen denselben Müllhersteller haben, um eine Abholanforderung zu starten", "module.contract.overview.bulk.plan_error.no_customer": "<PERSON>s scheint ein Fehler bei der Kunden -ID der ausgewählten Elemente zu geben.", "module.contract.overview.title": "Verträge", "module.dashboard.features.news_detail.more_news": "<PERSON><PERSON><PERSON> Na<PERSON>", "module.dashboard.features.overview.announcements.title": "Ankündigungen", "module.dashboard.features.overview.greeting.afternoon": "Guten Tag", "module.dashboard.features.overview.greeting.evening": "<PERSON><PERSON><PERSON>", "module.dashboard.features.overview.greeting.morning": "<PERSON><PERSON><PERSON>", "module.dashboard.features.overview.news.no_news": "Derzeit sind keine Nachrichtenartikel verfügbar", "module.dashboard.features.overview.news.title": "Nachrichten", "module.dashboard.features.overview.newsletter.email": "E-Mail-Adresse", "module.dashboard.features.overview.newsletter.subscribe": "Abonnieren", "module.dashboard.features.overview.newsletter.subscribe_success.description": "Sie haben unseren Newsletter erfolgreich abonniert.", "module.dashboard.features.overview.newsletter.subscribe_success.title": "Vielen Dank!", "module.dashboard.features.overview.newsletter.title": "Newsletter", "module.dashboard.overview.newsletter.title": "Bleiben Sie auf dem Laufenden! Abonnieren Sie unseren Newsletter, indem Sie unten Ihre E-Mail-Adresse eingeben.", "module.dashboard.overview.page_title": "Dashboard", "module.dashboard.overview.title": "Dashboard", "module.document.overview.columns.action_date": "Aktionsdatum", "module.document.overview.columns.applicable_from": "<PERSON><PERSON><PERSON><PERSON> von", "module.document.overview.columns.applicable_until": "Anwendbar bis", "module.document.overview.columns.document_name": "Dokumentname", "module.document.overview.columns.status": "Status", "module.document.overview.columns.type_tfs": "<PERSON><PERSON>en <PERSON>e tfs ein", "module.document.overview.columns.waste_producer": "Abfallproduzent", "module.document.overview.filters.customer": "Kunde", "module.document.overview.filters.status": "Status", "module.document.overview.filters.waste_producer": "Abfallproduzenten", "module.document.overview.filters.year": "<PERSON><PERSON><PERSON>", "module.document.overview.title": "Meine Dokumente", "module.document.overview.tooltip": "Wir haben alle Dokumente im Zusammenhang mit Ihrem TWM -Paket zusammengefasst und Dienste bereitgestellt. \nWenn Sie kein bestimmtes Dokument finden, z<PERSON><PERSON><PERSON>, <PERSON>hren vertrauenswürdigen Kontakt bei Indaver anzurufen.", "module.guidance_letter.overview.title": "Begleitschreiben", "module.guidance_letters.overview.table.download_attachment": "Download attachment", "module.guidance_letters.overview.table.download_preview": "<PERSON><PERSON><PERSON><PERSON>", "module.guidance_letters.overview.table.download_print": "Zum Drucken herunterladen", "module.invoice.create": "Rechnung erstellen", "module.invoice.create.title": "Rechnung erstellen", "module.invoice.detail.amount": "Betrag", "module.invoice.detail.edit_invoice": "Rechnung bearbeiten", "module.invoice.detail.files": "<PERSON><PERSON>", "module.invoice.detail.no_certificate_available": "<PERSON><PERSON> verfü<PERSON>", "module.invoice.detail.no_customer_reference": "<PERSON><PERSON>", "module.invoice.detail.no_document_available": "<PERSON>in <PERSON> verfügbar", "module.invoice.detail_title": "Rechnungsdetail", "module.invoice.info": "Rechnungsinformationen", "module.invoice.label.plural": "Re<PERSON><PERSON>ngen", "module.invoice.overview.status": "Status", "module.invoice.overview.tab.all": "Alle", "module.invoice.overview.tab.approved": "<PERSON><PERSON><PERSON><PERSON>", "module.invoice.overview.tab.cleared": "G<PERSON><PERSON><PERSON><PERSON>", "module.invoice.overview.tab.draft": "Entwürfe", "module.invoice.overview.tab.open": "<PERSON>en", "module.invoice.overview.tab.outstanding": "<PERSON><PERSON><PERSON>nd", "module.invoice.overview.tab.overdue": "Überfällig", "module.invoice.overview.tab.paid": "Be<PERSON>hlt", "module.invoice.overview.tab.proforma": "<PERSON><PERSON><PERSON>", "module.invoice.overview.tab.rejected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "module.invoice.overview.tab.submitted": "Eingereicht", "module.invoice.overview.tab.to_be_approved": "Zugelassen werden", "module.invoice.overview.title": "Re<PERSON><PERSON>ngen", "module.invoice.title": "<PERSON><PERSON><PERSON><PERSON>", "module.invoice.unknown": "Unbekannte Rechnung", "module.invoice.update.title": "Rechnung aktualisieren", "module.invoice.uuid": "<PERSON><PERSON><PERSON><PERSON>", "module.invoices.review.approve_description": "Optional eine Bemerkung und/oder eine PO -Nummer angeben.", "module.invoices.review.approve_subtitle": "Rechnung genehmigen", "module.invoices.review.approve_title": "Sind <PERSON><PERSON> sicher, dass Sie die Rechnung `{invoiceNumber}` genehmigen möchten?", "module.invoices.review.fields.approve_reason": "Grund der Genehmigung", "module.invoices.review.fields.comment": "Bemerkung", "module.invoices.review.fields.po_number": "PO -Nummer", "module.invoices.review.fields.reject_reason": "Grund der Ablehnung", "module.invoices.review.reject_description": "Bitte geben Si<PERSON> eine Bemerkung, die den Grund der Ablehnung angibt", "module.invoices.review.reject_subtitle": "<PERSON><PERSON><PERSON><PERSON>", "module.invoices.review.reject_title": "Sind Sie sich sicher, dass Sie die Rechnung `{invoiceNumber}` ablehnen möchten?", "module.invoices.review.submit_approval": "Einreichen und genehmigen", "module.invoices.review.submit_rejection": "Einreichen und ablehnen", "module.language_management.overview.title": "Übersetzungen", "module.news.announcement.delete.success_message.description": "Ihre Ankündigung wurde erfolgreich gelöscht und ist nicht mehr sichtbar.", "module.news.announcement.delete.success_message.title": "Ankündigung gelöscht", "module.news.announcement.update.title": "Ankündigung bearbeiten", "module.news.announcement_overview.title": "Ankündigungen", "module.news.announcement_overview.urgent_announcement": "Dringende Ankündigung", "module.news.announcements.create.page_title": "Neue Ankündigung", "module.news.announcements.update.return_to_overview": "Alle Ankündigungen", "module.news.article.create.page_title": "<PERSON><PERSON><PERSON> Artikel", "module.news.article.delete.success_message.description": "Ihr Artikel wurde erfolgreich gelöscht und ist nicht mehr sichtbar.", "module.news.article.delete.success_message.title": "<PERSON><PERSON><PERSON>", "module.news.article.fields.end_date": "Bis", "module.news.article.fields.publishing_date": "Veröffentlichungsdatum", "module.news.article.fields.start_date": "<PERSON>", "module.news.article.update.page_title": "<PERSON><PERSON><PERSON> bearbeiten", "module.news.delete.success_message.description": "Ihr Artikel wurde erfolgreich gelöscht und ist nicht mehr sichtbar.", "module.news.delete.success_message.title": "<PERSON><PERSON><PERSON>", "module.news.overview.long_title": "Artikel", "module.news.overview.new_announcement": "Neue Ankündigung", "module.news.overview.new_article": "<PERSON><PERSON><PERSON> Artikel", "module.news.overview.no_active_filters": "<PERSON><PERSON> Filter aktiv", "module.news.overview.table.author": "Autor", "module.news.overview.table.ends_on": "Endet am", "module.news.overview.table.published_on": "Veröffentlicht am", "module.news.overview.table.status": "Status", "module.news.overview.table.title": "Titel", "module.news.overview.title": "Nachrichten", "module.news.update.created_on_date_by_user": "Erstellt am {date} von {user}", "module.news.update.delete_article": "Löschen", "module.news.update.delete_message": "Sind <PERSON> sic<PERSON>, dass Sie diesen Artikel löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.", "module.news.update.fields.content": "Inhalt", "module.news.update.fields.from": "<PERSON>", "module.news.update.fields.image": "Bild", "module.news.update.fields.schedule_publishing": "Veröffentlichung planen", "module.news.update.fields.status": "Status", "module.news.update.fields.title": "Titel", "module.news.update.fields.title_placeholder": "Titel", "module.news.update.fields.until": "Bis", "module.news.update.publish_announcement": "Veröffentlichen", "module.news.update.publish_article": "Veröffentlichen", "module.news.update.publish_success_message.description": "Ihr Artikel wurde erfolgreich veröffentlicht.", "module.news.update.publish_success_message.title": "<PERSON><PERSON><PERSON> ve<PERSON><PERSON><PERSON><PERSON><PERSON>", "module.news.update.return_to_overview": "Alle Artikel", "module.news.update.save_changes": "Änderungen speichern", "module.news.update.success_message.description": "Ihr Artikel wurde erfolgreich gespeichert.", "module.news.update.success_message.title": "<PERSON><PERSON><PERSON>.", "module.news.update.validation.at_least_one_translation": "Mindestens 1 Übersetzung muss einen Titel und einen Inhalt haben.", "module.news.update.validation.end_date_after_start_date": "Das Enddatum muss nach dem Startdatum liegen", "module.news.update.validation.image_required": "Ein Vorschaubild ist erforderlich.", "module.news.update.validation.start_date_today_or_future": "Das Datum muss heute oder in der Zukunft liegen", "module.news.update.validation.title_and_content_required": "Entweder müssen Titel und Inhalt beide ausgefüllt sein oder beide müssen leer gelassen werden.", "module.news.update_announcement.delete_announcement": "Löschen", "module.news.update_announcement.delete_message": "Sind <PERSON> sic<PERSON>, dass Sie diese Ankündigung löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.", "module.news.update_announcement.fields.type": "Ankündigungstyp", "module.news.update_announcement.page_title": "Ankündigung bearbeiten", "module.news.update_announcement.publish_success_message.description": "Ihre Ankündigung wurde erfolgreich veröffentlicht.", "module.news.update_announcement.publish_success_message.title": "Ankündigung veröffentlicht", "module.news.update_announcement.return_to_overview": "Alle Ankündigungen", "module.news.update_announcement.success_message.description": "Ihre Ankündigung wurde erfolgreich gespeichert.", "module.news.update_announcement.success_message.title": "Ankündigung gespeichert", "module.packaging_request.overview.new_request": "Neue Verpackungsanfrage", "module.packaging_request.update.customer_and_location.customer.title": "Kunde", "module.packaging_request.update.customer_and_location.delivery.title": "Lieferadresse", "module.packaging_request.update.customer_and_location.title": "Abfallproduzent und Lieferadresse", "module.packaging_request.update.customer_and_location.waste_producer.title": "Abfallproduzent", "module.packaging_request.update.delivery.add_contact": "Kontakt hinzufügen", "module.packaging_request.update.delivery.add_existing_contact": "Fügen Sie vorhandenen Kontakt hinzu", "module.packaging_request.update.delivery.date_or_period": "Bevorzugtes Datum oder Zeitraum", "module.packaging_request.update.delivery.remarks": "Hast du irgendwelche Bemerkungen für uns?", "module.packaging_request.update.delivery.remarks_placeholder": "Be<PERSON>kungen, Kommentare oder Fragen?", "module.packaging_request.update.delivery.send_copy_to_contacts": "Möchten Si<PERSON> eine Kopie an andere Kontakte senden?", "module.packaging_request.update.delivery.title": "Lieferung", "module.packaging_request.update.delivery_details": "Lieferdetails", "module.packaging_request.update.general_info": "Allgemeine Info", "module.packaging_request.update.packaging.label": "Wählen Sie die benötigte Verpackung aus", "module.packaging_request.update.packaging.table.cost_center": "Kostenzentrum", "module.packaging_request.update.packaging.table.image_alt": "Bild für die Materialnummer {number}", "module.packaging_request.update.packaging.table.po_number": "PO -Nummer", "module.packaging_request.update.packaging.title": "Verpackungsauswahl", "module.packaging_request.update.packaging.too_little_selected": "Sie müssen mindestens 1 auswählen", "module.packaging_request.update.page_title": "Verpackungsanfrage", "module.packaging_request.update.submit.email_label": "E-Mail", "module.packaging_request.update.submit.email_placeholder": "Beispielemail.com", "module.packaging_request.update.submit.first_name": "<PERSON><PERSON><PERSON>", "module.packaging_request.update.submit.last_name": "Nachname", "module.packaging_request.update.submit.request_submitted": "Ihre Verpackung wurde eingereicht.", "module.packaging_request.update.submit.request_submitted_description": "Ihre Anfrage wurde erfolgreich unter der ID {id} übermittelt. Wir werden uns in Kürze bei Ihnen melden.", "module.packaging_request.update.submit.return_to_overview": "Zurück zur Übersicht", "module.packaging_request.update.submit.success": "Eingereicht!", "module.packaging_request.update.submit.success_description": "Übertragete Verpackungsanforderung erfolgreich mit ID {ID}", "module.packaging_request.update.submit.thank_you": "Vielen Dank!", "module.permissions.overview.title": "Berechtigungen", "module.pickup_request.detail.confirmed": "Bestätigte Abholung von {start} bis {end}", "module.pickup_request.detail.created_on": "Erstellt am", "module.pickup_request.detail.page_title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "module.pickup_request.detail.requested": "Angeforderte Abholung von {start} bis {end}", "module.pickup_request.detail.submitted_id": "ID {id}", "module.pickup_request.detail.title": "Abholung für", "module.pickup_request.overview.bulk.delete_draft": "Entwürfe löschen", "module.pickup_request.overview.bulk.delete_draft_description": "Ihre ausgewählten Entwürfe werden dauerhaft gelöscht", "module.pickup_request.overview.filter.draft_status": "Entwurf-Status", "module.pickup_request.overview.filter.hazardous": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "module.pickup_request.overview.filter.transport_by_indaver": "Transport durch Indaver", "module.pickup_request.overview.filter.transport_mode": "Transportmodus", "module.pickup_request.overview.new_pickup": "Neue Abholung", "module.pickup_request.overview.new_request": "Neue Anfrage", "module.pickup_request.overview.tab.awaiting_booking": "Wartet auf Buchung", "module.pickup_request.overview.tab.booked": "Gebucht", "module.pickup_request.overview.tab.cancelled": "<PERSON><PERSON><PERSON><PERSON>", "module.pickup_request.overview.tab.completed": "Abgeschlossen", "module.pickup_request.overview.tab.drafts": "Entwürfe", "module.pickup_request.overview.tab.indascan_drafts": "Indascan", "module.pickup_request.overview.tab.submitted": "Eingereicht", "module.pickup_request.overview.title": "Abholanfragen", "module.pickup_request.sidebar.title": "<PERSON><PERSON><PERSON><PERSON>", "module.pickup_request.update.administration.fields.cost_center": "Kostenstelle", "module.pickup_request.update.administration.fields.po_number": "Bestellnummer", "module.pickup_request.update.administration.fields.serial_number": "Seriennummer", "module.pickup_request.update.administration.fields.tfs_number": "TFS -Nummer", "module.pickup_request.update.administration.fields.waste": "Abfall", "module.pickup_request.update.administration.title": "Verwaltung", "module.pickup_request.update.container_info.title": "Containerinformationen", "module.pickup_request.update.customer_and_location.title": "Abfallerzeuger & Anfallstelle", "module.pickup_request.update.details.asn": "<PERSON>n", "module.pickup_request.update.details.bulk_unit": "Masseneinheiten", "module.pickup_request.update.details.bulk_unit_description": "Diese Änderungen werden auf alle Abfalllinien angewendet. \nSie können das Gerät für jede Abfalllinie einzeln wechseln.", "module.pickup_request.update.details.bulk_unit_weight_volume": "Setzen Sie 'geschätzte Gewicht/Volumeneinheit' auf:", "module.pickup_request.update.details.customer_reference": "Kundenreferenz", "module.pickup_request.update.details.delivery_info": "Lieferinformationen", "module.pickup_request.update.details.esn_number": "Esn", "module.pickup_request.update.details.ewc_code": "AVV Nr.", "module.pickup_request.update.details.material_analysis": "Materialanalyse", "module.pickup_request.update.details.materials_error": "Sie scheinen einen <PERSON>hler in diesem Feld zu haben: '{Feld}'. \n<PERSON><PERSON>, dass dieses Feld aufgrund Ihrer aktiven dynamischen Spalten verborgen sein könnte.", "module.pickup_request.update.details.process_code": "Prozesscode", "module.pickup_request.update.details.title": "Details", "module.pickup_request.update.details.waste_material": "Abfallmaterial", "module.pickup_request.update.general_info": "Allgemeine Informationen", "module.pickup_request.update.packaging.fields.container_covered": "<PERSON><PERSON><PERSON> a<PERSON><PERSON><PERSON><PERSON>", "module.pickup_request.update.packaging.fields.container_number": "Cont.-Nr.", "module.pickup_request.update.packaging.fields.container_size_volume": "Fortsetzung \nGröße/Volumen", "module.pickup_request.update.packaging.fields.container_type": "Cont.-<PERSON>p", "module.pickup_request.update.packaging.fields.estimated_weight_volume": "Gesch. Gewicht / Volumen", "module.pickup_request.update.packaging.fields.is_return_packaging": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "module.pickup_request.update.packaging.fields.is_return_packaging_description": "Ich möchte die gleiche Menge und Art der Verpackung zurückerhalten", "module.pickup_request.update.packaging.fields.is_return_packaging_tooltip": "<PERSON>ur <PERSON>, wenn Sie genau die gleiche Verpackung zurückerhalten möchten. Nur anwendbar für: IBC, ASF, ASP, Tankcontainer, Gebinde im ASP", "module.pickup_request.update.packaging.fields.number_of_containers": "<PERSON><PERSON><PERSON> der cont.", "module.pickup_request.update.packaging.fields.packaging_remark": "Bemerkungen zu den Verpackungen", "module.pickup_request.update.packaging.fields.packaging_type": "Verp.-Typ", "module.pickup_request.update.packaging.fields.quantity_labels": "<PERSON><PERSON><PERSON>", "module.pickup_request.update.packaging.fields.quantity_packages": "Anz. Verp.", "module.pickup_request.update.packaging.fields.quantity_pallets": "<PERSON><PERSON><PERSON>", "module.pickup_request.update.packaging.fields.reconciliation_number": "Versöhnung N °", "module.pickup_request.update.packaging.fields.tanker_type": "Tankfahrzeugtyp", "module.pickup_request.update.packaging.fields.total_quantity_pallets": "Gesamtanzahl der Paletten", "module.pickup_request.update.packaging.fields.transport_type": "Transp.-Typ", "module.pickup_request.update.packaging.fields.unit": "Einheit", "module.pickup_request.update.packaging.fields.waste": "Abfall", "module.pickup_request.update.packaging.placeholder.amount": "0", "module.pickup_request.update.packaging.placeholder.container_type": "<PERSON>tain<PERSON><PERSON>", "module.pickup_request.update.packaging.placeholder.container_volume_size": "4mx2mx2m", "module.pickup_request.update.packaging.placeholder.hazard_inducers": "Gefahreninduktoren", "module.pickup_request.update.packaging.placeholder.long_number": "0", "module.pickup_request.update.packaging.placeholder.packaging_type": "Verpackungsart", "module.pickup_request.update.packaging.placeholder.tanker_type": "Tankfahrzeugtyp", "module.pickup_request.update.packaging.placeholder.transport_type": "Transportart", "module.pickup_request.update.packaging.placeholder.un_number": "UN-Nummer", "module.pickup_request.update.packaging.title": "Verpackungsinformationen", "module.pickup_request.update.packaging_request.title": "Verpackung bestellen", "module.pickup_request.update.page_title": "Neue Abholung", "module.pickup_request.update.pickup_details": "Abholdetails", "module.pickup_request.update.planning.additional_files": "<PERSON><PERSON>chten Sie zusätzliche Dateien hochladen?", "module.pickup_request.update.planning.additional_files_hint": "<PERSON><PERSON><PERSON><PERSON>, Produktdatenb<PERSON><PERSON><PERSON>, Chemikalienlisten usw.", "module.pickup_request.update.planning.date_in_future": "Das Startdatum muss in der Zukunft liegen.", "module.pickup_request.update.planning.date_or_period": "Bevorzugtes Datum oder Zeitraum", "module.pickup_request.update.planning.remarks": "Haben Sie Anmerkungen für uns?", "module.pickup_request.update.planning.remarks_placeholder": "<PERSON><PERSON>kungen, Kommentare oder Fragen?", "module.pickup_request.update.planning.title": "Planung", "module.pickup_request.update.quick_entry_mode": "<PERSON><PERSON><PERSON>int<PERSON><PERSON><PERSON>", "module.pickup_request.update.return_to_overview": "Alle Abholanfragen", "module.pickup_request.update.submit.add_contact": "Kontakt hinzufügen", "module.pickup_request.update.submit.add_existing_contact": "Bestehenden Kontakt hinzufügen", "module.pickup_request.update.submit.almost_done": "Fast fertig!", "module.pickup_request.update.submit.edit_pickup": "Abholung bearbeiten", "module.pickup_request.update.submit.email_label": "E-Mail", "module.pickup_request.update.submit.email_placeholder": "beispiel{at}email.com", "module.pickup_request.update.submit.first_name": "<PERSON><PERSON><PERSON>", "module.pickup_request.update.submit.last_name": "Nachname", "module.pickup_request.update.submit.request_submitted": "Ihre Abholung wurde übermittelt", "module.pickup_request.update.submit.request_submitted_description": "Ihre Anfrage wurde erfolgreich mit der ID {id} übermittelt. Wir werden uns bald bei Ihnen melden.", "module.pickup_request.update.submit.return_to_overview": "Zurück zur Übersicht", "module.pickup_request.update.submit.send_copy_to_contacts": "Möchten Si<PERSON> eine Kopie an andere Kontakte senden?", "module.pickup_request.update.submit.submit_pickup": "Abholung einreichen", "module.pickup_request.update.submit.submit_request": "<PERSON><PERSON><PERSON>, Ihre Abholung e<PERSON>zureichen?", "module.pickup_request.update.submit.thank_you": "Vielen Dank!", "module.pickup_request.update.transport.fields.adr_class": "ADR-Klasse", "module.pickup_request.update.transport.fields.danger_label_one": "Gefahrzettel 1", "module.pickup_request.update.transport.fields.danger_label_three": "Gefahrzettel 3", "module.pickup_request.update.transport.fields.danger_label_two": "Gefahrzettel 2", "module.pickup_request.update.transport.fields.hazard_inducers": "Gefahreninduktoren", "module.pickup_request.update.transport.fields.hazard_inducers_hint": "<PERSON><PERSON><PERSON><PERSON><PERSON>, weil die ausgewählte UN -Nummer gefährlich ist.", "module.pickup_request.update.transport.fields.packaging_group": "Verp.-Gruppe", "module.pickup_request.update.transport.fields.un_number": "UN-Nummer", "module.pickup_request.update.transport.fields.waste": "Abfall", "module.pickup_request.update.transport.title": "Transport", "module.pickup_request.update.waste.contract_line.custmer_ref": "Kundenref.", "module.pickup_request.update.waste.contract_line.ewc_code": "AVV Nr.", "module.pickup_request.update.waste.contract_line.max_selection_reached": "<PERSON><PERSON> erre<PERSON>t", "module.pickup_request.update.waste.contract_line.pickup_address": "<PERSON><PERSON><PERSON><PERSON>", "module.pickup_request.update.waste.contract_line.pickup_date": "Bevorzugtes Abholdatum", "module.pickup_request.update.waste.contract_line.pickup_time": "Bevorzugte Abholzeit", "module.pickup_request.update.waste.contract_line.too_little_selected": "Sie müssen mindestens 1 auswählen", "module.pickup_request.update.waste.contract_line.too_many_selected": "<PERSON>e haben zu viele ausgewählt, bitte Auswahl aufheben oder Transportmodus ändern", "module.pickup_request.update.waste.contract_line.waste_material": "Abfallmaterial", "module.pickup_request.update.waste.materials.all": "Alle", "module.pickup_request.update.waste.materials.label": "<PERSON>en und wählen Sie die Abfallmaterialien aus der Liste aus", "module.pickup_request.update.waste.materials.selected": "Ausgewählt", "module.pickup_request.update.waste.materials.title": "Welche Abfallmaterialien möchten Sie entsorgen?", "module.pickup_request.update.waste.packaging_added": "Fügen Sie dieser Anfrage Verpackungen hinzu", "module.pickup_request.update.waste.title": "Abfall", "module.pickup_request.update.waste.transport_by_indaver": "<PERSON><PERSON> m<PERSON>cht<PERSON>, dass der Transport von Indaver organisiert wird.", "module.pickup_request.update.waste.transport_mode": "Transportmodus", "module.reporting.overview.title": "Berichterstattung", "module.roles_and_permissions.add_new_role": "Neue Rolle", "module.roles_and_permissions.create_role_dialog.description": "Eine neue Rolle erstellen", "module.roles_and_permissions.create_role_dialog.title": "<PERSON><PERSON> erstellen", "module.roles_and_permissions.save_changes": "Änderungen speichern", "module.setting.application.title": "<PERSON><PERSON><PERSON><PERSON>", "module.setting.contact.create": "Kontakt erstellen", "module.setting.contact.create_success": "Kontakt erfolgreich erstellt", "module.setting.contact.delete_description": "Sind <PERSON> sicher, dass Si<PERSON> diesen Kontakt löschen möchten?", "module.setting.contact.delete_success": "Kontakt erfolgreich gelöscht.", "module.setting.contact.edit": "Kontakt bearbeiten", "module.setting.contact.fields.email": "E-Mail", "module.setting.contact.fields.first_name": "<PERSON><PERSON><PERSON>", "module.setting.contact.fields.last_name": "Nachname", "module.setting.contact.no_contacts": "Sie haben derzeit keine Kontakte hinzugefügt.", "module.setting.font_size.default": "Standard", "module.setting.font_size.description": "Passen Sie Ihre Schriftgröße an.", "module.setting.font_size.large": "<PERSON><PERSON><PERSON>", "module.setting.font_size.larger": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "module.setting.font_size.small": "<PERSON>", "module.setting.font_size.smaller": "<PERSON><PERSON>", "module.setting.font_size.title": "Schriftgröße", "module.setting.high_contrast.description": "Verbessern Sie die Lesbarkeit und Sichtbarkeit durch Erhöhung des Kontrasts zwischen den UI-Elementen.", "module.setting.high_contrast.disabled.description": "Der Hochkontrastmodus ist deaktiviert.", "module.setting.high_contrast.disabled.label": "Deaktiviert", "module.setting.high_contrast.enabled.description": "Der Hochkontrastmodus ist aktiviert.", "module.setting.high_contrast.enabled.label": "Aktiviert", "module.setting.high_contrast.title": "Hochkontrastmodus", "module.setting.interface_theme.dark": "<PERSON><PERSON><PERSON>", "module.setting.interface_theme.description": "Wählen Sie Ihr UI-Thema aus oder passen Sie es an.", "module.setting.interface_theme.light": "Hell", "module.setting.interface_theme.system_preference": "Systemeinstellung", "module.setting.interface_theme.title": "Interface-<PERSON><PERSON>", "module.setting.keyboard_shortcuts.description": "Tastaturkürzel sind immer aktiviert, aber Si<PERSON> können wählen, ob ein Hinweis angezeigt werden soll.", "module.setting.keyboard_shortcuts.disabled.description": "<PERSON>n<PERSON><PERSON> zu Tastaturkürzeln sind ausgeblendet.", "module.setting.keyboard_shortcuts.disabled.label": "Deaktiviert", "module.setting.keyboard_shortcuts.enabled.description": "<PERSON><PERSON><PERSON><PERSON> zu Tastaturkürzeln sind sichtbar.", "module.setting.keyboard_shortcuts.enabled.label": "Aktiviert", "module.setting.keyboard_shortcuts.example_button": "Beispiel", "module.setting.keyboard_shortcuts.title": "<PERSON><PERSON><PERSON><PERSON> zu Tastaturkürzeln", "module.setting.language.description": "Ändern Sie Ihre Sprache.", "module.setting.language.locales.en_nl": "Englisch (Niederlande / Belgien)", "module.setting.language.locales.en_us": "<PERSON><PERSON><PERSON>", "module.setting.language.locales.nl_be": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Belgien)", "module.setting.language.title": "<PERSON><PERSON><PERSON>", "module.setting.reduce_motion.description": "Reduzieren Sie die Menge und Intensität von Animationen, Hover-Effekten und anderen Bewegungseffekten in der gesamten Anwendung.", "module.setting.reduce_motion.disabled.description": "Alle Animationen und Hover-Effekte sind für ein vollständiges Erlebnis aktiviert.", "module.setting.reduce_motion.disabled.label": "Deaktiviert", "module.setting.reduce_motion.enabled.description": "<PERSON>e sehen weniger Animationen und Hover-Effekte, um die Bewegung zu reduzieren.", "module.setting.reduce_motion.enabled.label": "Aktiviert", "module.setting.reduce_motion.title": "Bewegung reduzieren", "module.setting.roles_and_permissions.add_new_role": "<PERSON><PERSON> hinzufügen", "module.setting.roles_and_permissions.create_role_dialog.description": "<PERSON><PERSON><PERSON><PERSON> eine neue Rolle", "module.setting.roles_and_permissions.create_role_dialog.title": "<PERSON><PERSON> erstellen", "module.setting.roles_and_permissions.save_changes": "Änderungen speichern", "module.setting.roles_and_permissions.save_changes_success": "Änderungen erfolgreich gespeichert", "module.setting.roles_and_permissions.title": "Rollen und Berechtigungen", "module.setting.subtitle": "Verwalten Sie Ihre App-Einstellungen", "module.setting.title": "Einstellungen", "module.settings.back.label": "Zurück", "module.settings.contacts.description": "Verwalten Sie Ihre Kontakte.", "module.settings.contacts.title": "Kontakte", "module.settings.disabled": "Deaktiviert", "module.settings.display.title": "Anzeige", "module.settings.enabled": "Aktiviert", "module.settings.forward.label": "<PERSON><PERSON>", "module.settings.general": "Allgemein", "module.settings.maximize.label": "Einstellungsansicht maximieren", "module.settings.minimize.label": "Einstellungsansicht minimieren", "module.settings.no_results": "<PERSON><PERSON> für \"{searchTerm}\" gefunden.", "module.settings.roles_and_permissions.add_new_role": "<PERSON><PERSON> hinzufügen", "module.settings.roles_and_permissions.create_role_dialog.description": "Eine neue Rolle erstellen", "module.settings.roles_and_permissions.create_role_dialog.title": "<PERSON><PERSON> erstellen", "module.settings.roles_and_permissions.save_changes": "Änderungen speichern", "module.settings.roles_and_permissions.save_changes_success": "Änderungen erfolgreich gespeichert", "module.settings.roles_and_permissions.table.delete_role": "Rolle löschen", "module.settings.roles_and_permissions.table.permissions": "Berechtigungen", "module.settings.roles_and_permissions.title": "Rollen und Berechtigungen", "module.settings.search.clear.label": "Suchbegriff löschen", "module.settings.search.placeholder": "Schnellsuche", "module.settings.section.appearance.description": "Passen Sie das allgemeine Erscheinungsbild der App an.", "module.settings.section.appearance.option.dark_mode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "module.settings.section.appearance.option.light_mode": "<PERSON><PERSON>", "module.settings.section.appearance.option.system_preference": "Systemeinstellung", "module.settings.section.appearance.title": "Erscheinungsbild", "module.settings.section.font_size.description": "Passen Sie die Textgröße an, um die Lesbarkeit zu verbessern oder mehr Inhalt auf Ihrem Bildschirm unterzubringen.", "module.settings.section.font_size.option.default": "Standard", "module.settings.section.font_size.option.large": "<PERSON><PERSON><PERSON>", "module.settings.section.font_size.option.larger": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "module.settings.section.font_size.option.small": "<PERSON>", "module.settings.section.font_size.option.smaller": "<PERSON><PERSON>", "module.settings.section.font_size.title": "Schriftgröße", "module.settings.section.high_contrast.description": "Verbessern Sie die Sichtbarkeit und reduzieren Sie die Belastung der Augen mit dem Hochkontrastmodus.", "module.settings.section.high_contrast.disabled.label": "Hochkontrastmodus ist deaktiviert", "module.settings.section.high_contrast.enabled.label": "Hochkontrastmodus ist aktiviert", "module.settings.section.high_contrast.title": "<PERSON><PERSON>", "module.settings.section.keyboard_shortcut_hints.description": "Schalten Sie Hinweise für Tastaturkürzel um, damit Sie die App effizienter navigieren können.", "module.settings.section.keyboard_shortcut_hints.disabled.label": "<PERSON>n<PERSON><PERSON> zu Tastaturkürzeln sind ausgeblendet", "module.settings.section.keyboard_shortcut_hints.enabled.label": "<PERSON>n<PERSON><PERSON> zu Tastaturkürzeln sind sichtbar", "module.settings.section.keyboard_shortcut_hints.example": "Beispiel", "module.settings.section.keyboard_shortcut_hints.not_available_on_mobile": "Tastaturkürzel sind auf Mobil- oder Tablet-Geräten nicht verfügbar. Um diese Einstellung zu ändern, öffnen Sie die App auf einem Desktop.", "module.settings.section.keyboard_shortcut_hints.title": "<PERSON><PERSON><PERSON><PERSON> zu Tastaturkürzeln", "module.settings.section.language.description": "<PERSON>ählen Sie Ihre bevorzugte Sprache für die App aus.", "module.settings.section.language.title": "<PERSON><PERSON><PERSON>", "module.settings.settings_are_hidden.label": "{count} Einstellung in \"{viewName}\" ist derzeit ausgeblendet. | {count} Einstellungen in \"{viewName}\" sind derzeit ausgeblendet.", "module.settings.settings_are_hidden.show_all.label": "Alle Einstellungen anzeigen", "module.user.columns.upn": "Upn", "module.user.create.create_user": "<PERSON><PERSON><PERSON> er<PERSON>", "module.user.create.success_toast.message": "Benutzer wurde erfolgreich erstellt.", "module.user.create.title": "<PERSON><PERSON><PERSON>", "module.user.detail.edit_user": "<PERSON><PERSON><PERSON> bearbeiten", "module.user.detail.title": "<PERSON><PERSON><PERSON> bearbeiten", "module.user.form.section.name.description": "Vor- und Nachname des Benutzers.", "module.user.impersonation.description": "<PERSON>d <PERSON><PERSON> sic<PERSON>, dass <PERSON> {user} verkörpern möchten? Dadurch können Si<PERSON> in deren Namen handeln.", "module.user.impersonation.title": "Identitätswechsel Starten", "module.user.overview.title": "<PERSON><PERSON><PERSON>", "module.user.role": "<PERSON><PERSON>", "module.waste_inquiry.detail.additional_files": "Zusätzliche Dateien", "module.waste_inquiry.detail.analysis_report": "Analy<PERSON><PERSON><PERSON><PERSON>", "module.waste_inquiry.detail.conformity_assessment": "Konformitätsbewertung", "module.waste_inquiry.detail.contract_item": "Vertragsposition {item}", "module.waste_inquiry.detail.contract_number": "Vertragsnr. {number}", "module.waste_inquiry.detail.sds": "Sicherheitsdatenblatt (SDB)", "module.waste_inquiry.overview.bulk.all_items_selected": "Alle Elemente ausgewählt", "module.waste_inquiry.overview.bulk.delete_draft": "Entwürfe löschen", "module.waste_inquiry.overview.bulk.delete_draft_description": "Ihre ausgewählten Entwürfe werden dauerhaft gelöscht", "module.waste_inquiry.overview.bulk.items_selected": "Element ausgewählt | Elemente ausgewählt", "module.waste_inquiry.overview.bulk.items_unselected": "Element von allen abgewählt | Elemente von allen abgewählt", "module.waste_inquiry.overview.date": "Datum", "module.waste_inquiry.overview.new_waste_inquiry": "Neue Abfallanfrage", "module.waste_inquiry.overview.requested_by": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "module.waste_inquiry.overview.status": "Status", "module.waste_inquiry.overview.tab.completed": "Abgeschlossen", "module.waste_inquiry.overview.tab.drafts": "Entwürfe", "module.waste_inquiry.overview.tab.offers": "<PERSON><PERSON><PERSON>", "module.waste_inquiry.overview.tab.pending": "<PERSON><PERSON><PERSON><PERSON>", "module.waste_inquiry.overview.tab.submitted": "Eingereicht", "module.waste_inquiry.overview.title": "Abfallanfragen", "module.waste_inquiry.update.all_changes_saved": "Alle Änderungen gespeichert", "module.waste_inquiry.update.characteristics.flashpoint_type.label": "Flammpunkt", "module.waste_inquiry.update.characteristics.ph_type.label": "pH-Wert", "module.waste_inquiry.update.characteristics.specific_gravity.label": "Spezifisches Gewicht", "module.waste_inquiry.update.characteristics.stable_temperature.label": "Temperatur", "module.waste_inquiry.update.characteristics.stable_temperature.placeholder": "Wählen Sie einen Typ", "module.waste_inquiry.update.characteristics.title": "Eigenschaften", "module.waste_inquiry.update.collection.campaign_name.label": "Kampagnenname", "module.waste_inquiry.update.collection.expected_end_date.label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "module.waste_inquiry.update.collection.expected_per_collection_quantity.label": "Erwartete Menge pro Abholung", "module.waste_inquiry.update.collection.expected_yearly_volume_amount.label": "Erwartetes Volumen pro Jahr", "module.waste_inquiry.update.collection.first_collection_date.label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "module.waste_inquiry.update.collection.frequency_of_discharge.label": "Häufigkeit der Entladung", "module.waste_inquiry.update.collection.remarks.label": "Zusätzliche Informationen", "module.waste_inquiry.update.collection.remarks.placeholder": "Geben Sie zusätzliche Informationen, Anmerkungen oder Fragen an", "module.waste_inquiry.update.collection.title": "<PERSON><PERSON><PERSON>", "module.waste_inquiry.update.collection_and_transport": "Sammlung & Transport", "module.waste_inquiry.update.composition.analysis_report.label": "Haben Si<PERSON> einen Analysebericht?", "module.waste_inquiry.update.composition.analysis_report.no_report": "Ich habe keinen Analysebericht", "module.waste_inquiry.update.composition.components.label": "Spezifizieren Sie die Abfallzusammensetzung", "module.waste_inquiry.update.composition.sample.available": "Ich habe eine Probe zum Versenden verfügbar", "module.waste_inquiry.update.composition.sample.label": "Abfallprobe", "module.waste_inquiry.update.composition.sample.not_available": "Ich habe keine Probe zum Versenden verfügbar", "module.waste_inquiry.update.composition.sds.label": "Haben Sie ein Sicherheitsdatenblatt (SDB)?", "module.waste_inquiry.update.composition.sds.no_sds": "Ich habe kein SDB", "module.waste_inquiry.update.composition.title": "Zusammensetzung", "module.waste_inquiry.update.created_on": "Erstellt am", "module.waste_inquiry.update.customer_and_location.customer.title": "Wählen Si<PERSON> einen Kunden aus", "module.waste_inquiry.update.customer_and_location.pick_up_address.title": "<PERSON>ählen Si<PERSON> eine Anfallstelle aus", "module.waste_inquiry.update.customer_and_location.pick_up_address.unknown_label": "Anfallstel<PERSON> ist unbekannt", "module.waste_inquiry.update.customer_and_location.suggestions": "Vorschläge", "module.waste_inquiry.update.customer_and_location.title": "Kunde und Standort", "module.waste_inquiry.update.customer_and_location.waste_producer.title": "Wählen Sie einen Abfallerzeuger aus", "module.waste_inquiry.update.customer_and_location.waste_producer.unknown_label": "Abfallerzeuger ist unbekannt", "module.waste_inquiry.update.failed_to_save_changes": "Änderungen konnten nicht gespeichert werden", "module.waste_inquiry.update.general_info": "Allgemeine Informationen", "module.waste_inquiry.update.legislation.title": "Gesetzgebung", "module.waste_inquiry.update.legislation_and_properties.legislations.label": "Gesetzgebungen", "module.waste_inquiry.update.legislation_and_properties.legislations.remarks.placeholder": "Bitte spezifizieren Sie jede ausgewählte Gesetzgebung", "module.waste_inquiry.update.legislation_and_properties.properties.label": "Eigenschaften", "module.waste_inquiry.update.legislation_and_properties.properties.remarks.placeholder": "Bitte spezifizieren Sie jede ausgewählte Eigenschaft", "module.waste_inquiry.update.legislation_and_properties.remarks.label": "Spezifizieren", "module.waste_inquiry.update.legislation_and_properties.remarks.placeholder": "Bitte spezifizieren Sie jede ausgewählte Gesetzgebung", "module.waste_inquiry.update.legislation_and_properties.title": "Gesetzgebung & Eigenschaften", "module.waste_inquiry.update.packaging.add_un_number": "UN-Nummer <PERSON>ügen", "module.waste_inquiry.update.page_title": "Neue Abfallanfrage", "module.waste_inquiry.update.properties.title": "Eigenschaften", "module.waste_inquiry.update.return_to_overview": "Alle Abfallströme", "module.waste_inquiry.update.saving_changes": "Änderungen werden gespeichert", "module.waste_inquiry.update.submit.add_contact_label": "Kontakt hinzufügen", "module.waste_inquiry.update.submit.add_existing_contact_label": "Bestehenden Kontakt hinzufügen", "module.waste_inquiry.update.submit.additional_files_hint": "<PERSON><PERSON><PERSON><PERSON>, Produktdatenb<PERSON><PERSON><PERSON>, Chemikalienlisten usw.", "module.waste_inquiry.update.submit.additional_files_label": "<PERSON><PERSON>chten Sie zusätzliche Dateien hochladen?", "module.waste_inquiry.update.submit.copy_id": "ID kopieren.", "module.waste_inquiry.update.submit.edit": "<PERSON><PERSON><PERSON> bearbeiten", "module.waste_inquiry.update.submit.remarks_label": "Haben Sie Anmerkungen zu Ihrer Anfrage für uns?", "module.waste_inquiry.update.submit.remarks_placeholder": "<PERSON><PERSON>kungen, Kommentare oder Fragen?", "module.waste_inquiry.update.submit.return_to_overview": "Zurück zur Übersicht", "module.waste_inquiry.update.submit.send_copy_to_customer_label": "Möchten Si<PERSON> eine Kopie an andere Kontakte senden?", "module.waste_inquiry.update.submit.submit": "<PERSON><PERSON><PERSON> e<PERSON>ichen", "module.waste_inquiry.update.submit.success": "Ihre Anfrage wurde erfolgreich mit der ID {id} übermittelt. Wir werden uns bald bei Ihnen melden.", "module.waste_inquiry.update.submitted_by": "<PERSON> {user}", "module.waste_inquiry.update.submitted_id": "ID {id}", "module.waste_inquiry.update.submitted_on": "Eingereicht am", "module.waste_inquiry.update.transport.collection_requirements.label": "<PERSON><PERSON><PERSON><PERSON><PERSON>er<PERSON>", "module.waste_inquiry.update.transport.container_loading_type.label": "Containerverladungsart", "module.waste_inquiry.update.transport.hazard_inducer_1.label": "Gefahrenauslöser 1", "module.waste_inquiry.update.transport.hazard_inducer_2.label": "Gefahrenauslöser 2", "module.waste_inquiry.update.transport.hazard_inducer_3.label": "Gefahrenauslöser 3", "module.waste_inquiry.update.transport.is_tank_owned_by_customer.label": "Ist es im Besitz des Kunden?", "module.waste_inquiry.update.transport.is_transport_arranged_by_indaver.falsy.label": "<PERSON><PERSON>, durch den Kunden oder ein anderes Unternehmen", "module.waste_inquiry.update.transport.is_transport_arranged_by_indaver.label": "Wird es von Indaver arrangiert?", "module.waste_inquiry.update.transport.is_transport_arranged_by_indaver.truthy.label": "<PERSON><PERSON><PERSON> von In<PERSON>", "module.waste_inquiry.update.transport.loading_by_indaver.label": "Verladung durch Indaver?", "module.waste_inquiry.update.transport.loading_type.label": "Transportverladungsart", "module.waste_inquiry.update.transport.regulated_transport.label": "Ist es ein regulierter Transport?", "module.waste_inquiry.update.transport.title": "Transport", "module.waste_inquiry.update.transport.transport_in.label": "Transport in", "module.waste_inquiry.update.transport.transport_type.label": "Transportart", "module.waste_inquiry.update.transport.un_number_and_packing_group.label": "Geben Sie die UN-Nummer(n) und Verpackungsgruppen an", "module.waste_inquiry.update.type.description.label": "Beschreibung & Herkunft des Abfallstroms", "module.waste_inquiry.update.type.description.placeholder": "Geben Sie eine kurze Beschreibung des Abfalls und des Entstehungsprozesses in gängiger Formulierung an", "module.waste_inquiry.update.type.name.label": "Name", "module.waste_inquiry.update.type.name.placeholder": "Geben Sie einen Namen für den Abfallstrom an", "module.waste_inquiry.update.type.packaging_type.label": "Verpackungsart", "module.waste_inquiry.update.type.state_of_matter.label": "Aggregatzustand", "module.waste_inquiry.update.type.state_of_matter.placeholder": "Wählen Sie den Aggregatzustand aus", "module.waste_inquiry.update.type.title": "<PERSON><PERSON>", "module.waste_inquiry.update.waste_identification": "Abfallidentifikation", "module.weekly_planning.overview.new_planning": "Neue Wochenplanung", "module.weekly_planning.overview.title": "Wochenplanung", "module.weekly_planning.update.customer_and_location.customer.title": "Kunde", "module.weekly_planning.update.customer_and_location.pick_up_address.is_unknown_label": "Anfallstel<PERSON> ist unbekannt", "module.weekly_planning.update.customer_and_location.pick_up_address.title": "<PERSON><PERSON><PERSON><PERSON>", "module.weekly_planning.update.customer_and_location.title": "Abfallerzeuger & Anfallstelle", "module.weekly_planning.update.customer_and_location.waste_producer.is_unknown_label": "Abfallerzeuger ist unbekannt", "module.weekly_planning.update.customer_and_location.waste_producer.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "module.weekly_planning.update.extra_info.title": "Zusätzliche Informationen", "module.weekly_planning.update.general_info": "Allgemeine Informationen", "module.weekly_planning.update.page_title": "Wochenplanung", "module.weekly_planning.update.pickup.save_and_close": "Speichern und schließen", "module.weekly_planning.update.pickup.title": "Die Abholanfrage bearbeiten", "module.weekly_planning.update.pickup_details": "Abholdetails", "module.weekly_planning.update.planning.additional_files": "<PERSON><PERSON>chten Sie zusätzliche Dateien hochladen?", "module.weekly_planning.update.planning.additional_files_hint": "<PERSON><PERSON><PERSON><PERSON>, Produktdatenb<PERSON><PERSON><PERSON>, Chemikalienlisten usw.", "module.weekly_planning.update.planning.date": "Bevorzugt<PERSON>", "module.weekly_planning.update.planning.remarks": "Haben Sie Anmerkungen für uns?", "module.weekly_planning.update.planning.remarks_placeholder": "<PERSON><PERSON>kungen, Kommentare oder Fragen?", "module.weekly_planning.update.planning.time": "Bevorzugte Zeit", "module.weekly_planning.update.return_to_overview": "Zurück zur Übersicht", "module.weekly_planning.update.submit.add_contact": "Kontakt hinzufügen", "module.weekly_planning.update.submit.add_existing_contact": "Bestehenden Kontakt hinzufügen", "module.weekly_planning.update.submit.add_existing_contact_label": "Bestehenden Kontakt hinzufügen", "module.weekly_planning.update.submit.almost_done": "Fast fertig!", "module.weekly_planning.update.submit.edit_planning": "Wochenplanung bearbeiten", "module.weekly_planning.update.submit.email_label": "E-Mail", "module.weekly_planning.update.submit.email_placeholder": "beispiel{at}indaver.com", "module.weekly_planning.update.submit.first_name": "<PERSON><PERSON><PERSON>", "module.weekly_planning.update.submit.go_to_waste_step": "Gehen Sie zum Abfallschritt", "module.weekly_planning.update.submit.last_name": "Nachname", "module.weekly_planning.update.submit.pickup_request_error": "Wir können Ihre Wochenplanung nicht einreichen. Eine Ihrer Abholanfragen scheint ein Problem in diesem Feld zu haben: {field}", "module.weekly_planning.update.submit.request_submitted": "Wochenplanung eingereicht!", "module.weekly_planning.update.submit.request_submitted_description": "Ihre Anfrage wurde erfolgreich mit der ID {id} übermittelt. Wir werden uns bald bei Ihnen melden.", "module.weekly_planning.update.submit.return_to_overview": "Zurück zur Übersicht", "module.weekly_planning.update.submit.send_copy_to_contacts": "Möchten Si<PERSON> eine Kopie an andere Kontakte senden?", "module.weekly_planning.update.submit.submit_planning": "Wochenplanung einreichen", "module.weekly_planning.update.submit.submit_request": "<PERSON><PERSON><PERSON>, Ihre Wochenplanung e<PERSON>zureichen?", "module.weekly_planning.update.submit.thank_you": "Vielen Dank", "module.weekly_planning.update.waste.contract_line.start_date_required": "Geben Sie Ihr bevorzugtes Abholdatum an", "module.weekly_planning.update.waste.contract_line.too_little_selected": "Sie müssen mindestens 1 auswählen", "module.weekly_planning.update.waste.materials.all": "Alle", "module.weekly_planning.update.waste.materials.selected": "Ausgewählt", "module.weekly_planning.update.waste.materials.title": "Welche Abfallmaterialien möchten Sie entsorgen?", "module.weekly_planning.update.waste.title": "Abfall", "shared.actions": "Aktionen", "shared.add": "Hinzufügen", "shared.adr_labels": "ADR-Gefahrzettel", "shared.approve": "<PERSON><PERSON><PERSON><PERSON>", "shared.average_temperature": "Durchschnittstemperatur", "shared.back": "Zurück", "shared.cancel": "Abbrechen", "shared.clear": "Löschen", "shared.close": "Schließen", "shared.copy": "<PERSON><PERSON><PERSON>", "shared.copy_success_description": "Die Daten wurden erfolgreich kopiert.", "shared.copy_success_title": "<PERSON>n kop<PERSON>t", "shared.cz_manual.open_link": "Öffnen Sie den Link zum Handbuch", "shared.delete": "Löschen", "shared.description": "Beschreibung", "shared.download": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shared.edit": "<PERSON><PERSON><PERSON>", "shared.ewc_code": "AVV Nr.", "shared.expand": "<PERSON><PERSON><PERSON><PERSON>", "shared.export_excel": "Export Excel", "shared.filters": "Filter", "shared.general": "Allgemein", "shared.loading": "Wird geladen", "shared.max_image_size_mb": "Max. {amount} MB", "shared.max_temperature": "<PERSON><PERSON>", "shared.min_temperature": "<PERSON><PERSON>", "shared.minus": "Minus", "shared.more_info": "Mehr Informationen", "shared.next": "<PERSON><PERSON>", "shared.next_step": "Zum nächsten Schritt", "shared.no": "<PERSON><PERSON>", "shared.no_roles_assigned": "<PERSON><PERSON> Rollen zugewiesen", "shared.not_available.abbreviation": "N.V.", "shared.number_short": "Nr.", "shared.open_useful_links": "Nützliche Links öffnen", "shared.plus": "Plus", "shared.previous": "Zurück", "shared.previous_step": "Zum vorherigen Schritt", "shared.reject": "<PERSON><PERSON><PERSON><PERSON>", "shared.remove": "Entfernen", "shared.save": "<PERSON><PERSON><PERSON><PERSON>", "shared.save_changes": "Änderungen speichern", "shared.search": "<PERSON><PERSON>", "shared.shrink": "Einklappen", "shared.start_impersonation": "Identitätswechsel Starten", "shared.submit": "Einreichen", "shared.type": "<PERSON><PERSON>", "shared.unfinished_feature_description": "Diese Funktion ist in der Entwicklung und noch nicht fertig.", "shared.unfinished_feature_title": "Unvollendete Funktion", "shared.unknown_address": "Unbekannte Adresse", "shared.update": "Aktualisieren", "shared.view_detail": "Detail anzeigen", "shared.useful_link.ewastra_portal_title": "Ewastra-Portal", "shared.useful_link.indaver_report_description": "Unsere frühere Reporting-Umgebung mit Zugriff auf Ihre historischen Daten.", "shared.useful_link.indaver_report_title": "Indaver Reports", "shared.useful_link.waste_collectors_description": "Genehmigungen für die Abfallsammlung (multiregional).", "shared.useful_link.waste_collectors_title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shared.useful_link.waste_facilities_description": "Anlagengenehmigungen und -lizenzen nach Unternehmen aufgelistet.", "shared.useful_link.waste_facilities_title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shared.working_as": "Arbeiten als", "shared.yes": "<PERSON>a", "user.birth_date": "Geburtsdatum", "user.email": "E-Mail", "user.email_placeholder": "beispiel{at}email.com", "user.first_name": "<PERSON><PERSON><PERSON>", "user.label.plural": "<PERSON><PERSON><PERSON>", "user.label.singular": "<PERSON><PERSON><PERSON>", "user.label.user_with_count": "<PERSON><PERSON> | {count} <PERSON><PERSON><PERSON> | {count} <PERSON><PERSON><PERSON>", "user.last_name": "Nachname", "user.name": "Name", "validation.invalid_date": "Ungültiges Datum", "validation.invalid_datetime": "Ungültiges Datum und Uhrzeit", "validation.invalid_email": "Ungültige E-Mail-Adresse", "validation.invalid_regex": "Ungültiges Format", "validation.invalid_string": "Ungültige Eingabe", "validation.invalid_union": "Ungültige Eingabe", "validation.invalid_url": "Ungültige URL", "validation.invalid_uuid": "Ungültige UUID", "validation.required": "<PERSON><PERSON> ist erford<PERSON>lich", "validation.too_big": "Muss kleiner oder gleich {count} sein", "validation.too_big_array": "Muss höchstens {count} Element enthalten | Muss höchstens {count} Elemente enthalten", "validation.too_big_date": "Muss vor {count} liegen", "validation.too_big_number": "Muss kleiner oder gleich {count} sein", "validation.too_big_string": "<PERSON><PERSON> höchs<PERSON> {count} <PERSON><PERSON><PERSON> lang sein | <PERSON><PERSON> höchs<PERSON> {count} <PERSON><PERSON><PERSON> lang sein", "validation.too_small": "<PERSON>ss größer oder gleich {count} sein", "validation.too_small_array": "Muss mindestens {count} Element enthalten | Muss mindestens {count} Elemente enthalten", "validation.too_small_date": "Muss nach {count} liegen", "validation.too_small_number": "<PERSON>ss größer oder gleich {count} sein", "validation.too_small_string": "Muss mindestens {count} <PERSON><PERSON><PERSON> lang sein | Muss mindestens {count} <PERSON><PERSON><PERSON> lang sein", "enum.certificate_type.cor": "Recyclingzertifikat", "enum.certificate_type.cot_cob": "Behandlungszertifikat", "module.invoice.detail.return_to_drafts": "<PERSON><PERSON><PERSON> zu Entwürfen"}