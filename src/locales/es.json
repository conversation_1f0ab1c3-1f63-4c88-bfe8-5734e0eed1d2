{"auth.legal_links.company_info": "Company info", "auth.legal_links.disclaimer": "Disclaimer", "auth.legal_links.privacy_&_cookies": "Privacy & cookies", "component.contact_autocomplete.placeholder": "Buscar un contacto", "component.customer_autocomplete.placeholder": "Buscar un cliente", "component.editor.action.bold": "Negrita", "component.editor.action.h2": "H2", "component.editor.action.h3": "H3", "component.editor.action.italic": "Cursiva", "component.editor.action.link": "Enlace", "component.editor.action.underline": "Subrayado", "component.editor.add_link": "Agregar un enlace", "component.editor.link_begin_with_https": "Tu enlace debe comenzar con https", "component.editor.link_label": "Enlace", "component.editor.link_placeholder": "https://", "component.ewc_code.ewc_code_not_found.description": "El código EWC que ingresaste no existe. Por favor, verifica el código e inténtalo de nuevo.", "component.ewc_code.ewc_code_not_found.title": "Código EWC no encontrado.", "component.ewc_code.invalid_format.description": "Por favor ingresa el código EWC en el formato correcto: seis dígitos (por ejemplo, 010101).", "component.ewc_code.invalid_format.title": "Formato de código EWC inválido.", "component.file_upload.allowed_file_types": "Tipos de archivo permitidos", "component.file_upload.click_to_upload": "Haz clic para subir", "component.file_upload.status.failed": "Error al subir", "component.file_upload.status.finished": "Finalizado", "component.file_upload.status.pending": "Preparando", "component.filters.clear_all": "Borrar todos los filtros", "component.filters.date_dialog.description": "Seleccione una fecha para el filtro '{label}'.", "component.filters.date_range_dialog.description": "Seleccione un rango de fechas para el filtro '{label}'.", "component.filters.listbox.no_results": "No hay opciones coincidentes", "component.filters.listbox.search_placeholder": "Filtrar...", "component.filters.selected": "sele<PERSON><PERSON><PERSON>", "component.form.option.no_suggestions": "No se encontraron sugerencias", "component.keyboard_shortcut.then": "luego", "component.number_field.decrement": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "component.number_field.increment": "Incrementar", "component.password_input.hide_password": "Ocultar contraseña", "component.password_input.show_password": "Mostrar contraseña", "component.pick_up_address_autocomplete.placeholder": "Buscar una dirección de recogida", "component.refresh_prompt.new_version.action": "Actualizar", "component.refresh_prompt.new_version.description": "¡Nueva versión disponible! Recarga para recibir la funcionalidad más reciente.", "component.search_input.clear": "Limpiar", "component.search_input.placeholder": "Buscar...", "component.select.empty_text": "No se encontraron resultados para '{searchTerm}'.", "component.select.search_input_placeholder": "Buscar", "component.select.search_placeholder": "Buscar...", "component.sidebar.close_sidebar": "Ce<PERSON>r barra lateral", "component.sidebar.footer.environment": "Entorno", "component.sidebar.footer.sign_out": "<PERSON><PERSON><PERSON>", "component.sidebar.footer.stop_impersonation": "Parar Suplantación", "component.sidebar.footer.user_profile": "Perfil de usuario", "component.sidebar.footer.version": "Versión", "component.sidebar.group.administration": "Administración", "component.sidebar.group.system_administration": "Administración del sistema", "component.sidebar.group.waste_management": "Gestión de residuos", "component.sidebar.open_sidebar": "Abrir barra lateral", "component.sidebar.settings": "Configuración", "component.table.clear_filter": "Limpiar filtros", "component.table.dynamic_view.add_view": "Agregar vista", "component.table.dynamic_view.cannot_delete_global_default_view": "No puedes eliminar una vista global predeterminada", "component.table.dynamic_view.cannot_delete_global_view_if_not_admin": "Las vistas globales solo pueden eliminarse por administradores del sistema", "component.table.dynamic_view.cannot_delete_last_view": "No puedes eliminar la última vista de la tabla", "component.table.dynamic_view.change_order": "<PERSON><PERSON><PERSON> orden", "component.table.dynamic_view.change_sorting": "Cambiar ordenamiento", "component.table.dynamic_view.default_global_view": "Vista predeterminada global", "component.table.dynamic_view.default_view": "Tu vista predeterminada", "component.table.dynamic_view.delete_view": "Eliminar vista", "component.table.dynamic_view.delete_view_description": "¿Estás seguro de que deseas eliminar la vista '{name}'? Esta acción es irreversible.", "component.table.dynamic_view.edit_view": "Editar vista", "component.table.dynamic_view.manage_views": "Administrar vistas", "component.table.dynamic_view.reset_all_changes": "Restablecer todos los cambios", "component.table.dynamic_view.save": "Guardar vista", "component.table.dynamic_view.save_as_default_view_for_me": "Guardar como vista predeterminada para mí", "component.table.dynamic_view.save_as_default_view_globally": "Como administrador, establece esto como predeterminado globalmente", "component.table.dynamic_view.save_as_global_view": "Guardar como vista global para que todos puedan usarla", "component.table.dynamic_view.save_as_new": "Guardar como nueva", "component.table.dynamic_view.save_as_new_disabled": "Realiza cambios en la vista de la tabla antes de guardar como nueva", "component.table.dynamic_view.save_filter_view": "Guardar vista filtrada", "component.table.dynamic_view.settings": "Configuración de tabla", "component.table.dynamic_view.shared": "Compartida", "component.table.dynamic_view.toggle_columns": "Alternar columnas", "component.table.dynamic_view.update": "Actualizar", "component.table.dynamic_view.update_filter_view": "Actualizar vista filtrada", "component.table.dynamic_view.view_deleted": "Vista eliminada", "component.table.dynamic_view.view_deleted_description": "Eliminaste la vista '{name}'", "component.table.dynamic_view.view_name": "Nombre de la vista filtrada", "component.table.dynamic_view.view_name_placeholder": "Ejemplo de vista", "component.table.dynamic_view.view_saved": "Vista guardada con éxito", "component.table.dynamic_view.view_saved_description": "Guardaste la vista '{name}'", "component.table.dynamic_view.view_updated_description": "Actualizaste la vista '{name}'", "component.table.dynamic_view.views": "Vistas", "component.table.next_page": "Siguient<PERSON>", "component.table.no_active_filters": "No hay filtros activos", "component.table.no_data.description": "Actualmente no hay datos para mostrar.", "component.table.no_data.title": "Sin datos", "component.table.no_results.description": "No hay resultados que coincidan con tus criterios de búsqueda.", "component.table.no_results.title": "Sin resultados", "component.table.page_count": "{startIndex} - {endIndex} de {totalItems}", "component.table.previous_page": "Anterior", "component.table.results_may_be_hidden": "Algunos resultados pueden estar ocultos", "component.unsaved_changes_dialog.cancel": "<PERSON><PERSON><PERSON>", "component.unsaved_changes_dialog.confirm": "S<PERSON>, estoy seguro", "component.unsaved_changes_dialog.description": "Se perderán todos los cambios no guardados. ¿Estás seguro de que deseas continuar?", "component.unsaved_changes_dialog.title": "Cambios no guardados", "component.waste_producer_autocomplete.placeholder": "Buscar un productor de residuos", "components.form.file_upload.max_file_size_exceeded.description": "El archivo \"{name}\" supera el tamaño máximo de {max} MB.", "components.form.file_upload.max_file_size_exceeded.title": "Tamaño de archivo excedido", "components.waste_inquiry.composition.components.add": "Agregar componente", "components.waste_inquiry.composition.components.add_packaging": "Agregar embalaje", "components.waste_inquiry.composition.components.component.max_weight.label": "Peso máx.", "components.waste_inquiry.composition.components.component.min_weight.label": "Peso mín.", "components.waste_inquiry.composition.components.component_name.label": "Componente", "components.waste_inquiry.composition.components.component_name.placeholder": "Nombre del componente", "components.waste_inquiry.composition.components.has_inner_packaging.label": "Embalaje interior", "components.waste_inquiry.composition.components.loading_method.label": "Método de carga", "components.waste_inquiry.composition.components.packaging.label": "<PERSON><PERSON><PERSON><PERSON>", "components.waste_inquiry.composition.components.packaging.placeholder": "Selecciona un tipo de embalaje", "components.waste_inquiry.composition.components.packaging_group.label": "Grupo de embalaje", "components.waste_inquiry.composition.components.packaging_size.label": "<PERSON><PERSON><PERSON>", "components.waste_inquiry.composition.components.packaging_type.label": "Tipo de embalaje", "components.waste_inquiry.composition.components.remarks.placeholder": "Observaciones", "components.waste_inquiry.composition.components.stored_in.label": "Almacenado en", "components.waste_inquiry.composition.components.transport_volume.label": "Volumen de transporte", "components.waste_inquiry.composition.components.un_number.label": "Número ONU", "components.waste_inquiry.composition.components.un_number.placeholder": "Número ONU", "components.waste_inquiry.composition.components.weight_per_piece.label": "Peso/pieza", "enum.announcement_type.informational": "Informativo", "enum.announcement_type.urgent": "Urgente", "enum.collection_requirement_option.tractor": "Solo unidad tractora", "enum.collection_requirement_option.tractor_trailer": "Tractor y remolque solamente", "enum.collection_requirement_option.tractor_trailer_tank": "Tractor, remolque y contenedor tipo tank", "enum.container_loading_type.chain.description": "Una cadena para cargar residuos.", "enum.container_loading_type.chain.label": "Cadena", "enum.container_loading_type.hook.description": "Un gancho para cargar residuos.", "enum.container_loading_type.hook.label": "<PERSON><PERSON><PERSON>", "enum.container_transport_type.change_compaction_container": "Cambiar contenedor de compactación", "enum.container_transport_type.direct_loading": "Carga directa", "enum.container_transport_type.emptying_glass_bulb": "Vaciado de bulbo de vidrio", "enum.container_transport_type.emptying_wheelie_bin": "<PERSON><PERSON><PERSON><PERSON> conten<PERSON> de <PERSON>", "enum.container_transport_type.exchange": "Intercambio", "enum.container_transport_type.final_collection": "Recolección final", "enum.container_transport_type.first_placement": "Primera colocación", "enum.container_transport_type.internal_movement": "Movimiento interno", "enum.container_transport_type.return": "Devolución", "enum.draft_invoice_status.approved_by_customer": "Aprobado por el cliente", "enum.draft_invoice_status.auto_approved": "Auto aprobado", "enum.draft_invoice_status.internal_approved": "Aprobado internamente", "enum.draft_invoice_status.rejected_by_customer": "Rechazado por el cliente", "enum.draft_invoice_status.rejected_by_indaver": "Rechazado por Indaver", "enum.draft_invoice_status.to_be_approved_by_customer": "Ser aprobado por el cliente", "enum.draft_invoice_status.to_be_approved_by_indaver": "Ser aprobado por Indaver", "enum.draft_invoice_status_short.approved": "Aprobado", "enum.draft_invoice_status_short.rejected": "<PERSON><PERSON><PERSON><PERSON>", "enum.draft_invoice_status_short.to_be_approved": "Para ser aprobado", "enum.dynamic_table_column_name.account_document_number": "Documento de cuenta n °", "enum.dynamic_table_column_name.account_manager": "Gest<PERSON> de cuentas", "enum.dynamic_table_column_name.account_manager_name": "Nombre del administrador de cuentas", "enum.dynamic_table_column_name.amount": "Cantidad", "enum.dynamic_table_column_name.asn": "Asn n °", "enum.dynamic_table_column_name.auto_approved_on": "Auto aprobado en", "enum.dynamic_table_column_name.company_name": "Nombre de empresa", "enum.dynamic_table_column_name.confirmed_collection_date": "Fecha de recolección confirmada", "enum.dynamic_table_column_name.confirmed_transport_date": "Fecha de transporte confirmada", "enum.dynamic_table_column_name.container_number": "N.º de contenedor", "enum.dynamic_table_column_name.container_transport_type": "Tipo de transporte de contenedores", "enum.dynamic_table_column_name.container_type": "<PERSON><PERSON><PERSON> de contenedor", "enum.dynamic_table_column_name.container_volume_size": "Volumen/tamaño del contenedor", "enum.dynamic_table_column_name.contract_id": "ID de contrato", "enum.dynamic_table_column_name.contract_item": "Artículo del contrato", "enum.dynamic_table_column_name.contract_number": "N.º de contrato", "enum.dynamic_table_column_name.cost_center": "Centro de coste", "enum.dynamic_table_column_name.currency": "Divisa", "enum.dynamic_table_column_name.customer_id": "ID de cliente", "enum.dynamic_table_column_name.customer_name": "Nombre del cliente", "enum.dynamic_table_column_name.customer_reference": "Referencia del cliente", "enum.dynamic_table_column_name.customer_approval_by": "Customer approval by", "enum.dynamic_table_column_name.customer_approval_date": "Customer approval date", "enum.dynamic_table_column_name.danger_label1": "Peligro lbl 1", "enum.dynamic_table_column_name.danger_label2": "Peligro lbl 2", "enum.dynamic_table_column_name.danger_label3": "Peligro lbl 3", "enum.dynamic_table_column_name.date": "<PERSON><PERSON>", "enum.dynamic_table_column_name.date_of_request": "<PERSON><PERSON>", "enum.dynamic_table_column_name.delivery_info": "Información de entrega", "enum.dynamic_table_column_name.disposal_certificate_number": "N.º de certificado de eliminación", "enum.dynamic_table_column_name.due_on": "Debido a", "enum.dynamic_table_column_name.end_treatment_center_id": "ID de centro de tratamiento final", "enum.dynamic_table_column_name.end_treatment_center_name": "Nombre del centro de tratamiento final", "enum.dynamic_table_column_name.esn_number": "Esn n °", "enum.dynamic_table_column_name.estimated_weight_or_volume_unit": "Unidad de peso/volumen estimada", "enum.dynamic_table_column_name.estimated_weight_or_volume_value": "Valor estimado de peso/volumen", "enum.dynamic_table_column_name.ewc_code": "Código EWC", "enum.dynamic_table_column_name.first_reminder_mail_status": "1er estado de recordatorio de correo", "enum.dynamic_table_column_name.first_reminder_on": "1er recordatorio en", "enum.dynamic_table_column_name.hazard_inducers": "Inductores de riesgos", "enum.dynamic_table_column_name.inquiry_number": "N.º de solicitud", "enum.dynamic_table_column_name.installation_name": "Nombre de instalación", "enum.dynamic_table_column_name.invoice": "Factura", "enum.dynamic_table_column_name.invoice_number": "Factura n °", "enum.dynamic_table_column_name.is_container_covered": "Contenedor cubierto", "enum.dynamic_table_column_name.is_hazardous": "<PERSON><PERSON><PERSON><PERSON>", "enum.dynamic_table_column_name.is_return_packaging": "Embalaje de devolución", "enum.dynamic_table_column_name.is_transport_by_indaver": "Transporte por Indaver", "enum.dynamic_table_column_name.issued_on": "Emitido por", "enum.dynamic_table_column_name.material_analysis": "An<PERSON><PERSON><PERSON> de <PERSON>", "enum.dynamic_table_column_name.material_number": "Material n °", "enum.dynamic_table_column_name.material_type": "Tipo de material", "enum.dynamic_table_column_name.name_installation": "Nombre de la instalación", "enum.dynamic_table_column_name.name_of_applicant": "Nombre del solicitante", "enum.dynamic_table_column_name.net_amount": "<PERSON>o neto", "enum.dynamic_table_column_name.number": "N.º", "enum.dynamic_table_column_name.order_number": "N.º de pedido", "enum.dynamic_table_column_name.packaged": "Empaquetado", "enum.dynamic_table_column_name.packaging_indicator": "Indicador de embalaje", "enum.dynamic_table_column_name.packaging_remark": "Comentario de envasado", "enum.dynamic_table_column_name.packaging_type": "Tipo de embalaje", "enum.dynamic_table_column_name.packing_group": "Embalar. \ngrupo", "enum.dynamic_table_column_name.payer_id": "Identificación del pagador", "enum.dynamic_table_column_name.payer_name": "Nombre del pagador", "enum.dynamic_table_column_name.pick_up_address_id": "ID de dirección de recogida", "enum.dynamic_table_column_name.pick_up_address_name": "Nombre de la dirección de recogida", "enum.dynamic_table_column_name.pickup_address": "Dirección de recogida", "enum.dynamic_table_column_name.po_number": "Po n °", "enum.dynamic_table_column_name.process_code": "Código de proceso", "enum.dynamic_table_column_name.quantity_containers": "N ° de contenedores", "enum.dynamic_table_column_name.quantity_labels": "Etiquetas de cantidad", "enum.dynamic_table_column_name.quantity_packages": "Paquetes de cantidad", "enum.dynamic_table_column_name.quantity_pallets": "Paleta de cantidad", "enum.dynamic_table_column_name.reconciliation_number": "Reconciliación n °", "enum.dynamic_table_column_name.remarks": "Observaciones", "enum.dynamic_table_column_name.request_number": "N.º de solicitud", "enum.dynamic_table_column_name.requested_by": "Solicitado por", "enum.dynamic_table_column_name.requested_end_date": "Fecha de fin solicitada", "enum.dynamic_table_column_name.requested_start_date": "Fecha de inicio solicitada", "enum.dynamic_table_column_name.sales_order": "Orden de venta", "enum.dynamic_table_column_name.sales_organisation_id": "ID de organización de ventas", "enum.dynamic_table_column_name.sales_organisation_name": "Nombre de la organización de ventas", "enum.dynamic_table_column_name.second_reminder_mail_status": "Segundo recordatorio del estado del correo", "enum.dynamic_table_column_name.second_reminder_on": "Segundo recordatorio en", "enum.dynamic_table_column_name.serial_number": "Serie n °", "enum.dynamic_table_column_name.status": "Estado", "enum.dynamic_table_column_name.tanker_type": "Tipo de petrolero", "enum.dynamic_table_column_name.tc_number": "Tc n °", "enum.dynamic_table_column_name.tfs_number": "N.º TFS", "enum.dynamic_table_column_name.third_reminder_mail_status": "3er estado de recordatorio", "enum.dynamic_table_column_name.total_quantity_pallets": "Paletas de cantidad total", "enum.dynamic_table_column_name.transport_mode": "Modo de transporte", "enum.dynamic_table_column_name.treatment_center_name": "Nombre del centro de tratamiento", "enum.dynamic_table_column_name.type": "Tipo", "enum.dynamic_table_column_name.un_number": "Número ONU", "enum.dynamic_table_column_name.vat_amount": "Cantidad de IVA", "enum.dynamic_table_column_name.waste_item": "Ítem de residuo", "enum.dynamic_table_column_name.waste_material": "Material del residuo", "enum.dynamic_table_column_name.waste_producer": "Productor de residuos", "enum.dynamic_table_column_name.waste_producer_id": "ID del productor de residuos", "enum.flashpoint.high": "> 60°", "enum.flashpoint.low": "< 23°", "enum.flashpoint.medium": "23° - 60°", "enum.flashpoint.unknown": "Desconocido / No aplicable", "enum.invoice_status.cleared": "<PERSON><PERSON><PERSON><PERSON>", "enum.invoice_status.draft": "<PERSON><PERSON><PERSON>", "enum.invoice_status.outstanding": "Pendiente", "enum.invoice_status.overdue": "<PERSON><PERSON><PERSON>", "enum.invoice_type.invoice": "Factura", "enum.invoice_type.unknown": "Desconocido", "enum.language.de": "Alemán", "enum.language.en": "Inglés", "enum.language.es": "Español", "enum.language.fr": "<PERSON><PERSON><PERSON><PERSON>", "enum.language.nl": "Neerlandés", "enum.legislation_and_properties.more_info_table.header.adr": "ADR", "enum.legislation_and_properties.more_info_table.header.clp": "CLP", "enum.legislation_and_properties.more_info_table.header.examples": "<PERSON><PERSON><PERSON><PERSON>", "enum.legislation_and_properties.more_info_table.header.waste_classified_as": "Residuo clasificado como", "enum.legislation_and_properties.more_info_table.header.waste_classified_as_or_substances": "Residuo clasificado como O que contiene sustancias clasificadas como", "enum.legislation_and_properties.more_info_table.header.waste_containing": "Residuo que contiene", "enum.legislation_and_properties.more_info_table.header.waste_subject_to": "Residuo sujeto a", "enum.legislation_and_properties.more_info_table.header.waste_substances_classified_as": "Residuo que contiene sustancias clasificadas como", "enum.legislation_and_properties.more_info_table.header.waste_substances_subject_to": "Residuo que contiene sustancias sujetas a", "enum.mail_status.not_sent": "No enviado", "enum.mail_status.sent": "Enviado", "enum.packaging.bulk.description": "Residuos no envasados que se transportan en grandes cantidades, normalmente en tanques, silos u otros contenedores a granel.", "enum.packaging.bulk.label": "A granel", "enum.packaging.packaged.description": "Residuos contenidos en unidades selladas y más pequeñas, como cilindros, bidones o botellas.", "enum.packaging.packaged.label": "<PERSON><PERSON><PERSON>", "enum.packaging_request_type.rental": "<PERSON><PERSON><PERSON>", "enum.packaging_request_type.sales": "Ventas", "enum.pickup_request_status.cancelled": "Cancelado", "enum.pickup_request_status.completed": "Completado", "enum.pickup_request_status.confirmed": "<PERSON><PERSON><PERSON><PERSON>", "enum.pickup_request_status.draft": "<PERSON><PERSON><PERSON>", "enum.pickup_request_status.indascan_draft": "<PERSON><PERSON><PERSON>", "enum.pickup_request_status.pending": "Pendiente", "enum.pickup_transport_mode.bulk_iso_tank.description": "Indicar no más de 1 posición de contrato.", "enum.pickup_transport_mode.bulk_iso_tank.label": "Contenedor cisterna a granel o isotanque", "enum.pickup_transport_mode.bulk_skips_container.description": "Indicar no más de 3 posiciones de contrato.", "enum.pickup_transport_mode.bulk_skips_container.label": "Residuos a granel en volquetes o contenedores", "enum.pickup_transport_mode.bulk_vacuum_tankers_road_tankers.description": "Indicar no más de 1 posición de contrato.", "enum.pickup_transport_mode.bulk_vacuum_tankers_road_tankers.label": "Residuos a granel en camiones cisterna de vacío o camión cisterna (con bomba)", "enum.pickup_transport_mode.packaged_curtain_sider_truck.description": "Indicar cualquier número de posiciones de contrato", "enum.pickup_transport_mode.packaged_curtain_sider_truck.label": "Residuos envasados en camión de lona o camión", "enum.pickup_transport_mode.packaging_request_order.label": "Orden de solicitud de empaque", "enum.publish_status.archived": "Archivado", "enum.publish_status.published": "Publicado", "enum.publish_status.scheduled": "Programado", "enum.regulated_transport.no": "No", "enum.regulated_transport.unknown": "Desconocido", "enum.regulated_transport.yes": "Sí", "enum.role.admin": "Administrador", "enum.role.user": "Usuario", "enum.sharepoint_document_status.active": "Activo", "enum.sharepoint_document_status.archived": "Archivado", "enum.sharepoint_document_view_name.bsc": "Tarjeta de puntaje equilibrada", "enum.sharepoint_document_view_name.contract": "Contratos", "enum.sharepoint_document_view_name.manual": "Manual de TWM", "enum.sharepoint_document_view_name.mastertable": "Maestro", "enum.sharepoint_document_view_name.meetings": "<PERSON><PERSON><PERSON>", "enum.sharepoint_document_view_name.quotation": "Citas", "enum.sharepoint_document_view_name.tfs": "Archivos TFS", "enum.sharepoint_document_view_name.transport": "Documentos de transporte", "enum.stable_temperature.ambient": "Ambiente", "enum.stable_temperature.average": "Proporcionar un promedio", "enum.stable_temperature.min_max": "Proporcionar un mínimo y un máximo", "enum.state_of_matter.gaseous": "Gaseoso", "enum.state_of_matter.liquid": "Líquido", "enum.state_of_matter.liquid_with_solids": "Líquido con sólidos", "enum.state_of_matter.no_data_available": "No hay datos disponibles", "enum.state_of_matter.powder": "Polvo", "enum.state_of_matter.sludgy": "<PERSON><PERSON>", "enum.state_of_matter.solid": "<PERSON><PERSON><PERSON><PERSON>", "enum.state_of_matter.viscous": "Viscoso", "enum.svhc_extra_option.<_1_mg_kg": "< 1 mg/kg de sustancias persistentes", "enum.svhc_extra_option.>_1_mg_kg": "> 1 mg/kg de sustancias persistentes", "enum.svhc_extra_option.other": "<PERSON><PERSON>", "enum.tanker_type.road_tanker": "Camión cisterna", "enum.tanker_type.road_tanker_pump": "Bomba de camión cisterna", "enum.tanker_type.vacuum_tanker": "Camión de vacío", "enum.waste_discharge_frequency.once_off_campaign.description": "Una recogida única como parte de una campaña específica.", "enum.waste_discharge_frequency.once_off_campaign.label": "Campaña única", "enum.waste_discharge_frequency.once_off_stream.description": "Un único evento de descarga no recurrente.", "enum.waste_discharge_frequency.once_off_stream.label": "<PERSON><PERSON><PERSON>", "enum.waste_discharge_frequency.regular_campaign.description": "Recogidas repetidas que ocurren dentro de campañas planificadas.", "enum.waste_discharge_frequency.regular_campaign.label": "Campaña regular", "enum.waste_discharge_frequency.regular_stream.description": "Descarga continua y consistente a intervalos establecidos.", "enum.waste_discharge_frequency.regular_stream.label": "Flujo regular", "enum.waste_inquiry_status.completed": "Completado", "enum.waste_inquiry_status.conformity_confirmed": "Conformidad confirmada", "enum.waste_inquiry_status.draft": "<PERSON><PERSON><PERSON>", "enum.waste_inquiry_status.in_progress": "En progreso", "enum.waste_inquiry_status.new": "Nuevo", "enum.waste_inquiry_status.not_relevant": "No relevante", "enum.waste_inquiry_status.offer_approved": "<PERSON><PERSON><PERSON> aprobada", "enum.waste_inquiry_status.offer_sent": "Oferta enviada", "enum.waste_inquiry_status.rejected": "<PERSON><PERSON><PERSON><PERSON>", "enum.waste_inquiry_status.solution_defined": "Solución definida", "enum.waste_legislation_option.animal_byproduct": "Subproductos animales", "enum.waste_legislation_option.animal_byproduct.more_info_table.classified_as": "Subproductos animales (SANDACH)", "enum.waste_legislation_option.animal_byproduct.more_info_table.examples": "<div><strong>Subproductos animales y productos derivados que se excluyen del consumo humano:</strong><ul><li>Piensos para animales (p. ej., a base de harina de pescado y proteína animal procesada, ...)</li><li>Fertilizantes orgánicos y enmiendas del suelo (p. ej., estiércol, guano, FO/ES procesados a base de proteína animal procesada, ...)</li><li>Productos técnicos (p. ej., alimentos para mascotas, cueros y pieles para cuero, lana, sangre para producir herramientas de diagnóstico, ...)</li></ul></div>", "enum.waste_legislation_option.animal_byproduct.more_info_table.subject_to": "<div><ul><li>Reglamento (CE) n.º 1069/2009</li></ul></div>", "enum.waste_legislation_option.controlled_drugs": "Drogas controladas", "enum.waste_legislation_option.controlled_drugs.more_info_table.classified_as": "Drogas controladas", "enum.waste_legislation_option.controlled_drugs.more_info_table.examples": "<div><strong><PERSON><PERSON><PERSON><PERSON><PERSON></strong><ul><li><PERSON><PERSON><PERSON></li><li><PERSON><PERSON><PERSON></li><li>Codeína</li><li>Cocaína</li><br></ul><strong>Psicotrópicos</strong><ul><li>Benzodiazepinas</li><li>Alprazolam</li><li>Xanax</li></ul></div>", "enum.waste_legislation_option.controlled_drugs.more_info_table.subject_to": "<div><ul><li>Convención Única sobre Estupefacientes de 1961</li><li>Convenio sobre Sustancias Sicotrópicas de 1971</li><li>O sujeto a la legislación local para drogas controladas.</li></ul></div>", "enum.waste_legislation_option.cwc": "CAQ (Convención sobre Armas Químicas)", "enum.waste_legislation_option.cwc.more_info_table.classified_as": "Armas Químicas", "enum.waste_legislation_option.cwc.more_info_table.examples": "<div><strong>Lista 1</strong><ul><li>Mostaza de azufre</li><li>Somán</li><li><PERSON><PERSON><PERSON></li><li>VX</li><br></ul><strong>Lista 2</strong><ul><li>Dicloruro de metilfosfonilo</li><li>Tricloruro de arsénico</li><br></ul><strong>Lista 3</strong><ul><li>Oxicloruro de fósforo</li><li>Tricloruro de fósforo</li><li>Cianuro de hidrógeno</li><li>Trietanolamina</li><li>Cloruro de tionilo</li><br></ul></div>", "enum.waste_legislation_option.cwc.more_info_table.subject_to": "Convención sobre Armas Químicas (CAQ) y mencionado en: Lista 1, o Lista 2, o Lista 3", "enum.waste_legislation_option.drug_precursor": "Precursor de drogas", "enum.waste_legislation_option.drug_precursor.more_info_table.classified_as": "Precursores de drogas", "enum.waste_legislation_option.drug_precursor.more_info_table.examples": "<div><strong>Categoría 1</strong><ul><li>Ergotamina</li><li>Efedrina</li><li><PERSON><PERSON><PERSON></li></ul><br><strong>Categoría 2</strong><ul><li>Permanganato de potasio</li><li><PERSON>cido feni<PERSON></li><li>Anhídrido acético</li><li>Ácido antranílico</li><li><PERSON><PERSON><PERSON></li></ul></div>", "enum.waste_legislation_option.drug_precursor.more_info_table.subject_to": "<div><ul><li>Reglamento (CE) n.º 273/2004</li></ul><p>Con un enfoque en las sustancias de categoría 1 y categoría 2 (sustancias activas).</p></div>", "enum.waste_legislation_option.hg_containing": "Residuos que contienen Hg", "enum.waste_legislation_option.hg_containing.more_info_table.containing": "<PERSON><PERSON><PERSON><PERSON>", "enum.waste_legislation_option.hg_containing.more_info_table.examples": "<div><ul><li>Residuos que contienen concentraciones relevantes de Hg</li><li>Compuestos de mercurio puros</li><li>Artículos con mercurio añadido</li><li>Mercurio metá<PERSON></li></ul></div>", "enum.waste_legislation_option.hg_containing.more_info_table.substances_subject_to": "<div><ul><li>Reglamento (CE) n.º 1102/2008</li><li>Convenio de Minamata</li></ul><p>Residuos que contienen mercurio en concentraciones que requieren atención específica durante el procesamiento para evitar la emisión de mercurio al medio ambiente.</p></div>", "enum.waste_legislation_option.infectious_waste": "Residuos infecciosos", "enum.waste_legislation_option.infectious_waste.more_info_table.adr": "Clase 6.2, <PERSON><PERSON><PERSON><PERSON>", "enum.waste_legislation_option.infectious_waste.more_info_table.classified_as": "<PERSON><PERSON><PERSON><PERSON>", "enum.waste_legislation_option.infectious_waste.more_info_table.clp": "Sin clasificación específica", "enum.waste_legislation_option.infectious_waste.more_info_table.examples": "<div><strong>Residuos potencialmente contaminados con virus, bacterias, hongos que pueden causar una enfermedad humana/animal, p. ej.</strong><ul><li><PERSON><PERSON><PERSON><PERSON> (clostridium botulinum)</li><li><PERSON><PERSON><PERSON> (bacillus anthracis)</li><li>Corona COVID-19</li><li>Legionella</li><li>Influenza</li><li>Ébola</li></ul></div>", "enum.waste_legislation_option.none": "<PERSON><PERSON><PERSON>", "enum.waste_legislation_option.ozon_depleting_substance": "Sustancias que agotan la capa de ozono y gases fluorados de efecto invernadero", "enum.waste_legislation_option.ozon_depleting_substance.more_info_table.clp": "H420 (sustancias SAO)", "enum.waste_legislation_option.ozon_depleting_substance.more_info_table.examples": "<div><strong>Reglamento (CE) n.º 1005/2009</strong><ul><li>Clorofluorocarbonos (CFC)</li><li>Hidroclorofluorocarbonos (HCFC)</li><li>Tetracloruro de carbono</li><li><PERSON><PERSON>uro de metilo</li><li><PERSON><PERSON></li></ul><br><strong>Reglamento (UE) n.º 517/2014</strong><ul><li>Hidrofluorocarbonos, perfluorocarbonos</li><li>Hexafluoruro de azufre</li><li>Trifluoruro de nitrógeno</li></ul></div>", "enum.waste_legislation_option.ozon_depleting_substance.more_info_table.substances_classified_as": "Sustancias que agotan la capa de ozono y/o gases fluorados de efecto invernadero", "enum.waste_legislation_option.ozon_depleting_substance.more_info_table.substances_subject_to": "<div><ul><li>Reglamento (CE) n.º 1005/2009</li><li>Reglamento (UE) n.º 517/2014</li></ul></div>", "enum.waste_legislation_option.radioactive": "Residuos radiactivos y NORM (materiales radiactivos de origen natural)", "enum.waste_legislation_option.radioactive.more_info_table.adr": "No siempre aplicable", "enum.waste_legislation_option.radioactive.more_info_table.classified_or_substances": "Radiactivo y/o material radiactivo de origen natural (NORM)", "enum.waste_legislation_option.radioactive.more_info_table.examples": "<div><strong>Radiactivo</strong><ul><li><PERSON><PERSON><PERSON> de torio</li><li>Acetato de uranilo</li><li>Cesio 131</li><li>Tritio</li></ul><br><strong>NORM</strong><ul><li>Óxido de zirconio</li><li>Plomo-121</li></ul></div>", "enum.waste_legislation_option.radioactive.more_info_table.subject_to": "Directiva 2013/59/Euratom (o definido por la legislación local): residuos con actividad de radiación por encima de los valores de actividad para la desclasificación mencionados en el anexo VII.", "enum.waste_legislation_option.svhc": "SVHC (Sustancias altamente preocupantes)", "enum.waste_legislation_option.svhc.more_info_table.examples": "<div><strong>Sustancias mencionadas en REACH (Reglamento (CE) n.º 1907/2006):</strong><ul><li>Autorización (anexo XIV)</li><li>Restricción (anexo XVII)</li><li>Lista de candidatas</li><li>Hexabromociclododecano</li><li>Compuestos de Cd/As/Pb</li><li>Dicromato de sodio</li><li>1,2-dicloroetano</li><li>Ftalato de dibutilo</li><li>Asbesto</li><li>Benceno</li><li>GEN-X</li><li>PFOA</li><li>PAH</li></ul></div>", "enum.waste_legislation_option.svhc.more_info_table.substances_classified_as": "SVHC (Sustancias altamente preocupantes)", "enum.waste_legislation_option.svhc.more_info_table.substances_subject_to": "<div><strong>Criterios del Art. 57 de REACH*:</strong><ul><li>Carcinogénico cat. 1A o 1B</li><li>Mutagénico en células germinales cat. 1A o 1B</li><li>Tóxico para la reproducción cat. 1A o 1B</li><li>PBT (persistente, bioacumulable y tóxico)</li><li>mPmB (muy persistente y muy bioacumulable)</li><li>Sustancias para las que existe evidencia científica de probables efectos graves para la salud humana o el medio ambiente que dan lugar a un nivel de preocupación equivalente al de otras sustancias enumeradas en los puntos (a) a (e). (p. ej., propiedades de alteración endocrina)</li></ul></div>", "enum.waste_legislation_option.svhc.more_info_table.substances_subject_to_note": "* Países Bajos: regulación ZZS", "enum.waste_loading_method.gravitational.description": "Los residuos se cargan por gravedad.", "enum.waste_loading_method.gravitational.label": "Gravitacional", "enum.waste_loading_method.pump_from_customer.description": "Los residuos se bombean desde el cliente.", "enum.waste_loading_method.pump_from_customer.label": "Bomba del cliente", "enum.waste_loading_method.pump_from_haulier.description": "Los residuos son bombeados por el transportista.", "enum.waste_loading_method.pump_from_haulier.label": "Bomba del transportista", "enum.waste_loading_type.before_waste_collection.description": "Los residuos se cargan antes de la recogida de residuos.", "enum.waste_loading_type.before_waste_collection.label": "Antes de la recogida de residuos", "enum.waste_loading_type.on_waste_collection.description": "Los residuos se cargan en la recogida de residuos.", "enum.waste_loading_type.on_waste_collection.label": "En la recogida de residuos", "enum.waste_measurement_unit.kg": "KG", "enum.waste_measurement_unit.m3": "M3", "enum.waste_measurement_unit.pc": "PC", "enum.waste_measurement_unit.to": "TO", "enum.waste_measurement_unit.yd3": "Yd3", "enum.waste_packaging.asf": "Asf", "enum.waste_packaging.asp": "<PERSON><PERSON>", "enum.waste_packaging.big_bag": "Big bag", "enum.waste_packaging.cardboard_box": "Caja de cartón", "enum.waste_packaging.ibc": "Ibc", "enum.waste_packaging.metal_drum": "Bidón de metal", "enum.waste_packaging.other": "<PERSON><PERSON>", "enum.waste_packaging.oversized_drum": "Bidón de gran tamaño", "enum.waste_packaging.plastic_drum": "Bidón de plástico", "enum.waste_packaging_size.not_applicable": "N/A", "enum.waste_packaging_size.one": "I", "enum.waste_packaging_size.three": "III", "enum.waste_packaging_size.two": "II", "enum.waste_ph.high": "4 - 10", "enum.waste_ph.low": "< 2", "enum.waste_ph.medium": "2 - 4", "enum.waste_ph.very_high": "> 10", "enum.waste_property_option.explosive": "Residuos explosivos (clase 1 del ADR) y explosivos desensibilizados", "enum.waste_property_option.explosive.more_info_table.adr_2": "Clase 3 y 4.1 con código de clasificación D y DT", "enum.waste_property_option.explosive.more_info_table.classified_as_1": "Explosivos", "enum.waste_property_option.explosive.more_info_table.classified_as_2": "Explosivos desensibilizados", "enum.waste_property_option.explosive.more_info_table.clp_1": "<div>H200<br>H201<br>H202<br>H203<br>H204<br>H205</div>", "enum.waste_property_option.explosive.more_info_table.clp_2": "<div>H206<br>H207<br>H208</div>", "enum.waste_property_option.explosive.more_info_table.examples_1": "<div><ul><li><PERSON><PERSON><PERSON> de amonio (explosivo)</li><li>Trinitrotolueno</li><li>Nitrocelulosa</li><li>Nitroglicerina</li><li><PERSON><PERSON><PERSON> pí<PERSON></li><li>Fuegos artificiales</li><li>Munición</li></ul></div>", "enum.waste_property_option.explosive.more_info_table.examples_2": "<div><ul><li>Nitrocelulosa con al menos 25% de agua</li><li>Nitroglicerina en alcohol</li><li><PERSON>cido pícrico en agua</li><li>ISDN con lactosa</li></ul></div>", "enum.waste_property_option.gaseous": "Residuos gaseosos", "enum.waste_property_option.gaseous.more_info_table.adr": "Clase 2: inflamable, tóxico, comburente y corrosivo)", "enum.waste_property_option.gaseous.more_info_table.classified_as": "Gaseoso", "enum.waste_property_option.gaseous.more_info_table.clp": "<div>H220<br>H221<br>H270</div>", "enum.waste_property_option.gaseous.more_info_table.examples": "<div><ul><li>Residuos tipo GLP</li><li>Cloruro de vinilo</li><li>Fosfina</li><li>Isobutileno</li><li>Amoníaco</li><li>Cloro</li><li><PERSON><PERSON><PERSON></li><li><PERSON><PERSON></li></ul></div>", "enum.waste_property_option.high_acute_toxic": "Residuos clasificados como de alta toxicidad aguda (T+)", "enum.waste_property_option.high_acute_toxic.more_info_table.adr": "<div>Clase 6.1<br>GE I y II</div>", "enum.waste_property_option.high_acute_toxic.more_info_table.classified_as": "Alta toxicidad aguda (T+)", "enum.waste_property_option.high_acute_toxic.more_info_table.clp": "<div>H300<br>H310<br>H330</div>", "enum.waste_property_option.high_acute_toxic.more_info_table.examples": "<div><ul><li>Compuestos de mercurio</li><li>Cianuro de hidrógeno</li><li>Fluoruro de hidrógeno</li><li>Cianuro de sodio</li><li>Dinitrobenceno</li><li>Alquilos de plomo</li><li><PERSON><PERSON></li></ul></div>", "enum.waste_property_option.none": "<PERSON><PERSON><PERSON>", "enum.waste_property_option.peroxide": "Peróxido (orgánico e inorgánico) o residuos autorreactivos", "enum.waste_property_option.peroxide.more_info_table.adr_1": "Clase 5.2", "enum.waste_property_option.peroxide.more_info_table.adr_2": "Clase 4.1", "enum.waste_property_option.peroxide.more_info_table.adr_3": "Clase 5.1", "enum.waste_property_option.peroxide.more_info_table.classified_as_or_substances_1": "Peróxido orgánico", "enum.waste_property_option.peroxide.more_info_table.classified_as_or_substances_2": "Autorreactivo", "enum.waste_property_option.peroxide.more_info_table.classified_as_or_substances_3": "Peróxido de hidrógeno", "enum.waste_property_option.peroxide.more_info_table.clp_1": "<div>H240<br>H241<br>H242</div>", "enum.waste_property_option.peroxide.more_info_table.clp_2": "<div>H240<br>H241<br>H242</div>", "enum.waste_property_option.peroxide.more_info_table.clp_3": "H271", "enum.waste_property_option.peroxide.more_info_table.examples": "<div><ul><li>AIBN (azobisisobutironitrilo)</li><li>Peróxido de di-terc-butilo</li><li>Peróxido de hidrógeno</li><li>Peróxido de dilauroílo</li><li>Peróxido de benzoílo</li></ul></div>", "enum.waste_property_option.polymerisation_sensitive": "Residuos sensibles a la polimerización", "enum.waste_property_option.polymerisation_sensitive.more_info_table.adr_1": "Sin clasificación específica.<br>Los monómeros con estabilización química están marcados con la disposición especial 386.", "enum.waste_property_option.polymerisation_sensitive.more_info_table.adr_2": "Sin clasificación específica", "enum.waste_property_option.polymerisation_sensitive.more_info_table.classified_as_or_substances_1": "Monómeros insaturados", "enum.waste_property_option.polymerisation_sensitive.more_info_table.classified_as_or_substances_2": "Oxiranos", "enum.waste_property_option.polymerisation_sensitive.more_info_table.clp_1": "Sin clasificación específica", "enum.waste_property_option.polymerisation_sensitive.more_info_table.clp_2": "Sin clasificación específica", "enum.waste_property_option.polymerisation_sensitive.more_info_table.examples": "<div><ul><li>Monómeros de metacrilato</li><li><PERSON><PERSON>o a<PERSON>rí<PERSON></li><li>Acrilonitrilo</li><li>Acrilato</li><li>Estireno</li><li>MDI</li><li>TDI</li></ul><br><ul><li>Epiclorhidrina</li></ul><br><ul><li><PERSON><PERSON><PERSON> de propileno</li><li>Óxido de etileno</li></ul></div>", "enum.waste_property_option.pyrophoric": "Residuos pirofóricos o sustancias que experimentan calentamiento espontáneo o líquidos muy inflamables", "enum.waste_property_option.pyrophoric.more_info_table.adr_1": "Clase 4.2", "enum.waste_property_option.pyrophoric.more_info_table.adr_2": "Clase 3, GE I", "enum.waste_property_option.pyrophoric.more_info_table.classified_as_1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enum.waste_property_option.pyrophoric.more_info_table.classified_as_2": "Líquidos muy inflamables", "enum.waste_property_option.pyrophoric.more_info_table.clp_1": "<div>H250<br>H251<br>H252</div>", "enum.waste_property_option.pyrophoric.more_info_table.clp_2": "H224", "enum.waste_property_option.pyrophoric.more_info_table.examples_1": "<div><ul><li>Fósforo blanco</li><li>Triclorosilano</li><li><PERSON><PERSON><PERSON></li></ul></div>", "enum.waste_property_option.pyrophoric.more_info_table.examples_2": "<div><ul><li>Isopreno de decantación</li><li><PERSON><PERSON></li><li>Isopentano</li></ul></div>", "enum.waste_property_option.reactive_with_f_gas": "Residuos que reaccionan con agua/aire/ácido/base con formación de gas inflamable (p. ej., H2, etano, ...)", "enum.waste_property_option.reactive_with_f_gas.more_info_table.adr": "Clase 4.3", "enum.waste_property_option.reactive_with_f_gas.more_info_table.classified_as_or_substances": "Reactivo con agua/aire/ácido/base con formación de gas inflamable*", "enum.waste_property_option.reactive_with_f_gas.more_info_table.clp": "<div>H260<br>H261</div>", "enum.waste_property_option.reactive_with_f_gas.more_info_table.examples": "<div><ul><li><PERSON><PERSON><PERSON><PERSON> (borohidruro de sodio/potasio, hidruro de litio y aluminio)</li><li>Metales alcalinos (Na, K,...)</li><li>Metales (Mg, Al,...)</li><li><PERSON><PERSON>los metálic<PERSON></li></ul></div>", "enum.waste_property_option.reactive_with_f_gas.more_info_table.examples_note": "*Ejemplos de gases inflamables: H2, <PERSON><PERSON>,...", "enum.waste_property_option.reactive_with_t_gas": "Residuos que reaccionan con agua/aire/ácido/base con formación de un gas tóxico (p. ej., HCI/CI2, HCN, H2S, NH3, NOx, PH3, ...)", "enum.waste_property_option.reactive_with_t_gas.more_info_table.adr": "Sin clasificación específica", "enum.waste_property_option.reactive_with_t_gas.more_info_table.classified_as_or_substances": "Reactivo con agua/aire/ácido/base con formación de gas tóxico*", "enum.waste_property_option.reactive_with_t_gas.more_info_table.clp": "<div>H014<br>H029<br>H031<br>H032</div>", "enum.waste_property_option.reactive_with_t_gas.more_info_table.examples": "<div><ul><li>Compuestos de fosfuro (fosfuro de aluminio, fosfuro de calcio, fosfuro de magnesio,...)</li><li>Cloruros de ácido (cloruro de tionilo, oxicloruro de fósforo, tricloruro de fósforo, tetracloruro de titanio,...)</li><li>Hipohalogenuros (hipoclorito de sodio,...) </li><li>Compuestos de cianuro</li><li>Compuestos de sulfuro</li><li>Sales de amonio</li></ul></div>", "enum.waste_property_option.reactive_with_t_gas.more_info_table.examples_note": "*Ejemplos de gases tóxicos: HCI/CI2, HCN, H2S, NH3, NOX, PH3,...", "enum.waste_property_option.strong_oxidizing": "Fuertemente comburente", "enum.waste_property_option.strong_oxidizing.more_info_table.adr": "Clase 5.1", "enum.waste_property_option.strong_oxidizing.more_info_table.classified_as_or_substances": "Fuertemente comburente", "enum.waste_property_option.strong_oxidizing.more_info_table.clp": "H270<br>H271<br>H272", "enum.waste_property_option.strong_oxidizing.more_info_table.examples": "<div><ul><li>Permanganato de potasio</li><li>Trióxido de cromo (VI)</li><li>Nitrato de amonio</li><li>Percloratos</li><li>Cloratos</li></ul></div>", "enum.waste_property_option.thermal_unstable": "Residuos térmicamente inestables", "enum.waste_property_option.thermal_unstable.more_info_table.adr": "Sin clasificación específica", "enum.waste_property_option.thermal_unstable.more_info_table.classified_as_or_substances": "Termodinámicamente inestable", "enum.waste_property_option.thermal_unstable.more_info_table.clp": "Sin clasificación específica", "enum.waste_property_option.thermal_unstable.more_info_table.examples": "<div><ul><li>Sulfóxidos orgánicos</li><li>Diaz<PERSON>/diazonio</li><li><PERSON><PERSON><PERSON></li><li>Isocianato</li><li>Hidracina</li><li>N-óxidos</li><li>Alquinos</li><li>Alquenos</li><li>Nitroco</li><li>N-nitro</li></ul></div>", "enum.waste_stored_in.drums.description": "Bidones para almacenar residuos.", "enum.waste_stored_in.drums.label": "Bidones", "enum.waste_stored_in.ibcs.description": "IBC para almacenar residuos.", "enum.waste_stored_in.ibcs.label": "IBC", "enum.waste_stored_in.other.description": "Otro tipo de almacenamiento de residuos.", "enum.waste_stored_in.other.label": "<PERSON><PERSON>", "enum.waste_stored_in.storage_tank.description": "Tanque de almacenamiento para almacenar residuos.", "enum.waste_stored_in.storage_tank.label": "Tanque de almacenamiento", "enum.waste_stored_in.tank_container.description": "Contenedor cisterna para almacenar residuos.", "enum.waste_stored_in.tank_container.label": "Contenedor cisterna", "enum.waste_transport_in.no_preference.description": "Sin preferencia para el transporte de residuos.", "enum.waste_transport_in.no_preference.label": "Sin preferencia", "enum.waste_transport_in.other.description": "Otro tipo de transporte de residuos.", "enum.waste_transport_in.other.label": "<PERSON><PERSON>", "enum.waste_transport_in.tank_container.description": "Un contenedor para transportar residuos.", "enum.waste_transport_in.tank_container.label": "Contenedor cisterna", "enum.waste_transport_in.tank_trailer.description": "Un remolque para transportar residuos.", "enum.waste_transport_in.tank_trailer.label": "Remolque cisterna", "enum.waste_transport_type.container.description": "Un contenedor para el transporte de residuos.", "enum.waste_transport_type.container.label": "<PERSON><PERSON><PERSON><PERSON>", "enum.waste_transport_type.other.description": "Otro tipo de transporte de residuos.", "enum.waste_transport_type.other.label": "<PERSON><PERSON>", "enum.waste_transport_type.rel_truck.description": "Un camión para transportar residuos.", "enum.waste_transport_type.rel_truck.label": "Camión REL/REF", "enum.waste_transport_type.skip.description": "Transporte de residuos en volquete.", "enum.waste_transport_type.skip.label": "<PERSON><PERSON><PERSON>", "enum.waste_transport_type.tripper_truck.description": "Un camión para transportar residuos.", "enum.waste_transport_type.tripper_truck.label": "Camión volquete", "error.bad_request.description": "Algo salió mal. <PERSON><PERSON> favor, inténtelo de nuevo.", "error.bad_request.title": "Solicitud incorrecta", "error.default_error.description": "Por favor, inténtelo de nuevo más tarde.", "error.default_error.title": "Algo salió mal", "error.forbidden.description": "No tiene permiso para acceder a este recurso.", "error.forbidden.title": "Prohibido", "error.internal_server_error.description": "Por favor, inténtelo de nuevo más tarde.", "error.internal_server_error.title": "Error interno del servidor", "error.invalid_form_input.description": "Por favor, revise los campos resaltados e inténtelo de nuevo.", "error.invalid_form_input.title": "Formulario no válido", "error.resource_not_found.description": "No se pudo encontrar el recurso solicitado.", "error.resource_not_found.title": "Recurso no encontrado", "error.unauthorized.description": "No está autorizado para acceder a este recurso.", "error.unauthorized.title": "No autorizado", "error.validation_error.description": "Por favor, compruebe si hay errores en el formulario.", "error.validation_error.title": "Error de validación", "form.fields.email": "Correo electrónico", "form.fields.password": "Contraseña", "form.save_changes": "Guardar cambios", "impersonation.warning.continue": "<PERSON><PERSON><PERSON><PERSON>", "impersonation.warning.description": "Has estado inactivo durante 5 minutos mientras suplantabas a {user}. ¿Quieres continuar o detener la suplantación?", "impersonation.warning.stop": "Detener suplantación", "impersonation.warning.title": "<PERSON><PERSON>lant<PERSON>", "module.admin.overview.title": "Administración del sistema", "module.auth.legal_links.disclaimer.paragraph_1": "The Indaver Portal and all subordinate systems (a.o. the Indaver Customer Zone, the Indaver Supplier Zone, the Indaver E-reporting...) and documents were developed with the best care possible. However, Indaver cannot be held responsible for the proper description of it, nor for the proper functioning of each of the functionalities.", "module.auth.legal_links.disclaimer.paragraph_2": "The Indaver Portal and the subordinate systems and documentation are property of Indaver and may not be copied or be disclosed to third parties or made available, in any way and any medium without the prior written approval of Indaver.", "module.auth.legal_links.disclaimer.paragraph_3": "Indaver is in this respect not responsible for any loss of data that were entered, nor for any incorrect processing by the software available or for the consequences arising from it.", "module.auth.legal_links.disclaimer.paragraph_4": "Indaver provides no warranties regarding the availability or functionality of the Indaver Portal and the subordinate systems and documents.", "module.auth.legal_links.disclaimer.paragraph_5": "Indaver is not responsible for any damage to the IT systems (hardware or software) of the user whether directly related or not to the use of the Indaver Portal.", "module.auth.legal_links.disclaimer.paragraph_6": "The user commits himself to use the Indaver Portal only in connection with the proposed objectives and refrain from this for any other use, either for himself or for others to use, make available, copy or modify.", "module.auth.legal_links.disclaimer.paragraph_7": "Indaver reserves at all times the right to deny access to the Indaver Portal, either individually or collectively, in particular for breaches of these provisions set as access conditions, or even as a precaution against damage to the systems of Indaver, regardless if these are related to the use of the system by the user.", "module.auth.legal_links.disclaimer.paragraph_8": "By the mere use of the Indaver Portal, the user is considered to have accepted the conditions.", "module.auth.login.customer_zone.label": "Customer Zone", "module.auth.login.description": "Inicie sesión para continuar a la zona de clientes.", "module.auth.login.error": "Algo salió mal al iniciar sesión. Inténtelo de nuevo más tarde.", "module.auth.login.error.description": "Algo salió mal al iniciar sesión. Por favor, inténtelo de nuevo.", "module.auth.login.error.title": "Algo salió mal", "module.auth.login.leading_text": "Su portal seguro para administrar sus desechos", "module.auth.login.page_title": "In<PERSON><PERSON>", "module.auth.login.sign_in": "In<PERSON><PERSON>", "module.auth.login.title": "¡Bienvenido!", "module.auth.login.visit_website": "Indaver es un líder en", "module.auth.login.waste_management.label": "Waste management", "module.auth.roles.error.description": "Póngase en contacto con su administrador para verificar sus roles.", "module.auth.roles.error.title": "No tienes ningún rol asignado", "module.certificate.overview.title": "Certificados", "module.contract.overview.bulk.download_pdf": "Descargar criterios de aceptación", "module.contract.overview.bulk.plan": "Planificar recogida", "module.contract.overview.bulk.plan_error.different_waste_producers": "Todos los contratos seleccionados deben tener el mismo productor de residuos para comenzar una solicitud de recogida", "module.contract.overview.bulk.plan_error.no_customer": "Parece haber un error con la ID del cliente de los elementos seleccionados.", "module.contract.overview.title": "Contratos", "module.dashboard.features.news_detail.more_news": "Más noticias", "module.dashboard.features.overview.announcements.title": "<PERSON><PERSON><PERSON><PERSON>", "module.dashboard.features.overview.greeting.afternoon": "Buenas tardes", "module.dashboard.features.overview.greeting.evening": "Buenas noches", "module.dashboard.features.overview.greeting.morning": "Buenos días", "module.dashboard.features.overview.news.no_news": "Actualmente no hay noticias disponibles", "module.dashboard.features.overview.news.title": "Noticias", "module.dashboard.features.overview.newsletter.email": "Dirección de correo electrónico", "module.dashboard.features.overview.newsletter.subscribe": "Suscribirse", "module.dashboard.features.overview.newsletter.subscribe_success.description": "Se ha suscrito correctamente a nuestro boletín de noticias.", "module.dashboard.features.overview.newsletter.subscribe_success.title": "¡<PERSON><PERSON><PERSON>!", "module.dashboard.features.overview.newsletter.title": "Boletín de noticias", "module.dashboard.overview.newsletter.title": "¡Manténgase al día! Suscríbase a nuestro boletín introduciendo su dirección de correo electrónico a continuación.", "module.dashboard.overview.page_title": "Panel de control", "module.dashboard.overview.title": "Panel de control", "module.document.overview.columns.action_date": "Fecha de acción", "module.document.overview.columns.applicable_from": "Aplicable de", "module.document.overview.columns.applicable_until": "Aplicable hasta", "module.document.overview.columns.document_name": "Nombre del documento", "module.document.overview.columns.status": "Estado", "module.document.overview.columns.type_tfs": "Escriba TFS", "module.document.overview.columns.waste_producer": "Productor de residuos", "module.document.overview.filters.customer": "Cliente", "module.document.overview.filters.status": "Estado", "module.document.overview.filters.waste_producer": "Productores de residuos", "module.document.overview.filters.year": "<PERSON><PERSON>", "module.document.overview.title": "Mis documentos", "module.document.overview.tooltip": "Hemos agrupado todos los documentos relacionados con su paquete TWM y proporcionamos servicios. \nSi no puede encontrar un documento específico, no dude en llamar a su contacto de confianza a Indaver.", "module.guidance_letter.overview.title": "Cartas de porte", "module.guidance_letters.overview.table.download_attachment": "Download attachment", "module.guidance_letters.overview.table.download_preview": "Descargar vista previa", "module.guidance_letters.overview.table.download_print": "Descargar para imprimir", "module.invoice.create": "<PERSON><PERSON><PERSON> factura", "module.invoice.create.title": "<PERSON><PERSON><PERSON> factura", "module.invoice.detail.amount": "Cantidad", "module.invoice.detail.edit_invoice": "Editar fact<PERSON>", "module.invoice.detail.files": "Archivos", "module.invoice.detail.no_certificate_available": "No hay certificado disponible", "module.invoice.detail.no_customer_reference": "Sin referencia del cliente", "module.invoice.detail.no_document_available": "No hay documento disponible", "module.invoice.detail_title": "Detalle de la factura", "module.invoice.info": "Información de factura", "module.invoice.label.plural": "Facturas", "module.invoice.overview.status": "Estado", "module.invoice.overview.tab.all": "Todo", "module.invoice.overview.tab.approved": "Aprobado", "module.invoice.overview.tab.cleared": "<PERSON><PERSON><PERSON><PERSON>", "module.invoice.overview.tab.draft": "Damas", "module.invoice.overview.tab.open": "<PERSON>bie<PERSON>o", "module.invoice.overview.tab.outstanding": "Pendiente", "module.invoice.overview.tab.overdue": "<PERSON><PERSON><PERSON>", "module.invoice.overview.tab.paid": "<PERSON><PERSON>", "module.invoice.overview.tab.proforma": "Profesora", "module.invoice.overview.tab.rejected": "<PERSON><PERSON><PERSON><PERSON>", "module.invoice.overview.tab.submitted": "Enviado", "module.invoice.overview.tab.to_be_approved": "Para ser aprobado", "module.invoice.overview.title": "Facturas", "module.invoice.title": "Factura", "module.invoice.unknown": "Factura desconocida", "module.invoice.update.title": "Actualizar factura", "module.invoice.uuid": "Factura uuid", "module.invoices.review.approve_description": "Opcionalmente, proporcione un comentario y/o un número de PO.", "module.invoices.review.approve_subtitle": "Aprobar la factura", "module.invoices.review.approve_title": "¿Estás seguro de que quieres aprobar la factura `{invoiceNumber}`?", "module.invoices.review.fields.approve_reason": "Razón de aprobación", "module.invoices.review.fields.comment": "Observación", "module.invoices.review.fields.po_number": "Número de PO", "module.invoices.review.fields.reject_reason": "Razón de rechazo", "module.invoices.review.reject_description": "Proporcione un comentario que indique el motivo del rechazo", "module.invoices.review.reject_subtitle": "<PERSON><PERSON><PERSON>", "module.invoices.review.reject_title": "¿Estás seguro de que quieres rechazar la factura `{invoiceNumber}`?", "module.invoices.review.submit_approval": "Enviar y aprobar", "module.invoices.review.submit_rejection": "Enviar y rechazar", "module.language_management.overview.title": "Traducciones", "module.news.announcement.delete.success_message.description": "Su anuncio ha sido eliminado correctamente y ya no está visible.", "module.news.announcement.delete.success_message.title": "<PERSON><PERSON><PERSON> eliminado", "module.news.announcement.update.title": "<PERSON><PERSON>", "module.news.announcement_overview.title": "<PERSON><PERSON><PERSON><PERSON>", "module.news.announcement_overview.urgent_announcement": "Anuncio urgente", "module.news.announcements.create.page_title": "Nuevo anuncio", "module.news.announcements.update.return_to_overview": "Todos los anuncios", "module.news.article.create.page_title": "Nuevo artículo", "module.news.article.delete.success_message.description": "Su artículo ha sido eliminado correctamente y ya no está visible.", "module.news.article.delete.success_message.title": "<PERSON><PERSON><PERSON><PERSON> eliminado", "module.news.article.fields.end_date": "<PERSON><PERSON>", "module.news.article.fields.hint": "En este campo solo se aceptan etiquetas <iframe>. Para incrustar contenido, copie el código completo de incrustación iframe de plataformas como YouTube, Vimeo u otro servicio que esté utilizando.", "module.news.article.fields.publishing_date": "Fecha de publicación", "module.news.article.fields.start_date": "<PERSON><PERSON>", "module.news.article.fields.video": "Vídeo", "module.news.article.fields.video_placeholder": "<PERSON><PERSON><PERSON><PERSON>", "module.news.article.update.page_title": "<PERSON><PERSON>", "module.news.delete.success_message.description": "Su artículo ha sido eliminado correctamente y ya no está visible.", "module.news.delete.success_message.title": "<PERSON><PERSON><PERSON><PERSON> eliminado", "module.news.overview.long_title": "<PERSON><PERSON><PERSON><PERSON>", "module.news.overview.new_announcement": "Nuevo anuncio", "module.news.overview.new_article": "Nuevo artículo", "module.news.overview.no_active_filters": "No hay filtros activos", "module.news.overview.table.author": "Autor", "module.news.overview.table.ends_on": "Finaliza el", "module.news.overview.table.published_on": "Publicado el", "module.news.overview.table.status": "Estado", "module.news.overview.table.title": "<PERSON><PERSON><PERSON><PERSON>", "module.news.overview.title": "Noticias", "module.news.update.created_on_date_by_user": "<PERSON><PERSON>o el {date} por {user}", "module.news.update.delete_article": "Eliminar", "module.news.update.delete_message": "¿Está seguro de que desea eliminar este artículo? Esta acción no se puede deshacer.", "module.news.update.fields.content": "Contenido", "module.news.update.fields.from": "<PERSON><PERSON>", "module.news.update.fields.image": "Imagen", "module.news.update.fields.schedule_publishing": "Programar publicación", "module.news.update.fields.status": "Estado", "module.news.update.fields.title": "<PERSON><PERSON><PERSON><PERSON>", "module.news.update.fields.title_placeholder": "<PERSON><PERSON><PERSON><PERSON>", "module.news.update.fields.until": "<PERSON><PERSON>", "module.news.update.publish_announcement": "Publicar", "module.news.update.publish_article": "Publicar", "module.news.update.publish_success_message.description": "Su artículo ha sido publicado correctamente.", "module.news.update.publish_success_message.title": "Artículo publicado", "module.news.update.return_to_overview": "Todos los artículos", "module.news.update.save_changes": "Guardar cambios", "module.news.update.success_message.description": "Su artículo se ha guardado correctamente.", "module.news.update.success_message.title": "<PERSON><PERSON><PERSON><PERSON> guardado.", "module.news.update.validation.at_least_one_translation": "Al menos 1 traducción debe tener un título y un contenido.", "module.news.update.validation.end_date_after_start_date": "La fecha de finalización debe ser posterior a la fecha de inicio", "module.news.update.validation.image_required": "Se requiere una imagen en miniatura.", "module.news.update.validation.start_date_today_or_future": "La fecha debe ser hoy o en el futuro", "module.news.update.validation.title_and_content_required": "Tanto el título como el contenido deben rellenarse, o ambos deben dejarse vacíos.", "module.news.update_announcement.delete_announcement": "Eliminar", "module.news.update_announcement.delete_message": "¿Está seguro de que desea eliminar este anuncio? Esta acción no se puede deshacer.", "module.news.update_announcement.fields.type": "<PERSON><PERSON><PERSON> de anuncio", "module.news.update_announcement.page_title": "<PERSON><PERSON>", "module.news.update_announcement.publish_success_message.description": "Su anuncio ha sido publicado correctamente.", "module.news.update_announcement.publish_success_message.title": "<PERSON><PERSON><PERSON> publicado", "module.news.update_announcement.return_to_overview": "Todos los anuncios", "module.news.update_announcement.success_message.description": "Su anuncio se ha guardado correctamente.", "module.news.update_announcement.success_message.title": "<PERSON><PERSON><PERSON> guardado", "module.packaging_request.overview.new_request": "Nueva solicitud de empaque", "module.packaging_request.update.customer_and_location.customer.title": "Cliente", "module.packaging_request.update.customer_and_location.delivery.title": "Dirección de entrega", "module.packaging_request.update.customer_and_location.title": "Productor de residuos y dirección de entrega", "module.packaging_request.update.customer_and_location.waste_producer.title": "Productor de residuos", "module.packaging_request.update.delivery.add_contact": "Agregar contacto", "module.packaging_request.update.delivery.add_existing_contact": "Agregar contacto existente", "module.packaging_request.update.delivery.date_or_period": "Fecha o período preferidos", "module.packaging_request.update.delivery.remarks": "¿Tienes algún comentario para nosotros?", "module.packaging_request.update.delivery.remarks_placeholder": "Observaciones, comentarios o preguntas?", "module.packaging_request.update.delivery.send_copy_to_contacts": "¿Quieres enviar una copia a otros contactos?", "module.packaging_request.update.delivery.title": "Entrega", "module.packaging_request.update.delivery_details": "Detalles de entrega", "module.packaging_request.update.general_info": "Información general", "module.packaging_request.update.packaging.label": "Seleccione el embalaje que necesita", "module.packaging_request.update.packaging.table.cost_center": "Centro de costos", "module.packaging_request.update.packaging.table.image_alt": "Imagen para el número de material {number}", "module.packaging_request.update.packaging.table.po_number": "Número de PO", "module.packaging_request.update.packaging.title": "Selección de embalaje", "module.packaging_request.update.packaging.too_little_selected": "Necesita seleccionar al menos 1", "module.packaging_request.update.page_title": "Solicitud de empaque", "module.packaging_request.update.submit.email_label": "Correo electrónico", "module.packaging_request.update.submit.email_placeholder": "ejemploemail.com", "module.packaging_request.update.submit.first_name": "Nombre de pila", "module.packaging_request.update.submit.last_name": "Apellido", "module.packaging_request.update.submit.request_submitted": "Su embalaje ha sido enviado.", "module.packaging_request.update.submit.request_submitted_description": "Su solicitud ha sido enviada con éxito con la ID {id}. Nos pondremos en contacto con usted pronto.", "module.packaging_request.update.submit.return_to_overview": "Volver a la vista general", "module.packaging_request.update.submit.success": "¡Enviado!", "module.packaging_request.update.submit.success_description": "Solicitud de empaque enviada correctamente con ID {ID}", "module.packaging_request.update.submit.thank_you": "¡<PERSON><PERSON><PERSON>!", "module.permissions.overview.title": "<PERSON><PERSON><PERSON>", "module.pickup_request.detail.confirmed": "Recogida confirmada desde el {start} hasta el {end}", "module.pickup_request.detail.created_on": "Creado el", "module.pickup_request.detail.page_title": "Solicitud de recogida", "module.pickup_request.detail.requested": "Recogida solicitada desde el {start} hasta el {end}", "module.pickup_request.detail.submitted_id": "ID {id}", "module.pickup_request.detail.title": "Recogida para", "module.pickup_request.overview.bulk.delete_draft": "Eliminar borradores", "module.pickup_request.overview.bulk.delete_draft_description": "Sus borradores seleccionados se eliminarán de forma permanente", "module.pickup_request.overview.filter.draft_status": "Estado de borrador", "module.pickup_request.overview.filter.hazardous": "<PERSON><PERSON><PERSON><PERSON>", "module.pickup_request.overview.filter.transport_by_indaver": "Transporte por Indaver", "module.pickup_request.overview.filter.transport_mode": "Modo de transporte", "module.pickup_request.overview.new_pickup": "Nueva recogida", "module.pickup_request.overview.new_request": "Nueva solicitud", "module.pickup_request.overview.tab.awaiting_booking": "Esperando reserva", "module.pickup_request.overview.tab.booked": "Reservado", "module.pickup_request.overview.tab.cancelled": "Cancelado", "module.pickup_request.overview.tab.completed": "Completado", "module.pickup_request.overview.tab.drafts": "Borradores", "module.pickup_request.overview.tab.indascan_drafts": "Indascan", "module.pickup_request.overview.tab.submitted": "Enviado", "module.pickup_request.overview.title": "Solicitudes de recogida", "module.pickup_request.sidebar.title": "Recogidas", "module.pickup_request.update.administration.fields.cost_center": "Centro de coste", "module.pickup_request.update.administration.fields.po_number": "Número de pedido", "module.pickup_request.update.administration.fields.serial_number": "Número de serie", "module.pickup_request.update.administration.fields.tfs_number": "Número de TFS", "module.pickup_request.update.administration.fields.waste": "Residuo", "module.pickup_request.update.administration.title": "Administración", "module.pickup_request.update.container_info.title": "Información del contenedor", "module.pickup_request.update.customer_and_location.title": "Productor de residuos y dirección de recogida", "module.pickup_request.update.details.asn": "<PERSON>n", "module.pickup_request.update.details.bulk_unit": "Unidades a granel", "module.pickup_request.update.details.bulk_unit_description": "Estos cambios se aplicarán a todas las líneas residuales. \nTodavía podrá cambiar la unidad para cada línea de desechos individualmente.", "module.pickup_request.update.details.bulk_unit_weight_volume": "Establecer 'Unidad de peso/volumen estimada' a:", "module.pickup_request.update.details.customer_reference": "Referencia del cliente", "module.pickup_request.update.details.delivery_info": "Información de entrega", "module.pickup_request.update.details.esn_number": "Esn", "module.pickup_request.update.details.ewc_code": "Código EWC", "module.pickup_request.update.details.material_analysis": "Análisis de materiales", "module.pickup_request.update.details.materials_error": "Parece que tiene un error en este campo: '{campo}'. \nTenga en cuenta que este campo podría estar oculto debido a sus columnas dinámicas activas.", "module.pickup_request.update.details.process_code": "Código de proceso", "module.pickup_request.update.details.title": "Detalles", "module.pickup_request.update.details.waste_material": "Material de desecho", "module.pickup_request.update.general_info": "Información general", "module.pickup_request.update.packaging.fields.container_covered": "Cont. cubierto", "module.pickup_request.update.packaging.fields.container_number": "N.º de cont.", "module.pickup_request.update.packaging.fields.container_size_volume": "Cont. \ntamaño/volumen", "module.pickup_request.update.packaging.fields.container_type": "Tipo de cont.", "module.pickup_request.update.packaging.fields.estimated_weight_volume": "Peso/volumen est.", "module.pickup_request.update.packaging.fields.is_return_packaging": "Embalaje de retorno", "module.pickup_request.update.packaging.fields.is_return_packaging_description": "Me gustaría recibir la misma cantidad y tipo de embalaje de vuelta", "module.pickup_request.update.packaging.fields.is_return_packaging_tooltip": "Solo es relevante si desea que se le devuelva exactamente el mismo embalaje. Solo aplicable para: IBC, ASF, ASP, Contenedor cisterna, Gebinde im ASP", "module.pickup_request.update.packaging.fields.number_of_containers": "Número de cont.", "module.pickup_request.update.packaging.fields.packaging_remark": "Observaciones sobre el embalaje", "module.pickup_request.update.packaging.fields.packaging_type": "Tipo de emb.", "module.pickup_request.update.packaging.fields.quantity_labels": "N.º de etiquetas", "module.pickup_request.update.packaging.fields.quantity_packages": "N.º de emb.", "module.pickup_request.update.packaging.fields.quantity_pallets": "N.º de palés", "module.pickup_request.update.packaging.fields.reconciliation_number": "Reconciliación n °", "module.pickup_request.update.packaging.fields.tanker_type": "Tipo de cisterna", "module.pickup_request.update.packaging.fields.total_quantity_pallets": "Número total de palés", "module.pickup_request.update.packaging.fields.transport_type": "Tipo de transp.", "module.pickup_request.update.packaging.fields.unit": "Unidad", "module.pickup_request.update.packaging.fields.waste": "Residuo", "module.pickup_request.update.packaging.placeholder.amount": "0", "module.pickup_request.update.packaging.placeholder.container_type": "<PERSON><PERSON><PERSON> de contenedor", "module.pickup_request.update.packaging.placeholder.container_volume_size": "4mx2mx2m", "module.pickup_request.update.packaging.placeholder.hazard_inducers": "Inductores de riesgos", "module.pickup_request.update.packaging.placeholder.long_number": "0", "module.pickup_request.update.packaging.placeholder.packaging_type": "Tipo de embalaje", "module.pickup_request.update.packaging.placeholder.tanker_type": "Tipo de cisterna", "module.pickup_request.update.packaging.placeholder.transport_type": "Tipo de transporte", "module.pickup_request.update.packaging.placeholder.un_number": "Número UN", "module.pickup_request.update.packaging.title": "Información de embalaje", "module.pickup_request.update.packaging_request.title": "Envasado de pedido", "module.pickup_request.update.page_title": "Nueva recogida", "module.pickup_request.update.pickup_details": "Detalles de la recogida", "module.pickup_request.update.planning.additional_files": "¿Desea cargar archivos adicionales?", "module.pickup_request.update.planning.additional_files_hint": "P. ej., im<PERSON><PERSON>s, fichas de productos, listas de productos químicos, etc.", "module.pickup_request.update.planning.date_in_future": "La fecha de inicio debe ser en el futuro.", "module.pickup_request.update.planning.date_or_period": "Fecha o período preferido", "module.pickup_request.update.planning.remarks": "¿Tiene alguna observación para nosotros?", "module.pickup_request.update.planning.remarks_placeholder": "¿Observaciones, comentarios o preguntas?", "module.pickup_request.update.planning.title": "Planificación", "module.pickup_request.update.planning.wic_confirmation_label": "Confirmo que este desecho está alineado con el WIC firmado", "module.pickup_request.update.quick_entry_mode": "Modo de entrada rápido", "module.pickup_request.update.return_to_overview": "Todas las solicitudes de recogida", "module.pickup_request.update.submit.add_contact": "<PERSON><PERSON><PERSON>", "module.pickup_request.update.submit.add_existing_contact": "Añadir contacto existente", "module.pickup_request.update.submit.almost_done": "¡Casi listo!", "module.pickup_request.update.submit.edit_pickup": "Editar recogida", "module.pickup_request.update.submit.email_label": "Correo electrónico", "module.pickup_request.update.submit.email_placeholder": "ejemplo{at}email.com", "module.pickup_request.update.submit.first_name": "Nombre", "module.pickup_request.update.submit.last_name": "Apellido", "module.pickup_request.update.submit.request_submitted": "Su recogida ha sido enviada", "module.pickup_request.update.submit.request_submitted_description": "Su solicitud ha sido enviada correctamente con el ID {id}. Nos pondremos en contacto con usted en breve.", "module.pickup_request.update.submit.return_to_overview": "Volver a la vista general", "module.pickup_request.update.submit.send_copy_to_contacts": "¿Desea enviar una copia a otros contactos?", "module.pickup_request.update.submit.submit_pickup": "Enviar recogida", "module.pickup_request.update.submit.submit_request": "¿Listo para enviar su recogida?", "module.pickup_request.update.submit.thank_you": "¡<PERSON><PERSON><PERSON>!", "module.pickup_request.update.transport.fields.adr_class": "Clase ADR", "module.pickup_request.update.transport.fields.danger_label_one": "Etiqueta de peligro 1", "module.pickup_request.update.transport.fields.danger_label_three": "Etiqueta de peligro 3", "module.pickup_request.update.transport.fields.danger_label_two": "Etiqueta de peligro 2", "module.pickup_request.update.transport.fields.hazard_inducers": "Inductores de riesgos", "module.pickup_request.update.transport.fields.hazard_inducers_hint": "Requerido porque el número de la ONU seleccionado es peligroso.", "module.pickup_request.update.transport.fields.packaging_group": "Grupo de emb.", "module.pickup_request.update.transport.fields.un_number": "Número UN", "module.pickup_request.update.transport.fields.waste": "Residuo", "module.pickup_request.update.transport.title": "Transporte", "module.pickup_request.update.waste.contract_line.custmer_ref": "Ref. cliente", "module.pickup_request.update.waste.contract_line.ewc_code": "LER", "module.pickup_request.update.waste.contract_line.max_selection_reached": "Se ha alcanzado la selección máxima", "module.pickup_request.update.waste.contract_line.pickup_address": "Dirección de recogida", "module.pickup_request.update.waste.contract_line.pickup_date": "Fecha de recogida preferida", "module.pickup_request.update.waste.contract_line.pickup_time": "Hora de recogida preferida", "module.pickup_request.update.waste.contract_line.too_little_selected": "Debe seleccionar al menos 1", "module.pickup_request.update.waste.contract_line.too_many_selected": "Ha seleccionado demasiados, por favor, anule la selección o cambie el modo de transporte", "module.pickup_request.update.waste.contract_line.waste_material": "Material residual", "module.pickup_request.update.waste.materials.all": "Todos", "module.pickup_request.update.waste.materials.label": "Busque y seleccione los materiales residuales de la lista", "module.pickup_request.update.waste.materials.selected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "module.pickup_request.update.waste.materials.title": "¿De qué materiales residuales desea deshacerse?", "module.pickup_request.update.waste.packaging_added": "Agregue el embalaje a esta solicitud", "module.pickup_request.update.waste.title": "Residuo", "module.pickup_request.update.waste.transport_by_indaver": "Quiero que el transporte sea organizado por Indaver.", "module.pickup_request.update.waste.transport_mode": "Modo de transporte", "module.reporting.overview.title": "Informes", "module.roles_and_permissions.add_new_role": "Nuevo papel", "module.roles_and_permissions.create_role_dialog.description": "<PERSON>rear un nuevo rol", "module.roles_and_permissions.create_role_dialog.title": "Crear rol", "module.roles_and_permissions.save_changes": "Guardar cambios", "module.setting.application.title": "Aplicación", "module.setting.contact.create": "<PERSON><PERSON><PERSON> contacto", "module.setting.contact.create_success": "<PERSON>o creado correctamente", "module.setting.contact.delete_description": "¿Está seguro de que desea eliminar este contacto?", "module.setting.contact.delete_success": "Contacto eliminado correctamente.", "module.setting.contact.edit": "<PERSON><PERSON>", "module.setting.contact.fields.email": "Correo electrónico", "module.setting.contact.fields.first_name": "Nombre", "module.setting.contact.fields.last_name": "Apellido", "module.setting.contact.no_contacts": "Actualmente no tiene contactos agregados.", "module.setting.font_size.default": "Predeterminado", "module.setting.font_size.description": "Ajuste el tamaño de la fuente.", "module.setting.font_size.large": "Grande", "module.setting.font_size.larger": "Más grande", "module.setting.font_size.small": "Pequeño", "module.setting.font_size.smaller": "<PERSON><PERSON> peque<PERSON>", "module.setting.font_size.title": "Tamaño de fuente", "module.setting.high_contrast.description": "Mejora la legibilidad y visibilidad al aumentar el contraste entre los elementos de la interfaz de usuario.", "module.setting.high_contrast.disabled.description": "El modo de alto contraste está deshabilitado.", "module.setting.high_contrast.disabled.label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "module.setting.high_contrast.enabled.description": "El modo de alto contraste está habilitado.", "module.setting.high_contrast.enabled.label": "Habilitado", "module.setting.high_contrast.title": "<PERSON>do de alto contraste", "module.setting.interface_theme.dark": "Oscuro", "module.setting.interface_theme.description": "Selecciona o personaliza tu tema de interfaz de usuario.", "module.setting.interface_theme.light": "<PERSON><PERSON><PERSON>", "module.setting.interface_theme.system_preference": "Preferencia del sistema", "module.setting.interface_theme.title": "Tema de la interfaz", "module.setting.keyboard_shortcuts.description": "Los atajos de teclado siempre están habilitados, pero puedes elegir si deseas mostrar una pista.", "module.setting.keyboard_shortcuts.disabled.description": "Las pistas de atajos de teclado están ocultas.", "module.setting.keyboard_shortcuts.disabled.label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "module.setting.keyboard_shortcuts.enabled.description": "Las pistas de atajos de teclado están visibles.", "module.setting.keyboard_shortcuts.enabled.label": "Habilitado", "module.setting.keyboard_shortcuts.example_button": "Ejemplo", "module.setting.keyboard_shortcuts.title": "Pistas de atajos de teclado", "module.setting.language.description": "Cambia tu idioma.", "module.setting.language.locales.en_nl": "Inglés (Países Bajos / Bélgica)", "module.setting.language.locales.en_us": "Inglés", "module.setting.language.locales.nl_be": "Neerlandés (Bélgica)", "module.setting.language.title": "Idioma", "module.setting.reduce_motion.description": "Reduce la cantidad y la intensidad de las animaciones, los efectos de desplazamiento y otros efectos de movimiento en toda la aplicación.", "module.setting.reduce_motion.disabled.description": "Todas las animaciones y efectos de desplazamiento están habilitados para una experiencia completa.", "module.setting.reduce_motion.disabled.label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "module.setting.reduce_motion.enabled.description": "Verás menos animaciones y efectos de desplazamiento para reducir el movimiento.", "module.setting.reduce_motion.enabled.label": "Habilitado", "module.setting.reduce_motion.title": "<PERSON>uc<PERSON> movim<PERSON>o", "module.setting.roles_and_permissions.add_new_role": "<PERSON><PERSON><PERSON> rol", "module.setting.roles_and_permissions.create_role_dialog.description": "<PERSON>rear un nuevo rol", "module.setting.roles_and_permissions.create_role_dialog.title": "Crear rol", "module.setting.roles_and_permissions.save_changes": "Guardar cambios", "module.setting.roles_and_permissions.save_changes_success": "Cambios guardados correctamente", "module.setting.roles_and_permissions.title": "Roles y permisos", "module.setting.subtitle": "Gestiona la configuración de tu aplicación", "module.setting.title": "Configuración", "module.settings.back.label": "Atrás", "module.settings.contacts.description": "Gestiona tus contactos.", "module.settings.contacts.title": "Contactos", "module.settings.disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "module.settings.display.title": "<PERSON><PERSON><PERSON>", "module.settings.enabled": "Habilitado", "module.settings.forward.label": "Adelante", "module.settings.general": "General", "module.settings.maximize.label": "Maximizar vista de configuración", "module.settings.minimize.label": "Minimizar vista de configuración", "module.settings.no_results": "No se encontraron resultados para \"{searchTerm}\".", "module.settings.roles_and_permissions.add_new_role": "<PERSON><PERSON><PERSON> rol", "module.settings.roles_and_permissions.create_role_dialog.description": "<PERSON>rear un nuevo rol", "module.settings.roles_and_permissions.create_role_dialog.title": "Crear rol", "module.settings.roles_and_permissions.save_changes": "Guardar cambios", "module.settings.roles_and_permissions.save_changes_success": "Cambios guardados con éxito", "module.settings.roles_and_permissions.table.delete_role": "Eliminar rol", "module.settings.roles_and_permissions.table.permissions": "<PERSON><PERSON><PERSON>", "module.settings.roles_and_permissions.title": "<PERSON>s y Permisos", "module.settings.search.clear.label": "<PERSON><PERSON><PERSON> t<PERSON><PERSON><PERSON>", "module.settings.search.placeholder": "Búsqueda rápida", "module.settings.section.appearance.description": "Personaliza la apariencia general de la aplicación.", "module.settings.section.appearance.option.dark_mode": "<PERSON><PERSON> oscuro", "module.settings.section.appearance.option.light_mode": "<PERSON>do claro", "module.settings.section.appearance.option.system_preference": "Preferencia del sistema", "module.settings.section.appearance.title": "Apariencia", "module.settings.section.font_size.description": "Ajusta el tamaño del texto para una mejor legibilidad o para que quepa más contenido en tu pantalla.", "module.settings.section.font_size.option.default": "Predeterminado", "module.settings.section.font_size.option.large": "Grande", "module.settings.section.font_size.option.larger": "Más grande", "module.settings.section.font_size.option.small": "Pequeño", "module.settings.section.font_size.option.smaller": "<PERSON><PERSON> peque<PERSON>", "module.settings.section.font_size.title": "Tamaño de fuente", "module.settings.section.high_contrast.description": "Mejora la visibilidad y reduce la fatiga visual con el modo de alto contraste.", "module.settings.section.high_contrast.disabled.label": "El modo de alto contraste está deshabilitado", "module.settings.section.high_contrast.enabled.label": "El modo de alto contraste está habilitado", "module.settings.section.high_contrast.title": "<PERSON> contraste", "module.settings.section.keyboard_shortcut_hints.description": "Alterna las pistas de atajos de teclado para ayudarte a navegar por la aplicación de forma más eficiente.", "module.settings.section.keyboard_shortcut_hints.disabled.label": "Las pistas de atajos de teclado están ocultas", "module.settings.section.keyboard_shortcut_hints.enabled.label": "Las pistas de atajos de teclado están visibles", "module.settings.section.keyboard_shortcut_hints.example": "Ejemplo", "module.settings.section.keyboard_shortcut_hints.not_available_on_mobile": "Los atajos de teclado no están disponibles en dispositivos móviles o tabletas. Para cambiar esta configuración, abre la aplicación en un ordenador de escritorio.", "module.settings.section.keyboard_shortcut_hints.title": "Pistas de atajos de teclado", "module.settings.section.language.description": "Selecciona tu idioma preferido para la aplicación.", "module.settings.section.language.title": "Idioma", "module.settings.settings_are_hidden.label": "{count} ajuste en \"{viewName}\" está actualmente oculto. | {count} ajustes en \"{viewName}\" están actualmente ocultos.", "module.settings.settings_are_hidden.show_all.label": "Mostrar todos los ajustes", "module.user.columns.upn": "Upn", "module.user.create.create_user": "<PERSON><PERSON><PERSON> usuario", "module.user.create.success_toast.message": "El usuario se ha creado correctamente.", "module.user.create.title": "Nuevo usuario", "module.user.detail.edit_user": "<PERSON>ar usuario", "module.user.detail.title": "<PERSON>ar usuario", "module.user.form.section.name.description": "Nombre y apellido del usuario.", "module.user.impersonation.description": "¿Estás seguro de que quieres suplantar a {user}? Esto te permitirá actuar en su nombre.", "module.user.impersonation.title": "Iniciar Suplantac<PERSON>", "module.user.overview.title": "Usuarios", "module.user.role": "Rol", "module.waste_inquiry.detail.additional_files": "Archivos adicionales", "module.waste_inquiry.detail.analysis_report": "Informe de análisis", "module.waste_inquiry.detail.conformity_assessment": "Evaluación de conformidad", "module.waste_inquiry.detail.contract_item": "Artículo del contrato {item}", "module.waste_inquiry.detail.contract_number": "Nº de contrato {number}", "module.waste_inquiry.detail.sds": "Hoja de Datos de Seguridad (SDS)", "module.waste_inquiry.overview.bulk.all_items_selected": "Todos los elementos seleccionados", "module.waste_inquiry.overview.bulk.delete_draft": "Eliminar borradores", "module.waste_inquiry.overview.bulk.delete_draft_description": "Sus borradores seleccionados se eliminarán de forma permanente", "module.waste_inquiry.overview.bulk.items_selected": "elemento seleccionado | elementos seleccionados", "module.waste_inquiry.overview.bulk.items_unselected": "elemento deseleccionado de todos | elementos deseleccionados de todos", "module.waste_inquiry.overview.date": "<PERSON><PERSON>", "module.waste_inquiry.overview.new_waste_inquiry": "Nueva solicitud de residuos", "module.waste_inquiry.overview.requested_by": "Solicitado por", "module.waste_inquiry.overview.status": "Estado", "module.waste_inquiry.overview.tab.completed": "Completado", "module.waste_inquiry.overview.tab.drafts": "Borradores", "module.waste_inquiry.overview.tab.offers": "<PERSON><PERSON><PERSON>", "module.waste_inquiry.overview.tab.pending": "Pendiente", "module.waste_inquiry.overview.tab.submitted": "Enviado", "module.waste_inquiry.overview.title": "Solicitudes de residuos", "module.waste_inquiry.update.all_changes_saved": "Todos los cambios guardados", "module.waste_inquiry.update.characteristics.flashpoint_type.label": "Punto de inflamación", "module.waste_inquiry.update.characteristics.ph_type.label": "pH", "module.waste_inquiry.update.characteristics.specific_gravity.label": "Gravedad específica", "module.waste_inquiry.update.characteristics.stable_temperature.label": "Temperatura", "module.waste_inquiry.update.characteristics.stable_temperature.placeholder": "Seleccione un tipo", "module.waste_inquiry.update.characteristics.title": "Características", "module.waste_inquiry.update.collection.campaign_name.label": "Nombre de la campaña", "module.waste_inquiry.update.collection.expected_end_date.label": "Fecha de finalización prevista", "module.waste_inquiry.update.collection.expected_per_collection_quantity.label": "Cantidad esperada por recogida", "module.waste_inquiry.update.collection.expected_yearly_volume_amount.label": "Volumen anual previsto", "module.waste_inquiry.update.collection.first_collection_date.label": "Fecha de inicio prevista", "module.waste_inquiry.update.collection.frequency_of_discharge.label": "Frecuencia de descarga", "module.waste_inquiry.update.collection.remarks.label": "Información adicional", "module.waste_inquiry.update.collection.remarks.placeholder": "Proporciona información adicional, observaciones o preguntas", "module.waste_inquiry.update.collection.title": "Recogida", "module.waste_inquiry.update.collection_and_transport": "Recogida y transporte", "module.waste_inquiry.update.composition.analysis_report.label": "¿Tienes un informe de análisis?", "module.waste_inquiry.update.composition.analysis_report.no_report": "No tengo un informe de análisis", "module.waste_inquiry.update.composition.components.label": "Especifica la composición del residuo", "module.waste_inquiry.update.composition.sample.available": "Tengo una muestra disponible para enviar", "module.waste_inquiry.update.composition.sample.label": "Muestra de residuos", "module.waste_inquiry.update.composition.sample.not_available": "No tengo una muestra disponible para enviar", "module.waste_inquiry.update.composition.sds.label": "¿Tienes una Hoja de Datos de Seguridad (SDS)?", "module.waste_inquiry.update.composition.sds.no_sds": "No tengo una SDS", "module.waste_inquiry.update.composition.title": "Composición", "module.waste_inquiry.update.created_on": "Creado el", "module.waste_inquiry.update.customer_and_location.customer.title": "Selecciona un cliente", "module.waste_inquiry.update.customer_and_location.pick_up_address.title": "Selecciona una dirección de recogida", "module.waste_inquiry.update.customer_and_location.pick_up_address.unknown_label": "Dirección de recogida desconocida", "module.waste_inquiry.update.customer_and_location.suggestions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "module.waste_inquiry.update.customer_and_location.title": "Cliente y ubicación", "module.waste_inquiry.update.customer_and_location.waste_producer.title": "Selecciona un productor de residuos", "module.waste_inquiry.update.customer_and_location.waste_producer.unknown_label": "Productor de residuos desconocido", "module.waste_inquiry.update.failed_to_save_changes": "No se pudieron guardar los cambios", "module.waste_inquiry.update.general_info": "Información general", "module.waste_inquiry.update.legislation.title": "Legislación", "module.waste_inquiry.update.legislation_and_properties.legislations.label": "Legislaciones", "module.waste_inquiry.update.legislation_and_properties.legislations.remarks.placeholder": "Por favor, especifica cada legislación seleccionada", "module.waste_inquiry.update.legislation_and_properties.properties.label": "Propiedades", "module.waste_inquiry.update.legislation_and_properties.properties.remarks.placeholder": "Por favor, especifica cada propiedad seleccionada", "module.waste_inquiry.update.legislation_and_properties.remarks.label": "Especificar", "module.waste_inquiry.update.legislation_and_properties.remarks.placeholder": "Por favor, especifica cada legislación seleccionada", "module.waste_inquiry.update.legislation_and_properties.title": "Legislación y Propiedades", "module.waste_inquiry.update.packaging.add_un_number": "Añadir nú<PERSON>", "module.waste_inquiry.update.page_title": "Nueva solicitud de residuos", "module.waste_inquiry.update.properties.title": "Propiedades", "module.waste_inquiry.update.return_to_overview": "Todos los flujos de residuos", "module.waste_inquiry.update.saving_changes": "Guardando cambios", "module.waste_inquiry.update.submit.add_contact_label": "<PERSON><PERSON><PERSON>", "module.waste_inquiry.update.submit.add_existing_contact_label": "Añadir contacto existente", "module.waste_inquiry.update.submit.additional_files_hint": "<PERSON>r e<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, hojas de producto, listas de productos químicos, etc.", "module.waste_inquiry.update.submit.additional_files_label": "¿Quieres subir archivos adicionales?", "module.waste_inquiry.update.submit.copy_id": "Copiar ID.", "module.waste_inquiry.update.submit.edit": "<PERSON><PERSON> solicitud", "module.waste_inquiry.update.submit.remarks_label": "¿Tienes alguna observación para nosotros sobre tu solicitud?", "module.waste_inquiry.update.submit.remarks_placeholder": "¿Observaciones, comentarios o preguntas?", "module.waste_inquiry.update.submit.return_to_overview": "Volver a la vista general", "module.waste_inquiry.update.submit.send_copy_to_customer_label": "¿Quieres enviar una copia a otros contactos?", "module.waste_inquiry.update.submit.submit": "<PERSON><PERSON><PERSON> solicitud", "module.waste_inquiry.update.submit.success": "Tu solicitud se ha enviado correctamente con el ID {id}. Nos pondremos en contacto contigo pronto.", "module.waste_inquiry.update.submitted_by": "Por {user}", "module.waste_inquiry.update.submitted_id": "ID {id}", "module.waste_inquiry.update.submitted_on": "Enviado el", "module.waste_inquiry.update.transport.collection_requirements.label": "Requisitos de recogida", "module.waste_inquiry.update.transport.container_loading_type.label": "Tipo de carga del contenedor", "module.waste_inquiry.update.transport.hazard_inducer_1.label": "Inductor de peligro 1", "module.waste_inquiry.update.transport.hazard_inducer_2.label": "Inductor de peligro 2", "module.waste_inquiry.update.transport.hazard_inducer_3.label": "Inductor de peligro 3", "module.waste_inquiry.update.transport.is_tank_owned_by_customer.label": "¿Es propiedad del cliente?", "module.waste_inquiry.update.transport.is_transport_arranged_by_indaver.falsy.label": "No, por el cliente u otra empresa", "module.waste_inquiry.update.transport.is_transport_arranged_by_indaver.label": "¿Está organizado por Indaver?", "module.waste_inquiry.update.transport.is_transport_arranged_by_indaver.truthy.label": "Sí, por Indaver", "module.waste_inquiry.update.transport.loading_by_indaver.label": "¿Carga por Indaver?", "module.waste_inquiry.update.transport.loading_type.label": "Tipo de carga de transporte", "module.waste_inquiry.update.transport.regulated_transport.label": "¿Es transporte regulado?", "module.waste_inquiry.update.transport.title": "Transporte", "module.waste_inquiry.update.transport.transport_in.label": "Transporte en", "module.waste_inquiry.update.transport.transport_type.label": "Tipo de transporte", "module.waste_inquiry.update.transport.un_number_and_packing_group.label": "Especifica el/los número/s UN y los grupos de embalaje", "module.waste_inquiry.update.type.description.label": "Descripción y origen del flujo de residuos", "module.waste_inquiry.update.type.description.placeholder": "Proporciona una breve descripción en lenguaje común del residuo y el proceso de generación", "module.waste_inquiry.update.type.name.label": "Nombre", "module.waste_inquiry.update.type.name.placeholder": "Proporciona un nombre para el flujo de residuos", "module.waste_inquiry.update.type.packaging_type.label": "Tipo de embalaje", "module.waste_inquiry.update.type.state_of_matter.label": "Estado de la materia", "module.waste_inquiry.update.type.state_of_matter.placeholder": "Selecciona el estado de la materia", "module.waste_inquiry.update.type.title": "Tipo", "module.waste_inquiry.update.waste_identification": "Identificación de residuos", "module.weekly_planning.overview.new_planning": "Nueva planificación semanal", "module.weekly_planning.overview.title": "Planificación semanal", "module.weekly_planning.update.customer_and_location.customer.title": "Cliente", "module.weekly_planning.update.customer_and_location.pick_up_address.is_unknown_label": "Dirección de recogida desconocida", "module.weekly_planning.update.customer_and_location.pick_up_address.title": "Dirección de recogida", "module.weekly_planning.update.customer_and_location.title": "Productor de residuos y dirección de recogida", "module.weekly_planning.update.customer_and_location.waste_producer.is_unknown_label": "Productor de residuos desconocido", "module.weekly_planning.update.customer_and_location.waste_producer.title": "Productor de residuos", "module.weekly_planning.update.extra_info.title": "Información extra", "module.weekly_planning.update.general_info": "Información general", "module.weekly_planning.update.page_title": "Planificación semanal", "module.weekly_planning.update.pickup.save_and_close": "Guardar y cerrar", "module.weekly_planning.update.pickup.title": "Editar la solicitud de recogida", "module.weekly_planning.update.pickup_details": "Detalles de recogida", "module.weekly_planning.update.planning.additional_files": "¿Quieres subir archivos adicionales?", "module.weekly_planning.update.planning.additional_files_hint": "<PERSON>r e<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, hojas de producto, listas de productos químicos, etc.", "module.weekly_planning.update.planning.date": "<PERSON><PERSON>", "module.weekly_planning.update.planning.remarks": "¿Tienes alguna observación para nosotros?", "module.weekly_planning.update.planning.remarks_placeholder": "¿Observaciones, comentarios o preguntas?", "module.weekly_planning.update.planning.time": "<PERSON>ra preferida", "module.weekly_planning.update.return_to_overview": "Volver a la vista general", "module.weekly_planning.update.submit.add_contact": "<PERSON><PERSON><PERSON>", "module.weekly_planning.update.submit.add_existing_contact": "Añadir contacto existente", "module.weekly_planning.update.submit.add_existing_contact_label": "Añadir contacto existente", "module.weekly_planning.update.submit.almost_done": "¡Casi listo!", "module.weekly_planning.update.submit.edit_planning": "Editar planificación semanal", "module.weekly_planning.update.submit.email_label": "Correo electrónico", "module.weekly_planning.update.submit.email_placeholder": "ejemplo{at}indaver.com", "module.weekly_planning.update.submit.first_name": "Nombre", "module.weekly_planning.update.submit.go_to_waste_step": "Ir al paso de Residuos", "module.weekly_planning.update.submit.last_name": "Apellido", "module.weekly_planning.update.submit.pickup_request_error": "No podemos enviar tu planificación semanal. Parece que una de tus solicitudes de recogida tiene un problema en este campo: {field}", "module.weekly_planning.update.submit.request_submitted": "¡Planificación semanal enviada!", "module.weekly_planning.update.submit.request_submitted_description": "Tu solicitud se ha enviado correctamente con el ID {id}. Nos pondremos en contacto contigo pronto.", "module.weekly_planning.update.submit.return_to_overview": "Volver a la vista general", "module.weekly_planning.update.submit.send_copy_to_contacts": "¿Quieres enviar una copia a otros contactos?", "module.weekly_planning.update.submit.submit_planning": "Enviar planificación semanal", "module.weekly_planning.update.submit.submit_request": "¿Listo para enviar tu planificación semanal?", "module.weekly_planning.update.submit.thank_you": "<PERSON><PERSON><PERSON>", "module.weekly_planning.update.waste.contract_line.start_date_required": "Especifica tu fecha de recogida preferida", "module.weekly_planning.update.waste.contract_line.too_little_selected": "Debes seleccionar al menos 1", "module.weekly_planning.update.waste.materials.all": "Todos", "module.weekly_planning.update.waste.materials.selected": "Seleccionados", "module.weekly_planning.update.waste.materials.title": "¿Qué materiales de desecho quieres desechar?", "module.weekly_planning.update.waste.title": "Residuos", "shared.actions": "Acciones", "shared.add": "<PERSON><PERSON><PERSON>", "shared.adr_labels": "Etiquetas ADR", "shared.approve": "<PERSON><PERSON><PERSON>", "shared.average_temperature": "Temperatura promedio", "shared.back": "Atrás", "shared.cancel": "<PERSON><PERSON><PERSON>", "shared.clear": "Bo<PERSON>r", "shared.close": "<PERSON><PERSON><PERSON>", "shared.copy": "Copiar", "shared.copy_success_description": "Los datos fueron copiados con éxito.", "shared.copy_success_title": "<PERSON><PERSON> copiados", "shared.cz_manual.open_link": "A<PERSON>r el enlace al manual", "shared.delete": "Eliminar", "shared.description": "Descripción", "shared.download": "<PERSON><PERSON><PERSON>", "shared.edit": "<PERSON><PERSON>", "shared.ewc_code": "Código CEE", "shared.expand": "Expandir", "shared.export_excel": "Export Excel", "shared.filters": "<PERSON><PERSON><PERSON>", "shared.general": "General", "shared.loading": "Cargando", "shared.max_image_size_mb": "Máx. {amount} Mb", "shared.max_temperature": "Temperatura máxima", "shared.min_temperature": "Temperatura mínima", "shared.minus": "<PERSON><PERSON>", "shared.more_info": "Más información", "shared.next": "Siguient<PERSON>", "shared.next_step": "Ir al siguiente paso", "shared.no": "No", "shared.no_roles_assigned": "Sin roles as<PERSON>ados", "shared.not_available.abbreviation": "N/D", "shared.number_short": "Nº", "shared.open_useful_links": "<PERSON><PERSON><PERSON> enlaces <PERSON><PERSON>s", "shared.plus": "Más", "shared.previous": "Anterior", "shared.previous_step": "Ir al paso anterior", "shared.reject": "<PERSON><PERSON><PERSON>", "shared.remove": "Eliminar", "shared.save": "<PERSON><PERSON><PERSON>", "shared.save_changes": "Guardar cambios", "shared.search": "Buscar", "shared.shrink": "Reducir", "shared.start_impersonation": "Iniciar Suplantac<PERSON>", "shared.submit": "Enviar", "shared.type": "Tipo", "shared.unfinished_feature_description": "Esta característica está en desarrollo y aún no está terminada.", "shared.unfinished_feature_title": "Característica inacabada", "shared.unknown_address": "Dirección desconocida", "shared.update": "Actualizar", "shared.view_detail": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "shared.useful_link.ewastra_portal_description": "Si sus residuos no peligrosos son recogidos por nuestra filial Indaver Logistics, puede ahorrar mucho papeleo accediendo en línea a sus E-CMR a través de esta plataforma.", "shared.useful_link.ewastra_portal_title": "Portal Ewastra", "shared.useful_link.indaver_report_description": "Nuestro entorno de informes anterior con acceso a sus datos históricos.", "shared.useful_link.indaver_report_title": "Indaver Reports", "shared.useful_link.waste_collectors_description": "Permisos de recogida de residuos multirregional.", "shared.useful_link.waste_collectors_title": "Recolectores de residuos", "shared.useful_link.waste_facilities_description": "Licencias y permisos de instalaciones listados por empresa.", "shared.useful_link.waste_facilities_title": "Instalaciones de residuos", "shared.working_as": "Trabajando como", "shared.yes": "Sí", "user.birth_date": "Fecha de nacimiento", "user.email": "Correo electrónico", "user.email_placeholder": "ejemplo{at}email.com", "user.first_name": "Nombre", "user.label.plural": "Usuarios", "user.label.singular": "Usuario", "user.label.user_with_count": "Ningún usuario | {count} usuario | {count} usuarios", "user.last_name": "Apellido", "user.name": "Nombre", "validation.invalid_date": "<PERSON>cha no válida", "validation.invalid_datetime": "<PERSON>cha y hora no válidas", "validation.invalid_email": "Dirección de correo electrónico no válida", "validation.invalid_regex": "Formato no válido", "validation.invalid_string": "Entrada no válida", "validation.invalid_union": "Entrada no válida", "validation.invalid_url": "URL no válida", "validation.invalid_uuid": "UUID no válido", "validation.required": "Este campo es obligatorio", "validation.too_big": "Debe ser menor o igual a {count}", "validation.too_big_array": "Debe contener como máximo {count} elemento | Debe contener como máximo {count} elementos", "validation.too_big_date": "Debe ser anterior a {count}", "validation.too_big_number": "Debe ser menor o igual a {count}", "validation.too_big_string": "Debe tener como máximo {count} carácter | Debe tener como máximo {count} caracteres", "validation.too_small": "<PERSON>be ser mayor o igual a {count}", "validation.too_small_array": "Debe contener al menos {count} elemento | Debe contener al menos {count} elementos", "validation.too_small_date": "Debe ser posterior a {count}", "validation.too_small_number": "<PERSON>be ser mayor o igual a {count}", "validation.too_small_string": "Debe tener al menos {count} car<PERSON>cter | Debe tener al menos {count} caracteres", "enum.certificate_type.cor": "Certificado de reciclaje", "enum.certificate_type.cot_cob": "Certificado de tratamiento", "module.invoice.detail.return_to_drafts": "Volver a borradores"}