import type { TableColumn } from '@wisemen/vue-core-components'
import type { VNode } from 'vue'

import type { Filters } from '@/components/filters/filters.composable'
import type {
  Filter,
  FilterGroup,
} from '@/components/filters/filters.type'
import type { useSearch } from '@/composables/search/search.composable'
import type { useSort } from '@/composables/sort/sort.composable'
import type { RouteLocationCurrent } from '@/types/global/vueRouter'

export interface DataTableColumn<TSchema> {
  /**
   * The test id of the column
   * @default null
   */
  testId?: string
  /**
   * Whether the column should be hidden
   */
  isHidden?: boolean
  /**
   * The cell render function of the column
   */
  cell: (row: TSchema) => VNode
  /**
   * The header render function of the column
   */
  header?: (column: TableColumn<TSchema>) => VNode
  /**
   * The header label of the column
   */
  headerLabel?: string
  /**
   * The key of the column
   */
  key: string
  /**
   * The maximum width of the column. Be careful when using this property, as it can cause underflow issues
   * where the width of the content is smaller than the width of the table.
   * @default 'auto'
   */
  maxWidth?: string
  /**
   * The skeleton render function of the column
   */
  skeleton?: (row: TSchema) => VNode
  /**
   * The width of the column
   * @default 'min-content'
   */
  width?: string
}

export interface DataTableLinkAction<TData> {
  label: (data: TData) => string
  to: (data: TData) => RouteLocationCurrent
  type: 'link'
}

export interface DataTableButtonAction<TData> {
  label: (data: TData) => string
  type: 'button'
  onClick: (data: TData) => void
}

export type DataTableRowAction<TData> = DataTableButtonAction<TData> | DataTableLinkAction<TData>

export interface TableProps<
  TData,
  TFilters extends Filter[] = any[],
  TFilterGroups extends FilterGroup[] = any[],
  TSort extends string = any,
> {
  isLoading: boolean
  data: TData[]
  error: unknown
  filters?: Filters<TFilterGroups, TFilters> | null
  search?: ReturnType<typeof useSearch> | null
  sort?: ReturnType<typeof useSort<TSort>> | null
  onNextPage: () => Promise<void>
}
