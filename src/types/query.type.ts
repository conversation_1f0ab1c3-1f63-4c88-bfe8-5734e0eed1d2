import type { Ref } from 'vue'

import type { Sort } from '@/composables/sort/sort.composable'

export interface QueryParams {
  filters?: Record<string, any>
  search?: string
  sort?: Sort[]
}

export interface WithSearchQuery {
  search: string
}

export interface WithSortQuery<TKeys extends string> {
  sort: Sort<TKeys>[]
}

export interface WithFilterQuery<TFilters extends Record<string, any>> {
  filters: TFilters
}

export interface InfiniteQueryOptions<TParams> {
  params: {
    [K in keyof TParams]: Ref<TParams[K]>
  }
}
