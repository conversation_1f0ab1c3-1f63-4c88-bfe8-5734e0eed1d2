import type { PaginationOptions } from '@wisemen/vue-core-components'
import type {
  ComputedRef,
  MaybeRef,
} from 'vue'

import type { RequestType } from '@/client'
import type { DashboardAnnouncementIndexPagination } from '@/models/announcement/dashboard-index/dashboardAnnouncementIndexPagination.model.ts'
import type { NewsAnnouncementIndexPagination } from '@/models/announcement/index/announcementIndexPagination.model.ts'
import type { CertificateIndexQueryParams } from '@/models/certificate/index/certificateIndexQueryParams.model'
import type { ContractLineIndexPagination } from '@/models/contract-line/index/contractLineIndexPagination.model.ts'
import type { ContractLineIndexQueryParams } from '@/models/contract-line/index/contractLineIndexQueryParams.model.ts'
import type { CustomerIndexQueryParams } from '@/models/customer/index/customerIndexQueryParams.model.ts'
import type { DocumentIndexQueryParams } from '@/models/document/index/documentIndexQueryParams.model'
import type { DynamicTableViewIndexPagination } from '@/models/dynamic-table/view/dynamicTableViewIndexPagination.model'
import type { DynamicTableName } from '@/models/enums/dynamicTableName.enum'
import type { GuidanceLetterIndexQueryParams } from '@/models/guidance-letter/index/guidanceLetterIndexQueryParams.model'
import type { InvoiceDraftIndexPagination } from '@/models/invoice/draft-index/invoiceDraftIndexPagination.model'
import type { InvoiceIndexPagination } from '@/models/invoice/index/invoiceIndexPagination.model'
import type { DashboardNewsIndexPagination } from '@/models/news/dashboard-index/dashboardNewsIndexPagination.model.ts'
import type { NewsArticleIndexPagination } from '@/models/news/index/newsArticleIndexPagination.model.ts'
import type { NewsAnnouncementUuid } from '@/models/news/newsAnnouncementUuid.model'
import type { NewsArticleUuid } from '@/models/news/newsArticleUuid.model'
import type { NewsUuid } from '@/models/news/newsUuid.model'
import type { PackagingRequestUuid } from '@/models/packaging-request/packagingRequestUuid.model'
import type { PickUpAddressIndexQueryParams } from '@/models/pick-up-address/index/pickUpAddressIndexQueryParams.model.ts'
import type { PickupRequestIndexQueryParams } from '@/models/pickup-request/index/pickupRequestIndexQueryParams.model.ts'
import type { PickupRequestUuid } from '@/models/pickup-request/pickupRequestUuid.model'
import type { PickupRequestTemplateIndexQueryParams } from '@/models/pickup-request-template/index/pickupRequestTemplateIndexQueryParams.model'
import type { PickupRequestTemplateUuid } from '@/models/pickup-request-template/pickupRequestTemplateUuid.model'
import type { UnNumberIndexPagination } from '@/models/un-number/index/unNumberIndexPagination.model.ts'
import type { UserIndexQueryParams } from '@/models/user/index/userIndexQueryParams.model.ts'
import type { WasteInquiryIndexPagination } from '@/models/waste-inquiry/index/wasteInquiryIndexPagination.model'
import type { WasteInquiryIndexQueryParams } from '@/models/waste-inquiry/index/wasteInquiryIndexQueryParams.model'
import type { WasteInquiryUuid } from '@/models/waste-inquiry/wasteInquiryUuid.model'
import type { WasteProducerIndexQueryParams } from '@/models/waste-producer/index/wasteProducerIndexQueryParams.model.ts'
import type { WeeklyPlanningUuid } from '@/models/weekly-planning/weeklyPlanningUuid.model'
import type { UnNumberForPickUpRequestFilter } from '@/modules/un-number/api/queries/unNumberIndexForPickUpRequest.query'
import type { InfiniteQueryOptions } from '@/types/query.type'

interface ProjectQueryKeys {
  certificateIndex: {
    queryParams?: InfiniteQueryOptions<CertificateIndexQueryParams>['params']
  }
  contactIndex: {
    paginationOptions?: ComputedRef<PaginationOptions<ContractLineIndexPagination>>
  }
  containerTypeIndex: {
    customerId: ComputedRef<string>
  }
  contractLineIndex: {
    queryParams?: InfiniteQueryOptions<ContractLineIndexQueryParams>['params']
  }
  contractLineIndexOld: {
    paginationOptions?: ComputedRef<PaginationOptions<ContractLineIndexPagination>>
  }
  contractLineIndexV2: { queryParams?: InfiniteQueryOptions<ContractLineIndexQueryParams>['params'] }
  contractLinePackagingRequestIndex: {
    paginationOptions?: ComputedRef<PaginationOptions<ContractLineIndexPagination>>
  }
  contractLineWeeklyPlanningIndex: {
    paginationOptions?: ComputedRef<PaginationOptions<ContractLineIndexPagination>>
  }
  customerCountryCode: {
    customerId: ComputedRef<string>
  }
  customerIndex: {
    queryParams?: InfiniteQueryOptions<CustomerIndexQueryParams>['params']
  }
  dashboardAnnouncementIndex: {
    locale: ComputedRef<string>
    paginationOptions?: ComputedRef<PaginationOptions<DashboardAnnouncementIndexPagination>>
  }
  dashboardNewsDetail: {
    newsUuid: ComputedRef<NewsUuid>
    locale: ComputedRef<string>
  }
  dashboardNewsIndex: {
    locale: ComputedRef<string>
    paginationOptions?: ComputedRef<PaginationOptions<DashboardNewsIndexPagination>>
  }
  documentIndex: {
    queryParams?: InfiniteQueryOptions<DocumentIndexQueryParams>['params']
  }
  documentIndexFilters: {
    customerUuid: MaybeRef<string>
    viewName: MaybeRef<string>
    wasteProducerIds: MaybeRef<string[]>
  }
  documentSiteIndex: void
  dynamicTableColumnIndex: {
    dynamicTableName: DynamicTableName
  }
  dynamicTableDefaultView: {
    dynamicTableName: DynamicTableName
  }
  dynamicTableViewIndex: {
    dynamicTableName: DynamicTableName
    paginationOptions?: ComputedRef<PaginationOptions<DynamicTableViewIndexPagination>>
  }
  ewcCodeIndex: void
  guidanceLetterIndex: {
    queryParams?: InfiniteQueryOptions<GuidanceLetterIndexQueryParams>['params']
  }
  invoiceDraftIndex: { paginationOptions?: ComputedRef<PaginationOptions<InvoiceDraftIndexPagination>> }
  invoiceIndex: { paginationOptions?: ComputedRef<PaginationOptions<InvoiceIndexPagination>> }
  newsAnnouncementDetail: {
    newsAnnouncementUuid: ComputedRef<NewsAnnouncementUuid>
  }
  newsAnnouncementIndex: {
    paginationOptions?: ComputedRef<PaginationOptions<NewsAnnouncementIndexPagination>>
  }
  newsArticleDetail: {
    newsArticleUuid: ComputedRef<NewsArticleUuid>
  }
  newsArticleIndex: {
    paginationOptions?: ComputedRef<PaginationOptions<NewsArticleIndexPagination>>
  }
  newsDetail: {
    newsUuid: ComputedRef<NewsUuid>
  }
  newsIndex: {
    paginationOptions?: ComputedRef<PaginationOptions<NewsArticleIndexPagination>>
  }
  packagingRequestDetail: {
    packagingRequestUuid: ComputedRef<PackagingRequestUuid>
  }
  packagingTypeIndex: {
    customerId: ComputedRef<string>
  }
  permissions: void
  pickUpAddressIndex: {
    queryParams?: InfiniteQueryOptions<PickUpAddressIndexQueryParams>['params']
  }
  pickupRequestDetail: {
    pickupRequestUuid: ComputedRef<PickupRequestUuid>
  }
  pickupRequestDetailPoNumberAndCostCenterRequired: {
    customerId: ComputedRef<string>
  }
  pickupRequestIndex: {
    queryParams?: InfiniteQueryOptions<PickupRequestIndexQueryParams>['params']
  }
  pickupRequestSapDetail: {
    requestNumber: ComputedRef<string>
  }
  pickupRequestTemplateDetail: {
    pickupRequestTemplateUuid: ComputedRef<PickupRequestTemplateUuid>
  }
  pickupRequestTemplateIndex: {
    queryParams?: InfiniteQueryOptions<PickupRequestTemplateIndexQueryParams>['params']
  }
  preference: {
    userUuid: ComputedRef<string | null>
  }
  roles: void
  suggestedCustomerIndex: void
  suggestedPickUpAddressIndex: {
    customerId: ComputedRef<string | null>
    requestType: RequestType
  }
  suggestedWasteProducerIndex: {
    customerId: ComputedRef<string | null>
  }
  tankerTypeIndex: void
  transportTypeIndex: void
  unNumberDetail: {
    number: ComputedRef<string | null>
  }
  unNumberIndex: {
    paginationOptions?: ComputedRef<PaginationOptions<UnNumberIndexPagination>>
  }
  unNumberIndexForPickUpRequest: {
    filter: ComputedRef<UnNumberForPickUpRequestFilter>
  }
  userIndex: {
    queryParams?: InfiniteQueryOptions<UserIndexQueryParams>['params']
  }
  wasteInquiryDetail: {
    wasteInquiryUuid: ComputedRef<WasteInquiryUuid>
  }
  wasteInquiryIndex: {
    paginationOptions?: ComputedRef<PaginationOptions<WasteInquiryIndexPagination>>
  }
  wasteInquiryIndexV2: {
    queryParams?: InfiniteQueryOptions<WasteInquiryIndexQueryParams>['params']
  }
  wasteInquirySapDetail: {
    inquiryNumber: ComputedRef<string>
  }
  wasteProducerIndex: {
    queryParams?: InfiniteQueryOptions<WasteProducerIndexQueryParams>['params']
  }
  weeklyPlanningDetail: {
    weeklyPlanningUuid: ComputedRef<WeeklyPlanningUuid>
  }
}

declare module '@wisemen/vue-core-query' {
  interface QueryKeys extends ProjectQueryKeys {}
}
