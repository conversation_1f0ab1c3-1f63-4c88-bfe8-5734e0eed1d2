<script setup lang="ts">
import { useInfiniteScroll } from '@vueuse/core'
import {
  usePagination,
  useVcDialog,
  VcIconButton,
} from '@wisemen/vue-core-components'
import {
  computed,
  ref,
} from 'vue'
import { useI18n } from 'vue-i18n'

import { useContactIndexQuery } from '@/api/queries/contactIndex.query'
import { Permission } from '@/client'
import AppGroup from '@/components/app/AppGroup.vue'
import AppPaginationSearchField from '@/components/app/AppPaginationSearchField.vue'
import { TEST_ID } from '@/constants/testId.constant'
import type { ContactIndex } from '@/models/contact/index/contactIndex.model'
import type { ContactIndexPagination } from '@/models/contact/index/contactIndexPagination.model'
import SettingsContactOverviewContact from '@/modules/settings/features/contacts/components/overview/SettingsContactOverviewContact.vue'
import { useAuthStore } from '@/stores/auth.store'

const scrollContainerRef = ref<HTMLElement | null>(null)

const i18n = useI18n()
const authStore = useAuthStore()

const hasContactManagePermission = computed<boolean>(() => {
  return authStore.hasPermission(Permission.CONTACT_MANAGE)
})

const contactCreateDialog = useVcDialog({ component: () => import('./SettingsContactCreateDialog.vue') })

const pagination = usePagination<ContactIndexPagination>({ isRouteQueryEnabled: false })

const contactIndexQuery = useContactIndexQuery(pagination.paginationOptions)

useInfiniteScroll(
  computed<HTMLElement | null>(() => scrollContainerRef.value),
  contactIndexQuery.getNextPage,
  { offset: { bottom: 100 } },
)

const contacts = computed<ContactIndex[]>(() => {
  return contactIndexQuery.data.value?.data ?? []
})

const firstLetters = computed<string[]>(() => {
  const uniqueFirstLetters = new Set<string>()

  for (const contact of contacts.value) {
    uniqueFirstLetters.add(contact.firstName.charAt(0))
  }

  return Array.from(uniqueFirstLetters)
})

function getContactsByFirstLetter(letter: string): ContactIndex[] {
  return contacts.value.filter((contact) => contact.firstName.charAt(0) === letter)
}
</script>

<template>
  <div
    ref="scrollContainerRef"
    class="px-3xl pb-3xl h-full overflow-auto"
  >
    <div id="settings-contacts-dialog-container" />

    <Teleport to="#setting-actions">
      <div class="flex justify-end">
        <AppPaginationSearchField
          :pagination="pagination"
          :is-loading="contactIndexQuery.isFetching.value"
        />
      </div>
    </Teleport>

    <ul v-if="!contactIndexQuery.isLoading.value && contacts.length > 0">
      <li
        v-for="letter of firstLetters"
        :key="letter"
      >
        <div class="bg-primary pb-sm pt-3xl sticky top-0 z-10">
          <span class="text-quaternary text-xs font-bold uppercase">
            {{ letter }}
          </span>
        </div>

        <ul>
          <AppGroup
            gap="sm"
            direction="col"
          >
            <li
              v-for="contact of getContactsByFirstLetter(letter)"
              :key="contact.uuid"
              class="w-full"
            >
              <SettingsContactOverviewContact
                :contact="contact"
                :has-contact-manage-permission="hasContactManagePermission"
              />
            </li>
          </AppGroup>
        </ul>
      </li>
    </ul>
    <p
      v-else
      class="text-secondary mt-3xl text-center"
    >
      {{ i18n.t('module.setting.contact.no_contacts') }}
    </p>

    <VcIconButton
      v-if="hasContactManagePermission"
      v-bind="contactCreateDialog.getTriggerProps()"
      :label="i18n.t('shared.add')"
      :test-id="TEST_ID.SETTINGS.CONTACTS.CREATE_BUTTON"
      icon="plus"
      class="bottom-xl right-xl !absolute z-10"
      variant="primary"
      @click="contactCreateDialog.open()"
    />
  </div>
</template>
