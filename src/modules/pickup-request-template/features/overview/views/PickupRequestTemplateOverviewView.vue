<script setup lang="ts">
import { useI18n } from 'vue-i18n'

import { useSearch } from '@/composables/search/search.composable'
import { useSort } from '@/composables/sort/sort.composable'
import type { PickupRequestTemplateUuid } from '@/models/pickup-request-template/pickupRequestTemplateUuid.model'
import { usePickupRequestTemplateIndexQuery } from '@/modules/pickup-request-template/api/queries/pickupRequestTemplateIndex.query'
import PickupRequestTemplateHeader from '@/modules/pickup-request-template/components/PickupRequestTemplateHeader.vue'
import PickupRequestTemplateCreateButton from '@/modules/pickup-request-template/features/overview/components/PickupRequestTemplateCreateButton.vue'
import PickupRequestTemplateOverviewTable from '@/modules/pickup-request-template/features/overview/components/PickupRequestTemplateOverviewTable.vue'

const emit = defineEmits<{
  selectTemplate: [uuid: PickupRequestTemplateUuid]
}>()

const i18n = useI18n()
const search = useSearch({ persistInUrl: false })

const sort = useSort({
  enableMultiSort: true,
  initialValues: [
    {
      direction: 'asc',
      key: 'name',
    },
  ],
  keys: [
    'name',
    'createdAt',
    'updatedAt',
    'createdBy',
    'updatedBy',
  ],
  persistInUrl: false,
})

const {
  isLoading,
  data,
  error,
  fetchNextPage,
} = usePickupRequestTemplateIndexQuery({
  params: {
    search: search.debouncedSearch,
    sort: sort.values,
  },
})

function onSelectTemplate(uuid: PickupRequestTemplateUuid): void {
  emit('selectTemplate', uuid)
}
</script>

<template>
  <div class="flex h-full flex-col overflow-hidden">
    <PickupRequestTemplateHeader :title="i18n.t('module.pickup_request_template.overview.title')">
      <template #actions>
        <PickupRequestTemplateCreateButton @new-template-created="onSelectTemplate" />
      </template>
    </PickupRequestTemplateHeader>

    <div class="p-xl flex h-full flex-col overflow-hidden">
      <PickupRequestTemplateOverviewTable
        :data="data.data"
        :is-loading="isLoading"
        :error="error"
        :search="search"
        :sort="sort"
        @next-page="fetchNextPage"
        @select-template="onSelectTemplate"
      />
    </div>
  </div>
</template>
