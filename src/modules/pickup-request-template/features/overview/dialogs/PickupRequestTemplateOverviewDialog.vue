<script setup lang="ts">
import {
  VcDialogContent,
  VcDialogOverlay,
  VcDialogOverlayTransition,
  VcDialogPortal,
  VcDialogRoot,
} from '@wisemen/vue-core-components'
import { Motion } from 'motion-v'

import { onCreated } from '@/composables/created/created.composable'
import { useSearch } from '@/composables/search/search.composable'
import { useSort } from '@/composables/sort/sort.composable'
import { usePickupRequestTemplateIndexQuery } from '@/modules/pickup-request-template/api/queries/pickupRequestTemplateIndex.query'
import PickupRequestTemplateOverviewTable from '@/modules/pickup-request-template/features/overview/components/PickupRequestTemplateOverviewTable.vue'

const emit = defineEmits<{
  close: []
}>()

const pageInnerContent = document.querySelector('#page-inner-content') as HTMLElement

const search = useSearch({ persistInUrl: false })

const sort = useSort({
  enableMultiSort: true,
  initialValues: [
    {
      direction: 'asc',
      key: 'name',
    },
  ],
  keys: [
    'name',
    'createdAt',
    'updatedAt',
    'createdBy',
    'updatedBy',
  ],
  persistInUrl: false,
})

const {
  isLoading,
  data,
  error,
  fetchNextPage,
} = usePickupRequestTemplateIndexQuery({
  params: {
    search: search.debouncedSearch,
    sort: sort.values,
  },
})

onCreated(() => {
  pageInnerContent.style.scale = '0.98'
})

function onClose(): void {
  pageInnerContent.style.scale = '1'
  emit('close')
}
</script>

<template>
  <VcDialogRoot
    :class-config="{
      content: 'absolute translate-y-0 top-4 w-full h-[calc(100%-1rem)] rounded-none rounded-tl-4xl',
      overlay: 'backdrop-blur-none',
    }"
    teleport-target-id="page-content"
    class="w-full"
    @close="onClose"
  >
    <VcDialogPortal>
      <VcDialogContent>
        <Motion
          :initial="{ transform: 'translateY(100%)' }"
          :animate="{
            transform: 'translateY(0%)',
            transition: { type: 'spring', duration: 0.5, bounce: 0 },
          }"
          :exit="{
            transform: 'translateY(100%)',
            transition: { type: 'spring', duration: 0.3, bounce: 0 },
          }"
        >
          <div class="p-xl">
            <PickupRequestTemplateOverviewTable
              :data="data.data"
              :is-loading="isLoading"
              :error="error"
              :search="search"
              :sort="sort"
              @next-page="fetchNextPage"
            />
          </div>
        </Motion>
      </VcDialogContent>

      <VcDialogOverlay>
        <VcDialogOverlayTransition />
      </VcDialogOverlay>
    </VcDialogPortal>
  </VcDialogRoot>
</template>
