<script setup lang="ts">
import { VcButton } from '@wisemen/vue-core-components'
import { useI18n } from 'vue-i18n'

import { useApiErrorToast } from '@/composables/api-error-toast/apiErrorToast.composable'
import type { PickupRequestTemplateUuid } from '@/models/pickup-request-template/pickupRequestTemplateUuid.model'
import { usePickupRequestTemplateCreateMutation } from '@/modules/pickup-request-template/api/mutations/pickupRequestTemplateCreate.mutation'

const emit = defineEmits<{
  newTemplateCreated: [PickupRequestTemplateUuid]
}>()

const i18n = useI18n()
const apiErrorToast = useApiErrorToast()

const pickupRequestTemplateCreateMutation = usePickupRequestTemplateCreateMutation()

async function onCreateNewTemplate(): Promise<void> {
  try {
    const uuid = await pickupRequestTemplateCreateMutation.execute({ body: { name: i18n.t('module.pickup_request_template.overview.new_template') } })

    emit('newTemplateCreated', uuid)
  }
  catch (error) {
    apiErrorToast.show(error)
  }
}
</script>

<template>
  <VcButton
    :is-loading="pickupRequestTemplateCreateMutation.isLoading.value"
    icon-left="plus"
    @click="onCreateNewTemplate"
  >
    {{ i18n.t('module.pickup_request_template.overview.new_template') }}
  </VcButton>
</template>
