<script setup lang="ts">
import type { VNode } from 'vue'
import {
  computed,
  h,
} from 'vue'
import { useI18n } from 'vue-i18n'

import AppGroup from '@/components/app/AppGroup.vue'
import SearchField from '@/components/search-field/SearchField.vue'
import DataTable from '@/components/table/data-table/DataTable.vue'
import DataTableCell from '@/components/table/data-table/DataTableCell.vue'
import { useLocalizedDateFormat } from '@/composables/localized-date-format/localizedDateFormat.composable'
import type { PickupRequestTemplateIndex } from '@/models/pickup-request-template/index/pickupRequestTemplateIndex.model'
import type { PickupRequestTemplateUuid } from '@/models/pickup-request-template/pickupRequestTemplateUuid.model'
import type {
  DataTableColumn,
  TableProps,
} from '@/types/table.type'

const props = defineProps<TableProps<PickupRequestTemplateIndex>>()

const emit = defineEmits<{
  selectTemplate: [uuid: PickupRequestTemplateUuid]
}>()

const i18n = useI18n()
const dateFormatter = useLocalizedDateFormat()

const columns = computed<DataTableColumn<PickupRequestTemplateIndex>[]>(() => [
  {
    cell: (row): VNode => h(DataTableCell, () => row.name),
    headerLabel: i18n.t('module.pickup_request_template.overview.columns.name'),
    key: 'name',
  },
  {
    cell: (row): VNode => h(DataTableCell, () => dateFormatter.toDate(row.createdAt)),
    headerLabel: i18n.t('module.pickup_request_template.overview.columns.created_at'),
    key: 'createdAt',
  },
  {
    cell: (row): VNode => h(DataTableCell, () => row.createdBy),
    headerLabel: i18n.t('module.pickup_request_template.overview.columns.created_by'),
    key: 'createdBy',
  },
  {
    cell: (row): VNode => h(DataTableCell, () => dateFormatter.toDate(row.updatedAt)),
    headerLabel: i18n.t('module.pickup_request_template.overview.columns.updated_at'),
    key: 'updatedAt',
  },
  {
    cell: (row): VNode => h(DataTableCell, () => row.updatedBy),
    headerLabel: i18n.t('module.pickup_request_template.overview.columns.updated_by'),
    key: 'updatedBy',
  },
])
</script>

<template>
  <DataTable
    v-bind="props"
    :columns="columns"
    :has-shadow="true"
    :get-key="(template) => template.uuid"
    :row-action="{
      type: 'button',
      label: (row) => row.name,
      onClick: (row) => emit('selectTemplate', row.uuid),
    }"
  >
    <template #top>
      <AppGroup
        justify="end"
        class="px-md py-md"
      >
        <SearchField
          v-if="props.search !== undefined && props.search !== null"
          :search="props.search"
        />
      </AppGroup>
    </template>
  </DataTable>
</template>
