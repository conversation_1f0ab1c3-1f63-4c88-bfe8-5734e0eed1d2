<script setup lang="ts">
import {
  useVcDialog,
  useVcToast,
  VcButton,
  VcDialogTitle,
  VcIconButton,
  VcTextField,
} from '@wisemen/vue-core-components'
import { useForm } from 'formango'
import type { Component } from 'vue'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import { PickUpTransportMode } from '@/client/types.gen'
import AppGroup from '@/components/app/AppGroup.vue'
import WizardForm from '@/components/form/wizard/WizardForm.vue'
import WizardFormContent from '@/components/form/wizard/WizardFormContent.vue'
import WizardFormSidebar from '@/components/form/wizard/WizardFormSidebar.vue'
import { useGlobalCustomer } from '@/composables/global-customer/globalCustomer.composable'
import { useWizardForm } from '@/composables/wizard-form/wizardForm.composable'
import { useWizardFormStepState } from '@/composables/wizard-form/wizardFormStepState.composable'
import { AddressUtil } from '@/models/address/address.util'
import { PickupRequestFormSteps } from '@/models/enums/formSteps.enum'
import { pickupRequestAdministrationFormSchema } from '@/models/pickup-request/update/steps/pickupRequestAdministrationForm.model'
import type { PickupRequestCustomerAndLocationForm } from '@/models/pickup-request/update/steps/pickupRequestCustomerAndLocationForm.model'
import { pickupRequestCustomerAndLocationFormSchema } from '@/models/pickup-request/update/steps/pickupRequestCustomerAndLocationForm.model'
import { pickupRequestDetailsFormSchema } from '@/models/pickup-request/update/steps/pickupRequestDetailsForm.model'
import { pickupRequestPackagingFormSchema } from '@/models/pickup-request/update/steps/pickupRequestPackagingForm.model'
import type { pickupRequestPackagingRequestForm } from '@/models/pickup-request/update/steps/pickupRequestPackagingMaterialsForm.model'
import { pickupRequestPackagingRequestSchema } from '@/models/pickup-request/update/steps/pickupRequestPackagingMaterialsForm.model'
import type { PickupRequestPlanningForm } from '@/models/pickup-request/update/steps/pickupRequestPlanningForm.model'
import { pickupRequestPlanningFormSchema } from '@/models/pickup-request/update/steps/pickupRequestPlanningForm.model'
import { pickupRequestTransportFormSchema } from '@/models/pickup-request/update/steps/pickupRequestTransportForm.model'
import type { PickupRequestWasteForm } from '@/models/pickup-request/update/steps/pickupRequestWasteForm.model'
import { pickupRequestWasteFormSchema } from '@/models/pickup-request/update/steps/pickupRequestWasteForm.model'
import type { PickupRequestTemplateDetail } from '@/models/pickup-request-template/detail/pickupRequestTemplateDetail.model'
import { pickupRequestTemplateUpdateFormSchema } from '@/models/pickup-request-template/update/pickupRequestTemplateUpdateForm.model'
import { usePickupRequestTemplateDeleteMutation } from '@/modules/pickup-request-template/api/mutations/pickupRequestTemplateDelete.mutation'
import { usePickupRequestTemplateUpdateMutation } from '@/modules/pickup-request-template/api/mutations/pickupRequestTemplateUpdate.mutation'
import PickupRequestTemplateHeader from '@/modules/pickup-request-template/components/PickupRequestTemplateHeader.vue'
import { toFormField } from '@/utils/formango.util'

const props = defineProps<{
  pickupRequestTemplate: PickupRequestTemplateDetail
}>()

const emit = defineEmits<{
  goToOverview: []
}>()

const TELEPORT_TARGET_ID = 'pickup-request-template-update-view'

const i18n = useI18n()
const confirmDialog = useVcDialog({ component: () => import('@/components/dialog/AppConfirmDialog.vue') })
const toast = useVcToast()

const pickupRequestTemplateUpdateMutation = usePickupRequestTemplateUpdateMutation()
const pickupRequestTemplateDeleteMutation = usePickupRequestTemplateDeleteMutation()
const globalCustomer = useGlobalCustomer()

const customerAndLocationState = useWizardFormStepState<PickupRequestCustomerAndLocationForm>({
  customer: props.pickupRequestTemplate.customer ?? globalCustomer.globalCustomer.value ?? null,
  pickupAddresses: props.pickupRequestTemplate.pickupAddresses,
  wasteProducer: props.pickupRequestTemplate.wasteProducer,
})

const wasteState = useWizardFormStepState<PickupRequestWasteForm>({
  isPackagingAdded: props.pickupRequestTemplate.packagingRequestMaterials?.length > 0,
  isReturnPackaging: props.pickupRequestTemplate.isReturnPackaging ?? false,
  isTransportByIndaver: props.pickupRequestTemplate.isTransportByIndaver ?? true,
  customerCountryCode: null,
  materials: props.pickupRequestTemplate.materials.map((material) => ({
    ...material,
    isCostCenterRequired: false,
    isPoNumberRequired: false,
    amount: null,
    containerType: material.containerType === null ? null : { name: material.containerType },
    packagingType: material.packagingType === null ? null : { name: material.packagingType },
  })),
  packagingRemark: props.pickupRequestTemplate.packagingRemark,
  totalQuantityPallets: props.pickupRequestTemplate.totalQuantityPallets,
  transportMode: props.pickupRequestTemplate.transportMode,
})

const planningState = useWizardFormStepState<PickupRequestPlanningForm>({
  isWicConfirmationRequired: props.pickupRequestTemplate.isWicConfirmationRequired ?? false,
  isWicConfirmed: props.pickupRequestTemplate.isWicConfirmed ?? false,
  additionalFiles: props.pickupRequestTemplate.additionalFiles,
  range: {
    from: props.pickupRequestTemplate.startDate,
    until: props.pickupRequestTemplate.endDate,
  },
  remarks: props.pickupRequestTemplate.remarks,
})

const packagingRequestState = useWizardFormStepState<pickupRequestPackagingRequestForm>({
  isPackagingAdded: computed<boolean>(() => wasteState.value.isPackagingAdded).value,
  packagingRequestMaterials: props.pickupRequestTemplate.packagingRequestMaterials.map((material) => ({
    ...material,
    contractLineId: material.contractLineId,
    isSales: material.isSales ?? false,
    contractItem: material.contractItem,
    contractNumber: material.contractNumber,
    costCenter: material.costCenter,
    materialNumber: material.materialNumber ?? '',
    poNumber: material.poNumber,
    quantity: material.quantity,
    wasteMaterial: material.wasteMaterial ?? '',
  })),
})

const pickupRequestTemplateForm = useForm({
  initialState: () => ({ templateName: props.pickupRequestTemplate.templateName }),
  schema: pickupRequestTemplateUpdateFormSchema,
  onSubmit: async (values) => {
    await pickupRequestTemplateUpdateMutation.execute({
      body: {
        ...customerAndLocationState.value,
        ...wasteState.value,
        ...planningState.value,
        ...packagingRequestState.value,
        ...values,
      },
      params: { pickupRequestTemplateUuid: props.pickupRequestTemplate.uuid },
    })
  },
})

const templateNameField = pickupRequestTemplateForm.register('templateName')

const wizardForm = useWizardForm({
  categories: [
    {
      id: 'general',
      name: computed<string>(() => i18n.t('module.pickup_request.update.general_info')),
      icon: 'building',
      steps: [
        {
          id: PickupRequestFormSteps.CUSTOMER_AND_LOCATION,
          isReadonly: false,
          name: computed<string>(() => i18n.t('module.pickup_request.update.customer_and_location.title')),
          scheme: pickupRequestCustomerAndLocationFormSchema,
          showValue: 'customer',
          showValueDescription: computed<string>(() => customerAndLocationState.value.customer?.address == null ? '-' : AddressUtil.format(customerAndLocationState.value.customer?.address)),
          showValueTitle: computed<string>(() => customerAndLocationState.value.customer?.name ?? ''),
          state: customerAndLocationState,
          component: (): Promise<Component> => import('@/modules/pickup-request/features/update/components/steps/customer-and-location/PickupRequestCustomerAndLocationForm.vue'),
        },
      ],
    },
    {
      id: 'details',
      name: computed<string>(() => i18n.t('module.pickup_request.update.pickup_details')),
      icon: 'truck',
      steps: [
        {
          id: PickupRequestFormSteps.WASTE,
          isReadonly: false,
          name: computed<string>(() => i18n.t('module.pickup_request.update.waste.title')),
          props: { isIndascanDraft: false },
          scheme: pickupRequestWasteFormSchema,
          state: wasteState,
          component: (): Promise<Component> => import('@/modules/pickup-request/features/update/components/steps/waste/PickupRequestWasteForm.vue'),
        },
        {
          id: PickupRequestFormSteps.DETAILS,
          isReadonly: false,
          isStepHidden: computed<boolean>(() => false),
          name: computed<string>(() => i18n.t('module.pickup_request.update.details.title')),
          scheme: pickupRequestDetailsFormSchema,
          state: wasteState,
          component: (): Promise<Component> => import('@/modules/pickup-request/features/update/components/steps/details/PickupRequestDetailsForm.vue'),
        },
        {
          id: PickupRequestFormSteps.PACKAGING,
          isReadonly: false,
          isStepHidden: computed<boolean>(() => false),
          name: computed<string>(() => {
            if (wasteState.value.transportMode === PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK) {
              return i18n.t('module.pickup_request.update.packaging.title')
            }

            return i18n.t('module.pickup_request.update.container_info.title')
          }),
          scheme: pickupRequestPackagingFormSchema,
          state: wasteState,
          component: (): Promise<Component> => import('@/modules/pickup-request/features/update/components/steps/packaging/PickupRequestPackagingForm.vue'),
        },
        {
          id: PickupRequestFormSteps.TRANSPORT,
          isReadonly: false,
          isStepHidden: computed<boolean>(() => false),
          name: computed<string>(() => i18n.t('module.pickup_request.update.transport.title')),
          scheme: pickupRequestTransportFormSchema,
          state: wasteState,
          component: (): Promise<Component> => import('@/modules/pickup-request/features/update/components/steps/transport/PickupRequestTransportForm.vue'),
        },
        {
          id: PickupRequestFormSteps.ADMINISTRATION,
          isReadonly: false,
          isStepHidden: computed<boolean>(() => false),
          name: computed<string>(() => i18n.t('module.pickup_request.update.administration.title')),
          props: { isIndascanDraft: false },
          scheme: pickupRequestAdministrationFormSchema,
          state: wasteState,
          component: (): Promise<Component> => import('@/modules/pickup-request/features/update/components/steps/administration/PickupRequestAdministrationForm.vue'),
        },
        {
          id: PickupRequestFormSteps.PACKAGING_REQUEST,
          isReadonly: false,
          isStepHidden: computed<boolean>(() => !wasteState.value.isPackagingAdded),
          name: computed<string>(() => i18n.t('module.pickup_request.update.packaging_request.title')),
          scheme: pickupRequestPackagingRequestSchema,
          state: packagingRequestState,
          component: (): Promise<Component> => import('@/modules/pickup-request/features/update/components/steps/packaging-request/PickupRequestPackagingRequestForm.vue'),
        },
        {
          id: PickupRequestFormSteps.PLANNING,
          isReadonly: false,
          name: computed<string>(() => i18n.t('module.pickup_request.update.planning.title')),
          props: { isIndascanDraft: false },
          scheme: pickupRequestPlanningFormSchema,
          state: planningState,
          component: (): Promise<Component> => import('@/modules/pickup-request/features/update/components/steps/planning/PickupRequestPlanningForm.vue'),
        },
      ],
    },
  ],
  lastStepAction: null,
  onAutoSave: async (): Promise<void> => {
    await pickupRequestTemplateUpdateMutation.execute({
      body: {
        ...customerAndLocationState.value,
        ...wasteState.value,
        ...planningState.value,
        ...packagingRequestState.value,
        templateName: templateNameField.modelValue.value ?? '',
      },
      params: { pickupRequestTemplateUuid: props.pickupRequestTemplate.uuid },
    })
  },
})

function onGoToOverview(): void {
  emit('goToOverview')
}

function onDeleteTemplate(): void {
  confirmDialog.open({
    teleportTargetId: TELEPORT_TARGET_ID,
    title: i18n.t('module.pickup_request_template.update.delete_template'),
    isDestructive: true,
    isLoading: pickupRequestTemplateDeleteMutation.isLoading.value,
    cancelText: i18n.t('shared.cancel'),
    confirmText: i18n.t('shared.delete'),
    description: i18n.t('module.pickup_request_template.update.delete_template_description'),
    onConfirm: async () => {
      await pickupRequestTemplateDeleteMutation.execute({ body: props.pickupRequestTemplate.uuid })
      emit('goToOverview')

      toast.info({
        title: i18n.t('module.pickup_request_template.update.template_deleted'),
        description: i18n.t('module.pickup_request_template.update.template_deleted_description'),
      })
    },
  })
}
</script>

<template>
  <div class="flex h-full flex-col overflow-hidden">
    <div :id="TELEPORT_TARGET_ID" />

    <PickupRequestTemplateHeader :title="props.pickupRequestTemplate.templateName">
      <template #title>
        <VcDialogTitle>
          <h1 class="sr-only">
            {{ props.pickupRequestTemplate.templateName }}
          </h1>
        </VcDialogTitle>

        <AppGroup gap="xxs">
          <VcIconButton
            :label="i18n.t('shared.previous')"
            icon="chevronLeft"
            variant="tertiary"
            size="sm"
            @click="onGoToOverview"
          />

          <form
            :novalidate="true"
            @submit.prevent="pickupRequestTemplateForm.submit()"
          >
            <label
              for="template-name"
              class="sr-only"
            >
              {{ i18n.t('module.pickup_request_template.update.template_name') }}
            </label>

            <VcTextField
              id="template-name"
              v-bind="toFormField(templateNameField)"
              :class-config="{
                input: 'font-semibold text-xl text-primary field-sizing-content',
                iconRight: 'ml-md',
                root: 'relative border-0 outline-none hover:bg-primary-hover focus-within:bg-primary-hover',
              }"
              :placeholder="i18n.t('module.pickup_request_template.update.template_name')"
              icon-right="pencil"
              @blur="pickupRequestTemplateForm.submit()"
            />
          </form>
        </AppGroup>
      </template>

      <template #actions>
        <VcButton
          :is-loading="pickupRequestTemplateDeleteMutation.isLoading.value"
          variant="destructive-secondary"
          @click="onDeleteTemplate"
        >
          {{ i18n.t('module.pickup_request_template.update.delete_template') }}
        </VcButton>
      </template>
    </PickupRequestTemplateHeader>

    <WizardForm :wizard-form="wizardForm">
      <div class="flex h-full flex-col overflow-hidden">
        <div class="p-xl flex h-full overflow-auto">
          <WizardFormSidebar class="sticky top-0" />
          <WizardFormContent actions-teleport-target="#form-actions" />
        </div>
      </div>
    </WizardForm>

    <div id="form-actions" />
  </div>
</template>
