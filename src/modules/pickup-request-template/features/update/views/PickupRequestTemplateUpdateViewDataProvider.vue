<script setup lang="ts">
import { computed } from 'vue'

import AppDataProviderView from '@/components/app/AppDataProviderView.vue'
import type { PickupRequestTemplateUuid } from '@/models/pickup-request-template/pickupRequestTemplateUuid.model'
import { usePickupRequestTemplateDetailQuery } from '@/modules/pickup-request-template/api/queries/pickupRequestTemplateDetail.query'
import PickupRequestTemplateUpdateView from '@/modules/pickup-request-template/features/update/views/PickupRequestTemplateUpdateView.vue'

const props = defineProps<{
  pickupRequestTemplateUuid: PickupRequestTemplateUuid
}>()

const emit = defineEmits<{
  goToOverview: []
}>()

const pickupRequestTemplateDetailQuery = usePickupRequestTemplateDetailQuery(
  computed<PickupRequestTemplateUuid>(() => props.pickupRequestTemplateUuid),
)

function onGoToOverview(): void {
  emit('goToOverview')
}
</script>

<template>
  <AppDataProviderView
    v-slot="{ data }"
    :queries="{
      template: pickupRequestTemplateDetailQuery,
    }"
  >
    <PickupRequestTemplateUpdateView
      :pickup-request-template="data.template"
      @go-to-overview="onGoToOverview"
    />
  </AppDataProviderView>
</template>
