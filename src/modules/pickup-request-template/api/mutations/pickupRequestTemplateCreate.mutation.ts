import type { UseMutationReturnType } from '@wisemen/vue-core-query'
import { useMutation } from '@wisemen/vue-core-query'

import type { PickupRequestTemplateUuid } from '@/models/pickup-request-template/pickupRequestTemplateUuid.model'
import { PickupRequestTemplateService } from '@/modules/pickup-request-template/api/services/pickupRequestTemplate.service'

interface Body {
  name: string
}

export function usePickupRequestTemplateCreateMutation(): UseMutationReturnType<Body, PickupRequestTemplateUuid> {
  return useMutation<Body, PickupRequestTemplateUuid>({
    queryFn: async ({ body }) => {
      return await PickupRequestTemplateService.create(body.name)
    },
    queryKeysToInvalidate: { pickupRequestTemplateIndex: {} },
  })
}
