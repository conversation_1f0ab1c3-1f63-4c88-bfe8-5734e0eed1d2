import type { UseMutationReturnType } from '@wisemen/vue-core-query'
import { useMutation } from '@wisemen/vue-core-query'

import type { PickupRequestTemplateUuid } from '@/models/pickup-request-template/pickupRequestTemplateUuid.model'
import { PickupRequestTemplateService } from '@/modules/pickup-request-template/api/services/pickupRequestTemplate.service'

export function usePickupRequestTemplateDeleteMutation(): UseMutationReturnType<PickupRequestTemplateUuid, void> {
  return useMutation<PickupRequestTemplateUuid, void>({
    queryFn: async ({ body }) => {
      await PickupRequestTemplateService.delete(body)
    },
    queryKeysToInvalidate: { pickupRequestTemplateIndex: {} },
  })
}
