import type { UseMutationReturnType } from '@wisemen/vue-core-query'
import { useMutation } from '@wisemen/vue-core-query'

import type { PickupRequestUuid } from '@/models/pickup-request/pickupRequestUuid.model'
import type { PickupRequestTemplateUuid } from '@/models/pickup-request-template/pickupRequestTemplateUuid.model'
import { PickupRequestTemplateService } from '@/modules/pickup-request-template/api/services/pickupRequestTemplate.service'

interface Body {
  pickupRequestTemplateUuid: PickupRequestTemplateUuid
}

export function usePickupRequestTemplateCreatePickupRequestMutation(): UseMutationReturnType<Body, PickupRequestUuid> {
  return useMutation<Body, PickupRequestUuid>({
    queryFn: async ({ body }) => {
      return await PickupRequestTemplateService.createPickupRequestFromTemplate(body.pickupRequestTemplateUuid)
    },
    queryKeysToInvalidate: { pickupRequestIndex: {} },
  })
}
