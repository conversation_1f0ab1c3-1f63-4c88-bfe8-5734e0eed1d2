import type { UseMutationReturnType } from '@wisemen/vue-core-query'
import { useMutation } from '@wisemen/vue-core-query'

import type { PickupRequestUpdateForm } from '@/models/pickup-request/update/pickupRequestUpdateForm.model'
import type { PickupRequestTemplateUuid } from '@/models/pickup-request-template/pickupRequestTemplateUuid.model'
import type { PickupRequestTemplateUpdateForm } from '@/models/pickup-request-template/update/pickupRequestTemplateUpdateForm.model'
import { PickupRequestTemplateService } from '@/modules/pickup-request-template/api/services/pickupRequestTemplate.service'

interface Params {
  pickupRequestTemplateUuid: PickupRequestTemplateUuid
}

export function usePickupRequestTemplateUpdateMutation():
UseMutationReturnType<Partial<PickupRequestUpdateForm> & PickupRequestTemplateUpdateForm, void, Params> {
  return useMutation<Partial<PickupRequestUpdateForm> & PickupRequestTemplateUpdateForm, void, Params>({
    queryFn: async ({
      body, params,
    }) => {
      return await PickupRequestTemplateService.update(params.pickupRequestTemplateUuid, body)
    },
    queryKeysToInvalidate: { pickupRequestTemplateIndex: {} },
  })
}
