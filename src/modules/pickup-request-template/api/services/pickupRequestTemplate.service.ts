import {
  bulkDeletePickUpRequestTemplatesV1,
  createPickUpRequestFromTemplateV1,
  createPickUpRequestTemplateV1,
  updatePickUpRequestTemplateV1,
  viewPickUpRequestTemplateIndexV1,
  viewPickUpRequestTemplateV1,
} from '@/client'
import type { PickupRequestUuid } from '@/models/pickup-request/pickupRequestUuid.model'
import type { PickupRequestTemplateDetail } from '@/models/pickup-request-template/detail/pickupRequestTemplateDetail.model'
import { PickupRequestTemplateDetailTransformer } from '@/models/pickup-request-template/detail/pickupRequestTemplateDetail.transformer'
import type { PickupRequestTemplateIndex } from '@/models/pickup-request-template/index/pickupRequestTemplateIndex.model'
import { PickupRequestTemplateIndexTransformer } from '@/models/pickup-request-template/index/pickupRequestTemplateIndex.transformer'
import type { PickupRequestTemplateIndexQueryParams } from '@/models/pickup-request-template/index/pickupRequestTemplateIndexQueryParams.model'
import { PickupRequestTemplateIndexQueryParamsTransformer } from '@/models/pickup-request-template/index/pickupRequestTemplateIndexQueryParams.transformer'
import type { PickupRequestTemplateUuid } from '@/models/pickup-request-template/pickupRequestTemplateUuid.model'
import { PickupRequestTemplateUpdateTransformer } from '@/models/pickup-request-template/update/pickupRequestTemplateUpdate.transformer'
import type { PickupRequestTemplateUpdateForm } from '@/models/pickup-request-template/update/pickupRequestTemplateUpdateForm.model'
import type {
  OffsetPagination,
  OffsetPaginationResponse,
} from '@/types/pagination.type'

export class PickupRequestTemplateService {
  static async create(name: string): Promise<PickupRequestTemplateUuid> {
    const response = await createPickUpRequestTemplateV1({ body: { name } })

    return response.data.uuid as PickupRequestTemplateUuid
  }

  static async createPickupRequestFromTemplate(uuid: PickupRequestTemplateUuid): Promise<PickupRequestUuid> {
    const response = await createPickUpRequestFromTemplateV1({
      body: {},
      path: { uuid },
    })

    return response.data.uuid as PickupRequestUuid
  }

  static async delete(uuid: PickupRequestTemplateUuid): Promise<void> {
    await bulkDeletePickUpRequestTemplatesV1({
      body: {
        pickUpRequestUuids: [
          uuid,
        ],
      },
    })
  }

  static async getAll(
    params: OffsetPagination<PickupRequestTemplateIndexQueryParams>,
  ): Promise<OffsetPaginationResponse<PickupRequestTemplateIndex>> {
    const response
    = await viewPickUpRequestTemplateIndexV1({ query: PickupRequestTemplateIndexQueryParamsTransformer.toDto(params) })

    return {
      data: response.data.items.map(PickupRequestTemplateIndexTransformer.fromDto),
      meta: response.data.meta,
    }
  }

  static async getByUuid(uuid: PickupRequestTemplateUuid): Promise<PickupRequestTemplateDetail> {
    const response = await viewPickUpRequestTemplateV1({ path: { uuid } })

    return PickupRequestTemplateDetailTransformer.fromDto(response.data)
  }

  static async update(uuid: PickupRequestTemplateUuid, form: PickupRequestTemplateUpdateForm): Promise<void> {
    await updatePickUpRequestTemplateV1({
      body: PickupRequestTemplateUpdateTransformer.toDto(form),
      path: { uuid },
    })
  }
}
