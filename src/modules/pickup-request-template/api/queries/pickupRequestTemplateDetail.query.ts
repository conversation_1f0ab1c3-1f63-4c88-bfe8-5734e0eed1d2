import type { UseQueryReturnType } from '@wisemen/vue-core-query'
import { useQuery } from '@wisemen/vue-core-query'
import type { ComputedRef } from 'vue'
import { toValue } from 'vue'

import type { PickupRequestTemplateDetail } from '@/models/pickup-request-template/detail/pickupRequestTemplateDetail.model'
import type { PickupRequestTemplateUuid } from '@/models/pickup-request-template/pickupRequestTemplateUuid.model'
import { PickupRequestTemplateService } from '@/modules/pickup-request-template/api/services/pickupRequestTemplate.service'

export function usePickupRequestTemplateDetailQuery(
  pickupRequestTemplateUuid: ComputedRef<PickupRequestTemplateUuid>,
): UseQueryReturnType<PickupRequestTemplateDetail> {
  return useQuery({
    queryFn: () => PickupRequestTemplateService.getByUuid(toValue(pickupRequestTemplateUuid)),
    queryKey: { pickupRequestTemplateDetail: { pickupRequestTemplateUuid } },
  })
}
