import { useOffsetInfiniteQuery } from '@/composables/offset-infinite-query/offsetInfiniteQuery.composable'
import type { PickupRequestTemplateIndexQueryParams } from '@/models/pickup-request-template/index/pickupRequestTemplateIndexQueryParams.model'
import { PickupRequestTemplateService } from '@/modules/pickup-request-template/api/services/pickupRequestTemplate.service'
import type { InfiniteQueryOptions } from '@/types/query.type'

// eslint-disable-next-line ts/explicit-function-return-type
export function usePickupRequestTemplateIndexQuery(
  options?: InfiniteQueryOptions<PickupRequestTemplateIndexQueryParams>,
) {
  return useOffsetInfiniteQuery({
    queryFn: (pagination) => PickupRequestTemplateService.getAll({
      pagination,
      search: options?.params.search.value ?? '',
      sort: options?.params.sort.value ?? [],
    }),
    queryKey: { pickupRequestTemplateIndex: { queryParams: options?.params } },
  })
}
