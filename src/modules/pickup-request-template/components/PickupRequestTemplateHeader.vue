<script setup lang="ts">
import {
  VcDialogCloseButton,
  VcDialogTitle,
  VcIconButton,
} from '@wisemen/vue-core-components'
import { useI18n } from 'vue-i18n'

import AppGroup from '@/components/app/AppGroup.vue'

const props = defineProps<{
  title: string
}>()

const i18n = useI18n()
</script>

<template>
  <header
    class="
      border-secondary p-2xl py-lg flex items-center justify-between border-b
    "
  >
    <slot name="title">
      <VcDialogTitle>
        <h1 class="text-primary text-xl font-semibold">
          {{ props.title }}
        </h1>
      </VcDialogTitle>
    </slot>

    <AppGroup gap="xl">
      <slot name="actions" />

      <VcDialogCloseButton>
        <VcIconButton
          :label="i18n.t('shared.close')"
          icon="close"
          variant="secondary"
        />
      </VcDialogCloseButton>
    </AppGroup>
  </header>
</template>
