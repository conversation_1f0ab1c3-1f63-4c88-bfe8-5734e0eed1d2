<script setup lang="ts">
import {
  VcDialogContent,
  VcDialogOverlay,
  VcDialogOverlayTransition,
  VcDialogPortal,
  VcDialogRoot,
} from '@wisemen/vue-core-components'
import { Motion } from 'motion-v'
import { ref } from 'vue'

import { onCreated } from '@/composables/created/created.composable'
import type { PickupRequestTemplateUuid } from '@/models/pickup-request-template/pickupRequestTemplateUuid.model'
import PickupRequestTemplateOverviewView from '@/modules/pickup-request-template/features/overview/views/PickupRequestTemplateOverviewView.vue'
import PickupRequestTemplateUpdateViewDataProvider from '@/modules/pickup-request-template/features/update/views/PickupRequestTemplateUpdateViewDataProvider.vue'

const emit = defineEmits<{
  close: []
}>()

const pageInnerContent = document.querySelector('#page-inner-content') as HTMLElement

const editingPickupRequestTemplateUuid = ref<PickupRequestTemplateUuid | null>(null)

function onClose(): void {
  pageInnerContent.style.scale = '1'
  emit('close')
}

function onSelectTemplate(uuid: PickupRequestTemplateUuid | null): void {
  editingPickupRequestTemplateUuid.value = uuid
}

onCreated(() => {
  pageInnerContent.style.scale = '0.98'
})
</script>

<template>
  <VcDialogRoot
    :class-config="{
      content: 'absolute translate-y-0 top-4 w-full h-[calc(100%-1rem)] rounded-none rounded-tl-4xl',
      overlay: 'backdrop-blur-none',
    }"
    teleport-target-id="page-content"
    class="w-full"
    @close="onClose"
  >
    <VcDialogPortal>
      <VcDialogContent>
        <Motion
          :initial="{ transform: 'translateY(100%)' }"
          :animate="{
            transform: 'translateY(0%)',
            transition: { type: 'spring', duration: 0.5, bounce: 0 },
          }"
          :exit="{
            transform: 'translateY(100%)',
            transition: { type: 'spring', duration: 0.3, bounce: 0 },
          }"
        >
          <PickupRequestTemplateOverviewView
            v-if="editingPickupRequestTemplateUuid === null"
            @select-template="onSelectTemplate"
          />

          <PickupRequestTemplateUpdateViewDataProvider
            v-else
            :pickup-request-template-uuid="editingPickupRequestTemplateUuid"
            @go-to-overview="onSelectTemplate(null)"
          />
        </Motion>
      </VcDialogContent>

      <VcDialogOverlay>
        <VcDialogOverlayTransition />
      </VcDialogOverlay>
    </VcDialogPortal>
  </VcDialogRoot>
</template>
