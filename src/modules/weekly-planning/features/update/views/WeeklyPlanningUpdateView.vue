<script setup lang="ts">
import { useVcDialog } from '@wisemen/vue-core-components'
import type { Component } from 'vue'
import {
  computed,
  watch,
} from 'vue'
import { useI18n } from 'vue-i18n'

import AppTeleport from '@/components/app/teleport/AppTeleport.vue'
import WizardForm from '@/components/form/wizard/WizardForm.vue'
import WizardFormContent from '@/components/form/wizard/WizardFormContent.vue'
import WizardFormSidebar from '@/components/form/wizard/WizardFormSidebar.vue'
import WizardFormState from '@/components/form/wizard/WizardFormState.vue'
import AppPage from '@/components/layout/page/AppPage.vue'
import AppPageReturnLink from '@/components/layout/page/AppPageReturnLink.vue'
import { useDocumentTitle } from '@/composables/document-title/documentTitle.composable'
import { useGlobalCustomer } from '@/composables/global-customer/globalCustomer.composable'
import {
  useWizardForm,
  WizardFormLastStepAction,
} from '@/composables/wizard-form/wizardForm.composable'
import { useWizardFormStepState } from '@/composables/wizard-form/wizardFormStepState.composable'
import { AddressUtil } from '@/models/address/address.util'
import { WeeklyPlanningFormSteps } from '@/models/enums/formSteps.enum'
import type { WeeklyPlanningDetail } from '@/models/weekly-planning/detail/weeklyPlanningDetail.model'
import type { WeeklyPlanningCustomerAndLocationForm } from '@/models/weekly-planning/update/steps/weeklyPlanningCustomerAndLocationForm.model'
import { weeklyPlanningCustomerAndLocationFormSchema } from '@/models/weekly-planning/update/steps/weeklyPlanningCustomerAndLocationForm.model'
import type { WeeklyPlanningExtraInfoForm } from '@/models/weekly-planning/update/steps/weeklyPlanningExtraInfoForm.model'
import { weeklyPlanningExtraInfoFormSchema } from '@/models/weekly-planning/update/steps/weeklyPlanningExtraInfoForm.model'
import type { WeeklyPlanningWasteForm } from '@/models/weekly-planning/update/steps/weeklyPlanningWasteForm.model'
import { weeklyPlanningWasteFormSchema } from '@/models/weekly-planning/update/steps/weeklyPlanningWasteForm.model'
import { useWeeklyPlanningUpdateMutation } from '@/modules/weekly-planning/api/mutations/weeklyPlanningUpdate.mutation'

const props = defineProps<{
  planning: WeeklyPlanningDetail
}>()

const i18n = useI18n()
const documentTitle = useDocumentTitle()
const globalCustomer = useGlobalCustomer()

documentTitle.set(() => i18n.t('module.weekly_planning.update.page_title'))

const weeklyPlanningUpdateMutation = useWeeklyPlanningUpdateMutation()

const customerAndLocationState = useWizardFormStepState<WeeklyPlanningCustomerAndLocationForm>({
  customer: props.planning.customer ?? globalCustomer.globalCustomer.value ?? null,
  pickupAddresses: props.planning.pickUpAddresses,
  wasteProducer: props.planning.wasteProducer,
})
const wasteState = useWizardFormStepState<WeeklyPlanningWasteForm>({
  pickUpRequests: props.planning.pickUpRequests.map((pickupReq) => {
    return {
      ...pickupReq,
      id: pickupReq.contractLineId,
      pickupRequestUuid: pickupReq.uuid,
    }
  }),
})
const planningState = useWizardFormStepState<WeeklyPlanningExtraInfoForm>({
  additionalFiles: props.planning.additionalFiles,
  remarks: props.planning.remarks,
})

const submitDialog = useVcDialog({ component: () => import('@/modules/weekly-planning/features/update/components/submit/WeeklyPlanningSubmitDialog.vue') })

const wizardForm = useWizardForm({
  categories: [
    {
      id: 'general',
      name: computed<string>(() => i18n.t('module.weekly_planning.update.general_info')),
      icon: 'building',
      steps: [
        {
          id: WeeklyPlanningFormSteps.CUSTOMER_AND_LOCATION,
          name: computed<string>(() => i18n.t('module.weekly_planning.update.customer_and_location.title')),
          scheme: weeklyPlanningCustomerAndLocationFormSchema,
          showValue: 'customer',
          showValueDescription: computed<string>(() => customerAndLocationState.value.customer?.address == null ? '-' : AddressUtil.format(customerAndLocationState.value.customer?.address)),
          showValueTitle: computed<string>(() => customerAndLocationState.value.customer?.name ?? ''),
          state: customerAndLocationState,
          component: (): Promise<Component> => import('@/modules/weekly-planning/features/update/components/steps/customer-and-location/WeeklyPlanningCustomerAndLocationForm.vue'),
        },
      ],
    },
    {
      id: 'details',
      name: computed<string>(() => i18n.t('module.weekly_planning.update.pickup_details')),
      icon: 'truck',
      steps: [
        {
          id: WeeklyPlanningFormSteps.WASTE,
          name: computed<string>(() => i18n.t('module.weekly_planning.update.waste.title')),
          scheme: weeklyPlanningWasteFormSchema,
          state: wasteState,
          component: (): Promise<Component> => import('@/modules/weekly-planning/features/update/components/steps/waste/WeeklyPlanningWasteForm.vue'),
        },
        {
          id: WeeklyPlanningFormSteps.EXTRA,
          name: computed<string>(() => i18n.t('module.weekly_planning.update.extra_info.title')),
          scheme: weeklyPlanningExtraInfoFormSchema,
          state: planningState,
          component: (): Promise<Component> => import('@/modules/weekly-planning/features/update/components/steps/extra-info/WeeklyPlanningExtraInfoForm.vue'),
        },
      ],
    },
  ],
  lastStepAction: WizardFormLastStepAction.SUBMIT,
  onAutoSave: async (): Promise<void> => {
    await weeklyPlanningUpdateMutation.execute({
      body: {
        ...customerAndLocationState.value,
        ...wasteState.value,
        ...planningState.value,
      },
      params: { weeklyPlanningUuid: props.planning.uuid },
    })
  },
  onShowSubmitDialog: (): void => {
    submitDialog.open({
      weeklyPlanningUuid: props.planning.uuid,
      onRedirectToWasteStep: redirectToWasteStep,
    })
  },
})

function redirectToWasteStep(): void {
  submitDialog.close()
  wizardForm.goToStep(WeeklyPlanningFormSteps.WASTE)
}

watch(() => props.planning, (newPlanning) => {
  wasteState.value = {
    pickUpRequests: newPlanning.pickUpRequests.map((pickupReq) => ({
      ...pickupReq,
      id: pickupReq.contractLineId,
      pickupRequestUuid: pickupReq.uuid,
    })),
  }
}, { immediate: true })
</script>

<template>
  <AppTeleport to="headerLeft">
    <AppPageReturnLink
      :return-link="{
        label: i18n.t('module.weekly_planning.update.return_to_overview'),
        to: {
          name: 'pickup-request-overview',
        },
      }"
    />
  </AppTeleport>

  <WizardForm :wizard-form="wizardForm">
    <div
      id="weekly-planning-floating-content"
      class="relative flex size-full flex-col overflow-hidden"
    >
      <AppPage :title="i18n.t('module.weekly_planning.update.page_title')">
        <template #header-actions>
          <WizardFormState
            :created-at="props.planning.createdAt"
            :auto-save-error-message="wizardForm.isAutoSaving.value ? null : wizardForm.autoSaveErrorMessage.value"
            :is-auto-saving="wizardForm.isAutoSaving.value"
          />
        </template>

        <template #left-content>
          <WizardFormSidebar />
        </template>

        <template #default>
          <WizardFormContent />
        </template>
      </AppPage>
    </div>
  </WizardForm>
</template>
