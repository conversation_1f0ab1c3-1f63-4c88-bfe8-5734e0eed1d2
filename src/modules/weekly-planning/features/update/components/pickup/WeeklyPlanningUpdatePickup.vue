<script setup lang="ts">
import { useQueryClient } from '@tanstack/vue-query'
import {
  V<PERSON><PERSON>utton,
  VcDialogCloseButton,
} from '@wisemen/vue-core-components'
import type { Component } from 'vue'
import {
  computed,
  watch,
} from 'vue'
import { useI18n } from 'vue-i18n'

import { PickUpTransportMode } from '@/client'
import AppGroup from '@/components/app/AppGroup.vue'
import WizardForm from '@/components/form/wizard/WizardForm.vue'
import WizardFormContent from '@/components/form/wizard/WizardFormContent.vue'
import WizardFormSidebar from '@/components/form/wizard/WizardFormSidebar.vue'
import AppPage from '@/components/layout/page/AppPage.vue'
import { useApiErrorToast } from '@/composables/api-error-toast/apiErrorToast.composable'
import { useGlobalCustomer } from '@/composables/global-customer/globalCustomer.composable'
import {
  useWizardForm,
  WizardFormLastStepAction,
} from '@/composables/wizard-form/wizardForm.composable'
import { useWizardFormStepState } from '@/composables/wizard-form/wizardFormStepState.composable'
import { AddressUtil } from '@/models/address/address.util'
import { WeeklyPlanningPickupFormSteps } from '@/models/enums/formSteps.enum'
import type { PickupRequestDetail } from '@/models/pickup-request/detail/pickupRequestDetail.model'
import { pickupRequestAdministrationFormSchema } from '@/models/pickup-request/update/steps/pickupRequestAdministrationForm.model'
import type { PickupRequestCustomerAndLocationForm } from '@/models/pickup-request/update/steps/pickupRequestCustomerAndLocationForm.model'
import { pickupRequestCustomerAndLocationFormSchema } from '@/models/pickup-request/update/steps/pickupRequestCustomerAndLocationForm.model'
import { pickupRequestPackagingFormSchema } from '@/models/pickup-request/update/steps/pickupRequestPackagingForm.model'
import type { PickupRequestWeeklyPlanningForm } from '@/models/pickup-request/update/steps/pickupRequestPlanningForm.model'
import { pickupRequestWeeklyPlanningFormSchema } from '@/models/pickup-request/update/steps/pickupRequestPlanningForm.model'
import { pickupRequestTransportFormSchema } from '@/models/pickup-request/update/steps/pickupRequestTransportForm.model'
import type { PickupRequestWasteForm } from '@/models/pickup-request/update/steps/pickupRequestWasteForm.model'
import { pickupRequestWasteFormSchema } from '@/models/pickup-request/update/steps/pickupRequestWasteForm.model'
import { usePickupRequestUpdateMutation } from '@/modules/pickup-request/api/mutations/pickupRequestUpdate.mutation'

const props = defineProps<{
  pickup: PickupRequestDetail
}>()

const emit = defineEmits<{
  close: []
}>()

const i18n = useI18n()
const queryClient = useQueryClient()
const apiErrorToast = useApiErrorToast()
const globalCustomer = useGlobalCustomer()

const pickupRequestUpdateMutation = usePickupRequestUpdateMutation()

const customerAndLocationState = useWizardFormStepState<PickupRequestCustomerAndLocationForm>({
  customer: props.pickup.customer ?? globalCustomer.globalCustomer.value ?? null,
  pickupAddresses: props.pickup.pickupAddresses ?? [],
  wasteProducer: props.pickup.wasteProducer ?? null,
})
const wasteState = useWizardFormStepState<PickupRequestWasteForm>({
  isPackagingAdded: false,
  isReturnPackaging: props.pickup.isReturnPackaging ?? false,
  isTransportByIndaver: props.pickup.isTransportByIndaver === null ? false : props.pickup.isTransportByIndaver,
  customerCountryCode: null,
  materials: props.pickup.materials.map((material) => ({
    ...material,
    isCostCenterRequired: false,
    isPoNumberRequired: false,
    amount: null,
    containerType: material.containerType === null ? null : { name: material.containerType },
    containerVolumeSize: null,
    hazardInducers: null,
    packagingType: material.packagingType === null ? null : { name: material.packagingType },
    quantityContainers: null,
    reconciliationNumber: null,
  })) ?? [],
  packagingRemark: props.pickup.packagingRemark ?? null,
  totalQuantityPallets: props.pickup.totalQuantityPallets ?? null,
  transportMode: props.pickup.transportMode ?? null,
})

const planningState = useWizardFormStepState<PickupRequestWeeklyPlanningForm>({
  startDate: props.pickup.startDate,
  startTime: null,
})

const wizardForm = useWizardForm({
  categories: [
    {
      id: 'general',
      name: computed<string>(() => i18n.t('module.pickup_request.update.general_info')),
      icon: 'building',
      steps: [
        {
          id: WeeklyPlanningPickupFormSteps.CUSTOMER_AND_LOCATION,
          isReadonly: true,
          name: computed<string>(() => i18n.t('module.pickup_request.update.customer_and_location.title')),
          scheme: pickupRequestCustomerAndLocationFormSchema,
          showValue: 'customer',
          showValueDescription: computed<string>(() => customerAndLocationState.value.customer?.address == null ? '-' : AddressUtil.format(customerAndLocationState.value.customer?.address)),
          showValueTitle: computed<string>(() => customerAndLocationState.value.customer?.name ?? ''),
          state: customerAndLocationState,
          component: (): Promise<Component> => import('@/modules/pickup-request/features/update/components/steps/customer-and-location/PickupRequestCustomerAndLocationForm.vue'),
        },
      ],
    },
    {
      id: 'details',
      name: computed<string>(() => i18n.t('module.pickup_request.update.pickup_details')),
      icon: 'truck',
      steps: [
        {
          id: WeeklyPlanningPickupFormSteps.WASTE,
          name: computed<string>(() => i18n.t('module.pickup_request.update.waste.title')),
          scheme: pickupRequestWasteFormSchema,
          state: wasteState,
          component: (): Promise<Component> => import('@/modules/weekly-planning/features/update/components/pickup/steps/WeeklyPlanningPickupWasteForm.vue'),
        },
        {
          id: WeeklyPlanningPickupFormSteps.PACKAGING,
          name: computed<string>(() => {
            if (wasteState.value.transportMode === PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK) {
              return i18n.t('module.pickup_request.update.packaging.title')
            }

            return i18n.t('module.pickup_request.update.container_info.title')
          }),
          scheme: pickupRequestPackagingFormSchema,
          state: wasteState,
          component: (): Promise<Component> => import('@/modules/pickup-request/features/update/components/steps/packaging/PickupRequestPackagingForm.vue'),
        },
        {
          id: WeeklyPlanningPickupFormSteps.TRANSPORT,
          name: computed<string>(() => i18n.t('module.pickup_request.update.transport.title')),
          scheme: pickupRequestTransportFormSchema,
          state: wasteState,
          component: (): Promise<Component> => import('@/modules/pickup-request/features/update/components/steps/transport/PickupRequestTransportForm.vue'),
        },
        {
          id: WeeklyPlanningPickupFormSteps.ADMINISTRATION,
          name: computed<string>(() => i18n.t('module.pickup_request.update.administration.title')),
          scheme: pickupRequestAdministrationFormSchema,
          state: wasteState,
          component: (): Promise<Component> => import('@/modules/pickup-request/features/update/components/steps/administration/PickupRequestAdministrationForm.vue'),
        },
        {
          id: WeeklyPlanningPickupFormSteps.PLANNING,
          name: computed<string>(() => i18n.t('module.pickup_request.update.planning.title')),
          scheme: pickupRequestWeeklyPlanningFormSchema,
          state: planningState,
          component: (): Promise<Component> => import('@/modules/weekly-planning/features/update/components/pickup/steps/WeeklyPlanningPickupPlanningForm.vue'),
        },
      ],
    },
  ],
  lastStepAction: WizardFormLastStepAction.SAVE,
  onAutoSave: async (): Promise<void> => {},
  onShowSubmitDialog: (): void => {
    onSave()
  },
})

async function onSave(): Promise<void> {
  if (props.pickup.uuid == null) {
    return
  }
  try {
    await pickupRequestUpdateMutation.execute({
      body: {
        ...customerAndLocationState.value,
        ...wasteState.value,
        ...planningState.value,
      },
      params: {
        pickupRequestUuid: props.pickup.uuid,
        isWeeklyPlanning: true,
      },
    })
    queryClient.invalidateQueries({
      queryKey: [
        'weeklyPlanningDetail',
      ],
    })
    emit('close')
  }
  catch (error) {
    apiErrorToast.show(error)
  }
}

wizardForm.goToStep(WeeklyPlanningPickupFormSteps.WASTE)

watch(
  () => props.pickup,
  (newPickup) => {
    if (newPickup.startDate) {
      planningState.value.startDate = newPickup.startDate
    }
  },
  { immediate: true },
)
</script>

<template>
  <WizardForm :wizard-form="wizardForm">
    <AppPage :title="i18n.t('module.weekly_planning.update.pickup.title')">
      <template #header-actions>
        <AppGroup>
          <VcDialogCloseButton>
            <VcButton variant="secondary">
              {{ i18n.t('shared.cancel') }}
            </VcButton>
          </VcDialogCloseButton>

          <VcButton
            :is-disabled="!wizardForm.areAllStepsOfCategoryValid(wizardForm.categories[1].id)"
            @click="onSave"
          >
            {{ i18n.t('module.weekly_planning.update.pickup.save_and_close') }}
          </VcButton>
        </AppGroup>
      </template>

      <template #left-content>
        <WizardFormSidebar />
      </template>

      <template #default>
        <WizardFormContent />
      </template>
    </AppPage>
  </WizardForm>
</template>
