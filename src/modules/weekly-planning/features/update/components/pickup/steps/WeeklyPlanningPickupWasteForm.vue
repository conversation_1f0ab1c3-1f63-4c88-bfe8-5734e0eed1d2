<script setup lang="ts">
import type { PaginatedData } from '@wisemen/vue-core-components'
import {
  usePagination,
  VcCheckbox,
  VcFormField,
  VcRadioGroup,
} from '@wisemen/vue-core-components'
import {
  computed,
  onMounted,
  ref,
} from 'vue'
import { useI18n } from 'vue-i18n'

import type { PickUpAddressResponse } from '@/client'
import { PickUpTransportMode } from '@/client'
import AppGroup from '@/components/app/AppGroup.vue'
import AppHeightTransition from '@/components/app/AppHeightTransition.vue'
import AppCardWithIcon from '@/components/app/card/AppCardWithIcon.vue'
import FormRadioGroupItem from '@/components/form/FormRadioGroupItem.vue'
import FormRadioGroupLayout from '@/components/form/FormRadioGroupLayout.vue'
import WizardFormStep from '@/components/form/wizard/WizardFormStep.vue'
import type { WizardForm } from '@/composables/wizard-form/wizardForm.composable'
import { useWizardFormStep } from '@/composables/wizard-form/wizardFormStep.composable'
import type { ContractLineIndex } from '@/models/contract-line/index/contractLineIndex.model'
import type { ContractLineIndexPagination } from '@/models/contract-line/index/contractLineIndexPagination.model.ts'
import { PickupTransportModeEnumUtil } from '@/models/enums/transportMode.enum'
import type {
  PickupRequestWasteForm,
  pickupRequestWasteFormSchema,
} from '@/models/pickup-request/update/steps/pickupRequestWasteForm.model'
import { useContractLineIndexQueryOld } from '@/modules/contract-line/api/queries/contractLineIndexOld.query'
import PickupRequestWasteContractLineTable from '@/modules/pickup-request/features/update/components/steps/waste/PickupRequestWasteContractLineTable.vue'
import { toFormField } from '@/utils/formango.util'

const props = defineProps<{
  wizardForm: WizardForm<typeof pickupRequestWasteFormSchema>
}>()
const LIMIT = 1

const i18n = useI18n()

const wizardFormStep = useWizardFormStep(props.wizardForm)

const transportMode = wizardFormStep.form.register('transportMode')
const materials = wizardFormStep.form.register('materials')
const isTransportByIndaver = wizardFormStep.form.register('isTransportByIndaver', true)

const materialsHasError = computed<boolean>(() => toFormField(materials).errorMessage !== null)

const customerId = computed<string>(() => props.wizardForm.state.value.customer.id)
const wasteProducerId = computed<string | null>(() => props.wizardForm.state.value.wasteProducer
  ? props.wizardForm.state.value.wasteProducer.id
  : null)
const pickupAddressIds = computed<string[]>(() => props.wizardForm.state.value.pickUpAddresses
  ? props.wizardForm.state.value.pickUpAddresses.map(
      (address: PickUpAddressResponse) => address.id,
    )
  : [])

const contractLineIndexPagination = usePagination<ContractLineIndexPagination>({
  isRouteQueryEnabled: false,
  options: {
    staticFilters: {
      customerId: customerId.value,
      wasteProducerId: wasteProducerId.value ?? undefined,
      pickUpAddressIds: pickupAddressIds.value,
    },
  },
})

const contractLineIndexQuery = useContractLineIndexQueryOld(contractLineIndexPagination.paginationOptions)

const selectedContractLines = ref<ContractLineIndex[]>(props.wizardForm.state.value.materials)
const selectedContractLinesAmount = computed<number>(() => selectedContractLines.value.length)
const selectedContractLinesPaginated = computed<PaginatedData<ContractLineIndex>>(() => {
  return {
    data: selectedContractLines.value.filter((item, index, self) =>
      index === self.findIndex((t) => t.contractLineId === item.contractLineId)),
    meta: {
      limit: selectedContractLines.value.length,
      offset: 0,
      total: selectedContractLines.value.length,
    },
  }
})

function initializeMaterials(): void {
  const materialsData = props.wizardForm.state.value.materials as Array<PickupRequestWasteForm['materials'][number]>

  const materialCounts = materialsData.reduce<Record<string, number>>((acc, material) => {
    const key = material.contractLineId

    acc[key] = (acc[key] || 0) + 1

    return acc
  }, {})

  const pickupMaterials = materialsData.map((material) => ({
    ...material,
    isContainerCovered: material.isContainerCovered ?? null,
    amount: materialCounts[material.contractLineId],
    containerNumber: material.containerNumber ?? null,
    containerTransportType: material.containerTransportType ?? null,
    containerType: material.containerType ?? null,
    costCenter: material.costCenter ?? null,
    estimatedWeightOrVolumeUnit: material.estimatedWeightOrVolumeUnit ?? null,
    estimatedWeightOrVolumeValue: material.estimatedWeightOrVolumeValue ?? null,
    packagingType: material.packagingType ?? null,
    poNumber: material.poNumber ?? null,
    quantityLabels: material.quantityLabels ?? null,
    quantityPackages: material.quantityPackages ?? null,
    quantityPallets: material.quantityPallets ?? null,
    tankerType: material.tankerType ?? null,
    unNumber: material.unNumber ?? null,
  }))

  materials.setValue(pickupMaterials)

  selectedContractLines.value = pickupMaterials.map((item) => {
    return {
      ...item,
      amount: item.amount ?? null,
      containerType: item.containerType?.name ?? null,
      packagingType: item.packagingType?.name ?? null,
      unNumber: item.unNumber?.number ?? null,
    }
  })
}

onMounted(() => {
  initializeMaterials()
})
</script>

<template>
  <WizardFormStep :wizard-form-step="wizardFormStep">
    <AppGroup
      direction="col"
      gap="3xl"
      align="start"
    >
      <VcRadioGroup
        :is-required="true"
        :label="i18n.t('module.pickup_request.update.waste.transport_mode')"
        class="w-full"
        v-bind="toFormField(transportMode)"
      >
        <FormRadioGroupLayout :cols="2">
          <FormRadioGroupItem
            v-for="item of Object.values(PickUpTransportMode)"
            :key="item"
            :value="item"
            :is-disabled="item === PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK"
          >
            <AppCardWithIcon
              :title="i18n.t(PickupTransportModeEnumUtil.getLabelI18nKey(item))"
              :description="i18n.t(PickupTransportModeEnumUtil.getDescriptionI18nKey(item))"
              :is-selected="item === transportMode.value.value"
              :is-disabled="item === PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK"
              icon="truck"
              variant="gray-light"
            />
          </FormRadioGroupItem>
        </FormRadioGroupLayout>
      </VcRadioGroup>

      <AppHeightTransition
        :duration="300"
        class="w-full"
      >
        <Transition
          enter-active-class="duration-300"
          leave-active-class="duration-300"
          mode="out-in"
          enter-from-class="opacity-0"
          leave-to-class="opacity-0"
        >
          <div v-if="transportMode.modelValue.value !== null">
            <div class="px-xl py-lg border-secondary mb-3xl rounded-md border">
              <VcCheckbox
                v-bind="toFormField(isTransportByIndaver)"
                :label="i18n.t('module.pickup_request.update.waste.transport_by_indaver')"
              />
            </div>

            <VcFormField
              :label="i18n.t('module.pickup_request.update.waste.materials.title')"
              :is-required="true"
              :error-message="materialsHasError ? toFormField(materials).errorMessage : null"
              :is-touched="true"
              :class-config="{
                error: 'mb-xl',
              }"
              for="materials"
            />

            <PickupRequestWasteContractLineTable
              :customer-id="customerId"
              :data="selectedContractLinesPaginated.data"
              :is-loading="contractLineIndexQuery.isLoading.value"
              :is-fetching="contractLineIndexQuery.isFetching.value"
              :show-count="true"
              :is-count-disabled="true"
              :show-dynamic-views="false"
              :show-selected-only="true"
              :show-filters="false"
              :selected-items="selectedContractLines"
              :select-limit="LIMIT"
              :selected-items-amount="selectedContractLinesAmount"
            />
          </div>
        </Transition>
      </AppHeightTransition>
    </AppGroup>
  </WizardFormStep>
</template>
