<script setup lang="ts">
import type { VNode } from 'vue'
import {
  computed,
  h,
} from 'vue'
import { useI18n } from 'vue-i18n'

import DataTable from '@/components/table/data-table/DataTable.vue'
import { useGenericColumn } from '@/composables/table-columns/genericTableColumnsV2.composable'
import type { ContractLineWeeklyPlanningIndex } from '@/models/contract-line/weekly-planning/contractLineWeeklyPlanningIndex.model'
import type { WeeklyPlanningUuid } from '@/models/weekly-planning/weeklyPlanningUuid.model'
import WeeklyPlanningWasteContractLineTableActionsCell from '@/modules/weekly-planning/features/update/components/steps/waste/WeeklyPlanningWasteContractLineTableActionsCell.vue'
import WeeklyPlanningWasteContractLineTablePreferredDateCell from '@/modules/weekly-planning/features/update/components/steps/waste/WeeklyPlanningWasteContractLineTablePreferredDateCell.vue'
import WeeklyPlanningWasteContractLineTablePreferredTimeCell from '@/modules/weekly-planning/features/update/components/steps/waste/WeeklyPlanningWasteContractLineTablePreferredTimeCell.vue'
import type { DataTableColumn } from '@/types/table.type'

const props = defineProps<{
  weeklyPlanningUuid: WeeklyPlanningUuid
  data: ContractLineWeeklyPlanningIndex[]
}>()

const emit = defineEmits<{
  edit: [item: ContractLineWeeklyPlanningIndex]
}>()

const i18n = useI18n()

const columns = computed<DataTableColumn<ContractLineWeeklyPlanningIndex>[]>(() => [
  useGenericColumn('wasteMaterial', i18n.t('enum.dynamic_table_column_name.waste_material')),
  useGenericColumn('customerReference', i18n.t('enum.dynamic_table_column_name.customer_reference')),
  useGenericColumn('ewcCode', i18n.t('enum.dynamic_table_column_name.ewc_code')),
  {
    cell: (row): VNode => h(WeeklyPlanningWasteContractLineTablePreferredDateCell, {
      pickupRequestUuid: row.pickupRequestUuid,
      weeklyPlanningUuid: props.weeklyPlanningUuid,
      date: row.startDate?.toString() ?? null,
      time: row.startTime ?? null,
    }),
    headerLabel: `${i18n.t('module.pickup_request.update.waste.contract_line.pickup_date')}*`,
    key: 'preferredPickupDate',
    width: '12rem',
  },
  {
    cell: (row): VNode => h(WeeklyPlanningWasteContractLineTablePreferredTimeCell, {
      pickupRequestUuid: row.pickupRequestUuid,
      weeklyPlanningUuid: props.weeklyPlanningUuid,
      date: row.startDate?.toString() ?? null,
      time: row.startTime ?? null,
    }),
    headerLabel: i18n.t('module.pickup_request.update.waste.contract_line.pickup_time'),
    key: 'preferredPickupTime',
  },
  {
    cell: (row): VNode => h(WeeklyPlanningWasteContractLineTableActionsCell, {
      pickupRequestUuid: row.pickupRequestUuid,
      weeklyPlanningUuid: props.weeklyPlanningUuid,
      onEdit: () => {
        emit('edit', row)
      },
    }),
    headerLabel: '',
    key: 'actions',
  },
] as DataTableColumn<ContractLineWeeklyPlanningIndex>[])

async function fetchNextPage(): Promise<void> {
  // No pagination needed for selected items
}
</script>

<template>
  <DataTable
    :is-loading="false"
    :data="props.data"
    :columns="columns"
    :error="null"
    :get-key="(_, index) => `${index}`"
    :is-first-column-sticky="true"
    :is-last-column-sticky="true"
    :disable-top-left-border-radius="true"
    class="max-h-[calc(100vh-22rem)]"
    @next-page="fetchNextPage"
  />
</template>
