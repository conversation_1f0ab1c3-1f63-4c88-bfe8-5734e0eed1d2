<script setup lang="ts">
import { useVcDialog } from '@wisemen/vue-core-components'
import type { Component } from 'vue'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import AppTeleport from '@/components/app/teleport/AppTeleport.vue'
import WizardForm from '@/components/form/wizard/WizardForm.vue'
import WizardFormContent from '@/components/form/wizard/WizardFormContent.vue'
import WizardFormSidebar from '@/components/form/wizard/WizardFormSidebar.vue'
import WizardFormState from '@/components/form/wizard/WizardFormState.vue'
import AppPage from '@/components/layout/page/AppPage.vue'
import AppPageReturnLink from '@/components/layout/page/AppPageReturnLink.vue'
import { useApiErrorToast } from '@/composables/api-error-toast/apiErrorToast.composable'
import { useDocumentTitle } from '@/composables/document-title/documentTitle.composable'
import { useGlobalCustomer } from '@/composables/global-customer/globalCustomer.composable'
import {
  useWizardForm,
  WizardFormLastStepAction,
} from '@/composables/wizard-form/wizardForm.composable'
import { useWizardFormStepState } from '@/composables/wizard-form/wizardFormStepState.composable'
import { AddressUtil } from '@/models/address/address.util'
import type { PackagingRequestDetail } from '@/models/packaging-request/detail/packagingRequestDetail.model'
import type { PackagingRequestCustomerAndLocationForm } from '@/models/packaging-request/update/steps/packagingRequestCustomerAndLocationForm.model'
import { packagingRequestCustomerAndLocationFormSchema } from '@/models/packaging-request/update/steps/packagingRequestCustomerAndLocationForm.model'
import type { PackagingRequestDeliveryForm } from '@/models/packaging-request/update/steps/packagingRequestDeliveryForm.model'
import { packagingRequestDeliveryFormSchema } from '@/models/packaging-request/update/steps/packagingRequestDeliveryForm.model'
import type { PackagingRequestPackagingForm } from '@/models/packaging-request/update/steps/packagingRequestPackagingForm.model'
import { packagingRequestPackagingFormSchema } from '@/models/packaging-request/update/steps/packagingRequestPackagingForm.model'
import { usePackagingRequestSubmitMutation } from '@/modules/packaging-request/api/mutations/packagingRequestSubmit.mutation'
import { usePackagingRequestUpdateMutation } from '@/modules/packaging-request/api/mutations/packagingRequestUpdate.mutation'

const props = defineProps<{
  isReadonly?: boolean
  request: PackagingRequestDetail
}>()

enum PackagingRequestFormSteps {
  CUSTOMER_AND_LOCATION = 'customerAndLocation',
  DELIVERY = 'delivery',
  PACKAGING = 'packaging',
}

const i18n = useI18n()
const globalCustomer = useGlobalCustomer()
const documentTitle = useDocumentTitle()
const apiErrorToast = useApiErrorToast()

documentTitle.set(() => i18n.t('module.packaging_request.update.page_title'))

const packagingRequestUpdateMutation = usePackagingRequestUpdateMutation()
const packagingRequestSubmitMutation = usePackagingRequestSubmitMutation()

const customerAndLocationState = useWizardFormStepState<PackagingRequestCustomerAndLocationForm>({
  customer: props.request.customer ?? globalCustomer.globalCustomer.value ?? null,
  deliveryAddress: props.request.deliveryAddress,
  wasteProducer: props.request.wasteProducer,
})
const packagingState = useWizardFormStepState<PackagingRequestPackagingForm>({
  materials: props.request.materials.map((material) => {
    return {
      contractLineId: material.contractLineId,
      isSales: material.isSales,
      contractItem: material.contractItem,
      contractNumber: material.contractNumber,
      costCenter: material.costCenter,
      materialNumber: material.materialNumber,
      poNumber: material.poNumber,
      quantity: material.quantity,
      wasteMaterial: material.wasteMaterial,
    }
  }),
})
const deliveryState = useWizardFormStepState<PackagingRequestDeliveryForm>({
  contacts: props.request.contacts,
  range: {
    from: props.request.startDate,
    until: props.request.endDate,
  },
  remarks: props.request.remarks,
})

const submitDialog = useVcDialog({ component: () => import('@/modules/packaging-request/features/update/components/steps/submit/PackagingSubmitDialog.vue') })
const confirmDialog = useVcDialog({ component: () => import('@/components/dialog/AppConfirmDialog.vue') })

const wizardForm = useWizardForm({
  categories: [
    {
      id: 'general',
      name: computed<string>(() => i18n.t('module.packaging_request.update.general_info')),
      icon: 'building',
      steps: [
        {
          id: PackagingRequestFormSteps.CUSTOMER_AND_LOCATION,
          isReadonly: props.isReadonly,
          name: computed<string>(() => i18n.t('module.packaging_request.update.customer_and_location.title')),
          scheme: packagingRequestCustomerAndLocationFormSchema,
          showValue: 'customer',
          showValueDescription: computed<string>(() => customerAndLocationState.value.customer?.address == null ? '-' : AddressUtil.format(customerAndLocationState.value.customer?.address)),
          showValueTitle: computed<string>(() => customerAndLocationState.value.customer?.name ?? ''),
          state: customerAndLocationState,
          component: (): Promise<Component> => import('@/modules/packaging-request/features/update/components/steps/customer-and-location/PackagingRequestCustomerAndLocationForm.vue'),
        },
      ],
    },
    {
      id: 'delivery',
      name: computed<string>(() => i18n.t('module.packaging_request.update.delivery_details')),
      icon: 'truck',
      steps: [
        {
          id: PackagingRequestFormSteps.PACKAGING,
          isReadonly: props.isReadonly,
          name: computed<string>(() => i18n.t('module.packaging_request.update.packaging.title')),
          scheme: packagingRequestPackagingFormSchema,
          state: packagingState,
          component: (): Promise<Component> => import('@/modules/packaging-request/features/update/components/steps/packaging/PackagingRequestPackagingForm.vue'),
        },
        {
          id: PackagingRequestFormSteps.DELIVERY,
          isLoading: computed<boolean>(() => packagingRequestSubmitMutation.isLoading.value),
          isReadonly: props.isReadonly,
          name: computed<string>(() => i18n.t('module.packaging_request.update.delivery.title')),
          scheme: packagingRequestDeliveryFormSchema,
          state: deliveryState,
          component: (): Promise<Component> => import('@/modules/packaging-request/features/update/components/steps/delivery/PackagingRequestDeliveryForm.vue'),
        },
      ],
    },
  ],
  lastStepAction: props.isReadonly ? null : WizardFormLastStepAction.SUBMIT,
  onAutoSave: async (): Promise<void> => {
    try {
      if (props.request.uuid == null) {
        return
      }

      await packagingRequestUpdateMutation.execute({
        body: {
          ...customerAndLocationState.value,
          ...packagingState.value,
          ...deliveryState.value,
        },
        params: { packagingRequestUuid: props.request.uuid },
      })
    }
    catch (error) {
      apiErrorToast.show(error)
    }
  },
  onShowSubmitDialog: () => {
    confirmDialog.open({
      title: i18n.t('module.packaging_request.update.submit_confirm_title'),
      isDestructive: false,
      isLoading: packagingRequestSubmitMutation.isLoading,
      cancelText: i18n.t('shared.cancel'),
      confirmText: i18n.t('module.packaging_request.update.submit'),
      description: i18n.t('module.packaging_request.update.submit_confirm_description'),
      onConfirm: async () => {
        try {
          const response = await packagingRequestSubmitMutation.execute({
            body: {
              ...customerAndLocationState.value,
              ...packagingState.value,
              ...deliveryState.value,
            },
            params: { packagingRequestUuid: props.request.uuid },
          })

          confirmDialog.close()

          submitDialog.open({ requestNumber: response.requestNumber })
        }
        catch (error) {
          apiErrorToast.show(error)
        }
      },
    })
  },
})
</script>

<template>
  <AppTeleport to="headerLeft">
    <AppPageReturnLink
      :return-link="{
        label: i18n.t('module.weekly_planning.update.return_to_overview'),
        to: {
          name: 'pickup-request-overview',
        },
      }"
    />
  </AppTeleport>

  <WizardForm :wizard-form="wizardForm">
    <AppPage :title="i18n.t('module.packaging_request.update.page_title')">
      <template #header-actions>
        <WizardFormState
          :created-at="props.request.createdAt"
          :auto-save-error-message="wizardForm.isAutoSaving.value ? null : wizardForm.autoSaveErrorMessage.value"
          :is-auto-saving="wizardForm.isAutoSaving.value"
        />
      </template>

      <template #left-content>
        <WizardFormSidebar />
      </template>

      <template #default>
        <WizardFormContent />
      </template>
    </AppPage>
  </WizardForm>
</template>
