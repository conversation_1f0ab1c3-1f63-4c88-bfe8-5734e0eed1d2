<script setup lang="ts">
import {
  computed,
  ref,
  watch,
} from 'vue'
import { useI18n } from 'vue-i18n'

import { RequestType } from '@/client/types.gen'
import AppCustomerAutocomplete from '@/components/app/autocomplete/AppCustomerAutocomplete.vue'
import AppPickUpAddressAutocomplete from '@/components/app/autocomplete/AppPickUpAddressAutocomplete.vue'
import AppWasteProducerAutocomplete from '@/components/app/autocomplete/AppWasteProducerAutocomplete.vue'
import FormLayout from '@/components/form/FormLayout.vue'
import WizardFormSingleChoiceSection from '@/components/form/wizard/components/WizardFormSingleChoiceSection.vue'
import WizardFormStep from '@/components/form/wizard/WizardFormStep.vue'
import { useGlobalCustomer } from '@/composables/global-customer/globalCustomer.composable'
import type { WizardForm } from '@/composables/wizard-form/wizardForm.composable'
import { useWizardFormStep } from '@/composables/wizard-form/wizardFormStep.composable'
import { AddressUtil } from '@/models/address/address.util'
import type { CustomerIndex } from '@/models/customer/index/customerIndex.model'
import type { packagingRequestCustomerAndLocationFormSchema } from '@/models/packaging-request/update/steps/packagingRequestCustomerAndLocationForm.model'
import type { PickUpAddressIndex } from '@/models/pick-up-address/index/pickUpAddressIndex.model'
import type { WasteProducerIndex } from '@/models/waste-producer/index/wasteProducerIndex.model'
import { useSuggestedCustomerIndexQuery } from '@/modules/waste-inquiry/api/queries/suggestedCustomerIndex.query'
import { useSuggestedPickUpAddressIndexQuery } from '@/modules/waste-inquiry/api/queries/suggestedPickUpAddressIndex.query'
import { useSuggestedWasteProducerIndexQuery } from '@/modules/waste-inquiry/api/queries/suggestedWasteProducerIndex.query'

const props = defineProps<{
  isReadonly?: boolean
  wizardForm: WizardForm<typeof packagingRequestCustomerAndLocationFormSchema>
}>()

const i18n = useI18n()
const wizardFormStep = useWizardFormStep(props.wizardForm)
const globalCustomer = useGlobalCustomer()

const customer = wizardFormStep.form.register('customer')
const customerAutocompleteValue = ref<CustomerIndex | null>(null)
const suggestedCustomerIndexQuery = useSuggestedCustomerIndexQuery(RequestType.PICK_UP)
const selectedCustomer = computed<CustomerIndex | null>(() => customer.value.value)

const wasteProducer = wizardFormStep.form.register('wasteProducer')
const wasteProducerAutocompleteValue = ref<WasteProducerIndex | null>(null)
const suggestedWasteProducerIndexQuery = useSuggestedWasteProducerIndexQuery(
  computed<string | null>(() => selectedCustomer.value?.id ?? null),
)

const deliveryAddress = wizardFormStep.form.register('deliveryAddress')
const pickUpAddressAutocompleteValue = ref<PickUpAddressIndex | null>(null)
const suggestedPickUpAddressIndexQuery = useSuggestedPickUpAddressIndexQuery(
  computed<string | null>(() => selectedCustomer.value?.id ?? null),
  RequestType.PICK_UP,
)

watch(selectedCustomer, () => {
  wasteProducer.setValue(null)
  deliveryAddress.setValue(null)

  pickUpAddressAutocompleteValue.value = null
})
</script>

<template>
  <WizardFormStep :wizard-form-step="wizardFormStep">
    <FormLayout>
      <WizardFormSingleChoiceSection
        id="customer"
        :is-visible="true"
        :autocomplete-value="customerAutocompleteValue"
        :field="customer"
        :suggested-resources="globalCustomer.globalCustomer.value === null
          ? suggestedCustomerIndexQuery.data.value ?? []
          : []"
        :is-loading-suggested-resources="suggestedCustomerIndexQuery.isFetching.value"
        :title="i18n.t('module.packaging_request.update.customer_and_location.customer.title')"
        :card-title="(customer) => customer.name"
        :card-description="(customer) => customer.address === null ? '-' : AddressUtil.format(customer.address)"
        :is-required="true"
        :is-readonly="props.isReadonly || globalCustomer.globalCustomer.value !== null"
        card-icon="building"
      >
        <template #autocomplete>
          <AppCustomerAutocomplete v-model="customerAutocompleteValue" />
        </template>
      </WizardFormSingleChoiceSection>

      <WizardFormSingleChoiceSection
        id="wasteProducer"
        :is-visible="selectedCustomer !== null"
        :autocomplete-value="wasteProducerAutocompleteValue"
        :field="wasteProducer"
        :is-readonly="props.isReadonly"
        :suggested-resources="suggestedWasteProducerIndexQuery.data.value ?? []"
        :is-loading-suggested-resources="suggestedWasteProducerIndexQuery.isFetching.value"
        :title="i18n.t('module.packaging_request.update.customer_and_location.waste_producer.title')"
        :card-description="(wasteProducer) => wasteProducer.address === null ? '-' : AddressUtil.format(wasteProducer.address)"
        :card-title="(wasteProducer) => wasteProducer.name"
        :is-required="true"
        card-icon="locationPin"
      >
        <template #autocomplete>
          <AppWasteProducerAutocomplete
            v-model="wasteProducerAutocompleteValue"
            :customer-id="selectedCustomer!.id"
          />
        </template>
      </WizardFormSingleChoiceSection>

      <WizardFormSingleChoiceSection
        id="deliveryAddress"
        :is-readonly="props.isReadonly"
        :is-visible="selectedCustomer !== null"
        :autocomplete-value="pickUpAddressAutocompleteValue"
        :field="deliveryAddress"
        :suggested-resources="suggestedPickUpAddressIndexQuery.data.value ?? []"
        :is-loading-suggested-resources="suggestedPickUpAddressIndexQuery.isFetching.value"
        :title="i18n.t('module.packaging_request.update.customer_and_location.delivery.title')"
        :card-description="(pickUpAddress) => pickUpAddress.address === null ? '-' : AddressUtil.format(pickUpAddress.address)"
        :card-title="(pickUpAddress) => pickUpAddress.name"
        :is-required="true"
        card-icon="locationPin"
      >
        <template #autocomplete>
          <AppPickUpAddressAutocomplete
            v-model="pickUpAddressAutocompleteValue"
            :customer-id="selectedCustomer!.id"
          />
        </template>
      </WizardFormSingleChoiceSection>
    </FormLayout>
  </WizardFormStep>
</template>
