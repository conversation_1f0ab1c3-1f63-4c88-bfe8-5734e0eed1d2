<script setup lang="ts">
import type {
  PaginatedData,
  Pagination,
  TableColumn,
} from '@wisemen/vue-core-components'
import type { VNode } from 'vue'
import {
  computed,
  h,
} from 'vue'
import { useI18n } from 'vue-i18n'

import AppTable from '@/components/app/table/AppTable.vue'
import type { ContractLinePackagingRequestIndexPagination } from '@/models/contract-line/index/contractLineIndexPagination.model'
import type { ContractLinePackagingRequestIndex } from '@/models/contract-line/packaging-request/contractLinePackagingRequestIndex.model'
import PackagingRequestContractLineTableCostCenterCell from '@/modules/packaging-request/features/update/components/steps/packaging/PackagingRequestContractLineTableCostCenterCell.vue'
import PackagingRequestContractLineTableCountCell from '@/modules/packaging-request/features/update/components/steps/packaging/PackagingRequestContractLineTableCountCell.vue'
import PackagingRequestContractLineTableImageCell from '@/modules/packaging-request/features/update/components/steps/packaging/PackagingRequestContractLineTableImageCell.vue'
import PackagingRequestContractLineTablePoNumberCell from '@/modules/packaging-request/features/update/components/steps/packaging/PackagingRequestContractLineTablePoNumberCell.vue'
import { usePackagingRequestContractLineTableDefaultColumns } from '@/modules/pickup-request/features/update/composables/contractLineTableColumns.composable'

const props = defineProps<{
  isFetching: boolean
  isLoading: boolean
  isReadonly?: boolean
  data: PaginatedData<ContractLinePackagingRequestIndex> | null
  pagination: Pagination<ContractLinePackagingRequestIndexPagination>
  selectedItems?: ContractLinePackagingRequestIndex[]
  selectedItemsAmount?: number
  showImage?: boolean
  onTableNextPage?: () => Promise<void>
}>()

const emit = defineEmits<{
  tableNextPage: Promise<void>
  updateAmount: [item: ContractLinePackagingRequestIndex, value: number]
  updateCostCenter: [item: ContractLinePackagingRequestIndex, value: string | null]
  updatePoNumber: [item: ContractLinePackagingRequestIndex, value: string | null]
}>()

const i18n = useI18n()

const contractLineTableColumns = usePackagingRequestContractLineTableDefaultColumns()

const columns = computed<TableColumn<ContractLinePackagingRequestIndex>[]>(() => {
  const cols = contractLineTableColumns

  const firstColumns = [
    {
      cell: (row: ContractLinePackagingRequestIndex): VNode => {
        const amountInSelectedItems = props.selectedItems?.filter((item) =>
          item.contractLineId === row.contractLineId)[0]?.amount ?? 0

        return h(
          PackagingRequestContractLineTableCountCell,
          {
            isCountDisabled: props.isReadonly ?? false,
            amount: row.amount ?? amountInSelectedItems,
            total: props.selectedItemsAmount ?? 0,
            onUpdate: (value: number) => onUpdateAmount(row, value),
          },
        )
      },
      headerLabel: '',
      key: 'amount',
      width: '8rem',
    },
  ]

  if (props.showImage) {
    firstColumns.push(
      {
        cell: (row): VNode => {
          return h(
            PackagingRequestContractLineTableImageCell,
            { item: row },
          )
        },
        headerLabel: '',
        key: 'image',
        width: '5rem',
      },
    )
  }

  cols.unshift(...firstColumns)

  cols.splice(
    4,
    0,
    {
      cell: (row: ContractLinePackagingRequestIndex): VNode => {
        const amountInSelectedItems = props.selectedItems?.filter((item) =>
          item.contractLineId === row.contractLineId)[0]?.amount ?? 0
        const poNumber = props.selectedItems?.filter((item) =>
          item.contractLineId === row.contractLineId)[0]?.poNumber ?? null

        return h(
          PackagingRequestContractLineTablePoNumberCell,
          {
            'isDisabled': amountInSelectedItems === 0 || props.isReadonly,
            'modelValue': poNumber ?? null,
            'onUpdate:modelValue': (value: string | null) => onUpdatePoNumber(row, value),
          },
        )
      },
      headerLabel: i18n.t('module.packaging_request.update.packaging.table.po_number'),
      key: 'poNumber',
      width: '10rem',
    },
    {
      cell: (row: ContractLinePackagingRequestIndex): VNode => {
        const amountInSelectedItems = props.selectedItems?.filter((item) =>
          item.contractLineId === row.contractLineId)[0]?.amount ?? 0
        const costCenter = props.selectedItems?.filter((item) =>
          item.contractLineId === row.contractLineId)[0]?.costCenter ?? null

        return h(
          PackagingRequestContractLineTableCostCenterCell,
          {
            'isDisabled': amountInSelectedItems === 0 || props.isReadonly,
            'modelValue': costCenter ?? null,
            'onUpdate:modelValue': (value: string | null) => onUpdateCostCenter(row, value),
          },
        )
      },
      headerLabel: i18n.t('module.packaging_request.update.packaging.table.cost_center'),
      key: 'costCenter',
      width: '10rem',
    },
  )

  return cols
})

async function onNext(): Promise<void> {
  if (props.onTableNextPage === undefined) {
    return
  }

  await props.onTableNextPage()
}

function onUpdateAmount(item: ContractLinePackagingRequestIndex, amount: number): void {
  emit('updateAmount', item, amount)
}

function onUpdatePoNumber(item: ContractLinePackagingRequestIndex, poNumber: string | null): void {
  emit('updatePoNumber', item, poNumber)
}

function onUpdateCostCenter(item: ContractLinePackagingRequestIndex, costCenter: string | null): void {
  emit('updateCostCenter', item, costCenter)
}
</script>

<template>
  <AppTable
    :columns="columns"
    :data="props.data"
    :is-loading="props.isLoading"
    :pagination="props.pagination"
    :row-class="() => 'group-hover:bg-primary-hover/25'"
    :infinite-scroll="props.onTableNextPage !== undefined ? {
      distance: 300,
      onNext,
    } : undefined"
    :is-first-column-sticky="true"
  />
</template>
