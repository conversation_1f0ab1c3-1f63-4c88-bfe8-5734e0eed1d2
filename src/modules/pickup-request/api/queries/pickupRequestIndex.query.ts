import { useOffsetInfiniteQuery } from '@/composables/offset-infinite-query/offsetInfiniteQuery.composable.ts'
import type { PickupRequestIndexQueryParams } from '@/models/pickup-request/index/pickupRequestIndexQueryParams.model'
import { PickupRequestService } from '@/modules/pickup-request/api/services/pickupRequest.service'
import type { InfiniteQueryOptions } from '@/types/query.type'

// eslint-disable-next-line ts/explicit-function-return-type
export function usePickupRequestIndexQuery(options: InfiniteQueryOptions<PickupRequestIndexQueryParams>) {
  return useOffsetInfiniteQuery({
    queryFn: (pagination) => {
      return PickupRequestService.getAll({
        filters: options.params.filters.value,
        pagination,
        sort: options.params.sort.value,
      })
    },
    queryKey: { pickupRequestIndex: { queryParams: options.params } },
  })
}
