import {
  bulkDeletePickUpRequestV1,
  copyPickUpRequestSapV1,
  createPickUpRequestV1,
  downloadDocumentSubmittedPickUpRequestV1,
  getIsPoNumberAndCostCenterRequiredV1,
  submitPickUpRequestSapV1,
  submitPickUpRequestV1,
  updatePickUpRequestSapV1,
  updatePickUpRequestV1,
  viewPickUpRequestIndexV1,
  viewPickUpRequestSapV1,
  viewPickUpRequestV1,
} from '@/client'
import type { FileDownload } from '@/models/file/fileDownload.model'
import { PickupRequestCreateTransformer } from '@/models/pickup-request/create/pickupRequestCreate.transformer'
import type {
  PickupRequestDetail,
  PickupRequestDetailPoNumberAndCostCenterRequired,
} from '@/models/pickup-request/detail/pickupRequestDetail.model'
import {
  PickupRequestDetailPoNumberAndCostCenterRequiredTransformer,
  PickupRequestDetailTransformer,
} from '@/models/pickup-request/detail/pickupRequestDetail.transformer'
import type { PickupRequestIndex } from '@/models/pickup-request/index/pickupRequestIndex.model'
import { PickupRequestIndexTransformer } from '@/models/pickup-request/index/pickupRequestIndex.transformer'
import type { PickupRequestIndexQueryParams } from '@/models/pickup-request/index/pickupRequestIndexQueryParams.model'
import { PickupRequestIndexQueryParamsTransformer } from '@/models/pickup-request/index/pickupRequestIndexQueryParams.transformer'
import type { PickupRequestUuid } from '@/models/pickup-request/pickupRequestUuid.model'
import type { PickupRequestSapDetail } from '@/models/pickup-request/sap-detail/pickupRequestSapDetail.model'
import { PickupRequestSapDetailTransformer } from '@/models/pickup-request/sap-detail/pickupRequestSapDetail.transformer'
import {
  PickupRequestSapSubmitFormTransformer,
  PickupRequestSapUpdateTransformer,
} from '@/models/pickup-request/update/pickupRequestSapUpdate.transformer'
import {
  PickupRequestSubmitFormTransformer,
  PickupRequestSubmitResponseTransformer,
  PickupRequestUpdateTransformer,
} from '@/models/pickup-request/update/pickupRequestUpdate.transformer'
import type { PickupRequestUpdateForm } from '@/models/pickup-request/update/pickupRequestUpdateForm.model'
import type { PickupRequestStartDateForm } from '@/models/pickup-request/update/steps/pickupRequestStartDateForm.model'
import type { PickupRequestSubmitRequestForm } from '@/models/pickup-request/update/submit/pickupRequestSubmitForm.model'
import type { PickupRequestSubmitResponse } from '@/models/pickup-request/update/submit/pickupRequestSubmitResponse.model'
import type {
  OffsetPagination,
  OffsetPaginationResponse,
} from '@/types/pagination.type'

export class PickupRequestService {
  static async checkPoNumberAndCostCenterRequired(customerId: string):
  Promise<PickupRequestDetailPoNumberAndCostCenterRequired> {
    const response = await getIsPoNumberAndCostCenterRequiredV1({ query: { filter: { customerId } } })

    return PickupRequestDetailPoNumberAndCostCenterRequiredTransformer.fromDto(response.data)
  }

  static async copy(requestNumber: string): Promise<PickupRequestUuid> {
    const response = await copyPickUpRequestSapV1({ path: { requestNumber } })

    return response.data.uuid as PickupRequestUuid
  }

  static async create(): Promise<PickupRequestUuid> {
    const response = await createPickUpRequestV1({ body: {} })

    return PickupRequestCreateTransformer.fromDto(response.data)
  }

  static async delete(pickupRequestUuid: PickupRequestUuid): Promise<void> {
    await bulkDeletePickUpRequestV1({
      body: {
        pickUpRequestUuids: [
          pickupRequestUuid,
        ],
      },
    })
  }

  static async deleteInBulk(uuids: PickupRequestUuid[]): Promise<void> {
    await bulkDeletePickUpRequestV1({ body: { pickUpRequestUuids: uuids } })
  }

  static async downloadFile(requestNumber: string, fileName: string): Promise<FileDownload> {
    const response = await downloadDocumentSubmittedPickUpRequestV1({
      body: { fileName },
      path: { requestNumber },
    })
    const disposition = response.response.headers.get('Content-Disposition')

    return {
      blob: response.data as Blob,
      disposition,
    }
  }

  static async getAll(
    options: OffsetPagination<PickupRequestIndexQueryParams>,
  ): Promise<OffsetPaginationResponse<PickupRequestIndex>> {
    const response = await viewPickUpRequestIndexV1({ query: PickupRequestIndexQueryParamsTransformer.toDto(options) })

    return {
      data: response.data.items.map(PickupRequestIndexTransformer.fromDto),
      meta: {
        limit: options.pagination.limit,
        offset: options.pagination.offset,
        total: response.data.meta.total,
      },
    }
  }

  static async getByUuid(pickupRequestUuid: PickupRequestUuid): Promise<PickupRequestDetail> {
    const response = await viewPickUpRequestV1({ path: { uuid: pickupRequestUuid } })

    return PickupRequestDetailTransformer.fromDto(response.data)
  }

  static async getSapDetailByRequestNumber(requestNumber: string): Promise<PickupRequestSapDetail> {
    const response = await viewPickUpRequestSapV1({ path: { requestNumber } })

    return PickupRequestSapDetailTransformer.fromDto(response.data)
  }

  static async submit(
    pickupRequestUuid: PickupRequestUuid,
    form: PickupRequestSubmitRequestForm,
  ): Promise<PickupRequestSubmitResponse> {
    await updatePickUpRequestV1({
      body: PickupRequestSubmitFormTransformer.toDto(form),
      path: { uuid: pickupRequestUuid },
    })

    const response = await submitPickUpRequestV1({ path: { uuid: pickupRequestUuid } })

    return PickupRequestSubmitResponseTransformer.fromDto(response.data)
  }

  static async submitSapRequest(
    requestNumber: string,
    form: PickupRequestSubmitRequestForm,
  ): Promise<void> {
    await updatePickUpRequestSapV1({
      body: PickupRequestSapSubmitFormTransformer.toDto(form),
      path: { requestNumber },
    })

    await submitPickUpRequestSapV1({ path: { requestNumber } })
  }

  static async update(
    pickupRequestUuid: PickupRequestUuid,
    form: Partial<PickupRequestUpdateForm>,
    isWeeklyPlanning?: boolean,
  ): Promise<{ needsWicConfirmation: boolean }> {
    let body

    if (isWeeklyPlanning) {
      body = PickupRequestUpdateTransformer.toWeeklyPlanningDto(form)
    }
    else {
      body = PickupRequestUpdateTransformer.toDto(form)
    }

    const response = await updatePickUpRequestV1({
      body,
      path: { uuid: pickupRequestUuid },
    })

    return { needsWicConfirmation: response.data.needsWicConfirmation }
  }

  static async updateSapRequest(
    requestNumber: string,
    form: Partial<PickupRequestUpdateForm>,
  ): Promise<void> {
    await updatePickUpRequestSapV1({
      body: PickupRequestSapUpdateTransformer.toDto(form),
      path: { requestNumber },
    })
  }

  static async updateStartDates(
    pickupRequestUuid: PickupRequestUuid,
    form: PickupRequestStartDateForm,
  ): Promise<void> {
    await updatePickUpRequestV1({
      body: PickupRequestUpdateTransformer.mapStartDates(form),
      path: { uuid: pickupRequestUuid },
    })
  }
}
