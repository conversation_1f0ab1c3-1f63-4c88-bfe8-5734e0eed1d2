import type { UseMutationReturnType } from '@wisemen/vue-core-query'
import { useMutation } from '@wisemen/vue-core-query'

import type { PickupRequestSubmitRequestForm } from '@/models/pickup-request/update/submit/pickupRequestSubmitForm.model'
import { PickupRequestService } from '@/modules/pickup-request/api/services/pickupRequest.service'

interface Params {
  requestNumber: string
}

export function usePickupRequestSapSubmitMutation():
UseMutationReturnType<PickupRequestSubmitRequestForm, void, Params> {
  return useMutation<PickupRequestSubmitRequestForm, void, Params>({
    queryFn: async ({
      body, params,
    }) => {
      return await PickupRequestService.submitSapRequest(params.requestNumber, body)
    },
    queryKeysToInvalidate: {},
  })
}
