import type { UseMutationReturnType } from '@wisemen/vue-core-query'
import { useMutation } from '@wisemen/vue-core-query'

import type { FileDownload } from '@/models/file/fileDownload.model'
import { PickupRequestService } from '@/modules/pickup-request/api/services/pickupRequest.service'

interface Params {
  fileName: string
  requestNumber: string
}

export function usePickupRequestDownloadFileMutation(): UseMutationReturnType<Params, FileDownload> {
  return useMutation({
    queryFn: async ({ body }) => {
      return await PickupRequestService.downloadFile(body.requestNumber, body.fileName)
    },
    queryKeysToInvalidate: {},
  })
}
