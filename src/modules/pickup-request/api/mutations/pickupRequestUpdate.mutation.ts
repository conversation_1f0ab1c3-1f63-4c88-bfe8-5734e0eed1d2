import type { UseMutationReturnType } from '@wisemen/vue-core-query'
import { useMutation } from '@wisemen/vue-core-query'

import type { PickupRequestUuid } from '@/models/pickup-request/pickupRequestUuid.model'
import type { PickupRequestUpdateForm } from '@/models/pickup-request/update/pickupRequestUpdateForm.model'
import { PickupRequestService } from '@/modules/pickup-request/api/services/pickupRequest.service'

interface Params {
  pickupRequestUuid: PickupRequestUuid
  isWeeklyPlanning?: boolean
}

export function usePickupRequestUpdateMutation():
UseMutationReturnType<Partial<PickupRequestUpdateForm>, { needsWicConfirmation: boolean }, Params> {
  return useMutation<Partial<PickupRequestUpdateForm>, { needsWicConfirmation: boolean }, Params>({
    queryFn: async ({
      body, params,
    }) => {
      return await PickupRequestService.update(params.pickupRequestUuid, body, params.isWeeklyPlanning)
    },
    queryKeysToInvalidate: {},
  })
}
