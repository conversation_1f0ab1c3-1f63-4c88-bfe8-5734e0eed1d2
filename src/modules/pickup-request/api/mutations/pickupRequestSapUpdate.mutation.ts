import type { UseMutationReturnType } from '@wisemen/vue-core-query'
import { useMutation } from '@wisemen/vue-core-query'

import type { PickupRequestUpdateForm } from '@/models/pickup-request/update/pickupRequestUpdateForm.model'
import { PickupRequestService } from '@/modules/pickup-request/api/services/pickupRequest.service'

interface Params {
  requestNumber: string
}

export function usePickupRequestSapUpdateMutation():
UseMutationReturnType<Partial<PickupRequestUpdateForm>, void, Params> {
  return useMutation<Partial<PickupRequestUpdateForm>, void, Params>({
    queryFn: async ({
      body, params,
    }) => {
      return await PickupRequestService.updateSapRequest(params.requestNumber, body)
    },
    queryKeysToInvalidate: {},
  })
}
