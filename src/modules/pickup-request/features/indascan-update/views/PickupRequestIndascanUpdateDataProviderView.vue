<script setup lang="ts">
import { computed } from 'vue'

import AppDataProviderView from '@/components/app/AppDataProviderView.vue'
import { usePickupRequestSapDetailQuery } from '@/modules/pickup-request/api/queries/pickupRequestSapDetail.query'
import PickupRequestUpdateView from '@/modules/pickup-request/features/update/views/PickupRequestUpdateView.vue'

const props = defineProps<{
  requestNumber: string
}>()

const pickupSapDetailQuery = usePickupRequestSapDetailQuery(computed<string>(() => props.requestNumber))
</script>

<template>
  <AppDataProviderView
    :queries="{
      pickup: pickupSapDetailQuery,
    }"
  >
    <template #default="{ data }">
      <PickupRequestUpdateView
        :pickup="data.pickup"
        :is-indascan-draft="true"
      />
    </template>
  </AppDataProviderView>
</template>
