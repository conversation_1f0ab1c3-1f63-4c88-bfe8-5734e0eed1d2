<script setup lang="ts">
import {
  VcDropdownMenuGroup,
  VcDropdownMenuItem,
  VcDropdownMenuSubMenu,
} from '@wisemen/vue-core-components'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'

import type { PickupRequestTemplateIndex } from '@/models/pickup-request-template/index/pickupRequestTemplateIndex.model'
import { usePickupRequestTemplateCreatePickupRequestMutation } from '@/modules/pickup-request-template/api/mutations/pickupRequestTemplateCreatePickupRequest.mutation'
import { usePickupRequestTemplateIndexQuery } from '@/modules/pickup-request-template/api/queries/pickupRequestTemplateIndex.query'

const i18n = useI18n()
const router = useRouter()
const pickupRequestTemplateIndexQuery = usePickupRequestTemplateIndexQuery()
const pickupRequestTemplateCreatePickupRequestMutation = usePickupRequestTemplateCreatePickupRequestMutation()

async function onNewPickupFromTemplate(template: PickupRequestTemplateIndex): Promise<void> {
  // eslint-disable-next-line vue/max-len
  const pickupRequestUuid = await pickupRequestTemplateCreatePickupRequestMutation.execute({ body: { pickupRequestTemplateUuid: template.uuid } })

  await router.push({
    name: 'pickup-request-update',
    params: { pickupRequestUuid },
  })
}
</script>

<template>
  <VcDropdownMenuSubMenu
    :label="i18n.t('module.pickup_request.overview.new_pickup_from_template')"
    icon="plus"
  >
    <VcDropdownMenuGroup>
      <VcDropdownMenuItem
        v-for="template of pickupRequestTemplateIndexQuery.data.value.data"
        :key="template.uuid"
        :label="template.name"
        @select="onNewPickupFromTemplate(template)"
      />
    </VcDropdownMenuGroup>
  </VcDropdownMenuSubMenu>
</template>
