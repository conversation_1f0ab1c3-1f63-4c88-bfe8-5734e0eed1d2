<script setup lang="ts">
import {
  VcCheckboxControl,
  VcCheckboxRoot,
} from '@wisemen/vue-core-components'
import {
  ref,
  watch,
} from 'vue'

import DataTableCell from '@/components/table/data-table/DataTableCell.vue'
import type { PickupRequestUuid } from '@/models/pickup-request/pickupRequestUuid.model'

const props = defineProps<{
  rowUuid: PickupRequestUuid
  selectedItems: PickupRequestUuid[]
}>()

const emit = defineEmits<{
  update: [boolean]
}>()

const value = ref<boolean>(props.selectedItems.includes(props.rowUuid))

watch(() => props.selectedItems, (newSelectedItems) => {
  value.value = newSelectedItems.includes(props.rowUuid)
})
</script>

<template>
  <DataTableCell>
    <VcCheckboxRoot
      v-model="value"
      @update:model-value="(value) => emit('update', value)"
    >
      <VcCheckboxControl />
    </VcCheckboxRoot>
  </DataTableCell>
</template>
