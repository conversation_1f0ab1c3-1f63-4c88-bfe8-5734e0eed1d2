<script setup lang="ts">
import type { VcBadgeProps } from '@wisemen/vue-core-components'
import { VcBadge } from '@wisemen/vue-core-components'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import { PickUpRequestStatus } from '@/client'
import DataTableCell from '@/components/table/data-table/DataTableCell.vue'
import { PickupRequestStatusEnumUtil } from '@/models/enums/pickupRequestStatus.enum'

const props = defineProps<{
  status: PickUpRequestStatus
}>()

const i18n = useI18n()

const statusColor = computed<VcBadgeProps['color']>(() => {
  switch (props.status) {
    case PickUpRequestStatus.DRAFT:
    case PickUpRequestStatus.INDASCAN_DRAFT:
      return 'light-blue'
    case PickUpRequestStatus.PENDING:
      return 'purple'
    case PickUpRequestStatus.CONFIRMED:
      return 'success'
    case PickUpRequestStatus.CANCELLED:
      return 'gray'
    default:
      return 'gray'
  }
})
</script>

<template>
  <DataTableCell>
    <VcBadge
      :color="statusColor"
      variant="translucent"
      size="sm"
    >
      {{ i18n.t(PickupRequestStatusEnumUtil.getI18nKey(props.status)) }}
    </VcBadge>
  </DataTableCell>
</template>
