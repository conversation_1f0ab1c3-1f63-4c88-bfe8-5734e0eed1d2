<script setup lang="ts">
import { VcIconButton } from '@wisemen/vue-core-components'
import { useI18n } from 'vue-i18n'

import AppGroup from '@/components/app/AppGroup.vue'
import DataTableCell from '@/components/table/data-table/DataTableCell.vue'

const props = defineProps<{
  canCopy?: boolean
  hideCopyAction?: boolean
  hideEditAction?: boolean
}>()

const emit = defineEmits<{
  copy: []
  edit: []
}>()

const i18n = useI18n()

function onEdit(): void {
  emit('edit')
}

function onCopy(): void {
  emit('copy')
}
</script>

<template>
  <DataTableCell :has-interactive-content="true">
    <AppGroup gap="xs">
      <VcIconButton
        v-if="!props.hideCopyAction"
        :label="i18n.t('shared.copy')"
        :class-config="{
          icon: props.canCopy ? 'text-fg-brand-primary' : 'text-fg-disabled',
        }"
        :is-disabled="!props.canCopy"
        variant="tertiary"
        size="sm"
        icon="copy"
        @click="onCopy"
      />

      <VcIconButton
        v-if="!props.hideEditAction"
        :label="i18n.t('shared.edit')"
        :class-config="{
          icon: 'text-fg-brand-primary',
        }"
        variant="tertiary"
        size="sm"
        icon="chevronRight"
        @click="onEdit"
      />
    </AppGroup>
  </DataTableCell>
</template>
