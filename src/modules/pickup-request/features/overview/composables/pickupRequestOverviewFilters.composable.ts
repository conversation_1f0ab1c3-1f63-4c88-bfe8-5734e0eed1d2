import { ref } from 'vue'
import { useI18n } from 'vue-i18n'

import {
  PickUpRequestStatus,
  PickUpTransportMode,
} from '@/client'
import {
  createAutocompleteFilter,
  createDateRangeFilter,
  createMultiSelectFilter,
  createSelectFilter,
  useFilters,
} from '@/components/filters'
import { useCustomBooleanFilter } from '@/composables/custom-boolean-filter/customBooleanFilter.composable'
import { useGlobalCustomer } from '@/composables/global-customer/globalCustomer.composable'
import { useSearch } from '@/composables/search/search.composable'
import { PickupRequestStatusEnumUtil } from '@/models/enums/pickupRequestStatus.enum'
import { PickupTransportModeEnumUtil } from '@/models/enums/transportMode.enum'
import type { WasteProducerIndexQueryParams } from '@/models/waste-producer/index/wasteProducerIndexQueryParams.model'
import { useCustomerIndexQuery } from '@/modules/waste-inquiry/api/queries/customerIndex.query'
import { useEwcCodeIndexQuery } from '@/modules/waste-inquiry/api/queries/ewcCodeIndex.query'
import { useWasteProducerIndexQuery } from '@/modules/waste-inquiry/api/queries/wasteProducerIndex.query'

// eslint-disable-next-line ts/explicit-function-return-type
export function usePickupRequestOverviewFilters() {
  const i18n = useI18n()
  const globalCustomer = useGlobalCustomer()

  const customerIndexSearch = useSearch({ persistInUrl: false })
  const customerIndexQuery = useCustomerIndexQuery({ params: { search: customerIndexSearch.debouncedSearch } })

  const wasteProducerSearch = ref<string>('')
  const wasteProducerIndexFilters = ref<WasteProducerIndexQueryParams['filters']>({})
  const wasteProducerIndexQuery = useWasteProducerIndexQuery({
    params: {
      filters: wasteProducerIndexFilters,
      search: wasteProducerSearch,
    },
  })

  const ewcCodeIndexQuery = useEwcCodeIndexQuery()

  const isTransportByIndaverFilter = useCustomBooleanFilter(
    'isTransportByIndaver',
    i18n.t('module.pickup_request.overview.filter.transport_by_indaver'),
  )

  const isHazardousFilter = useCustomBooleanFilter(
    'isHazardous',
    i18n.t('module.pickup_request.overview.filter.hazardous'),
  )

  const filters = useFilters({
    filterGroups: () => [
      {
        filters: [
          createAutocompleteFilter({
            isHidden: globalCustomer.globalCustomer.value !== null,
            isLoading: customerIndexQuery.isLoading.value,
            defaultValue: null,
            displayFn: (option) => option.name,
            key: 'customerId',
            label: i18n.t('enum.dynamic_table_column_name.customer_name'),
            options: customerIndexQuery.data.value?.data.map((customer) => ({
              id: customer.id,
              name: customer.name,
            })) ?? [],
            onSearch: (value) => {
              customerIndexSearch.updateSearch(value)
            },
          }),
          createAutocompleteFilter({
            isLoading: wasteProducerIndexQuery.isLoading.value,
            defaultValue: null,
            displayFn: (option) => option.name,
            key: 'wasteProducerId',
            label: i18n.t('enum.dynamic_table_column_name.waste_producer'),
            options: wasteProducerIndexQuery.data.value?.data.map((wp) => ({
              id: wp.id,
              name: wp.name,
            })) ?? [],
            onSearch: (value) => {
              wasteProducerSearch.value = value
            },
          }),
          createSelectFilter({
            defaultValue: null,
            displayFn: (value) => value.label,
            key: 'ewc',
            label: i18n.t('enum.dynamic_table_column_name.ewc_code'),
            options: ewcCodeIndexQuery.data.value?.map((ewcCode) => ({
              label: ewcCode.code,
              value: ewcCode.code,
            })) ?? [],
          }),
          createMultiSelectFilter({
            defaultValue: [],
            displayFn: (value) => i18n.t(PickupRequestStatusEnumUtil.getI18nKey(value)),
            key: 'statuses',
            label: i18n.t('module.waste_inquiry.overview.status'),
            options: [
              PickUpRequestStatus.CANCELLED,
              PickUpRequestStatus.CONFIRMED,
              PickUpRequestStatus.PENDING,
            ],
          }),
          createSelectFilter({
            defaultValue: null,
            displayFn: (value) => i18n.t(PickupTransportModeEnumUtil.getLabelI18nKey(value)),
            key: 'transportMode',
            label: i18n.t('module.pickup_request.overview.filter.transport_mode'),
            options: [
              PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK,
              PickUpTransportMode.BULK_SKIPS_CONTAINER,
              PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS,
              PickUpTransportMode.BULK_ISO_TANK,
            ],
          }),
          createDateRangeFilter({
            defaultValue: {
              from: null,
              until: null,
            },
            key: 'dateOfRequest',
            label: i18n.t('enum.dynamic_table_column_name.date_of_request'),
          }),
          createDateRangeFilter({
            defaultValue: {
              from: null,
              until: null,
            },
            key: 'requestDate',
            label: i18n.t('enum.dynamic_table_column_name.request_date'),
          }),
          createDateRangeFilter({
            defaultValue: {
              from: null,
              until: null,
            },
            key: 'confirmedTransportDate',
            label: i18n.t('enum.dynamic_table_column_name.confirmed_transport_date'),
          }),
        ],
      },
      {
        filters: [
          isTransportByIndaverFilter,
          isHazardousFilter,
        ],
      },
    ],
  })

  return {
    customerIndexQuery,
    filters,
    wasteProducerIndexQuery,
  }
}
