<script setup lang="ts">
import {
  useVcDial<PERSON>,
  useVcToast,
  VcButton,
  VcDropdownMenu,
  VcDropdownMenuGroup,
  VcDropdownMenuItem,
  VcTabs,
  VcTabsItem,
} from '@wisemen/vue-core-components'
import type { VNode } from 'vue'
import {
  computed,
  h,
  ref,
} from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'

import {
  DynamicColumnNames,
  Permission,
  PickUpRequestStatus,
  TransportMode,
} from '@/client'
import AppGroup from '@/components/app/AppGroup.vue'
import AppTableBulkActions from '@/components/app/table/AppTableBulkActions.vue'
import FiltersActive from '@/components/filters/active/FiltersActive.vue'
import FiltersDropdownMenu from '@/components/filters/dropdown-menu/FiltersDropdownMenu.vue'
import FiltersRoot from '@/components/filters/FiltersRoot.vue'
import AppPage from '@/components/layout/page/AppPage.vue'
import DataTable from '@/components/table/data-table/DataTable.vue'
import DynamicTableSettings from '@/components/table/dynamic-table-settings/DynamicTableSettings.vue'
import DynamicTableViews from '@/components/table/dynamic-table-views/DynamicTableViews.vue'
import { useApiErrorToast } from '@/composables/api-error-toast/apiErrorToast.composable'
import { useDocumentTitle } from '@/composables/document-title/documentTitle.composable'
import { useDynamicTableV2 } from '@/composables/dynamic-table/dynamicTableV2.composable'
import { useSearchableTableColumns } from '@/composables/searchable-table-columns/searchableTableColumns.composable.ts'
import { useSort } from '@/composables/sort/sort.composable'
import { useGenericColumn } from '@/composables/table-columns/genericTableColumnsV2.composable'
import { TEST_ID } from '@/constants/testId.constant.ts'
import { DynamicTableName } from '@/models/enums/dynamicTableName.enum'
import { TransportModeEnumUtil } from '@/models/enums/transportMode.enum'
import type { PickupRequestIndex } from '@/models/pickup-request/index/pickupRequestIndex.model'
import { PickupRequestIndexTableTabs } from '@/models/pickup-request/index/pickupRequestIndex.model'
import type { PickupRequestIndexQueryParams } from '@/models/pickup-request/index/pickupRequestIndexQueryParams.model'
import type { PickupRequestUuid } from '@/models/pickup-request/pickupRequestUuid.model'
import { usePackagingRequestCopyMutation } from '@/modules/packaging-request/api/mutations/packagingRequestCopy.mutation'
import { usePackagingRequestCreateMutation } from '@/modules/packaging-request/api/mutations/packagingRequestCreate.mutation'
import { usePickupRequestBulkDeleteMutation } from '@/modules/pickup-request/api/mutations/pickupRequestBulkDelete.mutation'
import { usePickupRequestCopyMutation } from '@/modules/pickup-request/api/mutations/pickupRequestCopy.mutation'
import { usePickupRequestCreateMutation } from '@/modules/pickup-request/api/mutations/pickupRequestCreate.mutation'
import { usePickupRequestIndexQuery } from '@/modules/pickup-request/api/queries/pickupRequestIndex.query'
import PickupRequestOverviewTableActionsCell from '@/modules/pickup-request/features/overview/components/cells/PickupRequestOverviewTableActionsCell.vue'
import PickupRequestOverviewTableCheckboxCell from '@/modules/pickup-request/features/overview/components/cells/PickupRequestOverviewTableCheckboxCell.vue'
import PickupRequestOverviewTableStatusCell from '@/modules/pickup-request/features/overview/components/cells/PickupRequestOverviewTableStatusCell.vue'
import PickupRequestOverviewFromTemplateSubMenu from '@/modules/pickup-request/features/overview/components/PickupRequestOverviewFromTemplateSubMenu.vue'
import { usePickupRequestOverviewFilters } from '@/modules/pickup-request/features/overview/composables/pickupRequestOverviewFilters.composable'
import { useWeeklyPlanningCreateMutation } from '@/modules/weekly-planning/api/mutations/weeklyPlanningCreate.mutation'
import { useAuthStore } from '@/stores/auth.store'
import type { DataTableColumn } from '@/types/table.type'

interface Tab {
  id: string
  label: string
  statuses: PickUpRequestStatus[]
}

const i18n = useI18n()
const authStore = useAuthStore()
const router = useRouter()
const apiErrorToast = useApiErrorToast()
const documentTitle = useDocumentTitle()
const toast = useVcToast()

const { filters } = usePickupRequestOverviewFilters()

documentTitle.set(() => i18n.t('module.pickup_request.overview.title'))

const confirmDialog = useVcDialog({ component: () => import('@/components/dialog/AppConfirmDialog.vue') })
const pickupTemplateOverviewDialog = useVcDialog({ component: () => import('@/modules/pickup-request-template/components/PickupRequestTemplateDialog.vue') })

const hasPickupRequestManagePermission = computed<boolean>(() => {
  return authStore.hasPermission(Permission.PICK_UP_REQUEST_MANAGE)
})
const hasWeeklyPlanningManagePermission = computed<boolean>(() => {
  return authStore.hasPermission(Permission.WEEKLY_PLANNING_REQUEST_MANAGE)
})
const hasPackagingRequestManagePermission = computed<boolean>(() => {
  return authStore.hasPermission(Permission.PACKAGING_REQUEST_MANAGE)
})

const itemsSelectedInBulk = ref<PickupRequestUuid[]>([])
const isCreatingPickup = ref<boolean>(false)
const pickupRequestCreateMutation = usePickupRequestCreateMutation()
const isCreatingPlanning = ref<boolean>(false)
const weeklyPlanningCreateMutation = useWeeklyPlanningCreateMutation()
const isCreatingPackagingRequest = ref<boolean>(false)
const packagingRequestCreateMutation = usePackagingRequestCreateMutation()
const pickupRequestCopyMutation = usePickupRequestCopyMutation()
const packagingRequestCopyMutation = usePackagingRequestCopyMutation()

const isDeletingPickupRequests = ref<boolean>(false)
const pickupRequestBulkDeleteMutation = usePickupRequestBulkDeleteMutation()

const sort = useSort({
  keys: [
    'applicationDate',
    'confirmedTransportDate',
    'containerNumber',
    'contractItem',
    'contractNumber',
    'customerReference',
    'disposalCertificateNumber',
    'orderNumber',
    'pickUpAddressId',
    'requestedStartDate',
    'requestNumber',
    'salesOrder',
    'treatmentCenterName',
    'wasteMaterial',
    'wasteProducerId',
    'nameInstallation',
  ],
  persistInUrl: false,
})

const tabs = computed<Tab[]>(() => {
  return [
    {
      id: PickupRequestIndexTableTabs.SUBMITTED,
      label: i18n.t('module.pickup_request.overview.tab.submitted'),
      statuses: [
        PickUpRequestStatus.CANCELLED,
        PickUpRequestStatus.CONFIRMED,
        PickUpRequestStatus.PENDING,
      ],
    },
    {
      id: PickupRequestIndexTableTabs.DRAFTS,
      label: i18n.t('module.pickup_request.overview.tab.drafts'),
      statuses: [
        PickUpRequestStatus.DRAFT,
      ],
    },
    {
      id: PickupRequestIndexTableTabs.INDASCAN_DRAFTS,
      label: i18n.t('module.pickup_request.overview.tab.indascan_drafts'),
      statuses: [
        PickUpRequestStatus.INDASCAN_DRAFT,
      ],
    },
    {
      id: PickupRequestIndexTableTabs.AWAITING_BOOKING,
      label: i18n.t('module.pickup_request.overview.tab.awaiting_booking'),
      statuses: [
        PickUpRequestStatus.PENDING,
      ],
    },
    {
      id: PickupRequestIndexTableTabs.BOOKED,
      label: i18n.t('module.pickup_request.overview.tab.booked'),
      statuses: [
        PickUpRequestStatus.CONFIRMED,
      ],
    },
    {
      id: PickupRequestIndexTableTabs.CANCELLED,
      label: i18n.t('module.pickup_request.overview.tab.cancelled'),
      statuses: [
        PickUpRequestStatus.CANCELLED,
      ],
    },
  ]
})

const activeTab = ref<Tab>(tabs.value[0])

const tabModelValue = computed<string>({
  get: () => activeTab.value.id,
  set: (id: string) => {
    const selectedTab = tabs.value.find((tab) => tab.id === id) ?? null

    if (selectedTab !== null) {
      activeTab.value = selectedTab
    }
  },
})

const searchableColumns = useSearchableTableColumns({
  keys: [
    'requestNumber',
    'contractNumber',
    'contractItem',
    'treatmentCenterName',
    'purchaseOrderNumber',
    'containerNumber',
    'customerReference',
    'wasteMaterial',
    'accountManager',
    'costCenter',
    'salesOrder',
    'nameOfApplicant',
    'orderNumber',
    'deliveryInfo',
    'materialAnalysis',
    'nameInstallation',
    'disposalCertificateNumber',
  ],
})

const {
  isLoading,
  data,
  error,
  fetchNextPage,
} = usePickupRequestIndexQuery({
  params: {
    filters: computed<PickupRequestIndexQueryParams['filters']>(() => ({
      ...searchableColumns.values.value,
      ...(activeTab.value.id === PickupRequestIndexTableTabs.SUBMITTED ? filters.values.value : {}),
      statuses: activeTab.value.id === PickupRequestIndexTableTabs.SUBMITTED
        ? filters.values.value.statuses
        : activeTab.value.statuses,
    }) as PickupRequestIndexQueryParams['filters']),
    sort: sort.values,
  },
})

const dynamicTable = useDynamicTableV2({
  dynamicTableName: DynamicTableName.PICK_UP_REQUEST,
  filters,
  sort,
})

const columns = computed<DataTableColumn<PickupRequestIndex>[]>(() => {
  const testIdByDynamicName: Partial<Record<DynamicColumnNames, string>> = {
    [DynamicColumnNames.ACCOUNT_MANAGER]: TEST_ID.PICKUP.OVERVIEW.TABLE.ACCOUNT_MANAGER,
    [DynamicColumnNames.CONTAINER_NUMBER]: TEST_ID.PICKUP.OVERVIEW.TABLE.CONTAINER_NUMBER,
    [DynamicColumnNames.CONTRACT_ITEM]: TEST_ID.PICKUP.OVERVIEW.TABLE.CONTRACT_ITEM,
    [DynamicColumnNames.CONTRACT_NUMBER]: TEST_ID.PICKUP.OVERVIEW.TABLE.CONTRACT_NUMBER,
    [DynamicColumnNames.COST_CENTER]: TEST_ID.PICKUP.OVERVIEW.TABLE.COST_CENTER,
    [DynamicColumnNames.CUSTOMER_NAME]: TEST_ID.PICKUP.OVERVIEW.TABLE.CUSTOMER_NAME,
    [DynamicColumnNames.DATE_OF_REQUEST]: TEST_ID.PICKUP.OVERVIEW.TABLE.DATE_OF_REQUEST,
    [DynamicColumnNames.DISPOSAL_CERTIFICATE_NUMBER]: TEST_ID.PICKUP.OVERVIEW.TABLE.DISPOSAL_CERTIFICATE_NUMBER,
    [DynamicColumnNames.EWC]: TEST_ID.PICKUP.OVERVIEW.TABLE.EWC,
    [DynamicColumnNames.MATERIAL_ANALYSIS]: TEST_ID.PICKUP.OVERVIEW.TABLE.MATERIAL_ANALYSIS,
    [DynamicColumnNames.NAME_INSTALLATION]: TEST_ID.PICKUP.OVERVIEW.TABLE.NAME_INSTALLATION,
    [DynamicColumnNames.NAME_OF_APPLICANT]: TEST_ID.PICKUP.OVERVIEW.TABLE.NAME_OF_APPLICANT,
    [DynamicColumnNames.ORDER_NUMBER]: TEST_ID.PICKUP.OVERVIEW.TABLE.ORDER_NUMBER,
    [DynamicColumnNames.PICK_UP_ADDRESS_NAME]: TEST_ID.PICKUP.OVERVIEW.TABLE.PICK_UP_ADDRESS_NAME,
    [DynamicColumnNames.REQUEST_NUMBER]: TEST_ID.PICKUP.OVERVIEW.TABLE.REQUEST_NUMBER,
    [DynamicColumnNames.TFS_NUMBER]: TEST_ID.PICKUP.OVERVIEW.TABLE.TFS_NUMBER,
    [DynamicColumnNames.TRANSPORT_MODE]: TEST_ID.PICKUP.OVERVIEW.TABLE.TRANSPORT_MODE,
    [DynamicColumnNames.TREATMENT_CENTER_NAME]: TEST_ID.PICKUP.OVERVIEW.TABLE.TREATMENT_CENTER_NAME,
    [DynamicColumnNames.WASTE_MATERIAL]: TEST_ID.PICKUP.OVERVIEW.TABLE.WASTE_MATERIAL,
    [DynamicColumnNames.WASTE_PRODUCER_NAME]: TEST_ID.PICKUP.OVERVIEW.TABLE.WASTE_PRODUCER_NAME,
  }

  const dynamicTableColumns = dynamicTable.columns.value
    .filter((column) => column.isVisible)
    .filter((column) => {
      // Exclude 'status' column when viewing drafts
      return column.name !== DynamicColumnNames.STATUS || activeTab.value.id !== PickupRequestIndexTableTabs.DRAFTS
    })
    .map((column) => {
      switch (column.name) {
        case DynamicColumnNames.STATUS:
          return {
            testId: TEST_ID.PICKUP.OVERVIEW.TABLE.STATUS_COLUMN,
            cell: (row: PickupRequestIndex): VNode => h(PickupRequestOverviewTableStatusCell, { status: row.status }),
            headerLabel: i18n.t('enum.dynamic_table_column_name.status'),
            key: column.name,
          }
        case DynamicColumnNames.PICK_UP_ADDRESS_ID:
          return useGenericColumn<PickupRequestIndex>(column.name, column.label, (row) => row.pickupAddressId)
        case DynamicColumnNames.PICK_UP_ADDRESS_NAME:
          return {
            ...useGenericColumn<PickupRequestIndex>(column.name, column.label, (row) => row.pickupAddressName),
            testId: testIdByDynamicName[column.name],
          }
        case DynamicColumnNames.TRANSPORT_MODE:
          return {
            ...useGenericColumn<PickupRequestIndex>(column.name, column.label, (value) => {
              if (value.transportMode === null) {
                return null
              }

              return i18n.t(TransportModeEnumUtil.getLabelI18nKey(value.transportMode))
            }),
            testId: testIdByDynamicName[column.name],
          }
        default:
          return {
            ...useGenericColumn(column.name, column.label),
            testId: testIdByDynamicName[column.name],
          }
      }
    })

  return [
    {
      isHidden: activeTab.value.id !== PickupRequestIndexTableTabs.DRAFTS || !hasPickupRequestManagePermission.value,
      cell: (row): VNode => h(PickupRequestOverviewTableCheckboxCell, {
        rowUuid: row.uuid!,
        selectedItems: itemsSelectedInBulk.value,
        onUpdate: (value) => {
          if (row.uuid === null) {
            return
          }
          if (value) {
            itemsSelectedInBulk.value?.push(row.uuid)
          }
          else {
            itemsSelectedInBulk.value = itemsSelectedInBulk.value.filter(
              (uuid) => uuid !== row.uuid,
            ) ?? []
          }
        },
      }),
      key: 'actions',
    },
    ...dynamicTableColumns,
    {
      cell: (row): VNode => h(PickupRequestOverviewTableActionsCell, {
        canCopy: row.status !== PickUpRequestStatus.DRAFT,
        hideCopyAction: activeTab.value.id === PickupRequestIndexTableTabs.INDASCAN_DRAFTS,
        hideEditAction: activeTab.value.id === PickupRequestIndexTableTabs.DRAFTS
          && !hasPickupRequestManagePermission.value,
        onCopy: () => onCopy(row),
        onEdit: () => onEdit(row),
      }),
      key: 'actions',
    },
  ] as DataTableColumn<PickupRequestIndex>[]
})

function redirectToDetailView(pickup: PickupRequestIndex): void {
  if (pickup.transportMode === TransportMode.PACKAGING_REQUEST_ORDER) {
    if (pickup.status === PickUpRequestStatus.DRAFT) {
      if (pickup.uuid === null) {
        return
      }

      router.push({
        name: 'packaging-request-update',
        params: { packagingRequestUuid: pickup.uuid },
      })

      return
    }

    if (pickup.requestNumber === null) {
      return
    }

    router.push({
      name: 'packaging-request-detail',
      params: { requestNumber: pickup.requestNumber },
    })

    return
  }
  if (pickup.status === PickUpRequestStatus.DRAFT) {
    if (pickup.uuid === null) {
      return
    }

    router.push({
      name: 'pickup-request-update',
      params: { pickupRequestUuid: pickup.uuid },
    })

    return
  }

  if (pickup.status === PickUpRequestStatus.INDASCAN_DRAFT) {
    if (pickup.requestNumber === null) {
      return
    }

    router.push({
      name: 'pickup-request-indascan-update',
      params: { requestNumber: pickup.requestNumber },
    })

    return
  }

  if (pickup.requestNumber === null) {
    return
  }

  router.push({
    name: 'pickup-request-detail',
    params: { requestNumber: pickup.requestNumber },
  })
}

async function onNewPickup(): Promise<void> {
  isCreatingPickup.value = true

  try {
    const pickupRequestUuid = await pickupRequestCreateMutation.execute()

    await router.push({
      name: 'pickup-request-update',
      params: { pickupRequestUuid },
    })
  }
  catch (error) {
    isCreatingPickup.value = false
    apiErrorToast.show(error)
  }
}

async function onNewPlanning(): Promise<void> {
  isCreatingPlanning.value = true

  try {
    const weeklyPlanningUuid = await weeklyPlanningCreateMutation.execute()

    await router.push({
      name: 'weekly-planning-update',
      params: { weeklyPlanningUuid },
    })
  }
  catch (error) {
    isCreatingPlanning.value = false
    apiErrorToast.show(error)
  }
}

async function onNewPackaging(): Promise<void> {
  isCreatingPackagingRequest.value = true

  try {
    const packagingRequestUuid = await packagingRequestCreateMutation.execute()

    await router.push({
      name: 'packaging-request-update',
      params: { packagingRequestUuid },
    })
  }
  catch (error) {
    isCreatingPackagingRequest.value = false
    apiErrorToast.show(error)
  }
}

function onDeleteInBulk(uuids: PickupRequestUuid[]): void {
  isDeletingPickupRequests.value = true
  confirmDialog.open(
    {
      title: i18n.t('module.pickup_request.overview.bulk.delete_draft'),
      isDestructive: true,
      cancelText: i18n.t('shared.cancel'),
      confirmText: i18n.t('shared.delete'),
      description: i18n.t('module.pickup_request.overview.bulk.delete_draft_description'),
      onConfirm: async () => {
        try {
          await pickupRequestBulkDeleteMutation.execute({ params: { pickupRequestUuids: uuids } })

          isDeletingPickupRequests.value = false
          itemsSelectedInBulk.value = []
          confirmDialog.close()
        }
        catch (error) {
          apiErrorToast.show(error)
        }
      },
    },
  )
}

async function onCopyPackingRequest(pickupRequest: PickupRequestIndex): Promise<void> {
  try {
    if (pickupRequest.requestNumber === null) {
      return
    }

    const response = await packagingRequestCopyMutation.execute({ body: pickupRequest.requestNumber })

    router.push({
      name: 'packaging-request-update',
      params: { packagingRequestUuid: response },
    })

    toast.success({
      title: i18n.t('shared.copy_success_title'),
      description: i18n.t('shared.copy_success_description'),
    })
  }
  catch (error) {
    apiErrorToast.show(error)
  }
}

async function onCopyPickupRequest(pickupRequest: PickupRequestIndex): Promise<void> {
  try {
    if (pickupRequest.requestNumber === null) {
      return
    }

    const response = await pickupRequestCopyMutation.execute({ body: pickupRequest.requestNumber })

    router.push({
      name: 'pickup-request-update',
      params: { pickupRequestUuid: response },
    })

    toast.success({
      title: i18n.t('shared.copy_success_title'),
      description: i18n.t('shared.copy_success_description'),
    })
  }
  catch (error) {
    apiErrorToast.show(error)
  }
}

function onCopy(pickupRequest: PickupRequestIndex): void {
  if (pickupRequest.transportMode === TransportMode.PACKAGING_REQUEST_ORDER) {
    onCopyPackingRequest(pickupRequest)

    return
  }

  onCopyPickupRequest(pickupRequest)
}

function onEdit(pickupRequest: PickupRequestIndex): void {
  redirectToDetailView(pickupRequest)
}
</script>

<template>
  <AppPage
    :title="i18n.t('module.pickup_request.overview.title')"
    class="pb-xl"
  >
    <template #header-actions>
      <AppGroup>
        <VcButton
          v-bind="pickupTemplateOverviewDialog.getTriggerProps()"
          variant="secondary"
          @click="pickupTemplateOverviewDialog.open()"
        >
          {{ i18n.t('module.pickup_request.overview.manage_templates') }}
        </VcButton>

        <div class="flex h-full items-center justify-center gap-4">
          <VcDropdownMenu
            v-if="hasPickupRequestManagePermission || hasWeeklyPlanningManagePermission"
          >
            <template #trigger>
              <VcButton
                :is-loading="isCreatingPickup || isCreatingPlanning"
                :test-id="TEST_ID.PICKUP.OVERVIEW.CREATE_BUTTON"
                icon-right="chevronDown"
              >
                {{ i18n.t('module.pickup_request.overview.new_request') }}
              </VcButton>
            </template>
            <template #content>
              <VcDropdownMenuGroup class="w-64">
                <VcDropdownMenuItem
                  v-if="hasPickupRequestManagePermission"
                  :label="i18n.t('module.pickup_request.overview.new_pickup')"
                  icon="plus"
                  @select="onNewPickup"
                />
                <PickupRequestOverviewFromTemplateSubMenu v-if="hasPickupRequestManagePermission" />
                <VcDropdownMenuItem
                  v-if="hasWeeklyPlanningManagePermission"
                  :label="i18n.t('module.weekly_planning.overview.new_planning')"
                  icon="calendarPlus"
                  @select="onNewPlanning"
                />
                <VcDropdownMenuItem
                  v-if="hasPackagingRequestManagePermission"
                  :label="i18n.t('module.packaging_request.overview.new_request')"
                  icon="packagePlus"
                  @select="onNewPackaging"
                />
              </VcDropdownMenuGroup>
            </template>
          </VcDropdownMenu>
        </div>
      </AppGroup>
    </template>

    <VcTabs
      v-model="tabModelValue"
      :class-config="{
        base: 'pl',
        content: '',
        indicator: 'hidden',
        item: 'border h-9 border-b-0 border-primary rounded-xl rounded-b-none min-w-36 data-[state=inactive]:bg-secondary !m-0 data-[state=active]:text-primary data-[state=inactive]:font-regular enabled:data-[state=active]:hover:bg-transparent',
        list: 'gap-x-0 inline-flex',
        scrollContainer: 'p-xxs pb-0',
      }"
    >
      <template #items>
        <VcTabsItem
          v-for="(tab, statusIndex) of tabs"
          :key="tab.id"
          :value="tab.id"
          :class="{
            '!-ml-px': statusIndex !== 0,
          }"
        >
          {{ tab.label }}
        </VcTabsItem>
      </template>
    </VcTabs>

    <DataTable
      v-if="dynamicTable.activeView.value !== null"
      :is-loading="isLoading"
      :data="data.data"
      :get-key="(index) => `${index}`"
      :columns="columns"
      :error="error"
      :sort="sort"
      :is-first-column-sticky="true"
      :is-last-column-sticky="true"
      :disable-top-left-border-radius="true"
      :searchable-columns="searchableColumns"
      @next-page="fetchNextPage"
    >
      <template
        #top
      >
        <template v-if="activeTab.id === PickupRequestIndexTableTabs.SUBMITTED">
          <FiltersRoot :filters="filters">
            <AppGroup
              justify="between"
              class="px-xl h-14"
            >
              <FiltersActive />

              <AppGroup>
                <DynamicTableViews :dynamic-table="dynamicTable" />
                <FiltersDropdownMenu />
                <DynamicTableSettings :dynamic-table="dynamicTable" />
              </AppGroup>
            </AppGroup>
          </FiltersRoot>
        </template>
      </template>
    </DataTable>

    <AppTableBulkActions
      :count="itemsSelectedInBulk.length"
      @cancel-bulk-selection="itemsSelectedInBulk = []"
    >
      <template #actions>
        <VcButton
          :is-loading="isDeletingPickupRequests"
          variant="destructive-tertiary"
          @click="onDeleteInBulk(itemsSelectedInBulk)"
        >
          {{ i18n.t('module.pickup_request.overview.bulk.delete_draft') }}
        </VcButton>
      </template>
    </AppTableBulkActions>
  </AppPage>
</template>
