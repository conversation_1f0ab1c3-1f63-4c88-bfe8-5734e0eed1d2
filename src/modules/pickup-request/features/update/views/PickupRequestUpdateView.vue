<script setup lang="ts">
import { useRouteQuery } from '@vueuse/router'
import {
  useVcDialog,
  VcSwitch,
} from '@wisemen/vue-core-components'
import type { Component } from 'vue'
import {
  computed,
  ref,
  watch,
} from 'vue'
import { useI18n } from 'vue-i18n'

import { PickUpTransportMode } from '@/client'
import AppSeparator from '@/components/app/AppSeparator.vue'
import AppTeleport from '@/components/app/teleport/AppTeleport.vue'
import WizardForm from '@/components/form/wizard/WizardForm.vue'
import WizardFormContent from '@/components/form/wizard/WizardFormContent.vue'
import WizardFormSidebar from '@/components/form/wizard/WizardFormSidebar.vue'
import WizardFormState from '@/components/form/wizard/WizardFormState.vue'
import AppPage from '@/components/layout/page/AppPage.vue'
import AppPageReturnLink from '@/components/layout/page/AppPageReturnLink.vue'
import { useDocumentTitle } from '@/composables/document-title/documentTitle.composable'
import { useGlobalCustomer } from '@/composables/global-customer/globalCustomer.composable'
import {
  useWizardForm,
  WizardFormLastStepAction,
} from '@/composables/wizard-form/wizardForm.composable'
import { useWizardFormStepState } from '@/composables/wizard-form/wizardFormStepState.composable'
import { AddressUtil } from '@/models/address/address.util'
import { PickupRequestFormSteps } from '@/models/enums/formSteps.enum'
import type { PickupRequestDetail } from '@/models/pickup-request/detail/pickupRequestDetail.model'
import { pickupRequestAdministrationFormSchema } from '@/models/pickup-request/update/steps/pickupRequestAdministrationForm.model'
import type { PickupRequestCustomerAndLocationForm } from '@/models/pickup-request/update/steps/pickupRequestCustomerAndLocationForm.model'
import { pickupRequestCustomerAndLocationFormSchema } from '@/models/pickup-request/update/steps/pickupRequestCustomerAndLocationForm.model'
import { pickupRequestDetailsFormSchema } from '@/models/pickup-request/update/steps/pickupRequestDetailsForm.model'
import { pickupRequestPackagingFormSchema } from '@/models/pickup-request/update/steps/pickupRequestPackagingForm.model'
import type { pickupRequestPackagingRequestForm } from '@/models/pickup-request/update/steps/pickupRequestPackagingMaterialsForm.model'
import { pickupRequestPackagingRequestSchema } from '@/models/pickup-request/update/steps/pickupRequestPackagingMaterialsForm.model'
import type { PickupRequestPlanningForm } from '@/models/pickup-request/update/steps/pickupRequestPlanningForm.model'
import { pickupRequestPlanningFormSchema } from '@/models/pickup-request/update/steps/pickupRequestPlanningForm.model'
import { pickupRequestTransportFormSchema } from '@/models/pickup-request/update/steps/pickupRequestTransportForm.model'
import type { PickupRequestWasteForm } from '@/models/pickup-request/update/steps/pickupRequestWasteForm.model'
import { pickupRequestWasteFormSchema } from '@/models/pickup-request/update/steps/pickupRequestWasteForm.model'
import { usePickupRequestSapUpdateMutation } from '@/modules/pickup-request/api/mutations/pickupRequestSapUpdate.mutation'
import { usePickupRequestUpdateMutation } from '@/modules/pickup-request/api/mutations/pickupRequestUpdate.mutation'
import PickupRequestDetailHeader from '@/modules/pickup-request/features/detail/components/PickupRequestDetailHeader.vue'

const props = defineProps<{
  isIndascanDraft?: boolean
  isReadonly?: boolean
  pickup: PickupRequestDetail
  requestNumber?: string

}>()

const i18n = useI18n()
const documentTitle = useDocumentTitle()
const globalCustomer = useGlobalCustomer()
const quickEntryMode = ref<boolean>(!(props.isReadonly || props.isIndascanDraft))

const formStepQuery = useRouteQuery<string | null>('step')

documentTitle.set(() => props.isIndascanDraft || props.isReadonly ? i18n.t('module.pickup_request.detail.page_title') : i18n.t('module.pickup_request.update.page_title'))

const pickupRequestUpdateMutation = usePickupRequestUpdateMutation()
const pickupRequestSapUpdateMutation = usePickupRequestSapUpdateMutation()

const customerAndLocationState = useWizardFormStepState<PickupRequestCustomerAndLocationForm>({
  customer: props.pickup.customer ?? globalCustomer.globalCustomer.value ?? null,
  pickupAddresses: props.pickup.pickupAddresses,
  wasteProducer: props.pickup.wasteProducer,
})
const wasteState = useWizardFormStepState<PickupRequestWasteForm>({
  isPackagingAdded: props.pickup.packagingRequestMaterials?.length > 0,
  isReturnPackaging: props.pickup.isReturnPackaging ?? false,
  isTransportByIndaver: props.pickup.isTransportByIndaver ?? true,
  customerCountryCode: null,
  materials: props.pickup.materials.map((material) => ({
    ...material,
    isCostCenterRequired: false,
    isPoNumberRequired: false,
    amount: null,
    containerType: material.containerType === null ? null : { name: material.containerType },
    packagingType: material.packagingType === null ? null : { name: material.packagingType },
  })),
  packagingRemark: props.pickup.packagingRemark,
  totalQuantityPallets: props.pickup.totalQuantityPallets,
  transportMode: props.pickup.transportMode,
})
const planningState = useWizardFormStepState<PickupRequestPlanningForm>({
  isWicConfirmationRequired: props.pickup.isWicConfirmationRequired ?? false,
  isWicConfirmed: props.pickup.isWicConfirmed ?? false,
  additionalFiles: props.pickup.additionalFiles,
  range: {
    from: props.pickup.startDate,
    until: props.pickup.endDate,
  },
  remarks: props.pickup.remarks,
})
const packagingRequestState = useWizardFormStepState<pickupRequestPackagingRequestForm>({
  isPackagingAdded: computed<boolean>(() => wasteState.value.isPackagingAdded).value,
  packagingRequestMaterials: props.pickup.packagingRequestMaterials.map((material) => ({
    ...material,
    contractLineId: material.contractLineId,
    isSales: material.isSales ?? false,
    contractItem: material.contractItem,
    contractNumber: material.contractNumber,
    costCenter: material.costCenter,
    materialNumber: material.materialNumber ?? '',
    poNumber: material.poNumber,
    quantity: material.quantity,
    wasteMaterial: material.wasteMaterial ?? '',
  })),
})

const submitDialog = useVcDialog({ component: () => import('@/modules/pickup-request/features/update/components/submit/PickupRequestSubmitDialog.vue') })

const wizardForm = useWizardForm({
  categories: [
    {
      id: 'general',
      name: computed<string>(() => i18n.t('module.pickup_request.update.general_info')),
      icon: 'building',
      steps: [
        {
          id: PickupRequestFormSteps.CUSTOMER_AND_LOCATION,
          isReadonly: props.isReadonly || props.isIndascanDraft,
          name: computed<string>(() => i18n.t('module.pickup_request.update.customer_and_location.title')),
          scheme: pickupRequestCustomerAndLocationFormSchema,
          showValue: 'customer',
          showValueDescription: computed<string>(() => customerAndLocationState.value.customer?.address == null ? '-' : AddressUtil.format(customerAndLocationState.value.customer?.address)),
          showValueTitle: computed<string>(() => customerAndLocationState.value.customer?.name ?? ''),
          state: customerAndLocationState,
          component: (): Promise<Component> => import('@/modules/pickup-request/features/update/components/steps/customer-and-location/PickupRequestCustomerAndLocationForm.vue'),
        },
      ],
    },
    {
      id: 'details',
      name: computed<string>(() => i18n.t('module.pickup_request.update.pickup_details')),
      icon: 'truck',
      steps: [
        {
          id: PickupRequestFormSteps.WASTE,
          isReadonly: props.isReadonly,
          name: computed<string>(() => i18n.t('module.pickup_request.update.waste.title')),
          props: { isIndascanDraft: props.isIndascanDraft },
          scheme: pickupRequestWasteFormSchema,
          state: wasteState,
          component: (): Promise<Component> => import('@/modules/pickup-request/features/update/components/steps/waste/PickupRequestWasteForm.vue'),
        },
        {
          id: PickupRequestFormSteps.DETAILS,
          isReadonly: props.isReadonly,
          isStepHidden: computed<boolean>(() => quickEntryMode.value === false),
          name: computed<string>(() => i18n.t('module.pickup_request.update.details.title')),
          scheme: pickupRequestDetailsFormSchema,
          state: wasteState,
          component: (): Promise<Component> => import('@/modules/pickup-request/features/update/components/steps/details/PickupRequestDetailsForm.vue'),
        },
        {
          id: PickupRequestFormSteps.PACKAGING,
          isReadonly: props.isReadonly || props.isIndascanDraft,
          isStepHidden: computed<boolean>(() => quickEntryMode.value === true),
          name: computed<string>(() => {
            if (wasteState.value.transportMode === PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK) {
              return i18n.t('module.pickup_request.update.packaging.title')
            }

            return i18n.t('module.pickup_request.update.container_info.title')
          }),
          props: { isIndascanDraft: props.isIndascanDraft },
          scheme: pickupRequestPackagingFormSchema,
          state: wasteState,
          component: (): Promise<Component> => import('@/modules/pickup-request/features/update/components/steps/packaging/PickupRequestPackagingForm.vue'),
        },
        {
          id: PickupRequestFormSteps.TRANSPORT,
          isReadonly: props.isReadonly || props.isIndascanDraft,
          isStepHidden: computed<boolean>(() => quickEntryMode.value === true),
          name: computed<string>(() => i18n.t('module.pickup_request.update.transport.title')),
          scheme: pickupRequestTransportFormSchema,
          state: wasteState,
          component: (): Promise<Component> => import('@/modules/pickup-request/features/update/components/steps/transport/PickupRequestTransportForm.vue'),
        },
        {
          id: PickupRequestFormSteps.ADMINISTRATION,
          isReadonly: props.isReadonly,
          isStepHidden: computed<boolean>(() => quickEntryMode.value === true),
          name: computed<string>(() => i18n.t('module.pickup_request.update.administration.title')),
          props: { isIndascanDraft: props.isIndascanDraft },
          scheme: pickupRequestAdministrationFormSchema,
          state: wasteState,
          component: (): Promise<Component> => import('@/modules/pickup-request/features/update/components/steps/administration/PickupRequestAdministrationForm.vue'),
        },
        {
          id: PickupRequestFormSteps.PACKAGING_REQUEST,
          isReadonly: props.isReadonly,
          isStepHidden: computed<boolean>(() => !wasteState.value.isPackagingAdded),
          name: computed<string>(() => i18n.t('module.pickup_request.update.packaging_request.title')),
          scheme: pickupRequestPackagingRequestSchema,
          state: packagingRequestState,
          component: (): Promise<Component> => import('@/modules/pickup-request/features/update/components/steps/packaging-request/PickupRequestPackagingRequestForm.vue'),
        },
        {
          id: PickupRequestFormSteps.PLANNING,
          isReadonly: props.isReadonly,
          name: computed<string>(() => i18n.t('module.pickup_request.update.planning.title')),
          props: {
            isIndascanDraft: props.isIndascanDraft,
            requestNumber: props.requestNumber,
          },
          scheme: pickupRequestPlanningFormSchema,
          state: planningState,
          component: (): Promise<Component> => import('@/modules/pickup-request/features/update/components/steps/planning/PickupRequestPlanningForm.vue'),
        },
      ],
    },
  ],
  lastStepAction: props.isReadonly ? null : WizardFormLastStepAction.SUBMIT,
  onAutoSave: async (): Promise<void> => {
    if (props.isIndascanDraft) {
      if (props.pickup.requestNumber == null) {
        return
      }

      await pickupRequestSapUpdateMutation.execute({
        body: {
          ...customerAndLocationState.value,
          ...wasteState.value,
          ...planningState.value,
          ...packagingRequestState.value,
        },
        params: { requestNumber: props.pickup.requestNumber },
      })

      return
    }

    if (props.pickup.uuid == null) {
      return
    }

    const response = await pickupRequestUpdateMutation.execute({
      body: {
        ...customerAndLocationState.value,
        ...wasteState.value,
        ...planningState.value,
        ...packagingRequestState.value,
      },
      params: { pickupRequestUuid: props.pickup.uuid },
    })

    planningState.value.isWicConfirmationRequired = response.needsWicConfirmation
  },
  onShowSubmitDialog: (): void => {
    if (props.isIndascanDraft) {
      if (props.pickup.requestNumber == null) {
        return
      }

      submitDialog.open({
        pickupRequestUuid: null,
        isIndascanDraft: true,
        requestNumber: props.pickup.requestNumber,
      })

      return
    }

    if (props.pickup.uuid == null) {
      return
    }

    submitDialog.open({
      pickupRequestUuid: props.pickup.uuid,
      requestNumber: null,
    })
  },
})

if (formStepQuery.value != null) {
  wizardForm.goToStep(formStepQuery.value)
}
if (props.isIndascanDraft) {
  wizardForm.goToStep(PickupRequestFormSteps.WASTE)
}

function onQuickEntryModeChange(): void {
  wizardForm.goToStep(PickupRequestFormSteps.WASTE)
}

watch(() => wasteState, () => {
  packagingRequestState.value.isPackagingAdded = wasteState.value.isPackagingAdded
}, {
  deep: true,
  immediate: true,
})

watch(() => customerAndLocationState.value.customer, () => {
  wasteState.value.materials = []
})
</script>

<template>
  <AppTeleport to="headerLeft">
    <AppPageReturnLink
      :return-link="{
        label: i18n.t('module.pickup_request.update.return_to_overview'),
        to: {
          name: 'pickup-request-overview',
        },
      }"
    />
  </AppTeleport>

  <WizardForm :wizard-form="wizardForm">
    <AppPage :title="i18n.t('module.pickup_request.update.page_title')">
      <template
        v-if="props.isReadonly"
        #header
      >
        <PickupRequestDetailHeader :pickup="props.pickup" />
      </template>
      <template #header-actions>
        <div class="flex items-center gap-3">
          <VcSwitch
            v-if="!props.isIndascanDraft"
            v-model="quickEntryMode"
            :label="i18n.t('module.pickup_request.update.quick_entry_mode')"
            @update:model-value="onQuickEntryModeChange"
          />
          <AppSeparator
            direction="vertical"
            class="!h-5"
          />
          <WizardFormState
            :created-at="props.pickup.createdAt"
            :auto-save-error-message="wizardForm.isAutoSaving.value ? null : wizardForm.autoSaveErrorMessage.value"
            :is-auto-saving="wizardForm.isAutoSaving.value"
          />
        </div>
      </template>

      <template #left-content>
        <WizardFormSidebar />
      </template>

      <template #default>
        <WizardFormContent />
      </template>
    </AppPage>
  </WizardForm>
</template>
