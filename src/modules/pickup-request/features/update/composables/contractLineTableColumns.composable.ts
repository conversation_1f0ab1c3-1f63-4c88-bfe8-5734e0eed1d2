import type { TableColumn } from '@wisemen/vue-core-components'
import { VcTableCell } from '@wisemen/vue-core-components'
import type { VNode } from 'vue'
import { h } from 'vue'

import { DynamicColumnNames } from '@/client'
import {
  useGenericAsnColumn,
  useGenericContractItemColumn,
  useGenericContractNumberColumn,
  useGenericCustomerIdColumn,
  useGenericCustomerNameColumn,
  useGenericCustomerReferenceColumn,
  useGenericDeliveryInfoColumn,
  useGenericEndTreatmentCenterIdColumn,
  useGenericEndTreatmentCenterNameColumn,
  useGenericEsnNumberColumn,
  useGenericEwcCodeColumn,
  useGenericInstallationNameColumn,
  useGenericIsHazardousColumn,
  useGenericMaterialAnalysisColumn,
  useGenericMaterialNumberColumn,
  useGenericPackagedColumn,
  useGenericPickupAddressIdColumn,
  useGenericPickupAddressNameColumn,
  useGenericProcessCodeColumn,
  useGenericRemarksColumn,
  useGenericTcNumberColumn,
  useGenericTfsColumn,
  useGenericTreatmentCenterNameColumn,
  useGenericWasteMaterialColumn,
  useGenericWasteProducerIdColumn,
  useGenericWasteProducerNameColumn,
} from '@/composables/table-columns/genericTableColumns.composable'
import type { ContractLineIndex } from '@/models/contract-line/index/contractLineIndex.model'
import type { ContractLinePackagingRequestIndex } from '@/models/contract-line/packaging-request/contractLinePackagingRequestIndex.model'
import { DynamicTableEnumUtil } from '@/models/enums/dynamicTable.enum'
import { i18nPlugin } from '@/plugins/i18n.plugin'

export function useContractLineTableColumns(): Partial<{
  [K in DynamicColumnNames]: (
  label: string,
  isSortable: boolean,
  ) => TableColumn<ContractLineIndex>
}> {
  return {
    [DynamicColumnNames.ASN]: useGenericAsnColumn,
    [DynamicColumnNames.CONTRACT_ITEM]: useGenericContractItemColumn,
    [DynamicColumnNames.CONTRACT_NUMBER]: useGenericContractNumberColumn,
    [DynamicColumnNames.CUSTOMER_ID]: useGenericCustomerIdColumn,
    [DynamicColumnNames.CUSTOMER_NAME]: useGenericCustomerNameColumn,
    [DynamicColumnNames.CUSTOMER_REFERENCE]: useGenericCustomerReferenceColumn,
    [DynamicColumnNames.DELIVERY_INFO]: useGenericDeliveryInfoColumn,
    [DynamicColumnNames.END_TREATMENT_CENTER_ID]: useGenericEndTreatmentCenterIdColumn,
    [DynamicColumnNames.END_TREATMENT_CENTER_NAME]: useGenericEndTreatmentCenterNameColumn,
    [DynamicColumnNames.ESN_NUMBER]: useGenericEsnNumberColumn,
    [DynamicColumnNames.EWC_CODE]: useGenericEwcCodeColumn,
    [DynamicColumnNames.INSTALLATION_NAME]: useGenericInstallationNameColumn,
    [DynamicColumnNames.IS_HAZARDOUS]: useGenericIsHazardousColumn,
    [DynamicColumnNames.MATERIAL_ANALYSIS]: useGenericMaterialAnalysisColumn,
    [DynamicColumnNames.MATERIAL_NUMBER]: useGenericMaterialNumberColumn,
    [DynamicColumnNames.PACKAGED]: useGenericPackagedColumn,
    [DynamicColumnNames.PICK_UP_ADDRESS_ID]: useGenericPickupAddressIdColumn,
    [DynamicColumnNames.PICK_UP_ADDRESS_NAME]: useGenericPickupAddressNameColumn,
    [DynamicColumnNames.PROCESS_CODE]: useGenericProcessCodeColumn,
    [DynamicColumnNames.REMARKS]: useGenericRemarksColumn,
    [DynamicColumnNames.TC_NUMBER]: useGenericTcNumberColumn,
    [DynamicColumnNames.TFS]: useGenericTfsColumn,
    [DynamicColumnNames.TREATMENT_CENTER_NAME]: useGenericTreatmentCenterNameColumn,
    [DynamicColumnNames.WASTE_MATERIAL]: useGenericWasteMaterialColumn,
    [DynamicColumnNames.WASTE_PRODUCER_ID]: useGenericWasteProducerIdColumn,
    [DynamicColumnNames.WASTE_PRODUCER_NAME]: useGenericWasteProducerNameColumn,
  }
}

export function useContractLineTableDefaultColumns(): TableColumn<ContractLineIndex>[] {
  return [
    useGenericWasteMaterialColumn(
      i18nPlugin.global.t(DynamicTableEnumUtil.getLabelI18nKey(DynamicColumnNames.WASTE_MATERIAL)),
      false,
    ),
    useGenericCustomerReferenceColumn(
      i18nPlugin.global.t(DynamicTableEnumUtil.getLabelI18nKey(DynamicColumnNames.CUSTOMER_REFERENCE)),
      false,
    ),
    useGenericEwcCodeColumn(
      i18nPlugin.global.t(DynamicTableEnumUtil.getLabelI18nKey(DynamicColumnNames.EWC_CODE)),
      false,
    ),
  ]
}

export function usePackagingRequestContractLineTableDefaultColumns(): TableColumn<ContractLinePackagingRequestIndex>[] {
  return [
    useGenericMaterialNumberColumn(
      i18nPlugin.global.t(DynamicTableEnumUtil.getLabelI18nKey(DynamicColumnNames.MATERIAL_NUMBER)),
      false,
    ),
    useGenericWasteMaterialColumn(
      i18nPlugin.global.t('shared.description'),
      false,
    ),
    {
      isSortable: false,
      cell: (row): VNode => h(VcTableCell, () => {
        if (row.isSales === null) {
          return '-'
        }
        if (row.isSales === true) {
          return i18nPlugin.global.t('enum.packaging_request_type.sales')
        }
        if (row.isSales === false) {
          return i18nPlugin.global.t('enum.packaging_request_type.rental')
        }
      }),
      headerLabel: i18nPlugin.global.t('shared.type'),
      key: 'isSales',
    },
    useGenericContractNumberColumn(
      i18nPlugin.global.t(DynamicTableEnumUtil.getLabelI18nKey(DynamicColumnNames.CONTRACT_NUMBER)),
      false,
    ),
    useGenericContractItemColumn(
      i18nPlugin.global.t(DynamicTableEnumUtil.getLabelI18nKey(DynamicColumnNames.CONTRACT_ITEM)),
      false,
    ),
  ]
}
