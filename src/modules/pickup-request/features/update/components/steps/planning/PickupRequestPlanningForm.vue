<script setup lang="ts">
import type { FileUploadItem } from '@wisemen/vue-core-components'
import {
  VcCheckbox,
  VcDateRangeField,
  VcTextarea,
} from '@wisemen/vue-core-components'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import FormGrid from '@/components/app/grid/FormGrid.vue'
import FormFileUpload from '@/components/form/file-upload/FormFileUpload.vue'
import WizardFormStep from '@/components/form/wizard/WizardFormStep.vue'
import type { WizardForm } from '@/composables/wizard-form/wizardForm.composable'
import { useWizardFormStep } from '@/composables/wizard-form/wizardFormStep.composable'
import type { pickupRequestPlanningFormSchema } from '@/models/pickup-request/update/steps/pickupRequestPlanningForm.model'
import { usePickupRequestDownloadFileMutation } from '@/modules/pickup-request/api/mutations/pickupRequestDownloadFile.mutation'
import { DownloadUtil } from '@/utils/download.util'
import { FileUtil } from '@/utils/file.util'
import { toFormField } from '@/utils/formango.util'

const props = defineProps<{
  isIndascanDraft?: boolean
  isReadonly?: boolean
  requestNumber?: string
  wizardForm: WizardForm<typeof pickupRequestPlanningFormSchema>
}>()

const i18n = useI18n()
const wizardFormStep = useWizardFormStep(props.wizardForm)

const downloadFile = usePickupRequestDownloadFileMutation()

const range = wizardFormStep.form.register('range', {
  from: null,
  until: null,
})

const isWicConfirmationRequired = computed<boolean>(
  () => wizardFormStep.form.state.value.isWicConfirmationRequired ?? false,
)

const remarks = wizardFormStep.form.register('remarks')
const additionalFiles = wizardFormStep.form.register('additionalFiles', [])
const isWicConfirmed = wizardFormStep.form.register('isWicConfirmed', false)

async function onDownloadFile(file: FileUploadItem): Promise<void> {
  if (props.requestNumber === undefined) {
    return
  }
  const {
    blob, disposition,
  } = await downloadFile.execute({
    body: {
      fileName: file.name,
      requestNumber: props.requestNumber,
    },
  })

  DownloadUtil.downloadBlob(blob, disposition)
}
</script>

<template>
  <WizardFormStep :wizard-form-step="wizardFormStep">
    <FormGrid
      :cols="1"
      gap="3xl"
    >
      <VcDateRangeField
        v-bind="toFormField(range)"
        :label="i18n.t('module.pickup_request.update.planning.date_or_period')"
        :placeholder="i18n.t('module.pickup_request.update.planning.date_or_period')"
        :is-required="true"
        :is-disabled="props.isReadonly"
        :show-two-months="true"
        :min-date="new Date(new Date().setDate(new Date().getDate() + 1))"
        class="w-fit"
      />

      <VcCheckbox
        v-if="isWicConfirmationRequired"
        v-bind="toFormField(isWicConfirmed)"
        :is-required="true"
        :label="i18n.t('module.pickup_request.update.planning.wic_confirmation_label')"
      />

      <VcTextarea
        :is-disabled="props.isReadonly"
        v-bind="toFormField(remarks)"
        :label="i18n.t('module.pickup_request.update.planning.remarks')"
        :placeholder="i18n.t('module.pickup_request.update.planning.remarks_placeholder')"
        :class-config="{
          input: 'min-h-20',
        }"
        resize="auto-vertical"
      />

      <FormFileUpload
        v-if="props.requestNumber === undefined"
        :mime-types="FileUtil.getDefaultAllowedExtensions()"
        v-bind="toFormField(additionalFiles)"
        :label="i18n.t('module.pickup_request.update.planning.additional_files')"
        :hint="i18n.t('module.pickup_request.update.planning.additional_files_hint')"
        :max-file-size-mb="100"
      />
      <FormFileUpload
        v-else
        :mime-types="FileUtil.getDefaultAllowedExtensions()"
        v-bind="toFormField(additionalFiles)"
        :label="i18n.t('module.pickup_request.update.planning.additional_files')"
        :hint="i18n.t('module.pickup_request.update.planning.additional_files_hint')"
        :max-file-size-mb="100"
        :is-readonly="true"
        :download-fn="onDownloadFile"
      />
    </FormGrid>
  </WizardFormStep>
</template>
