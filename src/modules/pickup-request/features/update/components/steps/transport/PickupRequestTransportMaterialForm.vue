<script setup lang="ts">
import {
  usePagination,
  VcAutocomplete,
  VcTextField,
} from '@wisemen/vue-core-components'
import type { FieldArray } from 'formango'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import type { PackingGroup } from '@/client'
import {
  PickUpTransportMode,
  WasteMeasurementUnit,
} from '@/client'
import AppTransportTypeSelect from '@/components/app/select/AppTransportTypeSelect.vue'
import FormMeasurementField from '@/components/form/fields/FormMeasurementField.vue'
import { WastePackagingSizeEnumUtil } from '@/models/enums/wastePackagingSize.enum'
import type { PickupRequestWasteMaterialForm } from '@/models/pickup-request/update/steps/pickupRequestWasteForm.model'
import type { UnNumberIndex } from '@/models/un-number/index/unNumberIndex.model'
import type { UnNumberIndexPagination } from '@/models/un-number/index/unNumberIndexPagination.model'
import PickupRequestTransportMaterialUnNumberInfo from '@/modules/pickup-request/features/update/components/steps/transport/PickupRequestTransportMaterialUnNumberInfo.vue'
import { useUnNumberIndexQuery } from '@/modules/un-number/api/queries/unNumberIndex.query'
import { toFormField } from '@/utils/formango.util'

const props = defineProps<{
  isReadonly?: boolean
  isUserFromGermany: boolean
  gridColTemplate: string
  index: number
  materials: FieldArray<PickupRequestWasteMaterialForm>
  transportMode: PickUpTransportMode
}>()

const i18n = useI18n()

const estimatedWeightOrVolumeValue = props.materials.register(`${props.index}.estimatedWeightOrVolumeValue`, null)
const estimatedWeightOrVolumeUnit = props.materials.register(`${props.index}.estimatedWeightOrVolumeUnit`, WasteMeasurementUnit.TO)
const containerTransportType = props.materials.register(`${props.index}.containerTransportType`, null)
const hazardInducers = props.materials.register(`${props.index}.hazardInducers`, null)

const materialName = computed<string>(() => {
  const material = props.materials.value.value[props.index]

  return material.wasteMaterial ?? '-'
})
const materialNumber = computed<string>(() => {
  const material = props.materials.value.value[props.index]

  return material.contractNumber ?? '-'
})
const unNumber = props.materials.register(`${props.index}.unNumber`)

const pagination = usePagination<UnNumberIndexPagination>({
  isRouteQueryEnabled: false,
  options: {},
})

const unNumberIndexQuery = useUnNumberIndexQuery(pagination.paginationOptions)

const unNumbers = computed<UnNumberIndex[]>(() => unNumberIndexQuery.data.value?.data ?? [])

function onSearch(search: string): void {
  pagination.handleSearchChange(search)
}

function onUpdateUnNumber(value: {
  isHazardous: boolean | null
  dangerLabel1: string | null
  dangerLabel2: string | null
  dangerLabel3: string | null
  description: string | null
  number: string | null
  packingGroup: string | null
} | null): void {
  unNumber.setValue(value)
}
</script>

<template>
  <div
    :class="props.gridColTemplate"
    class="
      gap-xl px-xl py-md border-secondary mb-md grid min-w-fit items-center
      rounded-xl border
    "
  >
    <div>
      <p class="font-semibold">
        {{ materialName }}
      </p>
      <p class="text-secondary text-sm">
        {{ materialNumber }}
      </p>
    </div>

    <FormMeasurementField
      v-bind="estimatedWeightOrVolumeValue"
      :unit="estimatedWeightOrVolumeUnit"
      :is-required="false"
      :min="0.01"
      :max="99999999999.99"
      :is-readonly="props.isReadonly"
      :label="i18n.t('module.pickup_request.update.packaging.fields.estimated_weight_volume')"
      :is-label-hidden="true"
    />
    <VcAutocomplete
      v-bind="toFormField(unNumber)"
      :is-disabled="props.isReadonly"
      :items="unNumbers"
      :is-loading="unNumberIndexQuery.isFetching.value"
      :display-fn="(value) => `${value.number} - ${value.description ?? ''}`"
      :placeholder="i18n.t('module.pickup_request.update.packaging.placeholder.un_number')"
      :is-search-term-optional="true"
      icon-right="selectIconRight"
      popover-align="start"
      @search="onSearch"
      @update:model-value="onUpdateUnNumber"
    />

    <VcTextField
      v-if="isUserFromGermany"
      v-bind="toFormField(hazardInducers)"
      :hint="unNumber.value.value?.isHazardous ? i18n.t('module.pickup_request.update.transport.fields.hazard_inducers_hint') : null"
      :placeholder="`${i18n.t('module.pickup_request.update.packaging.placeholder.hazard_inducers')} ${unNumber.value.value?.isHazardous ? '*' : ''}`"
    />

    <PickupRequestTransportMaterialUnNumberInfo
      :content="unNumber.value.value?.packingGroup
        ? i18n.t(WastePackagingSizeEnumUtil.getI18nKey(unNumber.value.value.packingGroup as PackingGroup))
        : null"
    />
    <PickupRequestTransportMaterialUnNumberInfo :content="unNumber.value.value?.dangerLabel1" />
    <PickupRequestTransportMaterialUnNumberInfo :content="unNumber.value.value?.dangerLabel2" />
    <PickupRequestTransportMaterialUnNumberInfo :content="unNumber.value.value?.dangerLabel3" />

    <template
      v-if="props.transportMode === PickUpTransportMode.BULK_SKIPS_CONTAINER
        || props.transportMode === PickUpTransportMode.BULK_ISO_TANK"
    >
      <AppTransportTypeSelect
        v-bind="toFormField(containerTransportType)"
        :is-readonly="props.isReadonly"
        :label="i18n.t('module.pickup_request.update.packaging.placeholder.transport_type')"
        :is-label-hidden="true"
      />
    </template>
  </div>
</template>
