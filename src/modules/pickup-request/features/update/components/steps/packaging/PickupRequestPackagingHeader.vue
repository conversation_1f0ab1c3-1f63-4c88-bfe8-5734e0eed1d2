<script setup lang="ts">
import { useI18n } from 'vue-i18n'

import { PickUpTransportMode } from '@/client'
import AppLabel from '@/components/app/AppLabel.vue'

const props = defineProps<{
  isUserFromGermany: boolean
  isUserFromIreland: boolean
  gridColTemplate: string
  transportMode: PickUpTransportMode
}>()

const i18n = useI18n()
</script>

<template>
  <div
    :class="props.gridColTemplate"
    class="gap-xl py-md px-xl grid items-center"
  >
    <AppLabel
      :is-required="false"
      :label="i18n.t('module.pickup_request.update.packaging.fields.waste')"
    />
    <AppLabel
      v-if="props.isUserFromGermany"
      :is-required="false"
      :label="i18n.t('module.pickup_request.update.packaging.fields.reconciliation_number')"
    />
    <template v-if="props.transportMode === PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK">
      <AppLabel
        :is-required="true"
        :label="i18n.t('module.pickup_request.update.packaging.fields.packaging_type')"
      />
      <AppLabel
        :is-required="true"
        :label="i18n.t('module.pickup_request.update.packaging.fields.quantity_packages')"
      />
      <AppLabel
        :is-required="!props.isUserFromGermany"
        :label="i18n.t('module.pickup_request.update.packaging.fields.quantity_labels')"
      />
      <AppLabel
        :is-required="props.isUserFromGermany"
        :label="i18n.t('module.pickup_request.update.packaging.fields.quantity_pallets')"
      />
    </template>
    <template v-if="props.transportMode === PickUpTransportMode.BULK_SKIPS_CONTAINER">
      <AppLabel
        :is-required="false"
        :label="i18n.t('module.pickup_request.update.packaging.fields.container_number')"
      />
      <AppLabel
        :is-required="false"
        :label="i18n.t('module.pickup_request.update.packaging.fields.number_of_containers')"
      />
      <AppLabel
        v-if="props.isUserFromGermany || props.isUserFromIreland"
        :is-required="true"
        :label="i18n.t('module.pickup_request.update.packaging.fields.container_size_volume')"
      />
      <AppLabel
        :is-required="false"
        :label="i18n.t('module.pickup_request.update.packaging.fields.container_covered')"
      />
    </template>
    <template v-if="props.transportMode === PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS">
      <AppLabel
        :is-required="true"
        :label="i18n.t('module.pickup_request.update.packaging.fields.tanker_type')"
      />
      <AppLabel
        v-if="props.isUserFromGermany || props.isUserFromIreland"
        :is-required="true"
        :label="i18n.t('module.pickup_request.update.packaging.fields.container_size_volume')"
      />
    </template>
    <template v-if="props.transportMode === PickUpTransportMode.BULK_ISO_TANK">
      <AppLabel
        v-if="props.isUserFromGermany || props.isUserFromIreland"
        :is-required="true"
        :label="i18n.t('module.pickup_request.update.packaging.fields.container_size_volume')"
      />
      <AppLabel
        :is-required="false"
        :label="i18n.t('module.pickup_request.update.packaging.fields.container_number')"
      />
    </template>
  </div>
</template>
