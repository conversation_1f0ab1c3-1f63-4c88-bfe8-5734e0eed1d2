<script setup lang="ts">
import {
  VcCheckbox,
  VcIcon,
  VcNumberField,
  VcTextField,
  VcTooltip,
} from '@wisemen/vue-core-components'
import {
  computed,
  watch,
} from 'vue'
import { useI18n } from 'vue-i18n'

import { PickUpTransportMode } from '@/client'
import AppGroup from '@/components/app/AppGroup.vue'
import WizardFormStep from '@/components/form/wizard/WizardFormStep.vue'
import type { WizardForm } from '@/composables/wizard-form/wizardForm.composable'
import { useWizardFormStep } from '@/composables/wizard-form/wizardFormStep.composable'
import type { pickupRequestPackagingFormSchema } from '@/models/pickup-request/update/steps/pickupRequestPackagingForm.model'
import type { PickupRequestWasteMaterialForm } from '@/models/pickup-request/update/steps/pickupRequestWasteForm.model'
import PickupRequestPackagingHeader from '@/modules/pickup-request/features/update/components/steps/packaging/PickupRequestPackagingHeader.vue'
import PickupRequestPackagingMaterialForm from '@/modules/pickup-request/features/update/components/steps/packaging/PickupRequestPackagingMaterialForm.vue'
import { useCustomerCountryCodeQuery } from '@/modules/waste-inquiry/api/queries/customerCountryCode.query'
import { CustomerUtil } from '@/utils/customer.util'
import { toFormField } from '@/utils/formango.util'

const props = defineProps<{
  isIndascanDraft?: boolean
  isReadonly?: boolean
  wizardForm: WizardForm<typeof pickupRequestPackagingFormSchema>
}>()

const i18n = useI18n()
const wizardFormStep = useWizardFormStep(props.wizardForm)

const customerCountryCodeQuery = useCustomerCountryCodeQuery(props.wizardForm.state.value.customer.id)
const customerCountryCode = customerCountryCodeQuery.data

const isUserFromGermany = computed<boolean>(() => {
  return customerCountryCode.value ? CustomerUtil.isGerman(customerCountryCode.value) : false
})
const isUserFromIreland = computed<boolean>(() => {
  return customerCountryCode.value ? CustomerUtil.isIrish(customerCountryCode.value) : false
})

const materials = wizardFormStep.form.registerArray('materials')
const totalQuantityPallets = wizardFormStep.form.register(
  'totalQuantityPallets',
  props.wizardForm.state.value.transportMode !== PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK
    ? null
    : props.wizardForm.state.value.materials
      .reduce((sum: number, material: PickupRequestWasteMaterialForm) => sum + (material.quantityPallets || 0), 0)
      ?? 0,
)
const isReturnPackaging = wizardFormStep.form.register('isReturnPackaging', false)
const packagingRemark = wizardFormStep.form.register('packagingRemark')

const gridColTemplate = computed<string>(() => {
  if (isUserFromGermany.value) {
    if (wizardFormStep.form.state.value.transportMode === PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS) {
      return 'grid-cols-[minmax(11rem,_1fr)_minmax(8rem,_1fr)_minmax(12rem,_1fr)_minmax(8rem,_1fr)]'
    }
    if (wizardFormStep.form.state.value.transportMode === PickUpTransportMode.BULK_SKIPS_CONTAINER) {
      return 'grid-cols-[minmax(11rem,_1fr)_minmax(8rem,_1fr)_minmax(10rem,_1fr)_minmax(8rem,_1fr)_minmax(8rem,_1fr)_minmax(8rem,_1fr)_minmax(4rem,_8rem)]'
    }
    if (wizardFormStep.form.state.value.transportMode === PickUpTransportMode.BULK_ISO_TANK) {
      return 'grid-cols-[minmax(11rem,_1fr)_minmax(8rem,_1fr)_minmax(8rem,_1fr)_minmax(8rem,_1fr)_minmax(5rem,_8rem)]'
    }

    return 'grid-cols-[minmax(11rem,_1fr)_minmax(8rem,_1fr)_minmax(8rem,_1fr)_repeat(3,_5.5rem)]'
  }

  if (isUserFromIreland.value) {
    if (wizardFormStep.form.state.value.transportMode === PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS) {
      return 'grid-cols-[minmax(11rem,_1fr)_minmax(12rem,_1fr)_minmax(8rem,_1fr)]'
    }
    if (wizardFormStep.form.state.value.transportMode === PickUpTransportMode.BULK_SKIPS_CONTAINER) {
      return 'grid-cols-[minmax(11rem,_1fr)_minmax(10rem,_1fr)_minmax(8rem,_1fr)_minmax(8rem,_1fr)_minmax(8rem,_1fr)_minmax(4rem,_8rem)]'
    }
    if (wizardFormStep.form.state.value.transportMode === PickUpTransportMode.BULK_ISO_TANK) {
      return 'grid-cols-[minmax(11rem,_1fr)_minmax(8rem,_1fr)_minmax(8rem,_1fr)_minmax(5rem,_8rem)]'
    }

    return 'grid-cols-[minmax(11rem,_1fr)_minmax(8rem,_1fr)_minmax(8rem,_1fr)_repeat(3,_5.5rem)]'
  }

  if (wizardFormStep.form.state.value.transportMode === PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS) {
    return 'grid-cols-[minmax(11rem,_1fr)_minmax(12rem,_1fr)]'
  }
  if (wizardFormStep.form.state.value.transportMode === PickUpTransportMode.BULK_SKIPS_CONTAINER) {
    return 'grid-cols-[minmax(11rem,_1fr)_minmax(10rem,_1fr)_minmax(8rem,_1fr)_minmax(8rem,_1fr)_minmax(4rem,_8rem)]'
  }
  if (wizardFormStep.form.state.value.transportMode === PickUpTransportMode.BULK_ISO_TANK) {
    return 'grid-cols-[minmax(11rem,_1fr)_minmax(8rem,_1fr)_minmax(5rem,_8rem)]'
  }

  return 'grid-cols-[minmax(11rem,_1fr)_minmax(8rem,_1fr)_repeat(3,_5.5rem)]'
})

const isFieldDisabled = computed<boolean>(() => {
  return props.isReadonly && !props.isIndascanDraft
})

watch(customerCountryCode, (countryCode) => {
  if (countryCode) {
    wizardFormStep.form.register('customerCountryCode', countryCode)
  }
}, { immediate: true })
</script>

<template>
  <WizardFormStep :wizard-form-step="wizardFormStep">
    <AppGroup
      direction="col"
      align="start"
      gap="3xl"
    >
      <div class="max-h-[calc(100dvh-30rem)] w-full overflow-auto">
        <PickupRequestPackagingHeader
          v-if="customerCountryCode !== null"
          :grid-col-template="gridColTemplate"
          :customer-country-code="customerCountryCode"
          :transport-mode="wizardForm.state.value.transportMode"
          :is-user-from-germany="isUserFromGermany"
          :is-user-from-ireland="isUserFromIreland"
        />
        <PickupRequestPackagingMaterialForm
          v-for="(material, index) in materials.fields.value"
          :key="material"
          :is-readonly="props.isReadonly || props.isIndascanDraft"
          :materials="materials"
          :customer-id="wizardForm.state.value.customer.id"
          :total-quantity-pallets="totalQuantityPallets"
          :index="index"
          :grid-col-template="gridColTemplate"
          :transport-mode="wizardForm.state.value.transportMode"
          :is-user-from-germany="isUserFromGermany"
          :is-user-from-ireland="isUserFromIreland"
        />
      </div>
      <template
        v-if="wizardForm.state.value.transportMode === PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK"
      >
        <div
          class="
            px-xl py-md border-secondary -mt-3xl gap-3xl flex w-fit items-center
            rounded-xl border
          "
        >
          <p class="font-semibold">
            {{ i18n.t('module.pickup_request.update.packaging.fields.total_quantity_pallets') }}
          </p>
          <VcNumberField
            v-bind="toFormField(totalQuantityPallets)"
            :placeholder="i18n.t('module.pickup_request.update.packaging.placeholder.amount')"
            :is-required="true"
            :is-disabled="props.isReadonly || props.isIndascanDraft"
            :hide-controls="true"
            :min="0"
            :max="999"
            :class-config="{
              input: 'w-[5.5rem]',
            }"
          />
        </div>

        <AppGroup
          direction="col"
          align="start"
          class="w-full"
          gap="sm"
        >
          <div class="px-xl py-lg border-secondary rounded-md border">
            <VcCheckbox
              v-bind="toFormField(isReturnPackaging)"
              :label="i18n.t('module.pickup_request.update.packaging.fields.is_return_packaging_description')"
              :is-disabled="isFieldDisabled"
              :variant="props.isReadonly ? 'readonly' : undefined"
            >
              <template #label>
                <AppGroup>
                  <p class="text-secondary text-sm font-medium">
                    {{ i18n.t('module.pickup_request.update.packaging.fields.is_return_packaging') }}
                  </p>
                  <VcTooltip>
                    <template #trigger>
                      <VcIcon
                        class="size-4"
                        icon="infoCircle"
                      />
                    </template>
                    <template #content>
                      <p class="px-xl py-md max-w-96 text-center text-xs">
                        {{ i18n.t('module.pickup_request.update.packaging.fields.is_return_packaging_tooltip') }}
                      </p>
                    </template>
                  </VcTooltip>
                </AppGroup>
              </template>
            </VcCheckbox>
          </div>
        </AppGroup>
        <VcTextField
          :is-disabled="isFieldDisabled"
          v-bind="toFormField(packagingRemark)"
          :is-required="isReturnPackaging.modelValue.value === true"
          :label="i18n.t('module.pickup_request.update.packaging.fields.packaging_remark')"
          :placeholder="i18n.t('module.pickup_request.update.packaging.fields.packaging_remark')"
          :variant="props.isReadonly ? 'readonly' : undefined"
          class="w-full"
        />
      </template>
    </AppGroup>
  </WizardFormStep>
</template>
