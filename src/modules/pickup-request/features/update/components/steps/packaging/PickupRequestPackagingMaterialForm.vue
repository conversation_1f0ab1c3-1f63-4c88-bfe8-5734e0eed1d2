<script setup lang="ts">
import {
  VcCheckbox,
  VcNumberField,
  VcTextField,
} from '@wisemen/vue-core-components'
import type {
  Field,
  FieldArray,
} from 'formango'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import { PickUpTransportMode } from '@/client'
import AppPackagingTypeSelect from '@/components/app/select/AppPackagingTypeSelect.vue'
import AppTankerTypeSelect from '@/components/app/select/AppTankerTypeSelect.vue'
import type { PickupRequestWasteMaterialForm } from '@/models/pickup-request/update/steps/pickupRequestWasteForm.model'
import { toFormField } from '@/utils/formango.util'

const props = defineProps<{
  customerId: string
  isReadonly?: boolean
  isUserFromGermany: boolean
  isUserFromIreland: boolean
  gridColTemplate: string
  index: number
  materials: FieldArray<PickupRequestWasteMaterialForm>
  totalQuantityPallets: Field<number | null>
  transportMode: PickUpTransportMode
}>()

const i18n = useI18n()

const materialName = computed<string>(() => {
  const material = props.materials.value.value[props.index]

  return material.wasteMaterial ?? '-'
})

const materialNumberFormatted = computed<string>(() => {
  const material = props.materials.value.value[props.index]
  const contractItemWithoutLeadingZeros = material.contractItem?.replace(/^0+/, '')

  return `${material.contractNumber} / ${contractItemWithoutLeadingZeros ?? '-'}`
})

const materialNumberWithTCNumber = computed<string>(() => {
  const material = props.materials.value.value[props.index]

  return `${materialNumberFormatted.value} / ${material.tcNumber ?? '-'}`
})

const materialNumberDisplay = computed<string>(() => {
  return props.isUserFromIreland ? materialNumberWithTCNumber.value : materialNumberFormatted.value
})

const materialCustomerReference = computed<string>(() => {
  const material = props.materials.value.value[props.index]

  return material.customerReference ?? '-'
})

const packagingType = props.materials.register(`${props.index}.packagingType`, null)
const quantityLabels = props.materials.register(`${props.index}.quantityLabels`, null)
const quantityPackages = props.materials.register(`${props.index}.quantityPackages`, null)
const quantityPallets = props.materials.register(`${props.index}.quantityPallets`, null)
const containerNumber = props.materials.register(`${props.index}.containerNumber`, null)
const containerVolumeSize = props.materials.register(`${props.index}.containerVolumeSize`, null)
const isContainerCovered = props.materials.register(`${props.index}.isContainerCovered`, null)
const tankerType = props.materials.register(`${props.index}.tankerType`, null)
const reconciliationNumber = props.materials.register(`${props.index}.reconciliationNumber`, null)
const numberOfContainers = props.materials.register(`${props.index}.quantityContainers`, null)

function onPalletsChange(): void {
  const total = props.materials.value.value
    .reduce((sum: number, material: PickupRequestWasteMaterialForm) => sum + (material.quantityPallets || 0), 0)

  if (props.transportMode === PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK
    && (props.totalQuantityPallets.modelValue.value === null || props.totalQuantityPallets.modelValue.value <= total)
  ) {
    props.totalQuantityPallets.setValue(total)
  }
}
</script>

<template>
  <div
    :class="props.gridColTemplate"
    class="
      gap-xl px-xl py-md border-secondary mb-md grid min-w-fit rounded-xl border
    "
  >
    <div>
      <p class="font-semibold">
        {{ materialName }}
      </p>
      <p class="-mt-xs text-secondary min-w-60 text-sm">
        {{ materialNumberDisplay }}
      </p>
      <p class="-mt-xs text-secondary min-w-60 text-sm">
        {{ i18n.t('module.pickup_request.update.details.customer_reference') }}:
        {{ materialCustomerReference ?? '-' }}
      </p>
    </div>

    <VcTextField
      v-if="props.isUserFromGermany"
      v-bind="toFormField(reconciliationNumber)"
      :is-disabled="props.isReadonly"
      :placeholder="i18n.t('module.pickup_request.update.packaging.fields.reconciliation_number')"
    />

    <template v-if="props.transportMode === PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK">
      <AppPackagingTypeSelect
        v-bind="toFormField(packagingType)"
        :customer-id="props.customerId"
        :is-readonly="props.isReadonly"
        :placeholder="i18n.t('module.pickup_request.update.packaging.placeholder.packaging_type')"
      />

      <VcNumberField
        v-bind="toFormField(quantityPackages)"
        :hide-controls="true"
        :min="0"
        :max="999"
        :is-disabled="props.isReadonly"
        :placeholder="i18n.t('module.pickup_request.update.packaging.placeholder.amount')"
      />
      <VcNumberField
        v-bind="toFormField(quantityLabels)"
        :hide-controls="true"
        :min="0"
        :max="999"
        :is-disabled="props.isReadonly"
        :placeholder="i18n.t('module.pickup_request.update.packaging.placeholder.amount')"
      />
      <VcNumberField
        v-bind="toFormField(quantityPallets)"
        :hide-controls="true"
        :min="0"
        :max="999"
        :is-disabled="props.isReadonly"
        :placeholder="i18n.t('module.pickup_request.update.packaging.placeholder.amount')"
        @update:model-value="onPalletsChange"
      />
    </template>

    <template v-if="props.transportMode === PickUpTransportMode.BULK_SKIPS_CONTAINER">
      <VcTextField
        v-bind="toFormField(containerNumber)"
        :is-disabled="props.isReadonly"
        :placeholder="i18n.t('module.pickup_request.update.packaging.placeholder.amount')"
      />
      <VcNumberField
        v-bind="toFormField(numberOfContainers)"
        :hide-controls="true"
        :min="0"
        :max="9"
        :is-disabled="props.isReadonly"
        :placeholder="i18n.t('module.pickup_request.update.packaging.placeholder.amount')"
      />
      <VcTextField
        v-if="props.isUserFromGermany || props.isUserFromIreland"
        v-bind="toFormField(containerVolumeSize)"
        :is-disabled="props.isReadonly"
        :placeholder="i18n.t('module.pickup_request.update.packaging.placeholder.container_volume_size')"
      />
      <div class="flex h-full items-center justify-center">
        <VcCheckbox
          v-bind="toFormField(isContainerCovered as Field<boolean, boolean>)"
          :is-disabled="props.isReadonly"
          :variant="props.isReadonly ? 'readonly' : undefined"
        />
      </div>
    </template>

    <template v-if="props.transportMode === PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS">
      <AppTankerTypeSelect
        v-bind="toFormField(tankerType)"
        :is-readonly="props.isReadonly"
        :label="i18n.t('module.pickup_request.update.packaging.placeholder.tanker_type')"
        :is-label-hidden="true"
      />
      <VcTextField
        v-if="props.isUserFromGermany || props.isUserFromIreland"
        v-bind="toFormField(containerVolumeSize)"
        :is-disabled="props.isReadonly"
        :placeholder="i18n.t('module.pickup_request.update.packaging.placeholder.container_volume_size')"
      />
    </template>

    <template v-if="props.transportMode === PickUpTransportMode.BULK_ISO_TANK">
      <VcTextField
        v-if="props.isUserFromGermany || props.isUserFromIreland"
        v-bind="toFormField(containerVolumeSize)"
        :is-disabled="props.isReadonly"
        :placeholder="i18n.t('module.pickup_request.update.packaging.placeholder.container_volume_size')"
      />
      <VcTextField
        v-bind="toFormField(containerNumber)"
        :is-disabled="props.isReadonly"
        :placeholder="i18n.t('module.pickup_request.update.packaging.placeholder.amount')"
      />
    </template>
  </div>
</template>
