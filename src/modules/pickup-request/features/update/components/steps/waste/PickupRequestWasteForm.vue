<script setup lang="ts">
import {
  VcCheckbox,
  VcFormField,
  VcRadioGroup,
  VcT<PERSON>s,
  Vc<PERSON>absContent,
  VcTabsItem,
} from '@wisemen/vue-core-components'
import {
  computed,
  onMounted,
  ref,
  watch,
} from 'vue'
import { useI18n } from 'vue-i18n'

import type { PickUpAddressResponse } from '@/client'
import {
  Permission,
  PickUpTransportMode,
} from '@/client'
import AppAnimatedNumber from '@/components/app/animated-number/AppAnimatedNumber.vue'
import AppGroup from '@/components/app/AppGroup.vue'
import AppHeightTransition from '@/components/app/AppHeightTransition.vue'
import AppBadge from '@/components/app/badge/AppBadge.vue'
import AppCardWithIcon from '@/components/app/card/AppCardWithIcon.vue'
import FormRadioGroupItem from '@/components/form/FormRadioGroupItem.vue'
import FormRadioGroupLayout from '@/components/form/FormRadioGroupLayout.vue'
import WizardFormStep from '@/components/form/wizard/WizardFormStep.vue'
import type { WizardForm } from '@/composables/wizard-form/wizardForm.composable'
import { useWizardFormStep } from '@/composables/wizard-form/wizardFormStep.composable'
import { BadgeColor } from '@/models/badgeColors.enum'
import type { ContractLineIndex } from '@/models/contract-line/index/contractLineIndex.model'
import { PickupTransportModeEnumUtil } from '@/models/enums/transportMode.enum'
import type {
  PickupRequestWasteForm,
  pickupRequestWasteFormSchema,
} from '@/models/pickup-request/update/steps/pickupRequestWasteForm.model'
import PickupRequestWasteContractLineOverviewTable from '@/modules/pickup-request/features/update/components/steps/waste/PickupRequestWasteContractLineOverviewTable.vue'
import PickupRequestWasteSelectedContractLineTable from '@/modules/pickup-request/features/update/components/steps/waste/PickupRequestWasteSelectedContractLineTable.vue'
import { useCustomerCountryCodeQuery } from '@/modules/waste-inquiry/api/queries/customerCountryCode.query'
import { useAuthStore } from '@/stores/auth.store'
import { toFormField } from '@/utils/formango.util'

type Tab = 'all' | 'selected'

const props = defineProps<{
  isIndascanDraft?: boolean
  isReadonly?: boolean
  wizardForm: WizardForm<typeof pickupRequestWasteFormSchema>
}>()
const TRANSPORT_ISO_AND_VACUUM_LIMIT = 1
const TRANSPORT_BULK_SKIPS_LIMIT = 3

const i18n = useI18n()
const authStore = useAuthStore()

const wizardFormStep = useWizardFormStep(props.wizardForm)

const transportMode = wizardFormStep.form.register('transportMode')
const materials = wizardFormStep.form.register('materials')
const isTransportByIndaver = wizardFormStep.form.register('isTransportByIndaver', true)
const isPackagingAdded = wizardFormStep.form.register('isPackagingAdded', false)

const isPackagingRequestVisible = computed<boolean>(() => {
  return transportMode.value.value === PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK
    && authStore.hasPermission(Permission.PACKAGING_REQUEST_MANAGE)
})

const materialsHasError = computed<boolean>(() => toFormField(materials).errorMessage !== null)

const customerId = computed<string>(() => props.wizardForm.state.value.customer.id)
const wasteProducerId = computed<string>(() => props.wizardForm.state.value.wasteProducer.id)
const pickupAddressIds = computed<string[]>(() => props.wizardForm.state.value.pickupAddresses.map(
  (address: PickUpAddressResponse) => address.id,
))

const customerCountryCodeQuery = useCustomerCountryCodeQuery(props.wizardForm.state.value.customer.id)
const customerCountryCode = customerCountryCodeQuery.data

const selectedContractsLimit = computed<number | null>(() => {
  if (transportMode.value.value === PickUpTransportMode.BULK_ISO_TANK
    || transportMode.value.value === PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS) {
    return TRANSPORT_ISO_AND_VACUUM_LIMIT
  }
  if (transportMode.value.value === PickUpTransportMode.BULK_SKIPS_CONTAINER) {
    return TRANSPORT_BULK_SKIPS_LIMIT
  }

  return null
})

const selectedContractLines = ref<ContractLineIndex[]>(props.wizardForm.state.value.materials)
const selectedContractLinesAmount = computed<number>(() => selectedContractLines.value.length)

const activeTab = ref<Tab>('all')

function setMaterialsValue(): void {
  materials.setValue(selectedContractLines.value.map((item) => {
    return {
      ...item,
      isContainerCovered: item.isContainerCovered ?? null,
      isCostCenterRequired: false,
      isPoNumberRequired: false,
      containerNumber: item.containerNumber ?? null,
      containerTransportType: item.containerTransportType ?? null,
      containerType: item.containerType === null || item.containerType === undefined
        ? null
        : { name: item.containerType },
      containerVolumeSize: null,
      costCenter: item.costCenter ?? null,
      estimatedWeightOrVolumeUnit: item.estimatedWeightOrVolumeUnit ?? null,
      estimatedWeightOrVolumeValue: item.estimatedWeightOrVolumeValue ?? null,
      hazardInducers: item.hazardInducers ?? null,
      packagingType: item.packagingType === null || item.packagingType === undefined
        ? null
        : { name: item.packagingType },
      poNumber: item.poNumber ?? null,
      position: item.position ?? null,
      quantityContainers: null,
      quantityLabels: item.quantityLabels ?? null,
      quantityPackages: item.quantityPackages ?? null,
      quantityPallets: item.quantityPallets ?? null,
      reconciliationNumber: item.reconciliationNumber,
      // Backend request: prefill serialNumber with '0000' when adding a contract line
      serialNumber: '0000',
      tankerType: item.tankerType ?? null,
      tfsNumber: null,
      unNumber: (item.unNumber === null || item.unNumber === undefined)
        ? null
        : {
            isHazardous: null,
            dangerLabel1: null,
            dangerLabel2: null,
            dangerLabel3: null,
            description: null,
            number: item.unNumber,
            packingGroup: null,
          },
    }
  }))

  if (selectedContractLines.value.length === 0) {
    activeTab.value = 'all'
  }
}

function onAdd(item: ContractLineIndex): void {
  const matchingItems = selectedContractLines.value.filter((selectedItem) =>
    selectedItem.contractLineId === item.contractLineId)

  if (matchingItems.length > 0) {
    for (const matchingItem of matchingItems) {
      matchingItem.amount = (matchingItem.amount ?? 0) + 1
    }

    selectedContractLines.value.push({
      ...item,
      amount: matchingItems[0].amount,
    })
  }
  else {
    selectedContractLines.value.push({
      ...item,
      amount: 1,
    })
  }

  setMaterialsValue()
}

function onRemove(item: ContractLineIndex): void {
  const matchingItems = selectedContractLines.value.filter((selectedItem) =>
    selectedItem.contractLineId === item.contractLineId)

  if (!matchingItems) {
    return
  }

  const index = selectedContractLines.value.findIndex(
    (selectedItem) => selectedItem.contractLineId === item.contractLineId,
  )

  if (index !== -1) {
    selectedContractLines.value.splice(index, 1)
  }

  for (const existingItem of matchingItems) {
    if (existingItem.amount !== null && existingItem.amount > 1) {
      existingItem.amount = (existingItem.amount ?? 0) - 1
    }
    else {
      selectedContractLines.value = selectedContractLines.value.filter(
        (selectedItem) => selectedItem.contractLineId !== item.contractLineId,
      )
    }
  }

  setMaterialsValue()
}

function initializeMaterials(): void {
  const materialsData = props.wizardForm.state.value.materials as Array<PickupRequestWasteForm['materials'][number]>

  const materialCounts = materialsData.reduce<Record<string, number>>((acc, material) => {
    const key = material.contractLineId

    acc[key] = (acc[key] || 0) + 1

    return acc
  }, {})

  const pickupMaterials = materialsData.map((material) => ({
    ...material,
    isContainerCovered: material.isContainerCovered ?? null,
    amount: materialCounts[material.contractLineId],
    containerNumber: material.containerNumber ?? null,
    containerTransportType: material.containerTransportType ?? null,
    containerType: material.containerType ?? null,
    costCenter: material.costCenter ?? null,
    estimatedWeightOrVolumeUnit: material.estimatedWeightOrVolumeUnit ?? null,
    estimatedWeightOrVolumeValue: material.estimatedWeightOrVolumeValue ?? null,
    hazardInducers: null,
    packagingType: material.packagingType ?? null,
    poNumber: material.poNumber ?? null,
    quantityLabels: material.quantityLabels ?? null,
    quantityPackages: material.quantityPackages ?? null,
    quantityPallets: material.quantityPallets ?? null,
    tankerType: material.tankerType ?? null,
    unNumber: material.unNumber ?? null,
  }))

  materials.setValue(pickupMaterials)

  selectedContractLines.value = pickupMaterials.map((item) => {
    return {
      ...item,
      amount: item.amount ?? null,
      containerType: item.containerType?.name ?? null,
      hazardInducers: item.hazardInducers ?? null,
      packagingType: item.packagingType?.name ?? null,
      unNumber: item.unNumber?.number ?? null,
    }
  })
}

function onUpdatedTransportMode(value: PickUpTransportMode | null): void {
  if (value !== PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK) {
    isPackagingAdded.setValue(false)
  }
}

onMounted(() => {
  initializeMaterials()
})

watch(customerCountryCode, (countryCode) => {
  if (countryCode) {
    wizardFormStep.form.register('customerCountryCode', countryCode)
  }
}, { immediate: true })
</script>

<template>
  <WizardFormStep
    :wizard-form-step="wizardFormStep"
    class="!overflow-visible"
  >
    <AppGroup
      direction="col"
      gap="3xl"
      align="start"
    >
      <VcRadioGroup
        :is-required="true"
        :label="i18n.t('module.pickup_request.update.waste.transport_mode')"
        class="w-full"
        v-bind="toFormField(transportMode)"
        @update:model-value="onUpdatedTransportMode"
      >
        <FormRadioGroupLayout :cols="props.isReadonly || props.isIndascanDraft ? 1 : 2">
          <template v-if="!props.isReadonly && !props.isIndascanDraft">
            <FormRadioGroupItem
              v-for="item of Object.values(PickUpTransportMode)"
              :key="item"
              :value="item"
            >
              <AppCardWithIcon
                :title="i18n.t(PickupTransportModeEnumUtil.getLabelI18nKey(item))"
                :description="i18n.t(PickupTransportModeEnumUtil.getDescriptionI18nKey(item))"
                :is-selected="item === transportMode.value.value"
                :icon="PickupTransportModeEnumUtil.getIcon(item)"
                variant="gray-light"
              />
            </FormRadioGroupItem>
          </template>
          <AppCardWithIcon
            v-else-if="transportMode.modelValue.value !== null"
            :title="i18n.t(PickupTransportModeEnumUtil.getLabelI18nKey(transportMode.modelValue.value))"
            :description="i18n.t(PickupTransportModeEnumUtil.getDescriptionI18nKey(transportMode.modelValue.value))"
            :is-selected="false"
            icon="truck"
            variant="gray-light"
          />
        </FormRadioGroupLayout>
      </VcRadioGroup>

      <AppHeightTransition
        :duration="300"
        class="w-full !overflow-visible"
      >
        <Transition
          enter-active-class="duration-300"
          leave-active-class="duration-300"
          mode="out-in"
          enter-from-class="opacity-0"
          leave-to-class="opacity-0"
        >
          <div v-if="transportMode.modelValue.value !== null">
            <div class="px-xl py-lg border-secondary mb-lg rounded-md border">
              <VcCheckbox
                v-bind="toFormField(isTransportByIndaver)"
                :label="i18n.t('module.pickup_request.update.waste.transport_by_indaver')"
                :is-disabled="props.isReadonly || props.isIndascanDraft"
                :variant="props.isReadonly ? 'readonly' : undefined"
              />
            </div>
            <AppHeightTransition>
              <div
                v-if="isPackagingRequestVisible"
                class="px-xl py-lg border-secondary rounded-md border"
              >
                <VcCheckbox
                  v-bind="toFormField(isPackagingAdded)"
                  :label="i18n.t('module.pickup_request.update.waste.packaging_added')"
                  :is-disabled="props.isReadonly"
                  :variant="props.isReadonly ? 'readonly' : undefined"
                />
              </div>
            </AppHeightTransition>

            <VcFormField
              :label="i18n.t('module.pickup_request.update.waste.materials.title')"
              :is-required="true"
              :error-message="materialsHasError ? toFormField(materials).errorMessage : null"
              :is-touched="true"
              :class-config="{
                error: 'mb-xl',
              }"
              class="mt-3xl pb-md bg-primary sticky top-0 z-10"
              for="materials"
            />

            <VcTabs
              v-if="!props.isReadonly && !props.isIndascanDraft"
              v-model="activeTab"
              variant="bordered"
            >
              <template #items>
                <VcTabsItem value="all">
                  <AppGroup
                    justify="center"
                    class="mx-auto size-full"
                  >
                    {{ i18n.t('module.pickup_request.update.waste.materials.all') }}
                  </AppGroup>
                </VcTabsItem>

                <VcTabsItem
                  value="selected"
                  class="!-ml-px"
                >
                  <AppGroup
                    justify="center"
                    class="mx-auto size-full"
                  >
                    {{ i18n.t('module.pickup_request.update.waste.materials.selected') }}

                    <AppBadge
                      :color="materialsHasError
                        ? BadgeColor.ERROR : activeTab === 'selected'
                          ? BadgeColor.SUCCESS : BadgeColor.NEUTRAL"
                      class="font-regular"
                    >
                      <AppAnimatedNumber :value="selectedContractLinesAmount" />
                    </AppBadge>
                  </AppGroup>
                </VcTabsItem>
              </template>

              <template #content>
                <VcTabsContent
                  value="all"
                  class="focus-visible:ring-0 focus-visible:ring-offset-0"
                >
                  <PickupRequestWasteContractLineOverviewTable
                    :customer-id="customerId"
                    :waste-producer-id="wasteProducerId"
                    :pickup-address-ids="pickupAddressIds"
                    :selected-items="selectedContractLines"
                    :selected-items-amount="selectedContractLinesAmount"
                    :select-limit="selectedContractsLimit"
                    @add="onAdd"
                    @remove="onRemove"
                  />
                </VcTabsContent>
                <VcTabsContent
                  value="selected"
                  class="focus-visible:ring-0 focus-visible:ring-offset-0"
                >
                  <PickupRequestWasteSelectedContractLineTable
                    :selected-items="selectedContractLines"
                    :selected-items-amount="selectedContractLinesAmount"
                    :select-limit="selectedContractsLimit"
                    @add="onAdd"
                    @remove="onRemove"
                  />
                </VcTabsContent>
              </template>
            </VcTabs>
            <PickupRequestWasteSelectedContractLineTable
              v-else
              :selected-items="selectedContractLines"
              :selected-items-amount="selectedContractLinesAmount"
              :select-limit="selectedContractsLimit"
            />
          </div>
        </Transition>
      </AppHeightTransition>
    </AppGroup>
  </WizardFormStep>
</template>
