<script setup lang="ts">
import type { TableColumn } from '@wisemen/vue-core-components'
import type { VNode } from 'vue'
import {
  computed,
  h,
} from 'vue'
import { useI18n } from 'vue-i18n'

import DataTable from '@/components/table/data-table/DataTable.vue'
import DataTableCell from '@/components/table/data-table/DataTableCell.vue'
import type { ContractLineIndex } from '@/models/contract-line/index/contractLineIndex.model'
import PickupRequestWasteContractLineTableCountCell from '@/modules/pickup-request/features/update/components/steps/waste/PickupRequestWasteContractLineTableCountCell.vue'

const props = defineProps<{
  selectedItems: ContractLineIndex[]
  selectedItemsAmount: number
  selectLimit?: number | null
}>()

const emit = defineEmits<{
  add: [item: ContractLineIndex]
  remove: [item: ContractLineIndex]
}>()

const i18n = useI18n()

const columns = computed<TableColumn<ContractLineIndex>[]>(() => [
  {
    cell: (row): VNode => {
      const amountInSelectedItems = props.selectedItems?.filter((item) =>
        item.contractLineId === row.contractLineId)[0]?.amount ?? 0

      return h(
        PickupRequestWasteContractLineTableCountCell,
        {
          isCountDisabled: false,
          amount: row.amount ?? amountInSelectedItems,
          limit: props.selectLimit ?? null,
          total: props.selectedItemsAmount ?? 0,
          onAdd: () => onAdd(row),
          onRemove: () => onRemove(row),
        },
      )
    },
    headerLabel: '',
    key: 'amount',
    maxWidth: '5.5rem',
  },
  {
    cell: (row): VNode => h(DataTableCell, () => row.wasteMaterial ?? '-'),
    headerLabel: i18n.t('enum.dynamic_table_column_name.waste_material'),
    key: 'wasteMaterial',
  },
  {
    cell: (row): VNode => h(DataTableCell, () => row.customerReference ?? '-'),
    headerLabel: i18n.t('enum.dynamic_table_column_name.customer_reference'),
    key: 'customerReference',
  },
  {
    cell: (row): VNode => h(DataTableCell, () => row.ewcCode ?? '-'),
    headerLabel: i18n.t('enum.dynamic_table_column_name.ewc_code'),
    key: 'ewcCode',
  },
])

const data = computed<ContractLineIndex[]>(() => {
  return props.selectedItems
    .filter((item, index, self) => index === self.findIndex((t) => t.contractLineId === item.contractLineId))
    .sort((a, b) => (a.wasteMaterial ?? '').localeCompare(b.wasteMaterial ?? ''))
})

function onAdd(item: ContractLineIndex): void {
  emit('add', item)
}

function onRemove(item: ContractLineIndex): void {
  emit('remove', item)
}
</script>

<template>
  <DataTable
    :columns="columns"
    :data="data"
    :is-loading="false"
    :error="null"
    :get-key="(item) => item.contractLineId"
    :on-next-page="() => {}"
    class="max-h-[calc(100vh-22rem)] !rounded-tl-none"
  />
</template>
