<script setup lang="ts">
import type { VNode } from 'vue'
import {
  computed,
  h,
  ref,
} from 'vue'
import { useI18n } from 'vue-i18n'

import AppGroup from '@/components/app/AppGroup.vue'
import FiltersActive from '@/components/filters/active/FiltersActive.vue'
import { createAutocompleteFilter } from '@/components/filters/createFilters'
import FiltersDropdownMenu from '@/components/filters/dropdown-menu/FiltersDropdownMenu.vue'
import { useFilters } from '@/components/filters/filters.composable'
import FiltersRoot from '@/components/filters/FiltersRoot.vue'
import DataTable from '@/components/table/data-table/DataTable.vue'
import DynamicTableSettings from '@/components/table/dynamic-table-settings/DynamicTableSettings.vue'
import DynamicTableViews from '@/components/table/dynamic-table-views/DynamicTableViews.vue'
import { useCustomBooleanFilter } from '@/composables/custom-boolean-filter/customBooleanFilter.composable'
import { useDynamicTableV2 } from '@/composables/dynamic-table/dynamicTableV2.composable'
import { useEwcFilter } from '@/composables/ewc-filter/ewcFilter.composable'
import { useSearch } from '@/composables/search/search.composable'
import { useSearchableTableColumns } from '@/composables/searchable-table-columns/searchableTableColumns.composable'
import { useSort } from '@/composables/sort/sort.composable'
import { useGenericColumn } from '@/composables/table-columns/genericTableColumnsV2.composable'
import type { ContractLineIndex } from '@/models/contract-line/index/contractLineIndex.model'
import type { ContractLineIndexQueryParams } from '@/models/contract-line/index/contractLineIndexQueryParams.model'
import { DynamicTableName } from '@/models/enums/dynamicTableName.enum'
import type { WasteProducerIndexQueryParams } from '@/models/waste-producer/index/wasteProducerIndexQueryParams.model'
import { useContractLineIndexQuery } from '@/modules/contract-line/api/queries/contractLineIndex.query'
import PickupRequestWasteContractLineTableCountCell from '@/modules/pickup-request/features/update/components/steps/waste/PickupRequestWasteContractLineTableCountCell.vue'
import { useCustomerIndexQuery } from '@/modules/waste-inquiry/api/queries/customerIndex.query'
import { useWasteProducerIndexQuery } from '@/modules/waste-inquiry/api/queries/wasteProducerIndex.query'
import type { DataTableColumn } from '@/types/table.type'

const props = defineProps<{
  customerId: string
  wasteProducerId?: string
  isCountDisabled?: boolean
  pickUpAddressIds?: string[]
  selectedItems?: ContractLineIndex[]
  selectedItemsAmount?: number
  selectLimit?: number | null
  showCount?: boolean
  showDynamicViews?: boolean
  showFilters?: boolean
  showSelectedOnly?: boolean
}>()

const emit = defineEmits<{
  add: [item: ContractLineIndex]
  remove: [item: ContractLineIndex]
}>()

const i18n = useI18n()

// Search and sort
const search = useSearch({ persistInUrl: false })

const searchableColumns = useSearchableTableColumns({
  keys: [
    'asn',
    'contractNumber',
    'contractItem',
    'customerReference',
    'deliveryInfo',
    'endTreatmentCenterName',
    'esnNumber',
    'installationName',
    'materialAnalysis',
    'materialNumber',
    'materialDescription',
    'processCode',
    'tcNumber',
    'treatmentCenterName',
    'wasteProducerName',
    'wasteMaterial',
  ],
})

const sort = useSort({
  keys: [
    'asn',
    'contractItem',
    'contractNumber',
    'endTreatmentCenterId',
    'endTreatmentCenterName',
    'esnNumber',
    'ewcCode',
    'installationName',
    'pickUpAddressId',
    'pickUpAddressName',
    'processCode',
    'tcNumber',
    'treatmentCenterName',
    'wasteMaterial',
  ],
  persistInUrl: false,
})

// Filters
const customerIndexSearch = useSearch({ persistInUrl: false })
const customerIndexQuery = useCustomerIndexQuery({ params: { search: customerIndexSearch.debouncedSearch } })

const isHazardousFilter = useCustomBooleanFilter(
  'isHazardous',
  i18n.t('enum.dynamic_table_column_name.is_hazardous'),
)

const tfsFilter = useCustomBooleanFilter(
  'tfs',
  i18n.t('enum.dynamic_table_column_name.tfs_number'),
)

const wasteProducerIndexSearch = ref<string>('')
const wasteProducerIndexFilters = ref<WasteProducerIndexQueryParams['filters']>({})

const wasteProducerIndexQuery = useWasteProducerIndexQuery({
  params: {
    filters: wasteProducerIndexFilters,
    search: wasteProducerIndexSearch,
  },
})

const ewcFilter = useEwcFilter()

const filters = useFilters({
  filterGroups: () => [
    {
      filters: [
        createAutocompleteFilter({
          isLoading: customerIndexQuery.isLoading.value,
          defaultValue: null,
          displayFn: (option) => option.name,
          key: 'customerId',
          label: i18n.t('enum.dynamic_table_column_name.customer_name'),
          options: customerIndexQuery.data.value?.data.map((customer) => ({
            id: customer.id,
            name: customer.name,
          })) ?? [],
          onSearch: (value) => {
            customerIndexSearch.updateSearch(value)
          },
        }),
        createAutocompleteFilter({
          isLoading: wasteProducerIndexQuery.isLoading.value,
          defaultValue: null,
          displayFn: (option) => option.name,
          key: 'wasteProducerId',
          label: i18n.t('enum.dynamic_table_column_name.waste_producer'),
          options: wasteProducerIndexQuery.data.value?.data.map((wp) => ({
            id: wp.id,
            name: wp.name,
          })) ?? [],
          onSearch: (value) => {
            wasteProducerIndexSearch.value = value
          },
        }),
        ewcFilter,
      ],
    },
    {
      filters: [
        tfsFilter,
        isHazardousFilter,
      ],
    },
  ],
  persistInUrl: false,
})

// Dynamic table
const dynamicTable = useDynamicTableV2({
  dynamicTableName: DynamicTableName.CONTRACT_LINE,
  filters,
  sort,
})

// Columns
const columns = computed<DataTableColumn<ContractLineIndex>[]>(() => {
  const dynamicColumns = dynamicTable.columns.value
    .filter((column) => column.isVisible)
    .map((column) => {
      const columnFn = useGenericColumn

      return columnFn(column.name, column.label) as DataTableColumn<ContractLineIndex>
    })

  const tableColumns: DataTableColumn<ContractLineIndex>[] = [
    ...dynamicColumns,
  ]

  if (props.showCount) {
    tableColumns.unshift({
      cell: (row: ContractLineIndex): VNode => {
        const amountInSelectedItems = props.selectedItems?.filter((item) =>
          item.contractLineId === row.contractLineId)[0]?.amount ?? 0

        return h(
          PickupRequestWasteContractLineTableCountCell,
          {
            isCountDisabled: props.isCountDisabled ?? false,
            amount: row.amount ?? amountInSelectedItems,
            limit: props.selectLimit ?? null,
            total: props.selectedItemsAmount ?? 0,
            onAdd: () => onAdd(row),
            onRemove: () => onRemove(row),
          },
        )
      },
      header: () => h('span', ''),
      key: 'amount',
      width: '5.5rem',
    })
  }

  return tableColumns
})

// Data query
const contractLineIndexQuery = useContractLineIndexQuery({
  params: {
    filters: computed<ContractLineIndexQueryParams['filters']>(() => ({
      ...filters.values.value,
      ...searchableColumns.values.value,
      customerId: props.customerId
        ? {
            id: props.customerId,
            name: '',
          }
        : null,
      wasteProducerId: props.wasteProducerId
        ? {
            id: props.wasteProducerId,
            name: '',
          }
        : null,
      pickUpAddressIds: props.pickUpAddressIds?.map((id) => ({
        id,
        name: '',
      })) ?? undefined,
    })),
    search: search.debouncedSearch,
    sort: sort.values,
  },
})

function onAdd(item: ContractLineIndex): void {
  emit('add', item)
}

function onRemove(item: ContractLineIndex): void {
  emit('remove', item)
}
</script>

<template>
  <DataTable
    :is-loading="contractLineIndexQuery.isLoading.value"
    :data="contractLineIndexQuery.data.value.data"
    :columns="columns"
    :error="contractLineIndexQuery.error.value"
    :searchable-columns="props.showFilters ? searchableColumns : null"
    :get-key="(_, index) => `${index}`"
    :filters="props.showFilters ? filters : null"
    :sort="sort"
    :search="search"
    :disable-top-left-border-radius="props.showFilters || props.showDynamicViews"
    @next-page="contractLineIndexQuery.fetchNextPage()"
  >
    <template #top>
      <template v-if="props.showFilters || props.showDynamicViews">
        <FiltersRoot :filters="filters">
          <AppGroup
            justify="between"
            class="px-xl h-14"
          >
            <FiltersActive />

            <AppGroup>
              <DynamicTableViews
                v-if="props.showDynamicViews"
                :dynamic-table="dynamicTable"
              />
              <FiltersDropdownMenu v-if="props.showFilters" />
              <DynamicTableSettings
                v-if="props.showDynamicViews"
                :dynamic-table="dynamicTable"
              />
            </AppGroup>
          </AppGroup>
        </FiltersRoot>
      </template>
    </template>
  </DataTable>
</template>
