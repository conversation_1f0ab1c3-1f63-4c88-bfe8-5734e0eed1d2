<script setup lang="ts">
import type { VNode } from 'vue'
import {
  computed,
  h,
} from 'vue'
import { useI18n } from 'vue-i18n'

import { DynamicColumnNames } from '@/client/types.gen'
import AppGroup from '@/components/app/AppGroup.vue'
import FiltersActive from '@/components/filters/active/FiltersActive.vue'
import FiltersDropdownMenu from '@/components/filters/dropdown-menu/FiltersDropdownMenu.vue'
import { useFilters } from '@/components/filters/filters.composable'
import FiltersRoot from '@/components/filters/FiltersRoot.vue'
import DataTable from '@/components/table/data-table/DataTable.vue'
import DynamicTableSettings from '@/components/table/dynamic-table-settings/DynamicTableSettings.vue'
import DynamicTableViews from '@/components/table/dynamic-table-views/DynamicTableViews.vue'
import { useCustomBooleanFilter } from '@/composables/custom-boolean-filter/customBooleanFilter.composable'
import { useDynamicTableV2 } from '@/composables/dynamic-table/dynamicTableV2.composable'
import { useEwcFilter } from '@/composables/ewc-filter/ewcFilter.composable'
import { useSearch } from '@/composables/search/search.composable'
import { useSearchableTableColumns } from '@/composables/searchable-table-columns/searchableTableColumns.composable'
import { useSort } from '@/composables/sort/sort.composable'
import { useGenericColumn } from '@/composables/table-columns/genericTableColumnsV2.composable'
import type { ContractLineIndex } from '@/models/contract-line/index/contractLineIndex.model'
import type { ContractLineIndexQueryParams } from '@/models/contract-line/index/contractLineIndexQueryParams.model'
import { ContractLinePackagingTypeEnumUtil } from '@/models/enums/contractLinePackagingType.enum'
import { DynamicTableName } from '@/models/enums/dynamicTableName.enum'
import { useContractLineIndexQuery } from '@/modules/contract-line/api/queries/contractLineIndex.query'
import PickupRequestWasteContractLineTableCountCell from '@/modules/pickup-request/features/update/components/steps/waste/PickupRequestWasteContractLineTableCountCell.vue'
import type { DataTableColumn } from '@/types/table.type'

const props = defineProps<{
  customerId: string
  wasteProducerId: string
  pickupAddressIds: string[]
  selectedItems: ContractLineIndex[]
  selectedItemsAmount: number
  selectLimit?: number | null
}>()

const emit = defineEmits<{
  add: [item: ContractLineIndex]
  remove: [item: ContractLineIndex]
}>()

const i18n = useI18n()

const search = useSearch({ persistInUrl: false })

const searchableColumns = useSearchableTableColumns({
  keys: [
    'asn',
    'contractNumber',
    'contractItem',
    'customerReference',
    'deliveryInfo',
    'endTreatmentCenterName',
    'esnNumber',
    'installationName',
    'materialAnalysis',
    'materialNumber',
    'materialDescription',
    'processCode',
    'tcNumber',
    'treatmentCenterName',
    'wasteProducerName',
    'wasteMaterial',
  ],
})

const sort = useSort({
  keys: [
    'asn',
    'contractItem',
    'contractNumber',
    'endTreatmentCenterId',
    'endTreatmentCenterName',
    'esnNumber',
    'ewcCode',
    'installationName',
    'pickUpAddressId',
    'pickUpAddressName',
    'processCode',
    'tcNumber',
    'treatmentCenterName',
    'wasteMaterial',
    'wasteProducerId',
    'wasteProducerName',
  ],
  persistInUrl: false,
})

const isHazardousFilter = useCustomBooleanFilter(
  'isHazardous',
  i18n.t('enum.dynamic_table_column_name.is_hazardous'),
)

const tfsFilter = useCustomBooleanFilter(
  'tfs',
  i18n.t('enum.dynamic_table_column_name.tfs_number'),
)

const ewcFilter = useEwcFilter()

const filters = useFilters({
  filterGroups: () => [
    {
      filters: [
        ewcFilter,
      ],
    },
    {
      filters: [
        tfsFilter,
        isHazardousFilter,
      ],
    },
  ],
  persistInUrl: false,
})

const dynamicTable = useDynamicTableV2({
  dynamicTableName: DynamicTableName.CONTRACT_LINE,
  filters,
  sort,
})

const columns = computed<DataTableColumn<ContractLineIndex>[]>(() => {
  const dynamicColumns = dynamicTable.columns.value
    .filter((column) => column.isVisible)
    .map((column) => {
      switch (column.name) {
        case DynamicColumnNames.PACKAGED:
          return useGenericColumn(
            column.name,
            column.label,
            (value: ContractLineIndex) => value.packaged === null
              ? null
              : i18n.t(ContractLinePackagingTypeEnumUtil.getI18nKey(value.packaged)),
          )
        case DynamicColumnNames.PICK_UP_ADDRESS_ID:
          return useGenericColumn(
            column.name,
            column.label,
            (value: ContractLineIndex) => value.pickupAddressId,
          )
        case DynamicColumnNames.PICK_UP_ADDRESS_NAME:
          return useGenericColumn(
            column.name,
            column.label,
            (value: ContractLineIndex) => value.pickupAddressName,
          )
        default:
          return useGenericColumn(column.name, column.label)
      }
    })

  return [
    {
      cell: (row): VNode => {
        const amountInSelectedItems = props.selectedItems?.filter((item) =>
          item.contractLineId === row.contractLineId)[0]?.amount ?? 0

        return h(
          PickupRequestWasteContractLineTableCountCell,
          {
            isCountDisabled: false,
            amount: row.amount ?? amountInSelectedItems,
            limit: props.selectLimit ?? null,
            total: props.selectedItemsAmount ?? 0,
            onAdd: () => onAdd(row),
            onRemove: () => onRemove(row),
          },
        )
      },
      headerLabel: '',
      key: 'amount',
    },
    ...dynamicColumns,
  ] as DataTableColumn<ContractLineIndex>[]
})

const contractLineIndexQuery = useContractLineIndexQuery({
  params: {
    filters: computed<ContractLineIndexQueryParams['filters']>(() => ({
      ...filters.values.value,
      ...searchableColumns.values.value,
      customerId: {
        id: props.customerId,
        name: '',
      },
      wasteProducerId: {
        id: props.wasteProducerId,
        name: '',
      },
      pickUpAddressIds: props.pickupAddressIds.map((id) => ({
        id,
        name: '',
      })),
    })),
    search: search.debouncedSearch,
    sort: sort.values,
  },
})

function onAdd(item: ContractLineIndex): void {
  emit('add', item)
}

function onRemove(item: ContractLineIndex): void {
  emit('remove', item)
}

async function fetchNextPage(): Promise<void> {
  await contractLineIndexQuery.fetchNextPage()
}
</script>

<template>
  <DataTable
    :is-loading="contractLineIndexQuery.isLoading.value"
    :data="contractLineIndexQuery.data.value.data"
    :columns="columns"
    :error="contractLineIndexQuery.error.value"
    :searchable-columns="searchableColumns"
    :get-key="(_, index) => `${index}`"
    :filters="filters"
    :sort="sort"
    :search="search"
    :is-first-column-sticky="true"
    :disable-top-left-border-radius="true"
    class="max-h-[calc(100vh-22rem)]"
    @next-page="fetchNextPage"
  >
    <template #top>
      <FiltersRoot :filters="filters">
        <AppGroup
          justify="between"
          class="px-xl h-14"
        >
          <FiltersActive />

          <AppGroup>
            <DynamicTableViews :dynamic-table="dynamicTable" />
            <FiltersDropdownMenu />
            <DynamicTableSettings :dynamic-table="dynamicTable" />
          </AppGroup>
        </AppGroup>
      </FiltersRoot>
    </template>
  </DataTable>
</template>
