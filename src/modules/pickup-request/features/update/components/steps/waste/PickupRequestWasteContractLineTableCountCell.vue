<script setup lang="ts">
import {
  VcIconButton,
  VcTooltip,
} from '@wisemen/vue-core-components'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import AppAnimatedNumber from '@/components/app/animated-number/AppAnimatedNumber.vue'
import AppGroup from '@/components/app/AppGroup.vue'
import DataTableCell from '@/components/table/data-table/DataTableCell.vue'

const props = defineProps<{
  isCountDisabled: boolean
  amount: number
  limit: number | null
  total: number
}>()

const emit = defineEmits<{
  'add': []
  'remove': []
  'update:amount': [value: number]
}>()

const i18n = useI18n()

const amount = computed<number>({
  get: () => props.amount,
  set: (value: number) => {
    emit('update:amount', value)
  },
})

const isAddButtonDisabled = computed<boolean>(() => {
  if (props.isCountDisabled) {
    return true
  }

  return props.limit !== null && props.total >= props.limit
})

const isRemoveButtonDisabled = computed<boolean>(() => {
  if (props.isCountDisabled) {
    return true
  }

  return amount.value === 0
})

function onAdd(): void {
  amount.value++
  emit('add')
}

function onRemove(): void {
  amount.value--
  emit('remove')
}
</script>

<template>
  <DataTableCell>
    <AppGroup>
      <VcIconButton
        :class-config="{
          root: 'min-w-6 w-6 h-6',
          icon: 'size-4',
        }"
        :is-disabled="isRemoveButtonDisabled"
        :label="i18n.t('shared.minus')"
        icon="minus"
        variant="secondary"
        @click="onRemove"
      />
      <span
        :class="{
          'text-placeholder': props.amount === 0,
          'text-primary font-semibold': props.amount > 0,
          '!text-error-primary': props.limit !== null && props.total > props.limit,
        }"
        class="w-3 text-center text-sm"
      >
        <AppAnimatedNumber :value="amount" />
      </span>
      <VcTooltip
        :is-disabled="props.limit === null || props.total < props.limit"
      >
        <template #trigger>
          <VcIconButton
            :is-disabled="isAddButtonDisabled"
            :label="i18n.t('shared.plus')"
            :class-config="{
              root: 'min-w-6 w-6 h-6',
              icon: 'size-4',
            }"
            icon="plus"
            variant="secondary"
            @click="onAdd"
          />
        </template>
        <template #content>
          <p class="mx-lg my-md w- text-xs">
            {{ i18n.t('module.pickup_request.update.waste.contract_line.max_selection_reached') }}
          </p>
        </template>
      </VcTooltip>
    </AppGroup>
  </DataTableCell>
</template>
