<script setup lang="ts">
import type {
  PaginatedData,
  TableColumn,
} from '@wisemen/vue-core-components'
import {
  usePagination,
  VcIconButton,
} from '@wisemen/vue-core-components'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import type { WasteMeasurementUnit } from '@/client'
import AppAnimateHeight from '@/components/animate-height/AppAnimateHeight.vue'
import AppGroup from '@/components/app/AppGroup.vue'
import AppTable from '@/components/app/table/AppTable.vue'
import AppTableDynamicViewsDropdown from '@/components/app/table/AppTableDynamicViewsDropdown.vue'
import TableSettingsPopover from '@/components/table/TableSettingsPopover.vue'
import type { DynamicTable } from '@/composables/dynamic-table/dynamicTable.composable'
import type { DynamicTableViewIndex } from '@/models/dynamic-table/view/dynamicTableViewIndex.model'
import type { PickupRequestWasteMaterialForm } from '@/models/pickup-request/update/steps/pickupRequestWasteForm.model'
import PickupRequestDetailsFormUnitPopover from '@/modules/pickup-request/features/update/components/steps/details/PickupRequestDetailsFormUnitPopover.vue'

const props = defineProps<{
  hasActiveViewBeenUpdated: boolean
  isDynamicTableLoading: boolean
  activeView: DynamicTableViewIndex | null
  columns: TableColumn<PickupRequestWasteMaterialForm>[]
  dynamicTable: DynamicTable
  dynamicTableColumns: any
  dynamicTableViews: DynamicTableViewIndex[]
  tableData: PaginatedData<PickupRequestWasteMaterialForm>
  onViewsGetNextPage: () => Promise<void>
}>()

const emit = defineEmits<{
  'expand': []
  'update:activeView': [view: DynamicTableViewIndex | null]
  'updatePreferredEstimatedWeightVolumeUnit': [unit: WasteMeasurementUnit | null]
}>()

const i18n = useI18n()

const pagination = usePagination<any>({
  isRouteQueryEnabled: false,
  type: 'keyset',
})

const activeViewModel = computed<DynamicTableViewIndex | null>({
  get: () => props.activeView,
  set: (value) => emit('update:activeView', value),
})

function toggleTableExpansion(): void {
  emit('expand')
}

function onUpdatePreferredEstimatedWeightVolumeUnit(unit: WasteMeasurementUnit | null): void {
  emit('updatePreferredEstimatedWeightVolumeUnit', unit)
}
</script>

<template>
  <AppTable
    v-if="dynamicTableColumns.data.value && activeViewModel"
    :columns="columns"
    :data="tableData"
    :is-loading="isDynamicTableLoading"
    :pagination="pagination"
    :is-first-column-sticky="true"
    :is-table-results-hint-hidden="true"
    class="max-h-[calc(100vh-22rem)]"
  >
    <template #top>
      <AppAnimateHeight class="!overflow-visible">
        <AppGroup
          justify="between"
          class="px-3xl py-md"
        >
          <VcIconButton
            :label="i18n.t('shared.expand')"
            icon="expand"
            variant="secondary"
            @click="toggleTableExpansion"
          />
          <AppGroup>
            <PickupRequestDetailsFormUnitPopover
              @update="onUpdatePreferredEstimatedWeightVolumeUnit"
            />
            <AppTableDynamicViewsDropdown
              v-model:active-view="activeViewModel"
              :dynamic-table="dynamicTable"
              :is-updating-active-view="dynamicTable.isUpdateViewLoading.value"
              :table-views="dynamicTableViews"
              :has-active-view-been-updated="hasActiveViewBeenUpdated"
              :is-add-button-disabled="!hasActiveViewBeenUpdated"
              :on-views-get-next-page="onViewsGetNextPage"
            />
            <TableSettingsPopover
              :dynamic-table="dynamicTable"
              :table-views="dynamicTableViews"
              :has-active-view-been-updated="hasActiveViewBeenUpdated"
              :active-view="activeView"
            />
          </AppGroup>
        </AppGroup>
      </AppAnimateHeight>
    </template>
  </AppTable>
</template>
