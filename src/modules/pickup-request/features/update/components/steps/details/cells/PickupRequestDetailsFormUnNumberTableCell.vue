<script setup lang="ts">
import { VcAutocomplete } from '@wisemen/vue-core-components'
import type { FieldArray } from 'formango'
import {
  computed,
  ref,
} from 'vue'
import { useI18n } from 'vue-i18n'

import type { PackingGroup } from '@/client'
import { WastePackagingSizeEnumUtil } from '@/models/enums/wastePackagingSize.enum'
import type { PickupRequestWasteMaterialForm } from '@/models/pickup-request/update/steps/pickupRequestWasteForm.model'
import type { UnNumberIndex } from '@/models/un-number/index/unNumberIndex.model'
import PickupRequestDetailsFormTableCell from '@/modules/pickup-request/features/update/components/steps/details/cells/PickupRequestDetailsFormTableCell.vue'
import { useUnNumberIndexForPickUpRequestQuery } from '@/modules/un-number/api/queries/unNumberIndexForPickUpRequest.query'
import { toFormField } from '@/utils/formango.util'

const props = defineProps<{
  index: number
  materials: FieldArray<PickupRequestWasteMaterialForm>
}>()

const i18n = useI18n()

const unNumber = props.materials.register(`${props.index}.unNumber`)
const search = ref<string>('')

const filter = computed<{
  contractItem: string
  contractNumber: string
  search: string
  tcNumber: string | undefined
}>(() => ({
  contractItem: props.materials.value.value[props.index]?.contractItem ?? '',
  contractNumber: props.materials.value.value[props.index]?.contractNumber ?? '',
  search: search.value,
  tcNumber: props.materials.value.value[props.index]?.tcNumber ?? undefined,
}))

const unNumberIndexQuery = useUnNumberIndexForPickUpRequestQuery(filter)
const unNumbers = computed<UnNumberIndex[]>(() => unNumberIndexQuery.data.value ?? [])

function onSearch(searchTerm: string): void {
  search.value = searchTerm
}

function onUpdateUnNumber(value: {
  isHazardous: boolean | null
  dangerLabel1: string | null
  dangerLabel2: string | null
  dangerLabel3: string | null
  description: string | null
  number: string | null
  packingGroup: string | null
} | null): void {
  unNumber.setValue(value)
}

function displayUnNumber(item: {
  isHazardous: boolean | null
  dangerLabel1: string | null
  dangerLabel2: string | null
  dangerLabel3: string | null
  description: string | null
  number: string | null
  packingGroup: string | null
}): string {
  const unNumberDetail = unNumbers.value.find((num) =>
    num.number === item.number && num.packingGroup === item.packingGroup)

  if (!unNumberDetail) {
    return item.number ?? '-'
  }
  const packingGroup = unNumberDetail.packingGroup
    ? `(${i18n.t(WastePackagingSizeEnumUtil.getI18nKey(unNumberDetail.packingGroup as PackingGroup))})`
    : null

  return `${unNumberDetail.number} - ${unNumberDetail.description} ${packingGroup || ''}`
}
</script>

<template>
  <PickupRequestDetailsFormTableCell>
    <VcAutocomplete
      v-bind="toFormField(unNumber)"
      :items="unNumbers"
      :is-loading="unNumberIndexQuery.isFetching.value"
      :display-fn="(value) => displayUnNumber(value)"
      :placeholder="i18n.t('module.pickup_request.update.packaging.placeholder.un_number')"
      :is-search-term-optional="true"
      :class-config="{
        inlineSearchInput: 'z-3',
      }"
      icon-right="selectIconRight"
      popover-align="start"
      @search="onSearch"
      @update:model-value="onUpdateUnNumber"
    />
  </PickupRequestDetailsFormTableCell>
</template>
