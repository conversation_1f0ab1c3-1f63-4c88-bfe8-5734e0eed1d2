<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import type { CustomerCountryCode } from '@/models/enums/customerCountryCode.enum'
import type { PickupRequestWasteMaterialForm } from '@/models/pickup-request/update/steps/pickupRequestWasteForm.model'
import { CustomerUtil } from '@/utils/customer.util'

const props = withDefaults(defineProps<{
  isTruncated?: boolean
  customerCountryCode: CustomerCountryCode
  material: PickupRequestWasteMaterialForm
}>(), { isTruncated: false })

const i18n = useI18n()

const materialSubInfoStyle = computed<string>(() => {
  return props.isTruncated ? 'text-secondary text-xs max-w-60 truncate' : 'text-secondary text-xs'
})

const contractInfoFormatted = computed<string>(() => {
  const contractItemWithoutLeadingZeros = props.material.contractItem?.replace(/^0+/, '') || ''

  return `${props.material.contractNumber} / ${contractItemWithoutLeadingZeros}`
})

const contractInfoFormattedWithTCNumber = computed<string>(() => {
  return `${contractInfoFormatted.value} / ${props.material.tcNumber ?? '-'}`
})
</script>

<template>
  <div :class="materialSubInfoStyle">
    <p
      v-if="CustomerUtil.isIrish(props.customerCountryCode)"
      class="text-secondary"
    >
      {{ contractInfoFormattedWithTCNumber }}
    </p>
    <p
      v-else
      class="text-secondary"
    >
      {{ contractInfoFormatted }}
    </p>

    <template v-if="CustomerUtil.isIrish(props.customerCountryCode)">
      <p>
        {{ i18n.t('module.pickup_request.update.details.material_analysis') }}: {{ props.material.materialAnalysis ?? '-' }}
      </p>
      <p>
        {{ i18n.t('module.pickup_request.update.details.delivery_info') }}: {{ props.material.deliveryInfo ?? '-' }}
      </p>
      <p>
        {{ i18n.t('module.pickup_request.update.details.process_code') }}: {{ props.material.processCode ?? '-' }}
      </p>
    </template>

    <template v-if="CustomerUtil.isBelgian(props.customerCountryCode)">
      <p>
        {{ i18n.t('module.pickup_request.update.details.customer_reference') }}:
        {{ props.material.customerReference ?? '-' }}
      </p>
    </template>

    <template v-if="CustomerUtil.isDutch(props.customerCountryCode)">
      <p>
        {{ i18n.t('module.pickup_request.update.details.customer_reference') }}:
        {{ props.material.customerReference ?? '-' }}
      </p>
      <p>
        {{ i18n.t('module.pickup_request.update.details.asn') }}: {{ props.material.asn ?? '-' }}
      </p>
    </template>

    <template v-if="CustomerUtil.isGerman(props.customerCountryCode)">
      <p>
        {{ i18n.t('module.pickup_request.update.details.ewc_code') }}: {{ props.material.ewcCode ?? '-' }}
      </p>
      <p>
        {{ i18n.t('module.pickup_request.update.details.esn_number') }}: {{ props.material.esnNumber ?? '-' }}
      </p>
    </template>
  </div>
</template>
