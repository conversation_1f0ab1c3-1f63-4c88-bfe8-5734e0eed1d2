<script setup lang="ts">
import { useLocalStorage } from '@vueuse/core'
import type {
  PaginatedData,
  TableColumn,
} from '@wisemen/vue-core-components'
import {
  usePagination,
  VcCheckbox,
  VcIcon,
  VcNumberField,
  VcTextField,
  VcTooltip,
} from '@wisemen/vue-core-components'
import type { VNode } from 'vue'
import {
  computed,
  h,
  ref,
  watch,
} from 'vue'
import { useI18n } from 'vue-i18n'

import { useDynamicTableColumnIndexQuery } from '@/api/queries/dynamicTableColumnIndex.query'
import { useDynamicTableDefaultViewQuery } from '@/api/queries/dynamicTableDefaultView.query'
import { useDynamicTableViewIndexQuery } from '@/api/queries/dynamicTableViewIndex.query'
import type { WasteMeasurementUnit } from '@/client'
import {
  DynamicColumnNames,
  PickUpTransportMode,
} from '@/client'
import AppAnimateHeight from '@/components/animate-height/AppAnimateHeight.vue'
import AppGroup from '@/components/app/AppGroup.vue'
import WizardFormStep from '@/components/form/wizard/WizardFormStep.vue'
import { useDynamicTable } from '@/composables/dynamic-table/dynamicTable.composable'
import type { WizardForm } from '@/composables/wizard-form/wizardForm.composable'
import { useWizardFormStep } from '@/composables/wizard-form/wizardFormStep.composable'
import type { DynamicTableColumnIndex } from '@/models/dynamic-table/column/dynamicTableColumnIndex.model'
import type { DynamicTableViewIndex } from '@/models/dynamic-table/view/dynamicTableViewIndex.model'
import type { DynamicTableViewIndexPagination } from '@/models/dynamic-table/view/dynamicTableViewIndexPagination.model'
import { DynamicTableEnumUtil } from '@/models/enums/dynamicTable.enum'
import { DynamicTableName } from '@/models/enums/dynamicTableName.enum'
import { PickupRequestValidationUtil } from '@/models/pickup-request/pickupRequestValidation.util'
import type { pickupRequestDetailsFormSchema } from '@/models/pickup-request/update/steps/pickupRequestDetailsForm.model'
import type { PickupRequestWasteMaterialForm } from '@/models/pickup-request/update/steps/pickupRequestWasteForm.model'
import PickupRequestDetailsFormWasteMaterialTableCell from '@/modules/pickup-request/features/update/components/steps/details/cells/PickupRequestDetailsFormWasteMaterialTableCell.vue'
import { usePickupRequestDetailsFormTableColumns } from '@/modules/pickup-request/features/update/components/steps/details/composables/pickupRequestDetailsFormTableColumns.composable'
import PickupRequestDetailsFormCompactTable from '@/modules/pickup-request/features/update/components/steps/details/PickupRequestDetailsFormCompactTable.vue'
import PickupRequestDetailsFormExpandedTable from '@/modules/pickup-request/features/update/components/steps/details/PickupRequestDetailsFormExpandedTable.vue'
import { useCustomerCountryCodeQuery } from '@/modules/waste-inquiry/api/queries/customerCountryCode.query'
import { toFormField } from '@/utils/formango.util'
import { ObjectUtil } from '@/utils/object.util'

const props = defineProps<{
  wizardForm: WizardForm<typeof pickupRequestDetailsFormSchema>
}>()

const i18n = useI18n()

const isTableExpanded = useLocalStorage<boolean>('pickup-request-table-expanded', false)

const customerCountryCodeQuery = useCustomerCountryCodeQuery(props.wizardForm.state.value.customer.id)
const customerCountryCode = customerCountryCodeQuery.data

const wizardFormStep = useWizardFormStep(props.wizardForm)
const materials = wizardFormStep.form.registerArray('materials')
const totalQuantityPallets = wizardFormStep.form.register(
  'totalQuantityPallets',
  props.wizardForm.state.value.transportMode !== PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK
    ? null
    : props.wizardForm.state.value.materials
      .reduce((sum: number, material: PickupRequestWasteMaterialForm) => sum + (material.quantityPallets || 0), 0)
      ?? 0,
)
const isReturnPackaging = wizardFormStep.form.register('isReturnPackaging', false)
const packagingRemark = wizardFormStep.form.register('packagingRemark')

const materialsHasMissingFields = computed<boolean>(() => {
  return materials.rawErrors.value.length > 0
})
const materialsError = computed<string | null>(() => {
  const field = materials.rawErrors.value[0].path?.[1]

  return field != null ? String(field) : null
})

function getRowError(row: PickupRequestWasteMaterialForm): boolean {
  const rowIndex = materials.value.value.indexOf(row)

  return materials.rawErrors.value.some((error) => {
    return Number(error.path?.[0]) === rowIndex
  })
}

const dynamicTableIndexPagination = usePagination<DynamicTableViewIndexPagination>({ isRouteQueryEnabled: false })

const dynamicTableColumns = useDynamicTableColumnIndexQuery(DynamicTableName.FORM_PICK_UP_REQUEST_CONTRACT_LINE)
const dynamicTableDefaultView = useDynamicTableDefaultViewQuery(DynamicTableName.FORM_PICK_UP_REQUEST_CONTRACT_LINE)
const dynamicTableViewIndex = useDynamicTableViewIndexQuery(
  DynamicTableName.FORM_PICK_UP_REQUEST_CONTRACT_LINE,
  dynamicTableIndexPagination.paginationOptions,
)

const isDynamicTableLoading = computed<boolean>(() => dynamicTableColumns.isLoading.value
  || dynamicTableDefaultView.isLoading.value
  || dynamicTableViewIndex.isLoading.value)

const defaultView = computed<DynamicTableViewIndex | null>(() => dynamicTableDefaultView.data.value)
const activeView = ref<DynamicTableViewIndex | null>(ObjectUtil.deepClone(defaultView.value))

const dynamicTableViews = computed<DynamicTableViewIndex[]>(() => dynamicTableViewIndex.data.value?.data ?? [])

function filterDynamicTableColumns(name: DynamicColumnNames): boolean {
  const validation = new PickupRequestValidationUtil(props.wizardForm.state.value)

  if (name === DynamicColumnNames.IS_CONTAINER_COVERED) {
    return validation.isMaterialContainerCoveredAllowed
  }
  if (name === DynamicColumnNames.CONTAINER_NUMBER) {
    return validation.isMaterialContainerNumberAllowed
  }
  if (name === DynamicColumnNames.CONTAINER_TRANSPORT_TYPE) {
    return validation.isMaterialContainerTransportTypeAllowed
  }
  if (name === DynamicColumnNames.CONTAINER_TYPE) {
    return validation.isMaterialContainerTypeAllowed
  }
  if (name === DynamicColumnNames.PACKAGING_TYPE) {
    return validation.isMaterialPackagingTypeAllowed
  }
  if (name === DynamicColumnNames.QUANTITY_LABELS) {
    return validation.isMaterialQuantityLabelsAllowed
  }
  if (name === DynamicColumnNames.QUANTITY_PACKAGES) {
    return validation.isMaterialQuantityPackagesAllowed
  }
  if (name === DynamicColumnNames.QUANTITY_PALLETS) {
    return validation.isMaterialQuantityPalletsAllowed
  }
  if (name === DynamicColumnNames.QUANTITY_CONTAINERS) {
    return validation.isMaterialQuantityContainersAllowed
  }
  if (name === DynamicColumnNames.TANKER_TYPE) {
    return validation.isMaterialTankerTypeAllowed
  }
  if (name === DynamicColumnNames.PACKAGING_REMARK) {
    return validation.isPackagingRemarkAllowed
  }
  if (name === DynamicColumnNames.IS_RETURN_PACKAGING) {
    return validation.isReturnPackagingAllowed
  }
  if (name === DynamicColumnNames.TOTAL_QUANTITY_PALLETS) {
    return validation.isTotalQuantityPalletsAllowed
  }
  if (name === DynamicColumnNames.RECONCILIATION_NUMBER) {
    return validation.isReconciliationNumberAllowed
  }
  if (name === DynamicColumnNames.HAZARD_INDUCERS) {
    return validation.isHazardInducersAllowed
  }
  if (name === DynamicColumnNames.CONTAINER_VOLUME_SIZE) {
    return validation.isContainerVolumeSizeAllowed
  }

  return true
}

const isPackage = computed<boolean>(() => {
  return props.wizardForm.state.value.transportMode === PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK
})

const dynamicTable = useDynamicTable({
  activeView,
  columns: computed<DynamicTableColumnIndex[]>(() => dynamicTableColumns.data.value ?? []),
  columnsFilter: filterDynamicTableColumns,
  dynamicTableViews,
  tableName: DynamicTableName.FORM_PICK_UP_REQUEST_CONTRACT_LINE,
})

const hasActiveViewBeenUpdated = computed<boolean>(() => dynamicTable.hasActiveViewBeenUpdated.value)

const tableData: PaginatedData<PickupRequestWasteMaterialForm> = {
  data: materials.value.value,
  meta: {
    next: null,
    total: materials.value.value.length,
  },
}

async function onViewsGetNextPage(): Promise<void> {
  if (dynamicTableViewIndex.isFetching.value) {
    return
  }

  await dynamicTableViewIndex.getNextPage()
}

const detailsFormTableColumns = usePickupRequestDetailsFormTableColumns(
  materials,
  props.wizardForm.state.value,
  props.wizardForm.state.value.customer.id,
)

const columns = computed<TableColumn<PickupRequestWasteMaterialForm>[]>(() => {
  const cols = dynamicTable.columns.value
    .filter((column) => column.isVisible)
    .map((column) => {
      const sort = dynamicTable.sorts.value.find((sort) => sort.uuid === column.uuid) ?? null

      const isSortable = sort !== null && !sort.isDisabled && sort.direction !== null

      const columnFn = detailsFormTableColumns[column.name]

      return columnFn ? columnFn(column.label, isSortable) : null
    })
    .filter((col): col is TableColumn<PickupRequestWasteMaterialForm> => col !== null)

  cols.unshift({
    cell: (row): VNode => h(PickupRequestDetailsFormWasteMaterialTableCell, {
      hasError: getRowError(row),
      customerCountryCode: customerCountryCode.value,
      material: row,
    }),
    headerLabel: i18n.t('module.pickup_request.update.details.waste_material'),
    key: 'wasteMaterial',
  })

  return cols
})

function onUpdatePreferredEstimatedWeightVolumeUnit(unit: WasteMeasurementUnit | null): void {
  for (const material of materials.value.value) {
    material.estimatedWeightOrVolumeUnit = unit
  }
}

function toggleTableExpansion(): void {
  isTableExpanded.value = !isTableExpanded.value
}

watch(defaultView, (defaultView) => {
  activeView.value = defaultView
})

watch(materials.modelValue.value, () => {
  const totalPallets = materials.value.value
    .reduce((sum: number, material: PickupRequestWasteMaterialForm) => sum + (material.quantityPallets || 0), 0)

  if (props.wizardForm.state.value.transportMode === PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK
    && (totalQuantityPallets.modelValue.value === null || totalQuantityPallets.modelValue.value <= totalPallets)
  ) {
    totalQuantityPallets.setValue(totalPallets)
  }
})

watch(customerCountryCode, (countryCode) => {
  if (countryCode) {
    wizardFormStep.form.register('customerCountryCode', countryCode)
  }
}, { immediate: true })
</script>

<template>
  <WizardFormStep :wizard-form-step="wizardFormStep">
    <template v-if="!isTableExpanded">
      <PickupRequestDetailsFormCompactTable
        v-model:active-view="activeView"
        :columns="columns"
        :table-data="tableData"
        :is-dynamic-table-loading="isDynamicTableLoading"
        :dynamic-table-columns="dynamicTableColumns"
        :dynamic-table="dynamicTable"
        :dynamic-table-views="dynamicTableViews"
        :has-active-view-been-updated="hasActiveViewBeenUpdated"
        :on-views-get-next-page="onViewsGetNextPage"
        @expand="toggleTableExpansion"
        @update-preferred-estimated-weight-volume-unit="onUpdatePreferredEstimatedWeightVolumeUnit"
      />
    </template>

    <PickupRequestDetailsFormExpandedTable
      v-if="isTableExpanded"
      v-model:active-view="activeView"
      :columns="columns"
      :table-data="tableData"
      :is-dynamic-table-loading="isDynamicTableLoading"
      :dynamic-table-columns="dynamicTableColumns"
      :dynamic-table="dynamicTable"
      :dynamic-table-views="dynamicTableViews"
      :has-active-view-been-updated="hasActiveViewBeenUpdated"
      :on-views-get-next-page="onViewsGetNextPage"
      @shrink="toggleTableExpansion"
      @update-preferred-estimated-weight-volume-unit="onUpdatePreferredEstimatedWeightVolumeUnit"
    />

    <AppAnimateHeight>
      <p
        v-if="materialsHasMissingFields"
        class="text-error-primary pt-md text-xs"
      >
        {{
          materialsError && Object.values(DynamicColumnNames).includes(materialsError as DynamicColumnNames)
            ? i18n.t('module.pickup_request.update.details.materials_error', { field: i18n.t(DynamicTableEnumUtil.getLabelI18nKey(materialsError as DynamicColumnNames)) })
            : i18n.t('module.pickup_request.update.details.materials_error', { field: materialsError })
        }}
      </p>
    </AppAnimateHeight>

    <AppGroup
      v-if="!isTableExpanded && isPackage"
      direction="col"
      gap="3xl"
    >
      <div
        class="
          px-xl py-md border-secondary mt-xl flex w-full items-center
          justify-between rounded-xl border
        "
      >
        <p class="font-semibold">
          {{ i18n.t('module.pickup_request.update.packaging.fields.total_quantity_pallets') }}
        </p>
        <VcNumberField
          v-bind="toFormField(totalQuantityPallets)"
          :placeholder="i18n.t('module.pickup_request.update.packaging.placeholder.amount')"
          :is-required="true"
          :hide-controls="true"
          :min="0"
          :max="999"
          :class-config="{
            input: 'w-[5.5rem]',
          }"
        />
      </div>
      <div class="px-xl py-lg border-secondary mr-auto rounded-md border">
        <VcCheckbox
          v-bind="toFormField(isReturnPackaging)"
          :label="i18n.t('module.pickup_request.update.packaging.fields.is_return_packaging_description')"
        >
          <template #label>
            <AppGroup>
              <p class="text-secondary text-sm font-medium">
                {{ i18n.t('module.pickup_request.update.packaging.fields.is_return_packaging') }}
              </p>
              <VcTooltip>
                <template #trigger>
                  <VcIcon
                    class="size-4"
                    icon="infoCircle"
                  />
                </template>
                <template #content>
                  <p class="px-xl py-md max-w-96 text-center text-xs">
                    {{ i18n.t('module.pickup_request.update.packaging.fields.is_return_packaging_tooltip') }}
                  </p>
                </template>
              </VcTooltip>
            </AppGroup>
          </template>
        </VcCheckbox>
      </div>
      <VcTextField
        v-bind="toFormField(packagingRemark)"
        :is-required="isReturnPackaging.modelValue.value === true"
        :label="i18n.t('module.pickup_request.update.packaging.fields.packaging_remark')"
        :placeholder="i18n.t('module.pickup_request.update.packaging.fields.packaging_remark')"
        class="w-full"
      />
    </AppGroup>
  </WizardFormStep>
</template>
