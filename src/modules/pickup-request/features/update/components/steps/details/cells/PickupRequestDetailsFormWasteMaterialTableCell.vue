<script setup lang="ts">
import {
  VcButton,
  VcI<PERSON>,
  VcIconButton,
  VcPopover,
} from '@wisemen/vue-core-components'
import {
  computed,
  ref,
} from 'vue'

import type { CustomerCountryCode } from '@/models/enums/customerCountryCode.enum'
import type { PickupRequestWasteMaterialForm } from '@/models/pickup-request/update/steps/pickupRequestWasteForm.model'
import PickupRequestDetailsFormWasteMaterialTableCellInfo from '@/modules/pickup-request/features/update/components/steps/details/cells/PickupRequestDetailsFormWasteMaterialTableCellInfo.vue'

const props = defineProps<{
  hasError?: boolean
  customerCountryCode: CustomerCountryCode | null
  material: PickupRequestWasteMaterialForm
}>()

const isPopoverOpen = ref<boolean>(false)

const cellClass = computed<string>(() => {
  return props.hasError
    ? 'group-[&:has(:focus-visible)]/row:bg-error-100 group-[&:has(:focus-visible)]/row:border-error-500 group-hover/row:bg-error-100 group-hover/row:border-error-500 bg-error-50 border-error-500 border-r-2'
    : 'group-[&:has(:focus-visible)]/row:bg-brand-50 group-[&:has(:focus-visible)]/row:border-brand-500 group-hover/row:bg-brand-50 group-hover/row:border-brand-500 bg-primary border-r-2 border-transparent'
})

function openPopover(): void {
  isPopoverOpen.value = true
}

function closePopover(): void {
  isPopoverOpen.value = false
}
</script>

<template>
  <div
    v-if="material"
    :class="cellClass"
    class="
      px-3xl py-md relative flex h-full flex-col text-sm whitespace-nowrap
      shadow duration-100
    "
  >
    <div class="gap-xl flex flex-row items-center">
      <VcPopover
        :is-open="isPopoverOpen"
        popover-side="left"
        @interact-outside.prevent
      >
        <template #trigger>
          <VcButton
            :class-config="{
              root: '!justify-start underline',
              iconRight: 'text-tertiary size-3',
            }"
            variant="unstyled"
            icon-right="infoCircle"
            @click="openPopover"
          >
            <p
              class="
                text-tertiary font-semibold
                group-focus-within/row:text-primary
                group-hover/row:text-primary
              "
            >
              {{ props.material.wasteMaterial }}
            </p>
          </VcButton>
        </template>
        <template #content>
          <div
            v-if="props.customerCountryCode !== null"
            class="px-xl py-lg relative max-w-100 min-w-50"
          >
            <VcIconButton
              label="Close"
              icon="close"
              size="sm"
              variant="tertiary"
              class="mt-xs mr-xs text-secondary !absolute top-0 right-0"
              @click="closePopover"
            />

            <PickupRequestDetailsFormWasteMaterialTableCellInfo
              :customer-country-code="props.customerCountryCode"
              :material="props.material"
              :is-truncated="false"
            />
          </div>
        </template>
      </VcPopover>
      <VcIcon
        v-if="hasError"
        icon="alertTriangle"
        class="text-error-primary ml-auto h-4 w-4"
      />
    </div>

    <template v-if="props.customerCountryCode !== null">
      <PickupRequestDetailsFormWasteMaterialTableCellInfo
        :customer-country-code="props.customerCountryCode"
        :material="props.material"
        :is-truncated="true"
      />
    </template>
  </div>
</template>
