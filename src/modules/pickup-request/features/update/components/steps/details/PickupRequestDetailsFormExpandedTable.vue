<script setup lang="ts">
import type {
  PaginatedData,
  TableColumn,
} from '@wisemen/vue-core-components'
import {
  usePagination,
  VcIconButton,
} from '@wisemen/vue-core-components'
import { Motion } from 'motion-v'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import type { WasteMeasurementUnit } from '@/client'
import AppAnimateHeight from '@/components/animate-height/AppAnimateHeight.vue'
import AppGroup from '@/components/app/AppGroup.vue'
import AppTable from '@/components/app/table/AppTable.vue'
import AppTableDynamicViewsDropdown from '@/components/app/table/AppTableDynamicViewsDropdown.vue'
import TableSettingsPopover from '@/components/table/TableSettingsPopover.vue'
import type { DynamicTable } from '@/composables/dynamic-table/dynamicTable.composable'
import type { DynamicTableViewIndex } from '@/models/dynamic-table/view/dynamicTableViewIndex.model'
import type { PickupRequestWasteMaterialForm } from '@/models/pickup-request/update/steps/pickupRequestWasteForm.model'
import PickupRequestDetailsFormUnitPopover from '@/modules/pickup-request/features/update/components/steps/details/PickupRequestDetailsFormUnitPopover.vue'

const props = defineProps<{
  hasActiveViewBeenUpdated: boolean
  isDynamicTableLoading: boolean
  activeView: DynamicTableViewIndex | null
  columns: TableColumn<PickupRequestWasteMaterialForm>[]
  dynamicTable: DynamicTable
  dynamicTableColumns: any
  dynamicTableViews: DynamicTableViewIndex[]
  tableData: PaginatedData<PickupRequestWasteMaterialForm>
  onViewsGetNextPage: () => Promise<void>
}>()

const emit = defineEmits<{
  'shrink': []
  'update:activeView': [view: DynamicTableViewIndex | null]
  'updatePreferredEstimatedWeightVolumeUnit': [unit: WasteMeasurementUnit | null]
}>()

const i18n = useI18n()

const pagination = usePagination<any>({
  isRouteQueryEnabled: false,
  type: 'keyset',
})

const activeViewModel = computed<DynamicTableViewIndex | null>({
  get: () => props.activeView,
  set: (value) => emit('update:activeView', value),
})

function toggleTableExpansion(): void {
  emit('shrink')
}

function onUpdatePreferredEstimatedWeightVolumeUnit(unit: WasteMeasurementUnit | null): void {
  emit('updatePreferredEstimatedWeightVolumeUnit', unit)
}
</script>

<template>
  <div
    class="fixed inset-0 z-20 flex items-center justify-center bg-black/30"
    role="button"
    tabindex="0"
    @click.self="toggleTableExpansion"
    @keydown.escape="toggleTableExpansion"
  >
    <Motion
      :initial="{ scale: 0.95, opacity: 0 }"
      :animate="{ scale: 1, opacity: 1 }"
      :exit="{ scale: 0.95, opacity: 0 }"
      :transition="{ duration: 0.3 }"
      class="
        flex h-[95dvh] w-[97dvw] flex-col overflow-hidden rounded-lg bg-white
        shadow-2xl
      "
      @click.stop
    >
      <div
        class="flex items-center justify-between border-b border-gray-200 p-4"
      >
        <h2 class="text-lg font-semibold">
          {{ i18n.t('module.pickup_request.update.details.waste_material') }}
        </h2>
        <VcIconButton
          :label="i18n.t('shared.shrink')"
          icon="shrink"
          variant="secondary"
          @click="toggleTableExpansion"
        />
      </div>

      <div class="overflow-hidden">
        <AppTable
          v-if="dynamicTableColumns.data.value && activeViewModel"
          :columns="columns"
          :data="tableData"
          :is-loading="isDynamicTableLoading"
          :pagination="pagination"
          :is-first-column-sticky="true"
          :is-table-results-hint-hidden="true"
          class="h-full"
        >
          <template #top>
            <AppAnimateHeight class="!overflow-visible">
              <AppGroup
                justify="end"
                class="px-3xl py-md"
              >
                <PickupRequestDetailsFormUnitPopover
                  @update="onUpdatePreferredEstimatedWeightVolumeUnit"
                />
                <AppTableDynamicViewsDropdown
                  v-model:active-view="activeViewModel"
                  :dynamic-table="dynamicTable"
                  :is-updating-active-view="dynamicTable.isUpdateViewLoading.value"
                  :table-views="dynamicTableViews"
                  :has-active-view-been-updated="hasActiveViewBeenUpdated"
                  :is-add-button-disabled="!hasActiveViewBeenUpdated"
                  :on-views-get-next-page="onViewsGetNextPage"
                />
                <TableSettingsPopover
                  :dynamic-table="dynamicTable"
                  :table-views="dynamicTableViews"
                  :has-active-view-been-updated="hasActiveViewBeenUpdated"
                  :active-view="activeView"
                />
              </AppGroup>
            </AppAnimateHeight>
          </template>
        </AppTable>
      </div>
    </Motion>
  </div>
</template>
