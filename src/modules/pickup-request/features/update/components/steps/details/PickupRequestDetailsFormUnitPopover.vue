<script setup lang="ts">
import {
  VcButton,
  VcPopover,
  VcSelect,
  VcSelectItem,
} from '@wisemen/vue-core-components'
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'

import { WasteMeasurementUnit } from '@/client'
import { WasteMeasurementUnitEnumUtil } from '@/models/enums/wasteMeasurementUnit.enum'

const emit = defineEmits<{
  update: [value: WasteMeasurementUnit | null]
}>()

const i18n = useI18n()

const wasteMeasurementUnits = [
  WasteMeasurementUnit.TO,
  WasteMeasurementUnit.PC,
  WasteMeasurementUnit.M3,
  WasteMeasurementUnit.KG,
  WasteMeasurementUnit.YD3,
] as const

const preferredEstimatedWeightVolumeUnit = ref<WasteMeasurementUnit | null>(null)

function onUpdatePreferredEstimatedWeightVolumeUnit(): void {
  emit('update', preferredEstimatedWeightVolumeUnit.value)

  preferredEstimatedWeightVolumeUnit.value = null
}
</script>

<template>
  <VcPopover>
    <template #trigger>
      <VcButton
        variant="secondary"
        icon-right="chevronDown"
      >
        {{ i18n.t('module.pickup_request.update.details.bulk_unit') }}
      </VcButton>
    </template>
    <template #content>
      <div
        class="
          p-xl bg-primary border-secondary text-secondary max-w-100 rounded-xl
          text-sm
        "
      >
        <p>
          {{ i18n.t('module.pickup_request.update.details.bulk_unit_description') }}
        </p>
        <div class="flex items-center gap-2">
          <p>
            {{ i18n.t('module.pickup_request.update.details.bulk_unit_weight_volume') }}
          </p>
          <VcSelect
            v-model="preferredEstimatedWeightVolumeUnit"
            :display-fn="(value) => i18n.t(WasteMeasurementUnitEnumUtil.getI18nKey(value))"
            :class-config="{
              root: 'min-w-20',
            }"
          >
            <VcSelectItem
              v-for="wasteMeasurementUnit in wasteMeasurementUnits"
              :key="wasteMeasurementUnit"
              :value="wasteMeasurementUnit"
            >
              {{ i18n.t(WasteMeasurementUnitEnumUtil.getI18nKey(wasteMeasurementUnit)) }}
            </VcSelectItem>
          </VcSelect>
        </div>
        <VcButton
          variant="secondary"
          size="sm"
          class="mt-xl ml-auto"
          @click="onUpdatePreferredEstimatedWeightVolumeUnit()"
        >
          {{ i18n.t('shared.save_changes') }}
        </VcButton>
      </div>
    </template>
  </VcPopover>
</template>
