<script setup lang="ts">
import { VcDialog } from '@wisemen/vue-core-components'

import AppDialogContent from '@/components/app/dialog/AppDialogContent.vue'
import AppDialogHeader from '@/components/app/dialog/AppDialogHeader.vue'
import { TEST_ID } from '@/constants/testId.constant.ts'

const props = withDefaults(defineProps<{
  teleportTargetId?: string
  title: string
  description: string
}>(), {})

const emit = defineEmits<{
  closed: []
}>()

function onClose(): void {
  emit('closed')
}
</script>

<template>
  <VcDialog
    :test-id="TEST_ID.SHARED.CONFIRM_DIALOG.DIALOG"
    :teleport-target-id="props.teleportTargetId"
    class="w-dialog-sm"
    @close="onClose"
  >
    <AppDialogContent>
      <AppDialogHeader
        :title="props.title"
        :description="props.description"
      />
    </appdialogcontent>
  </vcdialog>
</template>
