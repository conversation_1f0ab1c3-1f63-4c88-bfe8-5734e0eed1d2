import type { UseQueryReturnType } from '@wisemen/vue-core-query'
import { useQuery } from '@wisemen/vue-core-query'
import type { ComputedRef } from 'vue'
import { computed } from 'vue'

import type { UnNumberIndex } from '@/models/un-number/index/unNumberIndex.model'
import { UnNumberService } from '@/modules/un-number/api/services/unNumber.service'

export interface UnNumberForPickUpRequestFilter {
  contractItem: string
  contractNumber: string
  search?: string
  tcNumber?: string
}

export function useUnNumberIndexForPickUpRequestQuery(
  filter: ComputedRef<UnNumberForPickUpRequestFilter>,
): UseQueryReturnType<UnNumberIndex[]> {
  const isEnabled = computed<boolean>(() => Boolean(filter.value.contractNumber && filter.value.contractItem))

  return useQuery<UnNumberIndex[]>({
    isEnabled,
    queryFn: async () => {
      return await UnNumberService.getAllForPickUpRequest(filter.value)
    },
    queryKey: { unNumberIndexForPickUpRequest: { filter } },
  })
}
