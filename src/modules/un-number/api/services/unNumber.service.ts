import type {
  PaginatedData,
  PaginationOptions,
} from '@wisemen/vue-core-components'
import { PaginationParamsBuilder } from '@wisemen/vue-core-components'

import {
  viewUnNumberIndexForPickUpRequestV1,
  viewUnNumberIndexV1,
} from '@/client'
import type { UnNumberIndex } from '@/models/un-number/index/unNumberIndex.model'
import type { UnNumberIndexPagination } from '@/models/un-number/index/unNumberIndexPagination.model'
import {
  UnNumberIndexPaginationTransformer,
  UnNumberIndexTransformer,
} from '@/models/un-number/unNumber.transformer'
import type { UnNumberForPickUpRequestFilter } from '@/modules/un-number/api/queries/unNumberIndexForPickUpRequest.query'

export class UnNumberService {
  static async getAll(
    paginationOptions: PaginationOptions<UnNumberIndexPagination>,
  ): Promise<PaginatedData<UnNumberIndex>> {
    const query = new PaginationParamsBuilder<UnNumberIndexPagination>(paginationOptions)
      .buildKeyset(UnNumberIndexPaginationTransformer.toDto)

    const response = await viewUnNumberIndexV1({ query })

    return {
      data: response.data.items.map(UnNumberIndexTransformer.fromDto),
      meta: {
        next: response.data.meta.next,
        total: response.data.items.length,
      },
    }
  }

  static async getAllForPickUpRequest(
    filter: UnNumberForPickUpRequestFilter,
  ): Promise<UnNumberIndex[]> {
    const response = await viewUnNumberIndexForPickUpRequestV1({
      query: {
        filter: {
          contractItem: filter.contractItem,
          contractNumber: filter.contractNumber,
          tcNumber: filter.tcNumber,
        },
        search: filter.search,
      },
    })

    return response.data.items.map(UnNumberIndexTransformer.fromDto)
  }
}
