import type { UseMutationReturnType } from '@wisemen/vue-core-query'
import { useMutation } from '@wisemen/vue-core-query'

import type { DownloadGuidanceLetterType } from '@/client'
import { GuidanceLetterService } from '@/modules/guidance-letter/api/services/guidanceLetter.service'

interface Params {
  shipmentId: string
  type: DownloadGuidanceLetterType
}

export function useGuidanceLetterDownloadMutation():
UseMutationReturnType<void, void, Params> {
  return useMutation<void, void, Params>({
    queryFn: async ({ params }) => {
      return await GuidanceLetterService.download(params.shipmentId, params.type)
    },
    queryKeysToInvalidate: {},
  })
}
