import { useOffsetInfiniteQuery } from '@/composables/offset-infinite-query/offsetInfiniteQuery.composable.ts'
import type { GuidanceLetterIndexQueryParams } from '@/models/guidance-letter/index/guidanceLetterIndexQueryParams.model'
import { GuidanceLetterService } from '@/modules/guidance-letter/api/services/guidanceLetter.service'
import type { InfiniteQueryOptions } from '@/types/query.type'

// eslint-disable-next-line ts/explicit-function-return-type
export function useGuidanceLetterIndexQuery(options: InfiniteQueryOptions<GuidanceLetterIndexQueryParams>) {
  return useOffsetInfiniteQuery({
    queryFn: (pagination) => {
      return GuidanceLetterService.getAll({
        pagination,
        sort: options.params.sort.value,
      })
    },
    queryKey: { guidanceLetterIndex: { queryParams: options.params } },
  })
}
