import type { DownloadGuidanceLetterType } from '@/client'
import {
  downloadGuidanceLetterSapV1,
  viewGuidanceLetterIndexV1,
} from '@/client'
import type { GuidanceLetterIndex } from '@/models/guidance-letter/index/guidanceLetterIndex.model'
import { GuidanceLetterIndexTransformer } from '@/models/guidance-letter/index/guidanceLetterIndex.transformer'
import type { GuidanceLetterIndexQueryParams } from '@/models/guidance-letter/index/guidanceLetterIndexQueryParams.model'
import { GuidanceLetterIndexQueryParamsTransformer } from '@/models/guidance-letter/index/guidanceLetterIndexQueryParams.transformer'
import type {
  OffsetPagination,
  OffsetPaginationResponse,
} from '@/types/pagination.type'
import { DownloadUtil } from '@/utils/download.util'

export class GuidanceLetterService {
  static async download(shipmentId: string, type: DownloadGuidanceLetterType): Promise<void> {
    const response = await downloadGuidanceLetterSapV1({
      query: { type },
      path: { shipmentId },
    })

    const disposition = response.response.headers.get('Content-Disposition')

    DownloadUtil.downloadBlob(response.data as Blob, disposition)
  }

  static async getAll(
    options: OffsetPagination<GuidanceLetterIndexQueryParams>,
  ): Promise<OffsetPaginationResponse<GuidanceLetterIndex>> {
    const response
      = await viewGuidanceLetterIndexV1({ query: GuidanceLetterIndexQueryParamsTransformer.toDto(options) })

    return {
      data: response.data.items.map(GuidanceLetterIndexTransformer.fromDto),
      meta: {
        limit: options.pagination.limit,
        offset: options.pagination.offset,
        total: response.data.meta.total,
      },
    }
  }
}
