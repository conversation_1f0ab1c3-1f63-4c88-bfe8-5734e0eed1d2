<script setup lang="ts">
import type { VNode } from 'vue'
import {
  computed,
  h,
} from 'vue'
import { useI18n } from 'vue-i18n'

import { DownloadGuidanceLetterType } from '@/client'
import AppGroup from '@/components/app/AppGroup.vue'
import { useFilters } from '@/components/filters/filters.composable'
import AppPage from '@/components/layout/page/AppPage.vue'
import DataTable from '@/components/table/data-table/DataTable.vue'
import DynamicTableSettings from '@/components/table/dynamic-table-settings/DynamicTableSettings.vue'
import DynamicTableViews from '@/components/table/dynamic-table-views/DynamicTableViews.vue'
import { useApiErrorToast } from '@/composables/api-error-toast/apiErrorToast.composable'
import { useDocumentTitle } from '@/composables/document-title/documentTitle.composable'
import { useDynamicTableV2 } from '@/composables/dynamic-table/dynamicTableV2.composable'
import { useSort } from '@/composables/sort/sort.composable'
import { useGenericColumn } from '@/composables/table-columns/genericTableColumnsV2.composable'
import { DynamicTableName } from '@/models/enums/dynamicTableName.enum'
import type { GuidanceLetterIndex } from '@/models/guidance-letter/index/guidanceLetterIndex.model'
import { useGuidanceLetterDownloadMutation } from '@/modules/guidance-letter/api/mutations/guidanceLetterDownload.mutation'
import { useGuidanceLetterIndexQuery } from '@/modules/guidance-letter/api/queries/guidanceLetterIndex.query'
import GuidanceLetterOverviewTableDownloadCell from '@/modules/guidance-letter/components/GuidanceLetterOverviewTableDownloadCell.vue'
import type { DataTableColumn } from '@/types/table.type'

const i18n = useI18n()
const documentTitle = useDocumentTitle()
const apiErrorToast = useApiErrorToast()

const guidanceLetterDownloadMutation = useGuidanceLetterDownloadMutation()

documentTitle.set(i18n.t('module.guidance_letter.overview.title'))

const sort = useSort({
  enableMultiSort: true,
  keys: [
    'shipmentId',
    'salesDoc',
    'requestNumber',
    'wasteMaterial',
    'transportDate',
    'customerId',
    'wasteProducerId',
    'pickUpAddressId',
    'customerName',
    'wasteProducerName',
    'pickUpAddressName',
  ],
  persistInUrl: false,
})

const filters = useFilters({
  filterGroups: () => [],
  persistInUrl: false,
})

const {
  isLoading,
  data,
  error,
  fetchNextPage,
} = useGuidanceLetterIndexQuery({ params: { sort: sort.values } })

const dynamicTable = useDynamicTableV2({
  dynamicTableName: DynamicTableName.GUIDANCE_LETTER,
  filters,
  sort,
})

const columns = computed<DataTableColumn<GuidanceLetterIndex>[]>(() => {
  const dynamicTableColumns = dynamicTable.columns.value
    .filter((column) => column.isVisible)
    .map((column) => {
      return useGenericColumn(column.name, column.label)
    })

  return [
    ...dynamicTableColumns,
    {
      cell: (row): VNode => h(GuidanceLetterOverviewTableDownloadCell, {
        hasAttachment: row.attachment,
        guidanceLetter: row.guidanceLetter,
        onAttachment: () => onDownloadAttachment(row),
        onPreview: () => onDownloadPreview(row),
        onPrint: () => onDownloadPrint(row),
      }),
      key: 'actions',
    },
  ] as DataTableColumn<GuidanceLetterIndex>[]
})

async function onDownloadPreview(guidanceLetter: GuidanceLetterIndex): Promise<void> {
  try {
    await guidanceLetterDownloadMutation.execute({
      params: {
        shipmentId: guidanceLetter.shipmentId,
        type: DownloadGuidanceLetterType.PREVIEW,
      },
    })
  }
  catch (error) {
    apiErrorToast.show(error)
  }
}

async function onDownloadPrint(guidanceLetter: GuidanceLetterIndex): Promise<void> {
  try {
    await guidanceLetterDownloadMutation.execute({
      params: {
        shipmentId: guidanceLetter.shipmentId,
        type: DownloadGuidanceLetterType.PRINT,
      },
    })
  }
  catch (error) {
    apiErrorToast.show(error)
  }
}

async function onDownloadAttachment(guidanceLetter: GuidanceLetterIndex): Promise<void> {
  try {
    await guidanceLetterDownloadMutation.execute({
      params: {
        shipmentId: guidanceLetter.shipmentId,
        type: DownloadGuidanceLetterType.ATTACHMENT,
      },
    })
  }
  catch (error) {
    apiErrorToast.show(error)
  }
}
</script>

<template>
  <AppPage
    :title="i18n.t('module.guidance_letter.overview.title')"
    class="pb-xl"
  >
    <DataTable
      v-if="dynamicTable.activeView.value !== null"
      :is-loading="isLoading"
      :data="data.data"
      :get-key="(row) => row.shipmentId"
      :columns="columns"
      :error="error"
      :sort="sort"
      :is-first-column-sticky="true"
      :is-last-column-sticky="true"
      @next-page="fetchNextPage"
    >
      <template #top>
        <AppGroup
          class="px-xl h-14"
          justify="end"
        >
          <DynamicTableViews :dynamic-table="dynamicTable" />
          <DynamicTableSettings :dynamic-table="dynamicTable" />
        </AppGroup>
      </template>
    </DataTable>
  </AppPage>
</template>
