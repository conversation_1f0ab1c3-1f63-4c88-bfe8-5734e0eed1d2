<script setup lang="ts">
import {
  VcDropdownMenu,
  VcDropdownMenuGroup,
  VcDropdownMenuItem,
  VcIconButton,
} from '@wisemen/vue-core-components'
import { useI18n } from 'vue-i18n'

import DataTableCell from '@/components/table/data-table/DataTableCell.vue'
import { TEST_ID } from '@/constants/testId.constant.ts'

interface Props {
  hasAttachment: boolean
  guidanceLetter: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  attachment: []
  preview: []
  print: []
}>()

const i18n = useI18n()

function onDownloadPreview(): void {
  emit('preview')
}

function onDownloadPrint(): void {
  emit('print')
}

function onDownloadAttachment(): void {
  emit('attachment')
}
</script>

<template>
  <DataTableCell :has-interactive-content="props.guidanceLetter">
    <VcDropdownMenu
      v-if="props.guidanceLetter"
      popover-side="left"
    >
      <template #trigger>
        <VcIconButton
          :label="i18n.t('shared.download')"
          :test-id="TEST_ID.GUIDANCE_LETTERS.ANNOUNCEMENTS_TABLE.ACTIONS.MENU"
          icon="download"
          size="sm"
          variant="tertiary"
        />
      </template>

      <template #content>
        <VcDropdownMenuGroup>
          <VcDropdownMenuItem
            :test-id="TEST_ID.GUIDANCE_LETTERS.ANNOUNCEMENTS_TABLE.ACTIONS.DOWNLOAD_PREVIEW"
            :label="i18n.t('module.guidance_letters.overview.table.download_preview')"
            icon="download"
            @click="onDownloadPreview"
          />
          <VcDropdownMenuItem
            :test-id="TEST_ID.GUIDANCE_LETTERS.ANNOUNCEMENTS_TABLE.ACTIONS.DOWNLOAD_PRINT"
            :label="i18n.t('module.guidance_letters.overview.table.download_print')"
            icon="printer"
            @click="onDownloadPrint"
          />
          <VcDropdownMenuItem
            v-if="props.hasAttachment"
            :test-id="TEST_ID.GUIDANCE_LETTERS.ANNOUNCEMENTS_TABLE.ACTIONS.DOWNLOAD_ATTACHMENT"
            :label="i18n.t('module.guidance_letters.overview.table.download_attachment')"
            icon="file"
            @click="onDownloadAttachment"
          />
        </VcDropdownMenuGroup>
      </template>
    </VcDropdownMenu>
  </DataTableCell>
</template>
