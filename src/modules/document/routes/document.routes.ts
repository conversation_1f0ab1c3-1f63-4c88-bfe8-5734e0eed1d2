import type { Component } from 'vue'
import type { RouteRecordRaw } from 'vue-router'

export const documentRoutes = [
  {
    name: 'documents-overview',
    path: '/documents',
    component: (): Component => import('@/modules/document/features/overview/views/DocumentOverviewView.vue'),
    redirect: { name: 'documents-overview-transport' },
    children: [
      {
        name: 'documents-overview-transport',
        path: 'transport',
        component: (): Component => import('@/modules/document/features/overview/views/DocumentEmptyView.vue'),
      },
      {
        name: 'documents-overview-tfs',
        path: 'tfs',
        component: (): Component => import('@/modules/document/features/overview/views/DocumentEmptyView.vue'),
      },
      {
        name: 'documents-overview-balanced-score-card',
        path: 'balanced-score-card',
        component: (): Component => import('@/modules/document/features/overview/views/DocumentEmptyView.vue'),
      },
      {
        name: 'documents-overview-contract',
        path: 'contract',
        component: (): Component => import('@/modules/document/features/overview/views/DocumentEmptyView.vue'),
      },
      {
        name: 'documents-overview-mastertable',
        path: 'mastertable',
        component: (): Component => import('@/modules/document/features/overview/views/DocumentEmptyView.vue'),
      },
      {
        name: 'documents-overview-minutes-and-presentations',
        path: 'minutes-and-presentations',
        component: (): Component => import('@/modules/document/features/overview/views/DocumentEmptyView.vue'),
      },
      {
        name: 'documents-overview-quotation',
        path: 'quotation',
        component: (): Component => import('@/modules/document/features/overview/views/DocumentEmptyView.vue'),
      },
      {
        name: 'documents-overview-twm-manual',
        path: 'twm-manual',
        component: (): Component => import('@/modules/document/features/overview/views/DocumentEmptyView.vue'),
      },
    ],
  },
] as const satisfies RouteRecordRaw[]
