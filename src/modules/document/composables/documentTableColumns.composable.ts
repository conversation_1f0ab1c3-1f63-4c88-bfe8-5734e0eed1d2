import type { TableColumn } from '@wisemen/vue-core-components'
import { VcTableCell } from '@wisemen/vue-core-components'
import type { VNode } from 'vue'
import { h } from 'vue'

import { useLocalizedDateFormat } from '@/composables/localized-date-format/localizedDateFormat.composable'
import type { DocumentIndex } from '@/models/document/index/documentIndex.model'
import { SharepointDocumentStatusEnumUtil } from '@/models/enums/sharepointDocumentStatus.enum'
import { i18nPlugin } from '@/plugins/i18n.plugin'

export function useDocumentNameColumn(
  isSortable?: boolean,
): TableColumn<DocumentIndex> {
  return {
    isSortable: isSortable ?? false,
    cell: (row): VNode => h(VcTableCell, () => row.name),
    headerLabel: i18nPlugin.global.t('module.document.overview.columns.document_name'),
    key: 'name',
  }
}

export function useDocumentApplicableFromColumn(
  isSortable?: boolean,
): TableColumn<DocumentIndex> {
  const localizedDateFormat = useLocalizedDateFormat()

  return {
    isSortable: isSortable ?? false,
    cell: (row): VNode => h(VcTableCell, () => row.applicableFrom ? localizedDateFormat.toNumericDate(new Date(row.applicableFrom)) : '-'),
    headerLabel: i18nPlugin.global.t('module.document.overview.columns.applicable_from'),
    key: 'applicableFrom',
  }
}

export function useDocumentApplicableUntilColumn(
  isSortable?: boolean,
): TableColumn<DocumentIndex> {
  const localizedDateFormat = useLocalizedDateFormat()

  return {
    isSortable: isSortable ?? false,
    cell: (row): VNode => h(VcTableCell, () => row.applicableTill ? localizedDateFormat.toNumericDate(new Date(row.applicableTill)) : '-'),
    headerLabel: i18nPlugin.global.t('module.document.overview.columns.applicable_until'),
    key: 'applicableUntil',
  }
}

export function useDocumentTfsTypeColumn(
  isSortable?: boolean,
): TableColumn<DocumentIndex> {
  return {
    isSortable: isSortable ?? false,
    // TODO: BACKEND NEEDED Fill in once TfsType enum is implemented
    cell: (): VNode => h(VcTableCell, () => '-'),
    headerLabel: i18nPlugin.global.t('module.document.overview.columns.type_tfs'),
    key: 'tfsType',
  }
}

export function useDocumentWasteProducerColumn(
  isSortable?: boolean,
): TableColumn<DocumentIndex> {
  return {
    isSortable: isSortable ?? false,
    cell: (row): VNode => h(VcTableCell, () => row.wasteProducer),
    headerLabel: i18nPlugin.global.t('module.document.overview.columns.waste_producer'),
    key: 'wasteProducer',
  }
}

export function useDocumentActionDateColumn(
  isSortable?: boolean,
): TableColumn<DocumentIndex> {
  const localizedDateFormat = useLocalizedDateFormat()

  return {
    isSortable: isSortable ?? false,
    cell: (row): VNode => h(VcTableCell, () => row.actionAt ? localizedDateFormat.toNumericDate(new Date(row.actionAt)) : '-'),
    headerLabel: i18nPlugin.global.t('module.document.overview.columns.action_date'),
    key: 'actionDate',
  }
}

export function useDocumentStatusColumn(
  isSortable?: boolean,
): TableColumn<DocumentIndex> {
  return {
    isSortable: isSortable ?? false,
    cell: (row): VNode => h(VcTableCell, () => row.status ? i18nPlugin.global.t(SharepointDocumentStatusEnumUtil.getLabelI18nKey(row.status)) : '-'),
    headerLabel: i18nPlugin.global.t('module.document.overview.columns.status'),
    key: 'status',
  }
}
