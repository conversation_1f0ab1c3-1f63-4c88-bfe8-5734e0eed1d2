<script setup lang="ts">
import {
  VcIconButton,
  VcTableCell,
} from '@wisemen/vue-core-components'
import { useI18n } from 'vue-i18n'

import AppGroup from '@/components/app/AppGroup.vue'

const props = defineProps<{
  isLoading?: boolean
  hideActions?: boolean
}>()

const emit = defineEmits<{
  download: []
}>()

const i18n = useI18n()

function onDownload(): void {
  emit('download')
}
</script>

<template>
  <VcTableCell class="!px-xl">
    <AppGroup
      v-if="!props.hideActions"
      gap="sm"
    >
      <VcIconButton
        :label="i18n.t('shared.download')"
        :class-config="{
          icon: 'text-fg-brand-primary',
          root: 'size-6',
        }"
        :is-loading="props.isLoading"
        variant="tertiary"
        size="sm"
        icon="download"
        @click="onDownload"
      />
    </AppGroup>
  </VcTableCell>
</template>
