import type { UseMutationReturnType } from '@wisemen/vue-core-query'
import { useMutation } from '@wisemen/vue-core-query'

import type { DocumentSiteUuid } from '@/models/document/site/documentSiteUuid.model'
import { DocumentService } from '@/modules/document/api/services/document.service'

interface Params {
  customerUuid: DocumentSiteUuid
  documentId: string
}

export function useDocumentDownloadMutation():
UseMutationReturnType<void, void, Params> {
  return useMutation<void, void, Params>({
    queryFn: async ({ params }) => {
      return await DocumentService.download(params.documentId, params.customerUuid)
    },
    queryKeysToInvalidate: {},
  })
}
