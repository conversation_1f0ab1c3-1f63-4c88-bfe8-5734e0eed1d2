import { useMutation } from '@wisemen/vue-core-query'

import type { SharepointDocumentViewName } from '@/client'
import { DocumentService } from '@/modules/document/api/services/document.service'

interface Body {
  customerUuid: string
  viewName: SharepointDocumentViewName
  wasteProducerIds: string[]
}

// eslint-disable-next-line ts/explicit-function-return-type
export function useDocumentIndexFiltersMutation() {
  return useMutation({
    queryFn: ({ body }: { body: Body }) => {
      return DocumentService.getFilters(
        body.viewName,
        body.customerUuid,
        body.wasteProducerIds,
      )
    },
    queryKeysToInvalidate: {},
  })
}
