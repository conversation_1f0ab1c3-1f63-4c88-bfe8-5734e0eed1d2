import type { SharepointDocumentViewName } from '@/client'
import {
  downloadDocumentV1,
  getDocumentFiltersV1,
  viewDocumentIndexV1,
  viewUserSiteIndexV1,
} from '@/client'
import type { DocumentIndex } from '@/models/document/index/documentIndex.model'
import { DocumentIndexTransformer } from '@/models/document/index/documentIndex.transformer'
import type { DocumentIndexFilter } from '@/models/document/index/documentIndexFilter.model'
import { DocumentIndexFiltersTransformer } from '@/models/document/index/documentIndexFilters.transformer'
import type { DocumentIndexQueryParams } from '@/models/document/index/documentIndexQueryParams.model'
import { DocumentIndexQueryParamsTransformer } from '@/models/document/index/documentIndexQueryParams.transformer'
import type { DocumentSiteIndex } from '@/models/document/site/documentSiteIndex.model'
import { DocumentSiteIndexTransformer } from '@/models/document/site/documentSiteIndex.transformer'
import type { DocumentSiteUuid } from '@/models/document/site/documentSiteUuid.model'
import type {
  KeysetPagination,
  KeysetPaginationResponse,
} from '@/types/pagination.type'
import { DownloadUtil } from '@/utils/download.util'

export class DocumentService {
  static async download(documentId: string, customerUuid: DocumentSiteUuid): Promise<void> {
    const response = await downloadDocumentV1({
      body: {
        customerUuid,
        documentId,
      },
    })

    const disposition = response.response.headers.get('Content-Disposition')

    DownloadUtil.downloadBlob(response.data as Blob, disposition)
  }

  static async getAll(
    params: KeysetPagination<DocumentIndexQueryParams>,
  ): Promise<KeysetPaginationResponse<DocumentIndex>> {
    const response = await viewDocumentIndexV1({ query: DocumentIndexQueryParamsTransformer.toDto(params) })

    return {
      data: response.data.items.map(DocumentIndexTransformer.fromDto),
      meta: response.data.meta,
    }
  }

  static async getFilters(
    viewName: SharepointDocumentViewName,
    customerUuid: string,
    wasteProducerIds: string[],
  ): Promise<DocumentIndexFilter[]> {
    const response = await getDocumentFiltersV1({
      query: {
        filter: {
          customerUuid,
          viewName,
          wasteProducerIds,
        },
      },
    })

    return DocumentIndexFiltersTransformer.fromDto(response.data)
  }

  static async getSites(): Promise<DocumentSiteIndex[]> {
    const response = await viewUserSiteIndexV1({})

    return response.data.map(DocumentSiteIndexTransformer.fromDto)
  }
}
