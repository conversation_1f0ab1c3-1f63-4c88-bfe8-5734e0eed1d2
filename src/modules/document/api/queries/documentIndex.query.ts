import type { ComputedRef } from 'vue'

import { useKeysetInfiniteQuery } from '@/composables/keyset-infinite-query/keysetInfiniteQuery.composable'
import type { DocumentIndexQueryParams } from '@/models/document/index/documentIndexQueryParams.model'
import { DocumentService } from '@/modules/document/api/services/document.service'
import type { InfiniteQueryOptions } from '@/types/query.type'

interface Options extends InfiniteQueryOptions<DocumentIndexQueryParams> {
  isEnabled: ComputedRef<boolean>
}

// eslint-disable-next-line ts/explicit-function-return-type
export function useDocumentIndexQuery(options: Options) {
  return useKeysetInfiniteQuery({
    isEnabled: options.isEnabled,
    queryFn: (pagination) => {
      return DocumentService.getAll({
        filters: options.params.filters.value,
        pagination,
        search: options.params.search.value,
      })
    },
    queryKey: { documentIndex: { queryParams: options.params } },
  })
}
