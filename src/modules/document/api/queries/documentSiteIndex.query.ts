import type { UseQueryReturnType } from '@wisemen/vue-core-query'
import { useQuery } from '@wisemen/vue-core-query'

import type { DocumentSiteIndex } from '@/models/document/site/documentSiteIndex.model'
import { DocumentService } from '@/modules/document/api/services/document.service'

export function useDocumentSiteIndexQuery(): UseQueryReturnType<DocumentSiteIndex[]> {
  return useQuery({
    queryFn: () => DocumentService.getSites(),
    queryKey: { documentSiteIndex: {} },
  })
}
