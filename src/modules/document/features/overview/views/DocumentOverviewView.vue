<script setup lang="ts">
import {
  VcRouterLinkTabs,
  VcRouterLinkTabsItem,
} from '@wisemen/vue-core-components'
import {
  AnimatePresence,
  Motion,
} from 'motion-v'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'

import {
  Permission,
  SharepointDocumentViewName,
} from '@/client/types.gen'
import AppGroup from '@/components/app/AppGroup.vue'
import FiltersActive from '@/components/filters/active/FiltersActive.vue'
import FiltersDropdownMenu from '@/components/filters/dropdown-menu/FiltersDropdownMenu.vue'
import FiltersRoot from '@/components/filters/FiltersRoot.vue'
import AppPage from '@/components/layout/page/AppPage.vue'
import SearchField from '@/components/search-field/SearchField.vue'
import DataTable from '@/components/table/data-table/DataTable.vue'
import { useDocumentTitle } from '@/composables/document-title/documentTitle.composable'
import { useSearch } from '@/composables/search/search.composable'
import type { DocumentIndexQueryParams } from '@/models/document/index/documentIndexQueryParams.model'
import type { DocumentSiteUuid } from '@/models/document/site/documentSiteUuid.model'
import { SharepointDocumentViewNameEnumUtil } from '@/models/enums/sharepointDocumentViewName.enum'
import { useDocumentIndexQuery } from '@/modules/document/api/queries/documentIndex.query'
import { useDocumentOverviewColumns } from '@/modules/document/features/overview/composables/documentOverviewColumns.composable'
import { useDocumentFilters } from '@/modules/document/features/overview/composables/documentOverviewFilters.composable'
import { useAuthStore } from '@/stores/auth.store'
import type { RouteLocationCurrent } from '@/types/global/vueRouter'

interface DocumentTab {
  id: SharepointDocumentViewName
  label: string
  to: RouteLocationCurrent
}

const authStore = useAuthStore()

const i18n = useI18n()
const search = useSearch({
  debounceMs: 300,
  persistInUrl: false,
})
const route = useRoute<string>()
const documentTitle = useDocumentTitle()

documentTitle.set(() => i18n.t('module.document.overview.title'))

const activeTabId = computed<SharepointDocumentViewName>(() => {
  const routeName = route.name

  switch (routeName) {
    case 'documents-overview-transport':
      return SharepointDocumentViewName.TRANSPORT
    case 'documents-overview-tfs':
      return SharepointDocumentViewName.TFS
    case 'documents-overview-balanced-score-card':
      return SharepointDocumentViewName.BSC
    case 'documents-overview-contract':
      return SharepointDocumentViewName.CONTRACT
    case 'documents-overview-mastertable':
      return SharepointDocumentViewName.MASTERTABLE
    case 'documents-overview-minutes-and-presentations':
      return SharepointDocumentViewName.MEETINGS
    case 'documents-overview-quotation':
      return SharepointDocumentViewName.QUOTATION
    case 'documents-overview-twm-manual':
      return SharepointDocumentViewName.MANUAL
    default:
      throw new Error(`Unknown route name: ${routeName?.toString()}`)
  }
})

const filters = useDocumentFilters(activeTabId)

const documentIndexQuery = useDocumentIndexQuery({
  isEnabled: computed<boolean>(
    () => {
      const {
        customerUuid, wasteProducerIds,
      } = filters.values.value

      return customerUuid !== null && wasteProducerIds.length > 0
    },
  ),
  params: {
    filters: computed<DocumentIndexQueryParams['filters']>(() => {
      return {
        customerUuid: filters.values.value.customerUuid!,
        refExt: filters.values.value.refExt?.value ?? null,
        transportType: filters.values.value.transportType?.value ?? null,
        viewName: activeTabId.value,
        wasteProducerIds: filters.values.value.wasteProducerIds,
        year: filters.values.value.year?.value ?? null,
      }
    }),
    search: search.debouncedSearch,
  },
})

const documentTabs = computed<DocumentTab[]>(() => {
  const items: DocumentTab[] = []

  if (authStore.hasPermission(Permission.DOCUMENT_TRANSPORT)) {
    items.push({
      id: SharepointDocumentViewName.TRANSPORT,
      label: i18n.t(SharepointDocumentViewNameEnumUtil.getLabelI18nKey(SharepointDocumentViewName.TRANSPORT)),
      to: { name: 'documents-overview-transport' },
    })
  }
  if (authStore.hasPermission(Permission.DOCUMENT_TFS)) {
    items.push({
      id: SharepointDocumentViewName.TFS,
      label: i18n.t(SharepointDocumentViewNameEnumUtil.getLabelI18nKey(SharepointDocumentViewName.TFS)),
      to: { name: 'documents-overview-tfs' },
    })
  }
  if (authStore.hasPermission(Permission.DOCUMENT_BSC)) {
    items.push({
      id: SharepointDocumentViewName.BSC,
      label: i18n.t(SharepointDocumentViewNameEnumUtil.getLabelI18nKey(SharepointDocumentViewName.BSC)),
      to: { name: 'documents-overview-balanced-score-card' },
    })
  }
  if (authStore.hasPermission(Permission.DOCUMENT_CONTRACT)) {
    items.push({
      id: SharepointDocumentViewName.CONTRACT,
      label: i18n.t(SharepointDocumentViewNameEnumUtil.getLabelI18nKey(SharepointDocumentViewName.CONTRACT)),
      to: { name: 'documents-overview-contract' },
    })
  }
  if (authStore.hasPermission(Permission.DOCUMENT_MASTER_TABLE)) {
    items.push({
      id: SharepointDocumentViewName.MASTERTABLE,
      label: i18n.t(SharepointDocumentViewNameEnumUtil.getLabelI18nKey(SharepointDocumentViewName.MASTERTABLE)),
      to: { name: 'documents-overview-mastertable' },
    })
  }
  if (authStore.hasPermission(Permission.DOCUMENT_MINUTES_AND_PRESENTATIONS)) {
    items.push({
      id: SharepointDocumentViewName.MEETINGS,
      label: i18n.t(SharepointDocumentViewNameEnumUtil.getLabelI18nKey(SharepointDocumentViewName.MEETINGS)),
      to: { name: 'documents-overview-minutes-and-presentations' },
    })
  }
  if (authStore.hasPermission(Permission.DOCUMENT_QUOTATION)) {
    items.push({
      id: SharepointDocumentViewName.QUOTATION,
      label: i18n.t(SharepointDocumentViewNameEnumUtil.getLabelI18nKey(SharepointDocumentViewName.QUOTATION)),
      to: { name: 'documents-overview-quotation' },
    })
  }
  if (authStore.hasPermission(Permission.DOCUMENT_MANUAL)) {
    items.push({
      id: SharepointDocumentViewName.MANUAL,
      label: i18n.t(SharepointDocumentViewNameEnumUtil.getLabelI18nKey(SharepointDocumentViewName.MANUAL)),
      to: { name: 'documents-overview-twm-manual' },
    })
  }

  return items
})

const columns = useDocumentOverviewColumns({
  activeTabId,
  customerUuid: computed<DocumentSiteUuid>(() => filters.values.value.customerUuid!),
})

const nonStaticFilterCount = computed<number>(() => {
  return filters.filters.value.filter((filter) => !filter.isStatic).length
})
</script>

<template>
  <AppPage
    :title="i18n.t('module.document.overview.title')"
    :tooltip="i18n.t('module.document.overview.tooltip')"
  >
    <VcRouterLinkTabs class="mb-xl border-secondary border-b">
      <template #items>
        <VcRouterLinkTabsItem
          v-for="tab of documentTabs"
          :key="tab.id"
          :value="tab.id"
          :to="tab.to"
        >
          {{ tab.label }}
        </VcRouterLinkTabsItem>
      </template>
    </VcRouterLinkTabs>

    <DataTable
      :is-loading="documentIndexQuery.isLoading.value"
      :error="documentIndexQuery.error.value"
      :columns="columns"
      :data="documentIndexQuery.data.value.data"
      :get-key="(item) => item.id"
      :is-first-column-sticky="true"
      :is-last-column-sticky="true"
      class="pb-xl"
      @next-page="documentIndexQuery.fetchNextPage()"
    >
      <template #top>
        <FiltersRoot :filters="filters">
          <AppGroup
            justify="between"
            class="px-xl h-14"
          >
            <FiltersActive />

            <AppGroup>
              <AnimatePresence mode="popLayout">
                <Motion
                  v-if="nonStaticFilterCount > 0"
                  :initial="{ opacity: 0 }"
                  :animate="{ opacity: 1 }"
                  :exit="{ opacity: 0 }"
                  :transition="{ duration: 0.2 }"
                >
                  <FiltersDropdownMenu />
                </Motion>
              </AnimatePresence>
              <SearchField :search="search" />
            </AppGroup>
          </AppGroup>
        </FiltersRoot>
      </template>
    </DataTable>
  </AppPage>
</template>
