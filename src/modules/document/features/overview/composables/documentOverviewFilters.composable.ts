import type { ComputedRef } from 'vue'
import {
  computed,
  watch,
} from 'vue'
import { useI18n } from 'vue-i18n'

import type { SharepointDocumentViewName } from '@/client'
import {
  createMultiSelectFilter,
  createSelectFilter,
  useFilters,
} from '@/components/filters'
import type { DocumentIndexFilter } from '@/models/document/index/documentIndexFilter.model'
import type { DocumentSiteIndex } from '@/models/document/site/documentSiteIndex.model'
import { useDocumentIndexFiltersMutation } from '@/modules/document/api/mutations/documentIndexFilters.mutation'
import { useDocumentSiteIndexQuery } from '@/modules/document/api/queries/documentSiteIndex.query'

// eslint-disable-next-line ts/explicit-function-return-type
export function useDocumentFilters(activeTabId: ComputedRef<SharepointDocumentViewName>) {
  const i18n = useI18n()
  const documentSiteIndexQuery = useDocumentSiteIndexQuery()
  const documentIndexFiltersMutation = useDocumentIndexFiltersMutation()
  const documentSiteIndex = computed<DocumentSiteIndex[]>(() => documentSiteIndexQuery.data.value ?? [])
  const documentIndexFilters = computed<DocumentIndexFilter[]>(
    // TODO: fix typing in vue-core-query useMutation
    () => (documentIndexFiltersMutation.data.value ?? []) as unknown as DocumentIndexFilter[],
  )

  const filters = useFilters({
    filterGroups: () => [
      {
        filters: [
          createMultiSelectFilter({
            isStatic: true,
            defaultValue: [],
            displayFn: (wasteProducerId) => {
              const wasteProducer = documentSiteIndex.value
                .flatMap((site) => site.wasteProducers)
                .find((wp) => wp.id.toString() === wasteProducerId)

              return wasteProducer?.name ?? wasteProducerId
            },
            key: 'wasteProducerIds',
            label: i18n.t('module.document.overview.filters.waste_producer'),
            options: computed<string[]>(() => getWasteProducerIdsFromActiveCustomer()),
          }),
          createSelectFilter({
            isStatic: true,
            defaultValue: null,
            displayFn: (siteUuid) => {
              return documentSiteIndex.value.find((site) => site.uuid === siteUuid)?.name ?? siteUuid
            },
            key: 'customerUuid',
            label: i18n.t('module.document.overview.filters.customer'),
            options: documentSiteIndex.value.map((site) => site.uuid),
          }),
          ...documentIndexFilters.value
            .filter((filter) => filter.filterValues.length > 0)
            .map((filter) => createSelectFilter({
              defaultValue: null,
              displayFn: (value: { key: string
                value: string }) => {
                // Translate known value sets, fallback to provided display value
                if (filter.filterName === 'Status') {
                  const statusKey = value.key.toLowerCase()
                  const i18nKey = `enum.sharepoint_document_status.${statusKey}` as unknown as any

                  return i18n.t(i18nKey)
                }

                return value.value
              },
              key: filter.filterName,
              label: ((): string => {
                // Translate known dynamic filter labels; fallback to API-provided label
                switch (filter.filterName) {
                  case 'Status':
                    return i18n.t('module.document.overview.filters.status')
                  case 'Year':
                    return i18n.t('module.document.overview.filters.year')
                  default:
                    return filter.filterName
                }
              })(),
              options: filter.filterValues,
            })),
        ],
      },
    ],
    persistInUrl: false,
  })

  function getWasteProducerIdsFromActiveCustomer(): string[] {
    if (!filters.values.value.customerUuid) {
      return []
    }

    return getWasteProducerIdsFromCustomer(filters.values.value.customerUuid)
  }

  function getWasteProducerIdsFromCustomer(uuid: string): string[] {
    return documentSiteIndex.value
      .find((site) => site.uuid === uuid)
      ?.wasteProducers
      .map((wasteProducer) => wasteProducer.id.toString()) ?? []
  }

  // Clear non-static filters when tab changes
  watch(activeTabId, () => {
    filters.clearAllFilters()
  })

  // Set or reset customerUuid when site index changes
  watch(documentSiteIndex, (sites) => {
    if (sites.length === 0) {
      filters.values.value.customerUuid = null
    }
    else if (filters.values.value.customerUuid === null) {
      filters.values.value.customerUuid = sites[0].uuid
    }
  }, { immediate: true })

  // Keep wasteProducerIds in sync with customerUuid
  watch(() => filters.values.value.customerUuid, (customerUuid) => {
    filters.values.value.wasteProducerIds = customerUuid === null
      ? []
      : getWasteProducerIdsFromCustomer(customerUuid)
  }, { immediate: true })

  // Refetch filters whenever wasteProducerIds or tab changes
  watch(
    [
      (): string[] => filters.values.value.wasteProducerIds,
      activeTabId,
    ],
    ([
      wasteProducerIds,
      activeTabId,
    ]) => {
      const { customerUuid } = filters.values.value

      if (customerUuid === null || wasteProducerIds.length === 0) {
        return
      }

      void documentIndexFiltersMutation.execute({
        body: {
          customerUuid,
          viewName: activeTabId,
          wasteProducerIds,
        },
      })
    },
    { immediate: true },
  )

  return filters
}
