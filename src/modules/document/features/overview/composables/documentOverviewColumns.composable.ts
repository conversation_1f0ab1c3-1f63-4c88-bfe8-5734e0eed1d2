import type {
  ComputedRef,
  VNode,
} from 'vue'
import {
  computed,
  h,
} from 'vue'
import { useI18n } from 'vue-i18n'

import { SharepointDocumentViewName } from '@/client'
import DataTableCell from '@/components/table/data-table/DataTableCell.vue'
import { useLocalizedDateFormat } from '@/composables/localized-date-format/localizedDateFormat.composable'
import type { DocumentIndex } from '@/models/document/index/documentIndex.model'
import type { DocumentSiteUuid } from '@/models/document/site/documentSiteUuid.model'
import DocumentOverviewTableDownloadCell from '@/modules/document/features/overview/components/DocumentOverviewTableDownloadCell.vue'
import type { DataTableColumn } from '@/types/table.type'

interface Options {
  activeTabId: ComputedRef<SharepointDocumentViewName>
  customerUuid: ComputedRef<DocumentSiteUuid>
}

// eslint-disable-next-line ts/explicit-function-return-type
export function useDocumentOverviewColumns(options: Options) {
  const i18n = useI18n()
  const localizedDateFormat = useLocalizedDateFormat()

  function genericColumn(headerLabel: string, key: keyof DocumentIndex): DataTableColumn<DocumentIndex> {
    return {
      cell: (item): VNode => h(DataTableCell, () => {
        const value = item[key]

        if (value instanceof Date) {
          return localizedDateFormat.toNumericDate(value)
        }

        return value ?? '-'
      }),
      headerLabel,
      key,
    }
  }

  function downloadDocumentColumn(): DataTableColumn<DocumentIndex> {
    return {
      cell: (row): VNode => h(DocumentOverviewTableDownloadCell, {
        documentSiteUuid: options.customerUuid.value,
        document: row,
      }),
      key: 'download',
    }
  }

  const columns = computed<DataTableColumn<DocumentIndex>[]>(() => {
    switch (options.activeTabId.value) {
      case SharepointDocumentViewName.TRANSPORT:
        return [
          genericColumn(i18n.t('module.document.overview.columns.document_name'), 'name'),
          genericColumn(i18n.t('module.document.overview.columns.applicable_from'), 'applicableFrom'),
          genericColumn(i18n.t('module.document.overview.columns.applicable_until'), 'applicableTill'),
          genericColumn(i18n.t('module.document.overview.columns.waste_producer'), 'wasteProducer'),
          genericColumn(i18n.t('module.document.overview.columns.status'), 'status'),
          downloadDocumentColumn(),
        ]
      case SharepointDocumentViewName.TFS:
        return [
          genericColumn(i18n.t('module.document.overview.columns.document_name'), 'name'),
          genericColumn(i18n.t('module.document.overview.columns.applicable_from'), 'applicableFrom'),
          genericColumn(i18n.t('module.document.overview.columns.type_tfs'), 'tfsType'),
          genericColumn(i18n.t('module.document.overview.columns.waste_producer'), 'wasteProducer'),
          genericColumn(i18n.t('module.document.overview.columns.status'), 'status'),
          downloadDocumentColumn(),
        ]
      case SharepointDocumentViewName.BSC:
        return [
          genericColumn(i18n.t('module.document.overview.columns.document_name'), 'name'),
          genericColumn(i18n.t('module.document.overview.columns.action_date'), 'actionAt'),
          genericColumn(i18n.t('module.document.overview.columns.applicable_until'), 'applicableTill'),
          genericColumn(i18n.t('module.document.overview.columns.waste_producer'), 'wasteProducer'),
          genericColumn(i18n.t('module.document.overview.columns.status'), 'status'),
          downloadDocumentColumn(),

        ]
      case SharepointDocumentViewName.CONTRACT:
        return [
          genericColumn(i18n.t('module.document.overview.columns.document_name'), 'name'),
          genericColumn(i18n.t('module.document.overview.columns.applicable_from'), 'applicableFrom'),
          genericColumn(i18n.t('module.document.overview.columns.applicable_until'), 'applicableTill'),
          genericColumn(i18n.t('module.document.overview.columns.waste_producer'), 'wasteProducer'),
          genericColumn(i18n.t('module.document.overview.columns.status'), 'status'),
          downloadDocumentColumn(),
        ]
      case SharepointDocumentViewName.MASTERTABLE:
        return [
          genericColumn(i18n.t('module.document.overview.columns.document_name'), 'name'),
          genericColumn(i18n.t('module.document.overview.columns.applicable_from'), 'applicableFrom'),
          genericColumn(i18n.t('module.document.overview.columns.applicable_until'), 'applicableTill'),
          genericColumn(i18n.t('module.document.overview.columns.waste_producer'), 'wasteProducer'),
          genericColumn(i18n.t('module.document.overview.columns.status'), 'status'),
          downloadDocumentColumn(),
        ]
      case SharepointDocumentViewName.MEETINGS:
        return [
          genericColumn(i18n.t('module.document.overview.columns.document_name'), 'name'),
          genericColumn(i18n.t('module.document.overview.columns.action_date'), 'actionAt'),
          genericColumn(i18n.t('module.document.overview.columns.waste_producer'), 'wasteProducer'),
          genericColumn(i18n.t('module.document.overview.columns.status'), 'status'),
          downloadDocumentColumn(),
        ]
      case SharepointDocumentViewName.QUOTATION:
        return [
          genericColumn(i18n.t('module.document.overview.columns.document_name'), 'name'),
          genericColumn(i18n.t('module.document.overview.columns.applicable_from'), 'applicableFrom'),
          genericColumn(i18n.t('module.document.overview.columns.applicable_until'), 'applicableTill'),
          genericColumn(i18n.t('module.document.overview.columns.waste_producer'), 'wasteProducer'),
          genericColumn(i18n.t('module.document.overview.columns.status'), 'status'),
          downloadDocumentColumn(),
        ]
      case SharepointDocumentViewName.MANUAL:
        return [
          genericColumn(i18n.t('module.document.overview.columns.document_name'), 'name'),
          genericColumn(i18n.t('module.document.overview.columns.applicable_from'), 'applicableFrom'),
          genericColumn(i18n.t('module.document.overview.columns.waste_producer'), 'wasteProducer'),
          genericColumn(i18n.t('module.document.overview.columns.status'), 'status'),
          downloadDocumentColumn(),
        ]
      default:
        return []
    }
  })

  return columns
}
