<script setup lang="ts">
import { VcIconButton } from '@wisemen/vue-core-components'
import { useI18n } from 'vue-i18n'

import DataTableCell from '@/components/table/data-table/DataTableCell.vue'
import { useApiErrorToast } from '@/composables/api-error-toast/apiErrorToast.composable'
import type { DocumentIndex } from '@/models/document/index/documentIndex.model'
import type { DocumentSiteUuid } from '@/models/document/site/documentSiteUuid.model'
import { useDocumentDownloadMutation } from '@/modules/document/api/mutations/documentDownload.mutation'

const props = defineProps<{
  documentSiteUuid: DocumentSiteUuid
  document: DocumentIndex
}>()

const i18n = useI18n()
const apiErrorToast = useApiErrorToast()
const documentDownloadMutation = useDocumentDownloadMutation()

async function onDownloadDocument(): Promise<void> {
  try {
    await documentDownloadMutation.execute({
      params: {
        customerUuid: props.documentSiteUuid,
        documentId: props.document.id,
      },
    })
  }
  catch (error) {
    apiErrorToast.show(error)
  }
}
</script>

<template>
  <DataTableCell>
    <VcIconButton
      :label="i18n.t('shared.download')"
      :class-config="{
        icon: 'text-fg-brand-primary',
        root: 'size-6',
      }"
      :is-loading="documentDownloadMutation.isLoading.value"
      variant="tertiary"
      size="sm"
      icon="download"
      @click="onDownloadDocument"
    />
  </DataTableCell>
</template>
