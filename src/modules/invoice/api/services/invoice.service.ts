import type { CertificateType } from '@/client'
import {
  approveDraftInvoiceV1,
  downloadCertificateV1,
  downloadInvoiceV1,
  exportDraftInvoicesExcelV1,
  exportInvoicesExcelV1,
  rejectDraftInvoiceV1,
  viewDraftInvoiceDetailV1,
  viewDraftInvoiceIndexV1,
  viewInvoiceDetailV1,
  viewInvoiceIndexV1,
} from '@/client'
import type { FileDownload } from '@/models/file/fileDownload.model'
import type { InvoiceDetail } from '@/models/invoice/detail/invoiceDetail.model'
import { InvoiceDetailTransformer } from '@/models/invoice/detail/invoiceDetail.transformer'
import type { InvoiceDraftDetail } from '@/models/invoice/draft-detail/invoiceDraftDetail.model.ts'
import { InvoiceDraftDetailTransformer } from '@/models/invoice/draft-detail/invoiceDraftDetail.transformer.ts'
import type { InvoiceDraftIndex } from '@/models/invoice/draft-index/invoiceDraftIndex.model'
import { InvoiceDraftIndexTransformer } from '@/models/invoice/draft-index/invoiceDraftIndex.transformer'
import type { InvoiceDraftIndexQueryParams } from '@/models/invoice/draft-index/invoiceDraftIndexQueryParams.model'
import { InvoiceDraftIndexQueryParamsTransformer } from '@/models/invoice/draft-index/invoiceDraftIndexQueryParams.transformer'
import type { InvoiceDraftExportExcelFilter } from '@/models/invoice/export/invoiceDraftExportExcelFilter.model'
import { InvoiceDraftExportExcelFilterTransformer } from '@/models/invoice/export/invoiceDraftExportExcelFilter.transformer'
import type { InvoiceExportExcelFilter } from '@/models/invoice/export/invoiceExportExcelFilter.model'
import { InvoiceExportExcelFilterTransformer } from '@/models/invoice/export/invoiceExportExcelFilter.transformer'
import type { InvoiceIndex } from '@/models/invoice/index/invoiceIndex.model.ts'
import { InvoiceIndexTransformer } from '@/models/invoice/index/invoiceIndex.transformer'
import type { InvoiceIndexQueryParams } from '@/models/invoice/index/invoiceIndexQueryParams.model'
import { InvoiceIndexQueryParamsTransformer } from '@/models/invoice/index/invoiceIndexQueryParams.transformer'
import { InvoiceReviewTransformer } from '@/models/invoice/review/invoiceReview.transformer'
import type { InvoiceReviewForm } from '@/models/invoice/review/invoiceReviewForm.model'
import type {
  OffsetPagination,
  OffsetPaginationResponse,
} from '@/types/pagination.type'

export class InvoiceService {
  static async approveDraft(form: InvoiceReviewForm, invoiceNumber: string): Promise<void> {
    await approveDraftInvoiceV1({
      body: InvoiceReviewTransformer.toApproveDto(form),
      path: { invoiceNumber },
    })
  }

  static async downloadCertificate(fileName: string, type: CertificateType): Promise<FileDownload> {
    const response = await downloadCertificateV1({
      body: {
        fileName,
        type,
      },
    })

    return {
      blob: new Blob(
        [
          response.data as string,
        ],
        { type: 'application/pdf' },
      ),
      disposition: response.response.headers.get('Content-Disposition'),
    }
  }

  static async downloadInvoice(invoiceNumber: string): Promise<FileDownload> {
    const response = await downloadInvoiceV1({ path: { invoiceNumber } })

    const disposition = response.response.headers.get('Content-Disposition')

    return {
      blob: new Blob(
        [
          response.data as string,
        ],
        { type: 'application/pdf' },
      ),
      disposition,
    }
  }

  static async exportDraftExcel(filter: InvoiceDraftExportExcelFilter): Promise<FileDownload> {
    const response = await exportDraftInvoicesExcelV1({
      query:
            { filter: InvoiceDraftExportExcelFilterTransformer.toDto(filter) },
    })
    const disposition = response.response.headers.get('Content-Disposition')

    return {
      blob: response.data as Blob,
      disposition,
    }
  }

  static async exportExcel(filter: InvoiceExportExcelFilter): Promise<FileDownload> {
    const response = await exportInvoicesExcelV1({
      query:
            { filter: InvoiceExportExcelFilterTransformer.toDto(filter) },
    })
    const disposition = response.response.headers.get('Content-Disposition')

    return {
      blob: new Blob(
        [
          response.data as string,
        ],
        { type: 'text/csv' },
      ),
      disposition,
    }
  }

  static async getAll(params: OffsetPagination<InvoiceIndexQueryParams>):
  Promise<OffsetPaginationResponse<InvoiceIndex>> {
    const response = await viewInvoiceIndexV1({ query: InvoiceIndexQueryParamsTransformer.toDto(params) })

    return {
      data: response.data.items.map(InvoiceIndexTransformer.fromDto),
      meta: response.data.meta,
    }
  }

  static async getAllDrafts(params: OffsetPagination<InvoiceDraftIndexQueryParams>):
  Promise<OffsetPaginationResponse<InvoiceDraftIndex>> {
    const response = await viewDraftInvoiceIndexV1({ query: InvoiceDraftIndexQueryParamsTransformer.toDto(params) })

    return {
      data: response.data.items.map(InvoiceDraftIndexTransformer.fromDto),
      meta: response.data.meta,
    }
  }

  static async getByUuid(invoiceNumber: string): Promise<InvoiceDetail> {
    const response = await viewInvoiceDetailV1({ path: { requestNumber: invoiceNumber } })

    return InvoiceDetailTransformer.fromDto(response.data)
  }

  static async getDraftDetail(invoiceNumber: string): Promise<InvoiceDraftDetail> {
    const response = await viewDraftInvoiceDetailV1({ path: { invoiceNumber } })

    return InvoiceDraftDetailTransformer.fromDto(response.data)
  }

  static async rejectDraft(form: InvoiceReviewForm, invoiceNumber: string): Promise<void> {
    await rejectDraftInvoiceV1({
      body: InvoiceReviewTransformer.toRejectDto(form),
      path: { invoiceNumber },
    })
  }
}
