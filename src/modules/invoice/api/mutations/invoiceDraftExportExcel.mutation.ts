import type { UseMutationReturnType } from '@wisemen/vue-core-query'
import { useMutation } from '@wisemen/vue-core-query'

import type { FileDownload } from '@/models/file/fileDownload.model'
import type { InvoiceDraftExportExcelFilter } from '@/models/invoice/export/invoiceDraftExportExcelFilter.model'
import { InvoiceService } from '@/modules/invoice/api/services/invoice.service'

interface Params {
  filter: InvoiceDraftExportExcelFilter
}

export function useInvoiceDraftExportExcelMutation(): UseMutationReturnType<Params, FileDownload> {
  return useMutation<Params, FileDownload>({
    queryFn: async ({ body }) => {
      return await InvoiceService.exportDraftExcel(body.filter)
    },
    queryKeysToInvalidate: {},
  })
}
