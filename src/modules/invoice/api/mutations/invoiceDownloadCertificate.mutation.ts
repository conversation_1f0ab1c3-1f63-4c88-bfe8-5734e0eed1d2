import type { UseMutationReturnType } from '@wisemen/vue-core-query'
import { useMutation } from '@wisemen/vue-core-query'

import type { CertificateType } from '@/client'
import type { FileDownload } from '@/models/file/fileDownload.model'
import { InvoiceService } from '@/modules/invoice/api/services/invoice.service'

interface Params {
  fileName: string
  type: CertificateType
}

export function useInvoiceDownloadCertificate(): UseMutationReturnType<Params, FileDownload> {
  return useMutation<Params, FileDownload>({
    queryFn: async ({ body }) => {
      return await InvoiceService.downloadCertificate(body.fileName, body.type)
    },
    queryKeysToInvalidate: {},
  })
}
