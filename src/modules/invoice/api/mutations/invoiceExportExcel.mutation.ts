import type { UseMutationReturnType } from '@wisemen/vue-core-query'
import { useMutation } from '@wisemen/vue-core-query'

import type { FileDownload } from '@/models/file/fileDownload.model'
import type { InvoiceExportExcelFilter } from '@/models/invoice/export/invoiceExportExcelFilter.model'
import { InvoiceService } from '@/modules/invoice/api/services/invoice.service'

interface Params {
  filter: InvoiceExportExcelFilter
}

export function useInvoiceExportExcelMutation(): UseMutationReturnType<Params, FileDownload> {
  return useMutation<Params, FileDownload>({
    queryFn: async ({ body }) => {
      return await InvoiceService.exportExcel(body.filter)
    },
    queryKeysToInvalidate: {},
  })
}
