import type { UseMutationReturnType } from '@wisemen/vue-core-query'
import { useMutation } from '@wisemen/vue-core-query'

import type { FileDownload } from '@/models/file/fileDownload.model'
import { InvoiceService } from '@/modules/invoice/api/services/invoice.service'

interface Params {
  invoiceNumber: string
}

export function useInvoiceDownloadMutation(): UseMutationReturnType<Params, FileDownload> {
  return useMutation<Params, FileDownload>({
    queryFn: async ({ body }) => {
      return await InvoiceService.downloadInvoice(body.invoiceNumber)
    },
    queryKeysToInvalidate: {},
  })
}
