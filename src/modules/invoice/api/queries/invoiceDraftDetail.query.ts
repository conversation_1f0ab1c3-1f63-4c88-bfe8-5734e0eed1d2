import type { UseQueryReturnType } from '@wisemen/vue-core-query'
import { useQuery } from '@wisemen/vue-core-query'
import type { ComputedRef } from 'vue'

import type { InvoiceDraftDetail } from '@/models/invoice/draft-detail/invoiceDraftDetail.model.ts'
import { InvoiceService } from '@/modules/invoice/api/services/invoice.service'

export function useInvoiceDraftDetailQuery(
  invoiceNumber: ComputedRef<string>,
): UseQueryReturnType<InvoiceDraftDetail> {
  return useQuery({
    queryFn: () => InvoiceService.getDraftDetail(invoiceNumber.value),
    queryKey: { invoiceDraftDetail: { invoiceNumber } },
  })
}
