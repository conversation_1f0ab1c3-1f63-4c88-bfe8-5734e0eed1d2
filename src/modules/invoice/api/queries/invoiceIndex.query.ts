import { useOffsetInfiniteQuery } from '@/composables/offset-infinite-query/offsetInfiniteQuery.composable.ts'
import type { InvoiceIndexQueryParams } from '@/models/invoice/index/invoiceIndexQueryParams.model'
import { InvoiceService } from '@/modules/invoice/api/services/invoice.service'
import type { InfiniteQueryOptions } from '@/types/query.type'

// eslint-disable-next-line ts/explicit-function-return-type
export function useInvoiceIndexQuery(options: InfiniteQueryOptions<InvoiceIndexQueryParams>) {
  return useOffsetInfiniteQuery({
    queryFn: (pagination) => {
      return InvoiceService.getAll({
        filters: options.params.filters.value,
        pagination,
        sort: options.params.sort?.value || [],
      })
    },
    queryKey: { invoiceIndex: { queryParams: options.params } },
  })
}
