import { useOffsetInfiniteQuery } from '@/composables/offset-infinite-query/offsetInfiniteQuery.composable.ts'
import type { InvoiceDraftIndexQueryParams } from '@/models/invoice/draft-index/invoiceDraftIndexQueryParams.model'
import { InvoiceService } from '@/modules/invoice/api/services/invoice.service'
import type { InfiniteQueryOptions } from '@/types/query.type'

// eslint-disable-next-line ts/explicit-function-return-type
export function useInvoiceDraftIndexQuery(options: InfiniteQueryOptions<InvoiceDraftIndexQueryParams>) {
  return useOffsetInfiniteQuery({
    queryFn: (pagination) => {
      return InvoiceService.getAllDrafts({
        filters: options.params.filters.value,
        pagination,
      })
    },
    queryKey: { invoiceDraftIndex: { queryParams: options.params } },
  })
}
