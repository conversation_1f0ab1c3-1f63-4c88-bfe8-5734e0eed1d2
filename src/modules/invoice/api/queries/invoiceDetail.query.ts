import type { UseQueryReturnType } from '@wisemen/vue-core-query'
import { useQuery } from '@wisemen/vue-core-query'
import type { ComputedRef } from 'vue'

import type { InvoiceDetail } from '@/models/invoice/detail/invoiceDetail.model'
import { InvoiceService } from '@/modules/invoice/api/services/invoice.service'

export function useInvoiceDetailQuery(
  invoiceNumber: ComputedRef<string>,
): UseQueryReturnType<InvoiceDetail> {
  return useQuery({
    queryFn: () => InvoiceService.getByUuid(invoiceNumber.value),
    queryKey: { invoiceDetail: { invoiceNumber } },
  })
}
