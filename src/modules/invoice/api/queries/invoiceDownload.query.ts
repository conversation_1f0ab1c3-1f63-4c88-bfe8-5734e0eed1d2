import type { UseQueryReturnType } from '@wisemen/vue-core-query'
import { useQuery } from '@wisemen/vue-core-query'
import type { ComputedRef } from 'vue'

import type { FileDownload } from '@/models/file/fileDownload.model'
import { InvoiceService } from '@/modules/invoice/api/services/invoice.service'

export function useInvoiceDownloadQuery(
  invoiceNumber: ComputedRef<string>,
): UseQueryReturnType<FileDownload> {
  return useQuery({
    queryFn: () => InvoiceService.downloadInvoice(invoiceNumber.value),
    queryKey: { invoiceDownload: { invoiceNumber } },
  })
}
