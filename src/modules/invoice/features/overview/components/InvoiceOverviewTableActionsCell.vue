<script setup lang="ts">
import { VcIconButton } from '@wisemen/vue-core-components'
import { useI18n } from 'vue-i18n'

import AppGroup from '@/components/app/AppGroup.vue'
import DataTableCell from '@/components/table/data-table/DataTableCell.vue'

const props = defineProps<{
  hideActions?: boolean
}>()

const emit = defineEmits<{
  approve: []
  reject: []
}>()

const i18n = useI18n()

function onApprove(): void {
  emit('approve')
}

function onReject(): void {
  emit('reject')
}
</script>

<template>
  <DataTableCell>
    <AppGroup
      v-if="!props.hideActions"
      gap="sm"
    >
      <VcIconButton
        :label="i18n.t('shared.approve')"
        :class-config="{
          icon: 'text-fg-brand-primary',
          root: 'size-6',
        }"
        variant="tertiary"
        size="sm"
        icon="check"
        @click="onApprove"
      />
      <VcIconButton
        :label="i18n.t('shared.reject')"
        :class-config="{
          icon: 'text-error-primary',
          root: 'size-6',
        }"
        variant="tertiary"
        size="sm"
        icon="close"
        @click="onReject"
      />
    </AppGroup>
  </DataTableCell>
</template>
