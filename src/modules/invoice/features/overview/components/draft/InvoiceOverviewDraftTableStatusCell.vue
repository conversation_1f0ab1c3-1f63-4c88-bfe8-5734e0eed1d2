<script setup lang="ts">
import type { VcBadgeProps } from '@wisemen/vue-core-components'
import { VcBadge } from '@wisemen/vue-core-components'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import { DraftInvoiceStatus } from '@/client'
import AppGroup from '@/components/app/AppGroup.vue'
import DataTableCell from '@/components/table/data-table/DataTableCell.vue'
import { DraftInvoiceStatusEnumUtil } from '@/models/enums/draftInvoiceStatus.enum'

const props = defineProps<{
  isInternalUser: boolean
  status: DraftInvoiceStatus
}>()

const i18n = useI18n()

const statusBars = [
  [
    DraftInvoiceStatus.TO_BE_APPROVED_BY_CUSTOMER,
    DraftInvoiceStatus.TO_BE_APPROVED_BY_INDAVER,
  ],
  [
    DraftInvoiceStatus.APPROVED_BY_CUSTOMER,
    DraftInvoiceStatus.AUTO_APPROVED,
    DraftInvoiceStatus.INTERNAL_APPROVED,
  ],
  [
    DraftInvoiceStatus.REJECTED_BY_CUSTOMER,
    DraftInvoiceStatus.REJECTED_BY_INDAVER,
  ],

]

const statusColor = computed<VcBadgeProps['color']>(() => {
  switch (props.status) {
    case DraftInvoiceStatus.TO_BE_APPROVED_BY_CUSTOMER:
    case DraftInvoiceStatus.TO_BE_APPROVED_BY_INDAVER:
      return 'purple'
    case DraftInvoiceStatus.APPROVED_BY_CUSTOMER:
    case DraftInvoiceStatus.AUTO_APPROVED:
    case DraftInvoiceStatus.INTERNAL_APPROVED:
      return 'success'
    case DraftInvoiceStatus.REJECTED_BY_CUSTOMER:
    case DraftInvoiceStatus.REJECTED_BY_INDAVER:
      return 'error'
    default:
      return 'gray'
  }
})

function isStatusBarActive(statuses: DraftInvoiceStatus[]): boolean {
  return statuses.includes(props.status)
}
</script>

<template>
  <DataTableCell>
    <AppGroup gap="2xl">
      <div class="gap-x-sm flex items-center">
        <template
          v-for="(statusBarStatus, index) of statusBars"
          :key="index"
        >
          <div
            v-if="!isStatusBarActive(statusBarStatus)"
            class="bg-tertiary h-0.5 w-3 rounded-full"
          />
          <VcBadge
            v-else
            :class-config="{
              root: 'px-sm',
            }"
            :color="statusColor"
            variant="translucent"
            size="sm"
          >
            {{ index + 1 }}
          </VcBadge>
        </template>
      </div>

      <VcBadge
        :color="statusColor"
        variant="translucent"
        size="sm"
      >
        {{ props.isInternalUser
          ? i18n.t(DraftInvoiceStatusEnumUtil.getI18nKey(props.status))
          : i18n.t(DraftInvoiceStatusEnumUtil.getShortStatusI18nKey(props.status))
        }}
      </VcBadge>
    </AppGroup>
  </DataTableCell>
</template>
