<script setup lang="ts">
import {
  VcDialog,
  VcDialogCloseButton,
  VcDialogDescription,
  VcDialogTitle,
} from '@wisemen/vue-core-components'
import { useForm } from 'formango'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import AppGroup from '@/components/app/AppGroup.vue'
import { useApiErrorToast } from '@/composables/api-error-toast/apiErrorToast.composable'
import { TEST_ID } from '@/constants/testId.constant.ts'
import type { InvoiceDraftIndex } from '@/models/invoice/draft-index/invoiceDraftIndex.model'
import {
  InvoiceReview,
  invoiceReviewFormSchema,
} from '@/models/invoice/review/invoiceReviewForm.model'
import { useInvoiceApproveDraftMutation } from '@/modules/invoice/api/mutations/invoiceApproveDraft.mutation'
import { useInvoiceRejectDraftMutation } from '@/modules/invoice/api/mutations/invoiceRejectDraft.mutation'
import InvoiceReviewForm from '@/modules/invoice/features/overview/components/InvoiceReviewForm.vue'

const props = defineProps<{
  invoice: InvoiceDraftIndex
  reviewType: InvoiceReview
}>()
const emit = defineEmits<{
  close: []
}>()

const i18n = useI18n()
const apiErrorToast = useApiErrorToast()

const isApproval = computed<boolean>(() => props.reviewType === InvoiceReview.APPROVE)

const approvalMutation = useInvoiceApproveDraftMutation()
const rejectMutation = useInvoiceRejectDraftMutation()

const form = useForm({
  initialState: {
    poNumber: null,
    remark: null,
    review: props.reviewType,
  },
  schema: invoiceReviewFormSchema,
  onSubmit: async (values) => {
    try {
      if (isApproval.value) {
        await approvalMutation.execute({
          body: {
            form: values,
            invoiceNumber: props.invoice.invoiceNumber,
          },
        })
        emit('close')

        return
      }

      await rejectMutation.execute({
        body: {
          form: values,
          invoiceNumber: props.invoice.invoiceNumber,
        },
      })
      emit('close')
    }
    catch (error) {
      apiErrorToast.show(error)
    }
  },
})

function onCancel(): void {
  emit('close')
}
</script>

<template>
  <VcDialog
    :test-id="TEST_ID.INVOICES.REVIEW.REVIEW_DIALOG"
    class="w-dialog-md p-3xl"
    @close="emit('close')"
  >
    <AppGroup
      justify="between"
      align="start"
    >
      <AppGroup
        direction="col"
        justify="start"
        align="start"
        gap="none"
        class="mb-xl"
      >
        <p class="text-brand-700 text-xs">
          {{ isApproval
            ? i18n.t('module.invoices.review.approve_subtitle')
            : i18n.t('module.invoices.review.reject_subtitle')
          }}
        </p>

        <VcDialogTitle>
          <h1 class="text-primary text-xl font-semibold">
            {{ isApproval
              ? i18n.t('module.invoices.review.approve_title', {
                invoiceNumber: props.invoice.invoiceNumber,
              })
              : i18n.t('module.invoices.review.reject_title', {
                invoiceNumber: props.invoice.invoiceNumber,
              })
            }}
          </h1>
        </VcDialogTitle>
        <VcDialogDescription>
          <p class="text-secondary text-sm">
            {{ isApproval
              ? i18n.t('module.invoices.review.approve_description')
              : i18n.t('module.invoices.review.reject_description')
            }}
          </p>
        </VcDialogDescription>
      </AppGroup>

      <VcDialogCloseButton />
    </AppGroup>

    <InvoiceReviewForm
      :is-approval="props.reviewType === InvoiceReview.APPROVE"
      :form="form"
      @close="onCancel"
    />
  </VcDialog>
</template>
