<script setup lang="ts">
import {
  VcTextarea,
  VcTextField,
} from '@wisemen/vue-core-components'
import type { Form } from 'formango'
import { useI18n } from 'vue-i18n'

import AppSeparator from '@/components/app/AppSeparator.vue'
import AppDialogActionCancel from '@/components/app/dialog/AppDialogActionCancel.vue'
import AppDialogActions from '@/components/app/dialog/AppDialogActions.vue'
import FormGrid from '@/components/app/grid/FormGrid.vue'
import AppForm from '@/components/form/AppForm.vue'
import FormSubmitButton from '@/components/form/FormSubmitButton.vue'
import { TEST_ID } from '@/constants/testId.constant'
import type { invoiceReviewFormSchema } from '@/models/invoice/review/invoiceReviewForm.model'
import { toFormField } from '@/utils/formango.util'

const props = defineProps<{
  isApproval: boolean
  form: Form<typeof invoiceReviewFormSchema>
}>()

const i18n = useI18n()
const remark = props.form.register('remark')
const poNumber = props.form.register('poNumber', null)
</script>

<template>
  <AppForm
    :form="props.form"
    :can-exit-when-dirty="true"
  >
    <FormGrid
      :cols="1"
      gap="lg"
    >
      <VcTextField
        v-if="props.isApproval"
        v-bind="toFormField(poNumber)"
        :label="i18n.t('module.invoices.review.fields.po_number')"
        :placeholder="i18n.t('module.invoices.review.fields.po_number')"
      />

      <VcTextarea
        v-bind="toFormField(remark)"
        :is-required="!props.isApproval"
        :test-id="TEST_ID.INVOICES.REVIEW.FORM.COMMENT_FIELD"
        :label="i18n.t('module.invoices.review.fields.comment')"
        :placeholder="isApproval
          ? i18n.t('module.invoices.review.fields.approve_reason')
          : i18n.t('module.invoices.review.fields.reject_reason')"
      />
    </FormGrid>

    <AppSeparator class="mt-3xl" />
    <AppDialogActions class="mt-xl">
      <AppDialogActionCancel
        :label="i18n.t('shared.cancel')"
      />

      <FormSubmitButton
        :form="form"
        :test-id="TEST_ID.INVOICES.REVIEW.FORM.SUBMIT_BUTTON"
        :is-always-enabled="true"
        :label="
          isApproval
            ? i18n.t('module.invoices.review.submit_approval')
            : i18n.t('module.invoices.review.submit_rejection')
        "
      />
    </AppDialogActions>
  </AppForm>
</template>
