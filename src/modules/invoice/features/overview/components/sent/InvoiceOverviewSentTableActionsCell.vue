<script setup lang="ts">
import { VcIconButton } from '@wisemen/vue-core-components'
import { useI18n } from 'vue-i18n'

import AppGroup from '@/components/app/AppGroup.vue'
import DataTableCell from '@/components/table/data-table/DataTableCell.vue'
import { TEST_ID } from '@/constants/testId.constant'

const props = withDefaults(defineProps<{
  isLoading?: boolean
}>(), { isLoading: false })

const emit = defineEmits<{
  download: []
}>()

const i18n = useI18n()

function onDownload(): void {
  emit('download')
}
</script>

<template>
  <DataTableCell :has-interactive-content="true">
    <AppGroup gap="xs">
      <VcIconButton
        :label="i18n.t('shared.download')"
        :class-config="{
          icon: 'text-fg-brand-primary',
        }"
        :is-loading="props.isLoading"
        :is-disabled="props.isLoading"
        :test-id="TEST_ID.INVOICES.TABLE.ACTIONS_DOWNLOAD"
        icon="download"
        size="sm"
        variant="tertiary"
        @click="onDownload"
      />
    </AppGroup>
  </DataTableCell>
</template>
