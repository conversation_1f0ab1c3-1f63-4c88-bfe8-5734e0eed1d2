<script setup lang="ts">
import {
  VcRouterLinkTabs,
  VcRouterLinkTabsItem,
} from '@wisemen/vue-core-components'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import {
  RouterView,
  useRoute,
} from 'vue-router'

import { Permission } from '@/client'
import AppPage from '@/components/layout/page/AppPage.vue'
import { useDocumentTitle } from '@/composables/document-title/documentTitle.composable.ts'
import { useAuthStore } from '@/stores/auth.store.ts'
import type { RouteLocationCurrent } from '@/types/global/vueRouter'

interface Tab {
  id: string
  label: string
  to: RouteLocationCurrent
}

const i18n = useI18n()
const documentTitle = useDocumentTitle()
const authStore = useAuthStore()
const route = useRoute<'invoice-overview'>()

documentTitle.set(() => i18n.t('module.invoice.overview.title'))

const hasInvoiceReadPermission = computed<boolean>(() => {
  return authStore.hasPermission(Permission.INVOICE_READ)
})

const hasInvoiceManagePermission = computed<boolean>(() => {
  return authStore.hasPermission(Permission.INVOICE_MANAGE)
})

const tabs = computed<Tab[]>(() => {
  const tabList: Tab[] = []

  if (hasInvoiceReadPermission.value || hasInvoiceManagePermission.value) {
    tabList.push({
      id: 'sent',
      label: i18n.t('module.invoice.overview.title'),
      to: { name: 'invoices-overview-sent' },
    })
  }

  if (hasInvoiceManagePermission.value) {
    tabList.push({
      id: 'drafts',
      label: i18n.t('module.invoice.overview.tab.draft'),
      to: { name: 'invoices-overview-drafts' },
    })
  }

  return tabList
})

const overviewRouteNames = new Set([
  'invoices-overview-sent',
  'invoices-overview-drafts',
])

const isOverviewRoute = computed<boolean>(() => {
  return overviewRouteNames.has(route.name as string)
})
</script>

<template>
  <AppPage
    v-if="isOverviewRoute"
    :title="i18n.t('module.invoice.overview.title')"
    class="pb-xl"
  >
    <VcRouterLinkTabs
      :class-config="{
        scrollContainer: 'p-[3px] pb-0',
        item: '!mt-0',
      }"
      class="mb-xl border-secondary border-b"
    >
      <template #items>
        <VcRouterLinkTabsItem
          v-for="tab of tabs"
          :key="tab.id"
          :value="tab.id"
          :to="tab.to"
        >
          {{ tab.label }}
        </VcRouterLinkTabsItem>
      </template>
    </VcRouterLinkTabs>
    <RouterView />
  </AppPage>
  <RouterView v-else />
</template>
