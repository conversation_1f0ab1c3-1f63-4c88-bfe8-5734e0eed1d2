<script setup lang="ts">
import {
  V<PERSON><PERSON><PERSON><PERSON>,
  Vc<PERSON><PERSON><PERSON>,
  VcTabsItem,
} from '@wisemen/vue-core-components'
import type { VNode } from 'vue'
import {
  computed,
  h,
  ref,
} from 'vue'
import { useI18n } from 'vue-i18n'

import {
  DynamicColumnNames,
  InvoiceColumnName,
  InvoiceFilterType,
  InvoiceStatus,
} from '@/client/types.gen.ts'
import AppGroup from '@/components/app/AppGroup.vue'
import AppTeleport from '@/components/app/teleport/AppTeleport.vue'
import FiltersActive from '@/components/filters/active/FiltersActive.vue'
import {
  createAutocompleteFilter,
  createDateRangeFilter,
  createSelectFilter,
} from '@/components/filters/createFilters.ts'
import FiltersDropdownMenu from '@/components/filters/dropdown-menu/FiltersDropdownMenu.vue'
import { useFilters } from '@/components/filters/filters.composable.ts'
import FiltersRoot from '@/components/filters/FiltersRoot.vue'
import AppTablePage from '@/components/layout/AppTablePage.vue'
import DataTable from '@/components/table/data-table/DataTable.vue'
import DynamicTableSettings from '@/components/table/dynamic-table-settings/DynamicTableSettings.vue'
import DynamicTableViews from '@/components/table/dynamic-table-views/DynamicTableViews.vue'
import { useApiErrorToast } from '@/composables/api-error-toast/apiErrorToast.composable.ts'
import { useDynamicTableV2 } from '@/composables/dynamic-table/dynamicTableV2.composable.ts'
import { useSearch } from '@/composables/search/search.composable.ts'
import { useSearchableTableColumns } from '@/composables/searchable-table-columns/searchableTableColumns.composable.ts'
import { useSort } from '@/composables/sort/sort.composable.ts'
import { useGenericColumn } from '@/composables/table-columns/genericTableColumnsV2.composable.ts'
import { TEST_ID } from '@/constants/testId.constant.ts'
import { DynamicTableName } from '@/models/enums/dynamicTableName.enum.ts'
import { InvoiceTypeEnumUtil } from '@/models/enums/invoiceType.enum.ts'
import type { InvoiceIndex } from '@/models/invoice/index/invoiceIndex.model.ts'
import { InvoiceIndexInvoiceTableTabs } from '@/models/invoice/index/invoiceIndex.model.ts'
import type { InvoiceIndexQueryParams } from '@/models/invoice/index/invoiceIndexQueryParams.model.ts'
import { useInvoiceDownloadMutation } from '@/modules/invoice/api/mutations/invoiceDownload.mutation.ts'
import { useInvoiceExportExcelMutation } from '@/modules/invoice/api/mutations/invoiceExportExcel.mutation.ts'
import { useInvoiceIndexQuery } from '@/modules/invoice/api/queries/invoiceIndex.query.ts'
import InvoiceOverviewSentTableActionsCell from '@/modules/invoice/features/overview/components/sent/InvoiceOverviewSentTableActionsCell.vue'
import InvoiceOverviewSentTableStatusCell from '@/modules/invoice/features/overview/components/sent/InvoiceOverviewSentTableStatusCell.vue'
import { useCustomerIndexQuery } from '@/modules/waste-inquiry/api/queries/customerIndex.query.ts'
import type { DataTableColumn } from '@/types/table.type.ts'
import { DownloadUtil } from '@/utils/download.util.ts'

interface Tab {
  id: string
  label: string
  statuses: InvoiceStatus[]
}

const i18n = useI18n()
const apiErrorToast = useApiErrorToast()

const exportExcelMutation = useInvoiceExportExcelMutation()
const downloadInvoiceMutation = useInvoiceDownloadMutation()

const customerIndexSearch = useSearch({ persistInUrl: false })

const customerIndexQuery = useCustomerIndexQuery({ params: { search: customerIndexSearch.debouncedSearch } })

const tabs = computed<Tab[]>(() => {
  return [
    {
      id: InvoiceIndexInvoiceTableTabs.ALL,
      label: i18n.t('module.invoice.overview.tab.all'),
      statuses: [
        InvoiceStatus.OUTSTANDING,
        InvoiceStatus.OVERDUE,
        InvoiceStatus.CLEARED,
      ],
    },
    {
      id: InvoiceIndexInvoiceTableTabs.OUTSTANDING,
      label: i18n.t('module.invoice.overview.tab.outstanding'),
      statuses: [
        InvoiceStatus.OUTSTANDING,
      ],
    },
    {
      id: InvoiceIndexInvoiceTableTabs.OVERDUE,
      label: i18n.t('module.invoice.overview.tab.overdue'),
      statuses: [
        InvoiceStatus.OVERDUE,
      ],
    },
    {
      id: InvoiceIndexInvoiceTableTabs.CLEARED,
      label: i18n.t('module.invoice.overview.tab.cleared'),
      statuses: [
        InvoiceStatus.CLEARED,
      ],
    },
  ]
})

const activeTab = ref<Tab>(tabs.value[0])
const tabModelValue = computed<string>({
  get: () => activeTab.value.id,
  set: (id: string) => {
    const selectedTab = tabs.value.find((tab) => tab.id === id) ?? null

    if (selectedTab !== null) {
      activeTab.value = selectedTab
    }
  },
})

const filters = useFilters({
  filterGroups: () => [
    {
      filters: [
        createDateRangeFilter({
          defaultValue: {
            from: null,
            until: null,
          },
          key: 'issueDate',
          label: i18n.t('enum.dynamic_table_column_name.issued_on'),
        }),
        createDateRangeFilter({
          defaultValue: {
            from: null,
            until: null,
          },
          key: 'dueDate',
          label: i18n.t('enum.dynamic_table_column_name.due_on'),
        }),
        createAutocompleteFilter({
          isLoading: customerIndexQuery.isLoading.value,
          defaultValue: null,
          displayFn: (option) => option.name,
          key: 'customerId',
          label: i18n.t('enum.dynamic_table_column_name.customer_name'),
          options: customerIndexQuery.data.value?.data.map((customer) => ({
            id: customer.id,
            name: customer.name,
          })) ?? [],
          onSearch: (value) => {
            customerIndexSearch.updateSearch(value)
          },
        }),
        createSelectFilter({
          defaultValue: null,
          displayFn: (option) => option.label,
          key: 'type',
          label: i18n.t('enum.dynamic_table_column_name.type'),
          options: [
            {
              label: i18n.t('enum.invoice_type.invoice'),
              value: InvoiceFilterType.INVOICE,
            },
            {
              label: i18n.t('enum.invoice_type.credit_memo'),
              value: InvoiceFilterType.CREDIT_MEMO,
            },
            {
              label: i18n.t('enum.invoice_type.debit_memo'),
              value: InvoiceFilterType.DEBIT_MEMO,
            },
          ],
        }),
      ],
    },
  ],
  persistInUrl: false,
})

const sort = useSort({
  enableMultiSort: true,
  keys: [
    'accountDocumentNumber',
    'companyName',
    'customerName',
    'customerReference',
    'dueDate',
    'invoiceNumber',
    'issueDate',
    'netAmount',
    'payerName',
  ],
  persistInUrl: false,
})

const searchableColumns = useSearchableTableColumns({
  keys: [
    'customerReference',
    'companyName',
    'invoiceNumber',
    'accountDocumentNumber',
    'accountManagerName',
  ],
})

const dynamicTable = useDynamicTableV2({
  dynamicTableName: DynamicTableName.INVOICE,
  filters,
  sort,
})

const {
  isLoading,
  data,
  error,
  fetchNextPage,
} = useInvoiceIndexQuery({
  params: {
    filters: computed<InvoiceIndexQueryParams['filters']>(() => ({
      customerId: filters.values.value.customerId || null,
      payerId: null,
      dueDate: filters.values.value.dueDate || null,
      issueDate: filters.values.value.issueDate || null,
      accountDocumentNumber: searchableColumns.values.value.accountDocumentNumber || undefined,
      accountManagerName: searchableColumns.values.value.accountManagerName || undefined,
      companyName: searchableColumns.values.value.companyName || undefined,
      customerReference: searchableColumns.values.value.customerReference || undefined,
      invoiceNumber: searchableColumns.values.value.invoiceNumber || undefined,
      statuses: activeTab.value.statuses,
      type: filters.values.value.type?.value || undefined,
    })),
    sort: sort.values,
  },
})

const downloadingInvoiceNumber = ref<string | null>(null)

const dynamicColumns = computed<DataTableColumn<InvoiceIndex>[]>(() => {
  const testIdByDynamicName: Partial<Record<DynamicColumnNames, string>> = {
    [DynamicColumnNames.CURRENCY]: TEST_ID.INVOICES.TABLE.CURRENCY,
    [DynamicColumnNames.CUSTOMER_NAME]: TEST_ID.INVOICES.TABLE.CUSTOMER_NAME,
    [DynamicColumnNames.DUE_ON]: TEST_ID.INVOICES.TABLE.DUE_ON,
    [DynamicColumnNames.INVOICE_NUMBER]: TEST_ID.INVOICES.TABLE.INVOICE_NUMBER,
    [DynamicColumnNames.ISSUED_ON]: TEST_ID.INVOICES.TABLE.ISSUED_ON,
    [DynamicColumnNames.NET_AMOUNT]: TEST_ID.INVOICES.TABLE.NET_AMOUNT,
    [DynamicColumnNames.PAYER_NAME]: TEST_ID.INVOICES.TABLE.PAYER_NAME,
    [DynamicColumnNames.STATUS]: TEST_ID.INVOICES.TABLE.STATUS,
    [DynamicColumnNames.TYPE]: TEST_ID.INVOICES.TABLE.TYPE,
    [DynamicColumnNames.VAT_AMOUNT]: TEST_ID.INVOICES.TABLE.VAT_AMOUNT,
  }

  return dynamicTable.columns.value
    .filter((column) => column.isVisible)
    .map((column) => {
      switch (column.name) {
        case DynamicColumnNames.STATUS:
          return {
            testId: testIdByDynamicName[column.name],
            cell: (row): VNode => h(InvoiceOverviewSentTableStatusCell, { status: row.status }),
            headerLabel: column.label,
            key: 'status',
          }
        case DynamicColumnNames.TYPE:
          return {
            ...useGenericColumn(
              column.name,
              column.label,
              (value): string => i18n.t(InvoiceTypeEnumUtil.getI18nKey(value.type)),
            ),
            testId: testIdByDynamicName[column.name],
          }
        default:
          return {
            ...useGenericColumn(column.name, column.label),
            testId: testIdByDynamicName[column.name],
          }
      }
    })
})

const columns = computed<DataTableColumn<InvoiceIndex>[]>(() => [
  ...dynamicColumns.value,
  {
    cell: (row): VNode => h(InvoiceOverviewSentTableActionsCell, {
      isLoading: downloadInvoiceMutation.isLoading.value
        && downloadingInvoiceNumber.value === row.invoiceNumber,
      onDownload: () => onDownload(row),
    }),
    headerLabel: '',
    key: 'actions',
    maxWidth: 'max-content',
  },
])

async function handleExport(): Promise<void> {
  try {
    const visibleColumns = dynamicTable.columns.value.filter((column) => column.isVisible)

    const allowedColumnNames = Object.values(InvoiceColumnName) as string[]
    const filteredColumns = visibleColumns
      .filter((column) => allowedColumnNames.includes(column.name as string))

    const columns = filteredColumns
      .map((column) => column.name as unknown as InvoiceColumnName)

    const translatedColumns = filteredColumns
      .map((column) => String(column.label))

    const {
      blob, disposition,
    } = await exportExcelMutation.execute({
      body: {
        filter: {
          columns,
          statuses: activeTab.value.statuses,
          translatedColumns,
        },
      },
    })

    DownloadUtil.downloadBlob(blob, disposition)
  }
  catch (error) {
    apiErrorToast.show(error)
  }
}

async function onDownload(invoice: InvoiceIndex): Promise<void> {
  const invoiceNumber = invoice.invoiceNumber

  downloadingInvoiceNumber.value = invoiceNumber

  try {
    const {
      blob, disposition,
    } = await downloadInvoiceMutation.execute({ body: { invoiceNumber } })

    DownloadUtil.downloadBlob(blob, disposition)
  }
  catch (error) {
    apiErrorToast.show(error)
  }
  finally {
    downloadingInvoiceNumber.value = null
  }
}
</script>

<template>
  <AppTeleport to="headerActions">
    <VcButton
      :is-loading="exportExcelMutation.isLoading.value"
      variant="secondary"
      icon-left="download"
      @click="handleExport"
    >
      {{ i18n.t('shared.export_excel') }}
    </VcButton>
  </AppTeleport>

  <AppTablePage
    :title="i18n.t('module.invoice.overview.title')"
    :is-title-hidden="true"
    :is-header-hidden="true"
    :remove-content-padding="true"
  >
    <VcTabs
      v-model="tabModelValue"
      :class-config="{
        base: 'pl',
        indicator: 'hidden',
        item: 'border h-9 border-b-0 border-primary rounded-xl rounded-b-none min-w-36 data-[state=inactive]:bg-secondary !m-0 data-[state=active]:text-primary data-[state=inactive]:font-regular enabled:data-[state=active]:hover:bg-transparent',
        list: 'gap-x-0 inline-flex',
        scrollContainer: 'p-xxs pb-0',
      }"
    >
      <template #items>
        <VcTabsItem
          v-for="(tab, statusIndex) of tabs"
          :key="tab.id"
          :value="tab.id"
          :class="{
            '!-ml-px': statusIndex !== 0,
          }"
        >
          {{ tab.label }}
        </VcTabsItem>
      </template>
    </VcTabs>

    <DataTable
      v-if="dynamicTable.activeView.value !== null"
      :is-loading="isLoading"
      :data="data.data"
      :get-key="(row) => row.invoiceNumber"
      :columns="columns"
      :error="error"
      :sort="sort"
      :filters="filters"
      :searchable-columns="searchableColumns"
      :is-first-column-sticky="true"
      :disable-top-left-border-radius="true"
      :is-last-column-sticky="true"
      :row-action="{
        label: () => i18n.t('module.invoice.overview.action.view_invoice'),
        to: (row) => ({
          name: 'invoice-detail',
          params: {
            invoiceNumber: row.invoiceNumber,
          },
        }),
        type: 'link',
      }"
      @next-page="fetchNextPage"
    >
      <template #top>
        <FiltersRoot :filters="filters">
          <AppGroup
            justify="between"
            class="px-xl h-14"
          >
            <FiltersActive />

            <AppGroup>
              <DynamicTableViews :dynamic-table="dynamicTable" />
              <FiltersDropdownMenu />
              <DynamicTableSettings :dynamic-table="dynamicTable" />
            </AppGroup>
          </AppGroup>
        </FiltersRoot>
      </template>
    </DataTable>
  </AppTablePage>
</template>
