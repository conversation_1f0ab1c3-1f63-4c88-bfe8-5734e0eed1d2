<script setup lang="ts">
import {
  useV<PERSON><PERSON><PERSON><PERSON>,
  Vc<PERSON>utton,
  Vc<PERSON><PERSON><PERSON>,
  VcTabsItem,
} from '@wisemen/vue-core-components'
import type { VNode } from 'vue'
import {
  computed,
  h,
  ref,
} from 'vue'
import { useI18n } from 'vue-i18n'

import {
  DraftInvoiceFilterStatus,
  DynamicColumnNames,
  InvoiceColumnName,
  Permission,
} from '@/client/types.gen.ts'
import AppGroup from '@/components/app/AppGroup.vue'
import AppTeleport from '@/components/app/teleport/AppTeleport.vue'
import { useFilters } from '@/components/filters/filters.composable.ts'
import AppTablePage from '@/components/layout/AppTablePage.vue'
import DataTable from '@/components/table/data-table/DataTable.vue'
import DynamicTableSettings from '@/components/table/dynamic-table-settings/DynamicTableSettings.vue'
import DynamicTableViews from '@/components/table/dynamic-table-views/DynamicTableViews.vue'
import { useApiErrorToast } from '@/composables/api-error-toast/apiErrorToast.composable.ts'
import { useDynamicTableV2 } from '@/composables/dynamic-table/dynamicTableV2.composable.ts'
import { useSort } from '@/composables/sort/sort.composable.ts'
import { useGenericColumn } from '@/composables/table-columns/genericTableColumnsV2.composable.ts'
import { TEST_ID } from '@/constants/testId.constant.ts'
import { DynamicTableName } from '@/models/enums/dynamicTableName.enum.ts'
import { MailStatusEnumUtil } from '@/models/enums/mailStatus.enum.ts'
import type { InvoiceDraftIndex } from '@/models/invoice/draft-index/invoiceDraftIndex.model.ts'
import { InvoiceIndexDraftTableTabs } from '@/models/invoice/draft-index/invoiceDraftIndex.model.ts'
import type { InvoiceDraftIndexQueryParams } from '@/models/invoice/draft-index/invoiceDraftIndexQueryParams.model.ts'
import { InvoiceReview } from '@/models/invoice/review/invoiceReviewForm.model.ts'
import { useInvoiceDraftExportExcelMutation } from '@/modules/invoice/api/mutations/invoiceDraftExportExcel.mutation.ts'
import { useInvoiceDraftIndexQuery } from '@/modules/invoice/api/queries/invoiceDraftIndex.query.ts'
import InvoiceOverviewDraftTableStatusCell from '@/modules/invoice/features/overview/components/draft/InvoiceOverviewDraftTableStatusCell.vue'
import InvoiceOverviewTableActionsCell from '@/modules/invoice/features/overview/components/InvoiceOverviewTableActionsCell.vue'
import { useAuthStore } from '@/stores/auth.store.ts'
import type { DataTableColumn } from '@/types/table.type.ts'
import { DownloadUtil } from '@/utils/download.util.ts'

interface Tab {
  id: string
  label: string
  statuses: DraftInvoiceFilterStatus
}

const i18n = useI18n()
const authStore = useAuthStore()
const isInternalUser = ref<boolean>(authStore.isInternalUser())
const apiErrorToast = useApiErrorToast()

const exportExcelMutation = useInvoiceDraftExportExcelMutation()

const invoiceReviewDialog = useVcDialog({ component: () => import('@/modules/invoice/features/overview/components/InvoiceReviewDialog.vue') })

const hasInvoiceManagePermissions = computed<boolean>(() => {
  return authStore.hasPermission(Permission.INVOICE_MANAGE)
})

const tabs = computed<Tab[]>(() => {
  return [
    {
      id: InvoiceIndexDraftTableTabs.TO_BE_APPROVED,
      label: i18n.t('module.invoice.overview.tab.to_be_approved'),
      statuses: DraftInvoiceFilterStatus.TO_BE_APPROVED,
    },
    {
      id: InvoiceIndexDraftTableTabs.APPROVED,
      label: i18n.t('module.invoice.overview.tab.approved'),
      statuses: DraftInvoiceFilterStatus.APPROVED,
    },
    {
      id: InvoiceIndexDraftTableTabs.REJECTED,
      label: i18n.t('module.invoice.overview.tab.rejected'),
      statuses: DraftInvoiceFilterStatus.REJECTED,
    },
  ]
})

const activeTab = ref<Tab>(tabs.value[0])

const tabModelValue = computed<string>({
  get: () => activeTab.value.id,
  set: (id: string) => {
    const selectedTab = tabs.value.find((tab) => tab.id === id) ?? null

    if (selectedTab !== null) {
      activeTab.value = selectedTab
    }
  },
})

const filters = useFilters({
  filterGroups: () => [],
  persistInUrl: false,
})

const sort = useSort({
  enableMultiSort: true,
  keys: [],
  persistInUrl: false,
})

const dynamicTable = useDynamicTableV2({
  dynamicTableName: DynamicTableName.DRAFT_INVOICE,
  filters,
  sort,
})

const areActionsHidden = computed<boolean>(() => {
  return !hasInvoiceManagePermissions.value || activeTab.value.id !== InvoiceIndexDraftTableTabs.TO_BE_APPROVED
})

const dynamicColumns = computed<DataTableColumn<InvoiceDraftIndex>[]>(() => {
  const testIdByDynamicName: Partial<Record<DynamicColumnNames, string>> = {
    [DynamicColumnNames.CURRENCY]: TEST_ID.INVOICES.TABLE.CURRENCY,
    [DynamicColumnNames.FIRST_REMINDER_MAIL_STATUS]: TEST_ID.INVOICES.TABLE.FIRST_REMINDER_MAIL_STATUS,
    [DynamicColumnNames.FIRST_REMINDER_ON]: TEST_ID.INVOICES.TABLE.FIRST_REMINDER_ON,
    [DynamicColumnNames.INVOICE_NUMBER]: TEST_ID.INVOICES.TABLE.INVOICE_NUMBER,
    [DynamicColumnNames.ISSUED_ON]: TEST_ID.INVOICES.TABLE.ISSUED_ON,
    [DynamicColumnNames.NET_AMOUNT]: TEST_ID.INVOICES.TABLE.NET_AMOUNT,
    [DynamicColumnNames.PAYER_NAME]: TEST_ID.INVOICES.TABLE.PAYER_NAME,
    [DynamicColumnNames.SECOND_REMINDER_MAIL_STATUS]: TEST_ID.INVOICES.TABLE.SECOND_REMINDER_MAIL_STATUS,
    [DynamicColumnNames.SECOND_REMINDER_ON]: TEST_ID.INVOICES.TABLE.SECOND_REMINDER_ON,
    [DynamicColumnNames.STATUS]: TEST_ID.INVOICES.TABLE.STATUS,
    [DynamicColumnNames.THIRD_REMINDER_MAIL_STATUS]: TEST_ID.INVOICES.TABLE.THIRD_REMINDER_MAIL_STATUS,
    [DynamicColumnNames.VAT_AMOUNT]: TEST_ID.INVOICES.TABLE.VAT_AMOUNT,
  }

  if (!dynamicTable.columns.value) {
    return []
  }

  return dynamicTable.columns.value
    .filter((column) => column.isVisible)
    .map((column) => {
      switch (column.name) {
        case DynamicColumnNames.STATUS:
          return {
            testId: testIdByDynamicName[column.name],
            cell: (row): VNode => h(InvoiceOverviewDraftTableStatusCell, {
              isInternalUser: isInternalUser.value,
              status: row.status,
            }),
            headerLabel: column.label,
            key: 'status',
          }
        case DynamicColumnNames.FIRST_REMINDER_MAIL_STATUS:
          return {
            ...useGenericColumn(
              column.name,
              column.label,
              (row) => i18n.t(MailStatusEnumUtil.getI18nKey(row.firstReminderMailStatus)),
            ),
            testId: testIdByDynamicName[column.name],
          }
        case DynamicColumnNames.SECOND_REMINDER_MAIL_STATUS:
          return {
            ...useGenericColumn(
              column.name,
              column.label,
              (row) => i18n.t(MailStatusEnumUtil.getI18nKey(row.secondReminderMailStatus)),
            ),
            testId: testIdByDynamicName[column.name],
          }
        case DynamicColumnNames.THIRD_REMINDER_MAIL_STATUS:
          return {
            ...useGenericColumn(
              column.name,
              column.label,
              (row) => i18n.t(MailStatusEnumUtil.getI18nKey(row.thirdReminderMailStatus)),
            ),
            testId: testIdByDynamicName[column.name],
          }
        default:
          return {
            ...useGenericColumn(column.name, column.label),
            testId: testIdByDynamicName[column.name],
          }
      }
    })
})

const columns = computed<DataTableColumn<InvoiceDraftIndex>[]>(() => {
  const result: DataTableColumn<InvoiceDraftIndex>[] = [
    ...dynamicColumns.value,
    {
      cell: (row): VNode => h(InvoiceOverviewTableActionsCell, {
        hideActions: areActionsHidden.value,
        onApprove: () => onApproveDialog(row),
        onReject: () => onRejectDialog(row),
      }),
      headerLabel: '',
      key: 'actions',
      maxWidth: 'max-content',
    },
  ]

  return result
})

const {
  isLoading,
  data,
  error,
  fetchNextPage,
} = useInvoiceDraftIndexQuery({
  params: {
    filters: computed<InvoiceDraftIndexQueryParams['filters']>(() => ({
      customerId: null,
      payerId: null,
      issuedOn: null,
      invoiceNumber: undefined,
      statuses: [
        activeTab.value.statuses,
      ],
    })),
  },
})

function onApproveDialog(invoice: InvoiceDraftIndex): void {
  invoiceReviewDialog.open({
    invoice,
    reviewType: InvoiceReview.APPROVE,
  })
}

function onRejectDialog(invoice: InvoiceDraftIndex): void {
  invoiceReviewDialog.open({
    invoice,
    reviewType: InvoiceReview.REJECT,
  })
}

async function handleExport(): Promise<void> {
  try {
    const visibleColumns = dynamicTable.columns.value.filter((column) => column.isVisible)

    const allowedColumnNames = Object.values(InvoiceColumnName) as string[]
    const filteredColumns = visibleColumns
      .filter((column) => allowedColumnNames.includes(column.name as string))

    const columns = filteredColumns
      .map((column) => column.name as unknown as InvoiceColumnName)

    const translatedColumns = filteredColumns
      .map((column) => String(column.label))

    const {
      blob, disposition,
    } = await exportExcelMutation.execute({
      body: {
        filter: {
          columns,
          statuses: [
            activeTab.value.statuses,
          ],
          translatedColumns,
        },
      },
    })

    DownloadUtil.downloadBlob(blob, disposition)
  }
  catch (error) {
    apiErrorToast.show(error)
  }
}
</script>

<template>
  <AppTeleport to="headerActions">
    <VcButton
      :is-loading="exportExcelMutation.isLoading.value"
      variant="secondary"
      icon-left="download"
      @click="handleExport"
    >
      {{ i18n.t('shared.export_excel') }}
    </VcButton>
  </AppTeleport>

  <AppTablePage
    :title="i18n.t('module.invoice.overview.title')"
    :is-title-hidden="true"
    :is-header-hidden="true"
    :remove-content-padding="true"
  >
    <VcTabs
      v-model="tabModelValue"
      :class-config="{
        base: 'pl',
        content: '',
        indicator: 'hidden',
        item: 'border h-9 border-b-0 border-primary rounded-xl rounded-b-none min-w-36 data-[state=inactive]:bg-secondary !m-0 data-[state=active]:text-primary data-[state=inactive]:font-regular enabled:data-[state=active]:hover:bg-transparent',
        list: 'gap-x-0 inline-flex',
        scrollContainer: 'p-xxs pb-0',
      }"
    >
      <template #items>
        <VcTabsItem
          v-for="(tab, statusIndex) of tabs"
          :key="tab.id"
          :value="tab.id"
          :class="{
            '!-ml-px': statusIndex !== 0,
          }"
        >
          {{ tab.label }}
        </VcTabsItem>
      </template>
    </VcTabs>

    <DataTable
      v-if="dynamicTable.activeView.value !== null"
      :is-loading="isLoading"
      :data="data.data"
      :get-key="(row) => row.invoiceNumber"
      :columns="columns"
      :error="error"
      :sort="sort"
      :is-first-column-sticky="true"
      :disable-top-left-border-radius="true"
      :is-last-column-sticky="true"
      :row-action="{
        label: () => i18n.t('module.invoice.overview.action.view_invoice'),
        to: (row) => ({
          name: 'invoice-draft-detail',
          params: {
            invoiceNumber: row.invoiceNumber,
          },
        }),
        type: 'link',
      }"
      @next-page="fetchNextPage"
    >
      <template #top>
        <AppGroup
          class="px-xl h-14"
          justify="end"
        >
          <DynamicTableViews :dynamic-table="dynamicTable" />
          <DynamicTableSettings :dynamic-table="dynamicTable" />
        </AppGroup>
      </template>
    </DataTable>
  </AppTablePage>
</template>
