<script setup lang="ts">
import { VcBadge } from '@wisemen/vue-core-components'
import { useI18n } from 'vue-i18n'

import AppTeleport from '@/components/app/teleport/AppTeleport.vue'
import AppPage from '@/components/layout/page/AppPage.vue'
import AppPageReturnLink from '@/components/layout/page/AppPageReturnLink.vue'
import { InvoiceDraftStatusEnumUtil } from '@/models/enums/invoiceDraftStatus.enum.ts'
import type { InvoiceDraftDetail } from '@/models/invoice/draft-detail/invoiceDraftDetail.model.ts'
import InvoiceDetailPreview from '@/modules/invoice/features/detail/components/InvoiceDetailPreview.vue'
import InvoiceDraftDetailSidebar from '@/modules/invoice/features/detail/components/InvoiceDraftDetailSidebar.vue'

const props = defineProps<{
  invoiceDraft: InvoiceDraftDetail
}>()

const i18n = useI18n()
</script>

<template>
  <AppTeleport to="headerLeft">
    <AppPageReturnLink
      :return-link="{
        to: {
          name: 'invoices-overview-drafts',
        },
        label: i18n.t('module.waste_inquiry.overview.tab.drafts'),
      }"
    />
  </AppTeleport>

  <AppPage :title="`${i18n.t('module.invoice.detail_title')} ${props.invoiceDraft.invoiceNumber}`">
    <template #title-right>
      <VcBadge
        color="light-blue"
        variant="translucent"
        size="sm"
      >
        {{ i18n.t(InvoiceDraftStatusEnumUtil.getI18nKey(props.invoiceDraft.status)) }}
      </VcBadge>
    </template>

    <template #default>
      <div
        class="
          mb-xl gap-2xl grid h-full
          lg:grid-cols-[auto_25rem]
        "
      >
        <InvoiceDetailPreview :invoice-number="props.invoiceDraft.invoiceNumber" />
        <InvoiceDraftDetailSidebar :invoice="props.invoiceDraft" />
      </div>
    </template>
  </AppPage>
</template>
