<script setup lang="ts">
import { computed } from 'vue'

import AppDataProviderView from '@/components/app/AppDataProviderView.vue'
import { useInvoiceDetailQuery } from '@/modules/invoice/api/queries/invoiceDetail.query'
import InvoiceDetailView from '@/modules/invoice/features/detail/views/InvoiceDetailView.vue'

const props = defineProps<{
  invoiceNumber: string
}>()
const invoiceNumber = computed<string>(() => props.invoiceNumber)

const invoiceDetailQuery = useInvoiceDetailQuery(invoiceNumber)
</script>

<template>
  <AppDataProviderView
    v-slot="{ data }"
    :queries="{
      invoice: invoiceDetailQuery,
    }"
  >
    <InvoiceDetailView :invoice="data.invoice" />
  </AppDataProviderView>
</template>
