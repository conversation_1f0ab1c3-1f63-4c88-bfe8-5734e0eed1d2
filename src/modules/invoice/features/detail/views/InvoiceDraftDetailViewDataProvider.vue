<script setup lang="ts">
import { computed } from 'vue'

import AppDataProviderView from '@/components/app/AppDataProviderView.vue'
import { useInvoiceDraftDetailQuery } from '@/modules/invoice/api/queries/invoiceDraftDetail.query.ts'
import InvoiceDraftDetailView from '@/modules/invoice/features/detail/views/InvoiceDraftDetailView.vue'

const props = defineProps<{
  invoiceNumber: string
}>()
const invoiceNumber = computed<string>(() => props.invoiceNumber)

const invoiceDraftDetailQuery = useInvoiceDraftDetailQuery(invoiceNumber)
</script>

<template>
  <AppDataProviderView
    v-slot="{ data }"
    :queries="{
      invoice: invoiceDraftDetailQuery,
    }"
  >
    <InvoiceDraftDetailView :invoice-draft="data.invoice" />
  </AppDataProviderView>
</template>
