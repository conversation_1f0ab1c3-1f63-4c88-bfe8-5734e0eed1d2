<script setup lang="ts">
import { VcBadge } from '@wisemen/vue-core-components'
import { useI18n } from 'vue-i18n'

import AppTeleport from '@/components/app/teleport/AppTeleport.vue'
import AppPage from '@/components/layout/page/AppPage.vue'
import AppPageReturnLink from '@/components/layout/page/AppPageReturnLink.vue'
import { InvoiceStatusEnumUtil } from '@/models/enums/invoiceStatus.enum'
import type { InvoiceDetail } from '@/models/invoice/detail/invoiceDetail.model'
import InvoiceDetailPreview from '@/modules/invoice/features/detail/components/InvoiceDetailPreview.vue'
import InvoiceDetailSidebar from '@/modules/invoice/features/detail/components/InvoiceDetailSidebar.vue'

const props = defineProps<{
  invoice: InvoiceDetail
}>()

const i18n = useI18n()
</script>

<template>
  <AppTeleport to="headerLeft">
    <AppPageReturnLink
      :return-link="{
        to: {
          name: 'invoices-overview-sent',
        },
        label: i18n.t('module.invoice.overview.title'),
      }"
    />
  </AppTeleport>

  <AppPage :title="`${i18n.t('module.invoice.detail_title')} ${props.invoice.invoiceNumber}`">
    <template #title-right>
      <VcBadge
        color="light-blue"
        variant="translucent"
        size="sm"
      >
        {{ i18n.t(InvoiceStatusEnumUtil.getI18nKey(props.invoice.status)) }}
      </VcBadge>
    </template>

    <template #default>
      <div
        class="
          mb-xl gap-2xl grid h-full
          lg:grid-cols-[auto_25rem]
        "
      >
        <InvoiceDetailPreview :invoice-number="props.invoice.invoiceNumber" />
        <InvoiceDetailSidebar :invoice="props.invoice" />
      </div>
    </template>
  </AppPage>
</template>
