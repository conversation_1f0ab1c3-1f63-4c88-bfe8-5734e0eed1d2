<script setup lang="ts">
import { useI18n } from 'vue-i18n'

import { CertificateType } from '@/client/types.gen'
import AppGroup from '@/components/app/AppGroup.vue'
import InvoiceDetailDownloadCertificate from '@/modules/invoice/features/detail/components/InvoiceDetailDownloadCertificate.vue'

const props = defineProps<{
  certificateFileName: string | null
}>()

const i18n = useI18n()
</script>

<template>
  <div>
    <p
      v-if="props.certificateFileName === null"
      class="text-disabled text-sm"
    >
      {{ i18n.t('module.invoice.detail.no_certificate_available') }}
    </p>

    <AppGroup
      v-else
      direction="col"
    >
      <InvoiceDetailDownloadCertificate
        :certificate-file-name="props.certificateFileName"
        :type="CertificateType.COR"
      />

      <InvoiceDetailDownloadCertificate
        :certificate-file-name="props.certificateFileName"
        :type="CertificateType.COT_COB"
      />
    </AppGroup>
  </div>
</template>
