<script setup lang="ts">
import { useI18n } from 'vue-i18n'

import AppCardWithIcon from '@/components/app/card/AppCardWithIcon.vue'
import type { InvoiceDetail } from '@/models/invoice/detail/invoiceDetail.model'
import InvoiceDetailDownloadCertificates from '@/modules/invoice/features/detail/components/InvoiceDetailDownloadCertificates.vue'
import InvoiceDetailSeparator from '@/modules/invoice/features/detail/components/InvoiceDetailSeparator.vue'
import InvoiceDetailSidebarCard from '@/modules/invoice/features/detail/components/InvoiceDetailSidebarCard.vue'
import InvoiceDetailSidebarSubtitle from '@/modules/invoice/features/detail/components/InvoiceDetailSidebarSubtitle.vue'

const props = defineProps<{
  invoice: InvoiceDetail
}>()

const i18n = useI18n()

function formatCurrency(amount: number, currency: string): string {
  return new Intl.NumberFormat(i18n.locale.value, {
    currency,
    style: 'currency',
  }).format(amount)
}
</script>

<template>
  <div class="border-secondary p-xl size-full rounded-2xl border shadow-lg">
    <InvoiceDetailSidebarSubtitle>
      {{ i18n.t('module.invoice.detail_title') }} {{ props.invoice.invoiceNumber }}
    </InvoiceDetailSidebarSubtitle>

    <p class="text-secondary mt-xs text-sm">
      {{ props.invoice.customerReference ?? i18n.t('module.invoice.detail.no_customer_reference') }}
    </p>

    <div class="gap-md mt-2xl grid grid-cols-2">
      <InvoiceDetailSidebarCard
        :label="i18n.t('enum.dynamic_table_column_name.issued_on')"
        :value="props.invoice.issuedOn.toLocaleDateString()"
      />

      <InvoiceDetailSidebarCard
        :label="i18n.t('enum.dynamic_table_column_name.due_on')"
        :value="props.invoice.dueOn?.toLocaleDateString() ?? '-'"
      />
    </div>

    <AppCardWithIcon
      :title="props.invoice.customerName"
      :is-selected="false"
      icon="building"
      variant="light-blue"
      class="mt-md"
    />

    <InvoiceDetailSeparator />

    <InvoiceDetailSidebarSubtitle>
      {{ i18n.t('module.invoice.detail.amount') }}
    </InvoiceDetailSidebarSubtitle>

    <div class="gap-md mt-lg grid grid-cols-2">
      <InvoiceDetailSidebarCard
        :label="i18n.t('enum.dynamic_table_column_name.net_amount')"
        :value="formatCurrency(+props.invoice.netAmount, props.invoice.currency)"
      />

      <InvoiceDetailSidebarCard
        :label="i18n.t('enum.dynamic_table_column_name.vat_amount')"
        :value="formatCurrency(+props.invoice.vatAmount, props.invoice.currency)"
      />
    </div>

    <InvoiceDetailSeparator />

    <InvoiceDetailSidebarSubtitle>
      {{ i18n.t('module.invoice.detail.files') }}
    </InvoiceDetailSidebarSubtitle>

    <InvoiceDetailDownloadCertificates
      :certificate-file-name="props.invoice.certificateFileName"
      class="mt-lg"
    />
  </div>
</template>
