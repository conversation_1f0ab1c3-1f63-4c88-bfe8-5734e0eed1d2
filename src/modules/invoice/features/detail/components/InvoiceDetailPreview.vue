<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import { useInvoiceDownloadQuery } from '@/modules/invoice/api/queries/invoiceDownload.query'

const props = defineProps<{
  invoiceNumber: string
}>()

const i18n = useI18n()

const {
  isLoading, data,
} = useInvoiceDownloadQuery(
  computed<string>(() => props.invoiceNumber),
)

const pdfUrl = computed<string | null>(() => {
  if (data.value === null) {
    return null
  }

  return URL.createObjectURL(data.value.blob)
})
</script>

<template>
  <div
    :class="{
      'animate-pulse': isLoading,
    }"
    class="
      bg-secondary hidden size-full items-center justify-center overflow-hidden
      rounded-xl
      lg:flex
    "
  >
    <p
      v-if="!isLoading && data === null"
      class="text-tertiary"
    >
      {{ i18n.t('module.invoice.detail.no_document_available') }}
    </p>

    <iframe
      v-else-if="pdfUrl !== null"
      :src="pdfUrl"
      title="Invoice PDF Preview"
      width="100%"
      height="100%"
      style="border:none;"
    />
  </div>
</template>
