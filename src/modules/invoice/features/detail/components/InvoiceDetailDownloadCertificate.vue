<script setup lang="ts">
import {
  Vc<PERSON><PERSON>on,
  VcIcon,
} from '@wisemen/vue-core-components'
import { useI18n } from 'vue-i18n'

import type { CertificateType } from '@/client/types.gen'
import AppGroup from '@/components/app/AppGroup.vue'
import { useApiErrorToast } from '@/composables/api-error-toast/apiErrorToast.composable'
import { CertificateTypeEnumUtil } from '@/models/enums/certificateType.enum'
import { useInvoiceDownloadCertificate } from '@/modules/invoice/api/mutations/invoiceDownloadCertificate.mutation'
import { DownloadUtil } from '@/utils/download.util'

const props = defineProps<{
  certificateFileName: string
  type: CertificateType
}>()

const i18n = useI18n()
const apiErrorToast = useApiErrorToast()

const invoiceDownloadCertificateMutation = useInvoiceDownloadCertificate()

async function onDownloadCertificate(): Promise<void> {
  try {
    const response = await invoiceDownloadCertificateMutation.execute({
      body: {
        fileName: props.certificateFileName,
        type: props.type,
      },
    })

    DownloadUtil.downloadBlob(response.blob, response.disposition)
  }
  catch (error) {
    apiErrorToast.show(error)
  }
}
</script>

<template>
  <div
    class="
      border-xl border-primary p-xl w-full overflow-hidden rounded-xl border
    "
  >
    <AppGroup
      gap="lg"
      class="overflow-hidden"
    >
      <VcIcon
        icon="pdf"
        class="w-10 shrink-0"
      />

      <AppGroup
        justify="between"
        class="w-full"
      >
        <span class="text-primary w-full truncate font-medium">
          {{ props.certificateFileName }}
        </span>

        <AppGroup direction="col">
          <VcButton
            :is-loading="invoiceDownloadCertificateMutation.isLoading.value"
            variant="tertiary"
            icon="download"
            class="shrink-0"
            size="sm"
            icon-right="download"
            @click="onDownloadCertificate"
          >
            {{ i18n.t(CertificateTypeEnumUtil.getLabelI18nKey(props.type)) }}
          </VcButton>
        </AppGroup>
      </AppGroup>
    </AppGroup>
  </div>
</template>
