<script setup lang="ts">
import AppGroup from '@/components/app/AppGroup.vue'
import AppCard from '@/components/app/card/AppCard.vue'

const props = defineProps<{
  label: string
  value: string
}>()
</script>

<template>
  <AppCard
    variant="transparent"
    spacing="sm"
  >
    <AppGroup
      direction="col"
      align="start"
      gap="xs"
    >
      <span class="text-secondary text-sm font-medium">
        {{ props.label }}
      </span>

      <span class="text-primary">
        {{ props.value }}
      </span>
    </AppGroup>
  </AppCard>
</template>
