<script setup lang="ts">
import { useI18n } from 'vue-i18n'

import AppCardWithIcon from '@/components/app/card/AppCardWithIcon.vue'
import type { InvoiceDraftDetail } from '@/models/invoice/draft-detail/invoiceDraftDetail.model'
import InvoiceDetailDownloadCertificates from '@/modules/invoice/features/detail/components/InvoiceDetailDownloadCertificates.vue'
import InvoiceDetailSeparator from '@/modules/invoice/features/detail/components/InvoiceDetailSeparator.vue'
import InvoiceDetailSidebarCard from '@/modules/invoice/features/detail/components/InvoiceDetailSidebarCard.vue'
import InvoiceDetailSidebarSubtitle from '@/modules/invoice/features/detail/components/InvoiceDetailSidebarSubtitle.vue'

const props = defineProps<{
  invoice: InvoiceDraftDetail
}>()

const i18n = useI18n()

function formatCurrency(amount: string, currency: string): string {
  const parsedAmount = Number(amount)

  return new Intl.NumberFormat(i18n.locale.value, {
    currency,
    style: 'currency',
  }).format(Number.isNaN(parsedAmount) ? 0 : parsedAmount)
}

function formatDate(date: InvoiceDraftDetail['issuedOn'] | null): string {
  return date ? date.toLocaleDateString() : '-'
}
</script>

<template>
  <div class="border-secondary p-xl size-full rounded-2xl border shadow-lg">
    <InvoiceDetailSidebarSubtitle>
      {{ i18n.t('module.invoice.detail_title') }} {{ props.invoice.invoiceNumber }}
    </InvoiceDetailSidebarSubtitle>

    <p class="text-secondary mt-xs text-sm">
      {{ props.invoice.poNumber ?? i18n.t('module.invoice.detail.no_customer_reference') }}
    </p>

    <div class="gap-md mt-2xl grid grid-cols-2">
      <InvoiceDetailSidebarCard
        :label="i18n.t('enum.dynamic_table_column_name.issued_on')"
        :value="formatDate(props.invoice.issuedOn)"
      />

      <InvoiceDetailSidebarCard
        :label="i18n.t('enum.dynamic_table_column_name.customer_approval_date')"
        :value="formatDate(props.invoice.customerApprovalDate)"
      />
    </div>

    <AppCardWithIcon
      :title="props.invoice.payerName"
      :is-selected="false"
      icon="building"
      variant="light-blue"
      class="mt-md"
    />

    <InvoiceDetailSeparator />

    <InvoiceDetailSidebarSubtitle>
      {{ i18n.t('module.invoice.detail.amount') }}
    </InvoiceDetailSidebarSubtitle>

    <div class="gap-md mt-lg grid grid-cols-2">
      <InvoiceDetailSidebarCard
        :label="i18n.t('enum.dynamic_table_column_name.net_amount')"
        :value="formatCurrency(props.invoice.netAmount, props.invoice.currency)"
      />

      <InvoiceDetailSidebarCard
        :label="i18n.t('enum.dynamic_table_column_name.vat_amount')"
        :value="formatCurrency(props.invoice.vatAmount, props.invoice.currency)"
      />
    </div>

    <InvoiceDetailSeparator />

    <InvoiceDetailSidebarSubtitle>
      {{ i18n.t('module.invoice.detail.files') }}
    </InvoiceDetailSidebarSubtitle>

    <InvoiceDetailDownloadCertificates
      :certificate-file-name="null"
      class="mt-lg"
    />
  </div>
</template>
