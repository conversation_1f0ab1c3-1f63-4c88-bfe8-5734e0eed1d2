import type { Component } from 'vue'
import type { RouteRecordRaw } from 'vue-router'

import {
  invoiceAccessMiddleware,
  invoiceManageMiddleware,
  invoiceReadMiddleware,
} from '@/middlewares/invoice.middleware'

export const invoiceRoutes = [
  {
    path: 'invoices',
    children: [
      {
        name: 'invoices-overview',
        path: '',
        component: (): Component => import('@/modules/invoice/features/overview/views/InvoiceOverviewView.vue'),
        meta: {
          middleware: [
            invoiceAccessMiddleware,
            invoiceManageMiddleware,
          ],
        },
        redirect: { name: 'invoices-overview-sent' },
        children: [
          {
            name: 'invoices-overview-drafts',
            path: 'drafts',
            component: (): Component => import('@/modules/invoice/features/overview/views/InvoiceOverviewDraftView.vue'),
            meta: {
              middleware: [
                invoiceReadMiddleware,
              ],
            },
          },
          {
            name: 'invoices-overview-sent',
            path: 'sent',
            component: (): Component => import('@/modules/invoice/features/overview/views/InvoiceOverviewSentView.vue'),
            meta: {
              middleware: [
                invoiceManageMiddleware,
              ],
            },
          },
        ],
      },
      {
        name: 'invoice-draft-detail',
        props: true,
        path: 'drafts/:invoiceNumber',
        component: (): Component => import('@/modules/invoice/features/detail/views/InvoiceDraftDetailViewDataProvider.vue'),
        meta: {
          middleware: [
            invoiceManageMiddleware,
          ],
        },
      },
      {
        name: 'invoice-detail',
        props: true,
        path: ':invoiceNumber',
        component: (): Component => import('@/modules/invoice/features/detail/views/InvoiceDetailViewDataProvider.vue'),
        meta: {
          middleware: [
            invoiceReadMiddleware,
          ],
        },
      },
    ],
  },
] as const satisfies RouteRecordRaw[]
