<script setup lang="ts">
import { useI18n } from 'vue-i18n'

import AppGroup from '@/components/app/AppGroup.vue'
import AppTeleport from '@/components/app/teleport/AppTeleport.vue'
import TipTapContentRenderer from '@/components/app/tip-tap-content-renderer/TipTapContentRenderer.vue'
import AppPage from '@/components/layout/page/AppPage.vue'
import AppPageReturnLink from '@/components/layout/page/AppPageReturnLink.vue'
import { useDocumentTitle } from '@/composables/document-title/documentTitle.composable'
import type { DashboardNewsDetail } from '@/models/news/dashboard-detail/dashboardNewsDetail.model'
import DashboardNewsDetailMoreNews from '@/modules/dashboard/features/news-detail/components/DashboardNewsDetailMoreNews.vue'

const props = defineProps<{
  news: DashboardNewsDetail
}>()

const i18n = useI18n()
const documentTitle = useDocumentTitle()

documentTitle.set(props.news.translation.title)

function setIFrameSize(iframe: string): string {
  return iframe
    .replace(/\sclass="[^"]*"/i, '')
    .replace(
      /<iframe/i,
      '<iframe class="h-60 w-100"',
    )
}
</script>

<template>
  <AppTeleport to="headerLeft">
    <AppPageReturnLink
      :return-link="{
        to: {
          name: 'dashboard-overview',
        },
        label: i18n.t('module.dashboard.overview.page_title'),
      }"
    />
  </AppTeleport>

  <AppPage
    :is-header-hidden="true"
    :title="props.news.translation.title"
    class="pt-3xl"
  >
    <div class="gap-3xl pb-3xl mx-auto grid grid-cols-[3fr_2fr] items-start">
      <AppGroup
        gap="3xl"
        direction="col"
        align="start"
      >
        <div class="prose w-full !max-w-full">
          <div
            class="
              bg-secondary h-90 w-full min-w-120 overflow-hidden rounded-2xl
            "
          >
            <img
              v-if="props.news.image !== null && props.news.image.url !== null"
              :src="props.news.image.url"
              :alt="props.news.image.name"
              class="bg-primary !m-0 size-full object-contain"
            >
          </div>

          <div class="my-4xl">
            <h1>
              {{ props.news.translation.title }}
            </h1>
          </div>

          <TipTapContentRenderer :content="props.news.translation.content" />

          <div
            v-if="props.news.videoIFrame !== null"
            class="mt-3xl h-60 w-100 overflow-hidden rounded-xl"
          >
            <div
              v-html="setIFrameSize(props.news.videoIFrame)"
            />
          </div>
        </div>
      </AppGroup>

      <DashboardNewsDetailMoreNews :news-uuid="props.news.uuid" />
    </div>
  </AppPage>
</template>
