<script setup lang="ts">
import { debouncedRef } from '@vueuse/core'
import { computed } from 'vue'

import AppHeightTransition from '@/components/app/AppHeightTransition.vue'
import AppSeparator from '@/components/app/AppSeparator.vue'
import AppSpinner from '@/components/app/AppSpinner.vue'
import AppCard from '@/components/app/card/AppCard.vue'

const props = withDefaults(defineProps<{
  title: string
  hasSeparator?: boolean
  isLoading?: boolean
  isTitleHidden?: boolean
}>(), {
  hasSeparator: false,
  isTitleHidden: false,
})

const debouncedIsLoading = debouncedRef(computed<boolean>(() => props.isLoading), 300)
</script>

<template>
  <AppCard
    :is-disabled="true"
    variant="transparent"
    class="w-full overflow-hidden"
  >
    <h2
      :class="{
        'mb-xs': !props.hasSeparator,
        'sr-only': props.isTitleHidden,
      }"
      class="text-primary text-lg font-bold"
    >
      {{ props.title }}
    </h2>

    <AppSeparator
      v-if="props.hasSeparator"
      class="my-xl"
    />

    <AppHeightTransition>
      <template v-if="debouncedIsLoading">
        <div class="py-11xl flex items-center justify-center">
          <AppSpinner />
        </div>
      </template>

      <template v-else>
        <slot />
      </template>
    </AppHeightTransition>
  </AppCard>
</template>
