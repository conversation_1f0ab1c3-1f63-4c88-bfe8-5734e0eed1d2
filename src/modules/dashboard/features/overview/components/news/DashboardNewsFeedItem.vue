<script setup lang="ts">
import { computed } from 'vue'

import AppGroup from '@/components/app/AppGroup.vue'
import AppRouterLink from '@/components/app/link/AppRouterLink.vue'
import type { DashboardNewsIndex } from '@/models/news/dashboard-index/dashboardNewsIndex.model'

const props = defineProps<{
  news: DashboardNewsIndex
  newsItemIndex: number
}>()

const firstParagraphContent = computed<string>(() => {
  const firstParagraph = props.news.translation.content.content?.find((c) => c.type === 'paragraph') ?? null

  if (firstParagraph === null) {
    return ''
  }

  const firstParagraphText = firstParagraph.content?.map((c) => c.text).join(' ') ?? ''

  return firstParagraphText
})
</script>

<template>
  <article
    class="group/article relative isolate w-full overflow-hidden"
  >
    <AppGroup gap="3xl">
      <div class="bg-secondary h-32 w-44 shrink-0 overflow-hidden rounded-xl">
        <img
          v-if="props.news.image !== null && props.news.image.url !== null"
          :src="props.news.image.url"
          :alt="props.news.image.name"
          class="
            size-full object-contain duration-300
            group-hover/article:scale-105
          "
        >
      </div>

      <div class="overflow-hidden">
        <h3 class="text-primary pb-sm line-clamp-2 font-semibold">
          <AppRouterLink
            :to="{
              name: 'dashboard-news-detail',
              params: {
                newsUuid: props.news.uuid,
              },
            }"
          >
            {{ props.news.translation.title }}
            <span class="absolute inset-0" />
          </AppRouterLink>
        </h3>

        <p class="text-secondary line-clamp-3 text-sm">
          {{ firstParagraphContent }}
        </p>
      </div>
    </AppGroup>
  </article>
</template>
