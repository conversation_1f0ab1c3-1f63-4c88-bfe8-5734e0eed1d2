<script setup lang="ts">
import {
  computed,
  ref,
} from 'vue'
import { useI18n } from 'vue-i18n'

import {
  CertificateDocType,
  DynamicColumnNames,
  InvoiceStatus,
} from '@/client/types.gen'
import AppGroup from '@/components/app/AppGroup.vue'
import FiltersActive from '@/components/filters/active/FiltersActive.vue'
import {
  createAutocompleteFilter,
  createDateRangeFilter,
  createMultiSelectFilter,
  createSelectFilter,
} from '@/components/filters/createFilters'
import FiltersDropdownMenu from '@/components/filters/dropdown-menu/FiltersDropdownMenu.vue'
import { useFilters } from '@/components/filters/filters.composable'
import FiltersRoot from '@/components/filters/FiltersRoot.vue'
import AppPage from '@/components/layout/page/AppPage.vue'
import DataTable from '@/components/table/data-table/DataTable.vue'
import DynamicTableSettings from '@/components/table/dynamic-table-settings/DynamicTableSettings.vue'
import DynamicTableViews from '@/components/table/dynamic-table-views/DynamicTableViews.vue'
import { useDocumentTitle } from '@/composables/document-title/documentTitle.composable'
import { useDynamicTableV2 } from '@/composables/dynamic-table/dynamicTableV2.composable'
import { useSearchableTableColumns } from '@/composables/searchable-table-columns/searchableTableColumns.composable'
import { useSort } from '@/composables/sort/sort.composable'
import { useGenericColumn } from '@/composables/table-columns/genericTableColumnsV2.composable'
import type { CertificateIndex } from '@/models/certificate/index/certificateIndex.model'
import type { CertificateIndexQueryParams } from '@/models/certificate/index/certificateIndexQueryParams.model'
import { CertificateDocTypeEnumUtil } from '@/models/enums/certificateDocType.enum'
import { DynamicTableName } from '@/models/enums/dynamicTableName.enum'
import type { InvoiceIndexQueryParams } from '@/models/invoice/index/invoiceIndexQueryParams.model'
import type { WasteProducerIndexQueryParams } from '@/models/waste-producer/index/wasteProducerIndexQueryParams.model'
import { useCertificateIndexQuery } from '@/modules/certificate/api/queries/certificateIndex.query'
import { useInvoiceIndexQuery } from '@/modules/invoice/api/queries/invoiceIndex.query'
import { useCustomerIndexQuery } from '@/modules/waste-inquiry/api/queries/customerIndex.query'
import { useEwcCodeIndexQuery } from '@/modules/waste-inquiry/api/queries/ewcCodeIndex.query'
import { useWasteProducerIndexQuery } from '@/modules/waste-inquiry/api/queries/wasteProducerIndex.query'
import type { DataTableColumn } from '@/types/table.type'

const i18n = useI18n()
const documentTitle = useDocumentTitle()

documentTitle.set(i18n.t('module.certificates.overview.title'))

const sort = useSort({
  enableMultiSort: true,
  keys: [
    'collectionDate',
    'deliveryDate',
    'disposalDate',
    'dispositionDeliveryDate',
    'dispositionPickUpDate',
    'endTreatmentCentre',
    'ewc',
    'invoice',
    'printDate',
    'salesOrder',
    'salesOrderLine',
    'tfs',
    'treatmentCentre',
    'wtfForm',
    'pickupAddress',
    'customerId',
    'wasteProducerId',
    'contract',
    'contractItem',
  ],
  persistInUrl: false,
})

const wasteProducerIndexSearch = ref<string>('')
const wasteProducerIndexFilters = ref<WasteProducerIndexQueryParams['filters']>({})

const wasteProducerIndexQuery = useWasteProducerIndexQuery({
  params: {
    filters: wasteProducerIndexFilters,
    search: wasteProducerIndexSearch,
  },
})

const customerIndexSearch = ref<string>('')
const customerIndexQuery = useCustomerIndexQuery({ params: { search: customerIndexSearch } })

const invoiceIndexSearch = ref<string>('')

const invoiceIndexQuery = useInvoiceIndexQuery({
  params: {
    filters: computed<InvoiceIndexQueryParams['filters']>(() => ({
      customerId: null,
      payerId: null,
      dueDate: null,
      issueDate: null,
      accountDocumentNumber: undefined,
      accountManagerName: undefined,
      companyName: undefined,
      customerReference: undefined,
      invoiceNumber: undefined,
      statuses: [
        InvoiceStatus.CLEARED,
        InvoiceStatus.OVERDUE,
        InvoiceStatus.OUTSTANDING,
      ],
      type: undefined,
    })),
    sort: ref<InvoiceIndexQueryParams['sort']>([]),
  },
})

const ewcCodeIndexQuery = useEwcCodeIndexQuery()

const searchableColumns = useSearchableTableColumns({
  keys: [
    'contract',
    'contractItem',
    'description',
    'endTreatmentCentre',
    'salesOrder',
    'salesOrderLine',
    'tfs',
    'treatmentCentre',
    'wtfForm',
  ],
})

const filters = useFilters({
  filterGroups: () => [
    {
      filters: [
        createMultiSelectFilter({
          defaultValue: [],
          displayFn: (value) => i18n.t(CertificateDocTypeEnumUtil.getI18nKey(value)),
          key: 'docTypes',
          label: i18n.t('enum.dynamic_table_column_name.doc_type'),
          options: Object.values(CertificateDocType),
        }),
        createAutocompleteFilter({
          isLoading: customerIndexQuery.isLoading.value,
          defaultValue: null,
          displayFn: (option) => option.name,
          key: 'customerId',
          label: i18n.t('enum.dynamic_table_column_name.customer_name'),
          options: customerIndexQuery.data.value?.data.map((customer) => ({
            id: customer.id,
            name: customer.name,
          })) ?? [],
          onSearch: (value) => {
            customerIndexSearch.value = value
          },
        }),
        createAutocompleteFilter({
          isLoading: wasteProducerIndexQuery.isLoading.value,
          defaultValue: null,
          displayFn: (option) => option.name,
          key: 'wasteProducerId',
          label: i18n.t('enum.dynamic_table_column_name.waste_producer'),
          options: wasteProducerIndexQuery.data.value?.data.map((wp) => ({
            id: wp.id,
            name: wp.name,
          })) ?? [],
          onSearch: (value) => {
            wasteProducerIndexSearch.value = value
          },
        }),
        createAutocompleteFilter({
          isLoading: invoiceIndexQuery.isLoading.value,
          defaultValue: null,
          displayFn: (option) => option.name,
          key: 'invoice',
          label: i18n.t('enum.dynamic_table_column_name.invoice_number'),
          options: invoiceIndexQuery.data.value?.data.map((invoice) => ({
            id: invoice.invoiceNumber,
            name: invoice.invoiceNumber,
          })) ?? [],
          onSearch: (value) => {
            invoiceIndexSearch.value = value
          },
        }),
        createSelectFilter({
          defaultValue: null,
          displayFn: (value: {
            label: string
            value: string
          }) => value.label,
          key: 'ewc',
          label: i18n.t('enum.dynamic_table_column_name.ewc_code'),
          options: ewcCodeIndexQuery.data.value?.map((ewcCode) => ({
            label: ewcCode.code,
            value: ewcCode.code,
          })) ?? [],
        }),
        createDateRangeFilter({
          defaultValue: {
            from: null,
            until: null,
          },
          key: 'collectionDate',
          label: i18n.t('enum.dynamic_table_column_name.collection_date'),
        }),
        createDateRangeFilter({
          defaultValue: {
            from: null,
            until: null,
          },
          key: 'deliveryDate',
          label: i18n.t('enum.dynamic_table_column_name.delivery_date'),
        }),
        createDateRangeFilter({
          defaultValue: {
            from: null,
            until: null,
          },
          key: 'dispositionPickUpDate',
          label: i18n.t('enum.dynamic_table_column_name.disposition_pickup_date'),
        }),
        createDateRangeFilter({
          defaultValue: {
            from: null,
            until: null,
          },
          key: 'dispositionDeliveryDate',
          label: i18n.t('enum.dynamic_table_column_name.disposition_delivery_date'),
        }),
        createDateRangeFilter({
          defaultValue: {
            from: null,
            until: null,
          },
          key: 'disposalDate',
          label: i18n.t('enum.dynamic_table_column_name.disposal_date'),
        }),
        createDateRangeFilter({
          defaultValue: {
            from: null,
            until: null,
          },
          key: 'printDate',
          label: i18n.t('enum.dynamic_table_column_name.print_date'),
        }),
      ],
    },
  ],
  persistInUrl: false,
})

const {
  isLoading,
  data,
  error,
  fetchNextPage,
} = useCertificateIndexQuery({
  params: {
    filters: computed<CertificateIndexQueryParams['filters']>(() => {
      return {
        customerId: filters.values.value.customerId || null,
        wasteProducerId: filters.values.value.wasteProducerId || null,
        collectionDate: filters.values.value.collectionDate || null,
        deliveryDate: filters.values.value.deliveryDate || null,
        disposalDate: filters.values.value.disposalDate || null,
        dispositionDeliveryDate: filters.values.value.dispositionDeliveryDate || null,
        dispositionPickUpDate: filters.values.value.dispositionPickUpDate || null,
        printDate: filters.values.value.printDate || null,
        contract: searchableColumns.values.value.contract || undefined,
        contractItem: searchableColumns.values.value.contractItem || undefined,
        description: searchableColumns.values.value.description || undefined,
        docTypes: filters.values.value.docTypes || [],
        endTreatmentCentre: searchableColumns.values.value.endTreatmentCentre || undefined,
        ewc: filters.values.value.ewc || null,
        invoice: filters.values.value.invoice || null,
        salesOrder: searchableColumns.values.value.salesOrder || undefined,
        salesOrderLine: searchableColumns.values.value.salesOrderLine || undefined,
        tfs: searchableColumns.values.value.tfs || undefined,
        treatmentCentre: searchableColumns.values.value.treatmentCentre || undefined,
        wtfForm: searchableColumns.values.value.wtfForm || undefined,
      }
    }),
    sort: sort.values,
  },
})

const dynamicTable = useDynamicTableV2({
  dynamicTableName: DynamicTableName.CERTIFICATE,
  filters,
  sort,
})

const columns = computed<DataTableColumn<CertificateIndex>[]>(() => {
  return dynamicTable.columns.value
    .filter((column) => column.isVisible)
    .map((column) => {
      switch (column.name) {
        case DynamicColumnNames.DOC_TYPE:
          return useGenericColumn(column.name, column.label, (row) => {
            if (row.docType === null) {
              return null
            }

            return i18n.t(CertificateDocTypeEnumUtil.getI18nKey(row.docType))
          })
        default:
          return useGenericColumn(column.name, column.label)
      }
    })
})
</script>

<template>
  <AppPage
    :title="i18n.t('module.certificates.overview.title')"
    class="pb-xl"
  >
    <DataTable
      v-if="dynamicTable.activeView.value !== null"
      :is-loading="isLoading"
      :data="data.data"
      :get-key="(_, index) => `${index}`"
      :columns="columns"
      :error="error"
      :sort="sort"
      :filters="filters"
      :is-first-column-sticky="true"
      :searchable-columns="searchableColumns"
      @next-page="fetchNextPage"
    >
      <template #top>
        <FiltersRoot :filters="filters">
          <AppGroup
            justify="between"
            class="px-xl h-14"
          >
            <FiltersActive />

            <AppGroup>
              <DynamicTableViews :dynamic-table="dynamicTable" />
              <FiltersDropdownMenu />
              <DynamicTableSettings :dynamic-table="dynamicTable" />
            </AppGroup>
          </AppGroup>
        </FiltersRoot>
      </template>
    </DataTable>
  </AppPage>
</template>
