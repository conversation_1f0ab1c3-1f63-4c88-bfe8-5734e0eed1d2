import { viewCertificateIndexV1 } from '@/client'
import type { CertificateIndex } from '@/models/certificate/index/certificateIndex.model'
import { CertificateIndexTransformer } from '@/models/certificate/index/certificateIndex.transformer'
import type { CertificateIndexQueryParams } from '@/models/certificate/index/certificateIndexQueryParams.model'
import { CertificateIndexQueryParamsTransformer } from '@/models/certificate/index/certificateIndexQueryParams.transformer'
import type {
  OffsetPagination,
  OffsetPaginationResponse,
} from '@/types/pagination.type'

export class CertificateService {
  static async getAll(
    params: OffsetPagination<CertificateIndexQueryParams>,
  ): Promise<OffsetPaginationResponse<CertificateIndex>> {
    const response = await viewCertificateIndexV1({ query: CertificateIndexQueryParamsTransformer.toDto(params) })

    return {
      data: response.data.items.map(CertificateIndexTransformer.fromDto),
      meta: response.data.meta,
    }
  }
}
