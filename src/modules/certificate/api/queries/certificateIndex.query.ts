import { useOffsetInfiniteQuery } from '@/composables/offset-infinite-query/offsetInfiniteQuery.composable'
import type { CertificateIndexQueryParams } from '@/models/certificate/index/certificateIndexQueryParams.model'
import { CertificateService } from '@/modules/certificate/api/services/certificate.service'
import type { InfiniteQueryOptions } from '@/types/query.type'

// eslint-disable-next-line ts/explicit-function-return-type
export function useCertificateIndexQuery(options: InfiniteQueryOptions<CertificateIndexQueryParams>) {
  return useOffsetInfiniteQuery({
    queryFn: (pagination) => {
      return CertificateService.getAll({
        filters: options.params.filters.value,
        pagination,
        sort: options.params.sort.value,
      })
    },
    queryKey: { certificateIndex: { queryParams: options.params } },
  })
}
