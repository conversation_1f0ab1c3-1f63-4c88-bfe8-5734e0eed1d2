import type { Component } from 'vue'
import type { RouteRecordRaw } from 'vue-router'

export const certificateRoutes = [
  {
    path: '/certificates',
    children: [
      {
        name: 'certificates-overview',
        path: '',
        component: (): Component => import('@/modules/certificate/features/overview/views/CertificateOverviewView.vue'),
      },
    ],
  },
] as const satisfies RouteRecordRaw[]
