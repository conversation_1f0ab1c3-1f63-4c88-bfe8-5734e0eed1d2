<script setup lang="ts">
import {
  VcButton,
  VcDateField,
  VcIconButton,
  VcTabs,
  VcTabsContent,
  VcTabsItem,
  VcTextarea,
} from '@wisemen/vue-core-components'
import dayjs from 'dayjs'
import type { Form } from 'formango'
import { AnimatePresence } from 'motion-v'
import {
  computed,
  ref,
} from 'vue'
import { useI18n } from 'vue-i18n'

import {
  Locale,
  MimeType,
} from '@/client/types.gen'
import AppAnimateHeight from '@/components/animate-height/AppAnimateHeight.vue'
import AnimatedSimple from '@/components/animated/AnimatedSimple.vue'
import AppGroup from '@/components/app/AppGroup.vue'
import AppHorizontalFieldLayout from '@/components/app/AppHorizontalFieldLayout.vue'
import AppSeparator from '@/components/app/AppSeparator.vue'
import FormGrid from '@/components/app/grid/FormGrid.vue'
import AppTeleport from '@/components/app/teleport/AppTeleport.vue'
import AppForm from '@/components/form/AppForm.vue'
import FormSingleFileUpload from '@/components/form/file-upload/FormSingleFileUpload.vue'
import FormLayout from '@/components/form/FormLayout.vue'
import FormSubmitButton from '@/components/form/FormSubmitButton.vue'
import { TEST_ID } from '@/constants/testId.constant'
import { LanguageEnumUtil } from '@/models/enums/language.enum'
import type { newsArticleFormSchema } from '@/models/news/article-update/newsArticleCreateForm.model'
import NewsArticleFormContent from '@/modules/news/components/article-form/NewsArticleFormContent.vue'
import { toFormField } from '@/utils/formango.util'

const props = defineProps<{
  isUpdate?: boolean
  form: Form<typeof newsArticleFormSchema>
}>()

const emit = defineEmits<{
  delete: []
}>()

const i18n = useI18n()

const today = dayjs().startOf('day').toDate()

const image = props.form.register('image')
const translations = props.form.registerArray('newsItemTranslations')
const video = props.form.register('videoIFrame', null)
const startDate = props.form.register('startDate', today)
const endDate = props.form.register('endDate')

const supportedLanguages = Object.values(Locale)
const activeLanguageTab = ref<Locale>(supportedLanguages[0])

const showVideoTextarea = computed<boolean>(() => {
  return !video.isValid.value || video.modelValue.value == null || video.modelValue.value === ''
})

function setIFrameSize(iframe: string | null): string | null {
  if (iframe == null) {
    return null
  }

  return iframe
    .replace(/\swidth="[^"]*"/i, '')
    .replace(/\sheight="[^"]*"/i, '')
    .replace(
      /<iframe/i,
      '<iframe class="h-48 w-85"',
    )
}

function clearVideo(): void {
  video.setValue(null)
}

function onUpdateVideo(val: string | null): void {
  video.setValue(setIFrameSize(val))
}
</script>

<template>
  <AppForm
    v-slot="{ formId }"
    :form="props.form"
  >
    <AppTeleport to="headerActions">
      <AppGroup>
        <VcButton
          v-if="props.isUpdate"
          :test-id="TEST_ID.NEWS.ARTICLE_UPDATE.DELETE_BUTTON"
          variant="destructive-secondary"
          icon-left="trash"
          @click="emit('delete')"
        >
          {{ i18n.t('module.news.update.delete_article') }}
        </VcButton>
        <FormSubmitButton
          :test-id="TEST_ID.NEWS.ARTICLE_UPDATE.PUBLISH_BUTTON"
          :form-id="formId"
          :form="props.form"
          :label="props.isUpdate ? i18n.t('shared.save_changes') : i18n.t('shared.save') "
        />
      </AppGroup>
    </AppTeleport>

    <FormLayout>
      <AppHorizontalFieldLayout :label="i18n.t('module.news.update.fields.image')">
        <FormSingleFileUpload
          v-bind="toFormField(image)"
          :mime-types="[MimeType.IMAGE_JPEG, MimeType.IMAGE_PNG]"
          :max-file-size-mb="1"
          display="block"
        />
      </AppHorizontalFieldLayout>

      <VcTabs
        v-model="activeLanguageTab"
        :class-config="{
          base: 'pl-64 border-b border-solid border-secondary',
        }"
      >
        <template #items>
          <VcTabsItem
            v-for="language in supportedLanguages"
            :key="language"
            :value="language"
          >
            {{ i18n.t(LanguageEnumUtil.getLabelI18nKey(language)) }}
          </VcTabsItem>
        </template>

        <template #content>
          <VcTabsContent
            v-for="(language, index) of supportedLanguages"
            :key="language"
            :value="language"
          >
            <NewsArticleFormContent
              :index="index"
              :translations="translations"
              :language="language"
            />
          </VcTabsContent>
        </template>
      </VcTabs>

      <AppSeparator />

      <AppHorizontalFieldLayout :label="i18n.t('module.news.article.fields.video')">
        <AppAnimateHeight>
          <AnimatePresence mode="popLayout">
            <AnimatedSimple v-if="showVideoTextarea">
              <VcTextarea
                v-bind="toFormField(video)"
                :placeholder="i18n.t('module.news.article.fields.video_placeholder')"
                :hint="i18n.t('module.news.article.fields.hint')"
                class="p-px"
                @update:model-value="onUpdateVideo"
              />
            </AnimatedSimple>
            <AnimatedSimple v-else>
              <div class="relative h-48 w-85 overflow-hidden rounded-xl">
                <div v-html="video.modelValue.value" />
                <VcIconButton
                  :label="i18n.t('shared.remove')"
                  :class-config="{
                    root: 'rounded-md backdrop-blur-xl bg-white/50',
                  }"
                  icon="trash"
                  size="sm"
                  variant="destructive-tertiary"
                  class="!absolute top-1 right-1"
                  @click="clearVideo"
                />
              </div>
            </AnimatedSimple>
          </AnimatePresence>
        </AppAnimateHeight>
      </AppHorizontalFieldLayout>

      <AppHorizontalFieldLayout :label="i18n.t('module.news.article.fields.publishing_date')">
        <FormGrid :cols="2">
          <VcDateField
            v-bind="toFormField(startDate)"
            :label="i18n.t('module.news.article.fields.start_date')"
            :is-required="true"
            :is-date-disabled="date => (endDate.modelValue.value !== null && date > endDate.modelValue.value)"
          />

          <VcDateField
            v-bind="toFormField(endDate)"
            :label="i18n.t('module.news.article.fields.end_date')"
            :allow-deselect="true"
            :is-date-disabled="date => (startDate.modelValue.value === null
              ? false
              : date < startDate.modelValue.value)"
          />
        </FormGrid>
      </AppHorizontalFieldLayout>
    </FormLayout>
  </AppForm>
</template>
