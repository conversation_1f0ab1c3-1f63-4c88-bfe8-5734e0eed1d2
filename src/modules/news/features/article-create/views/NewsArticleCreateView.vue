<script setup lang="ts">
import { useForm } from 'formango'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'

import { Locale } from '@/client/types.gen'
import AppTeleport from '@/components/app/teleport/AppTeleport.vue'
import FormPage from '@/components/form/FormPage.vue'
import AppPageReturnLink from '@/components/layout/page/AppPageReturnLink.vue'
import { useApiErrorToast } from '@/composables/api-error-toast/apiErrorToast.composable'
import { useDocumentTitle } from '@/composables/document-title/documentTitle.composable'
import type { NewsArticleTranslationForm } from '@/models/news/article-update/newsArticleCreateForm.model'
import { newsArticleFormSchema } from '@/models/news/article-update/newsArticleCreateForm.model'
import { useNewsArticleCreateMutation } from '@/modules/news/api/mutations/newsArticleCreate.mutation'
import NewsArticleForm from '@/modules/news/components/article-form/NewsArticleForm.vue'

const i18n = useI18n()
const documentTitle = useDocumentTitle()
const newsArticleCreateMutation = useNewsArticleCreateMutation()
const apiErrorToast = useApiErrorToast()
const router = useRouter()

documentTitle.set(() => i18n.t('module.news.article.create.page_title'))

const form = useForm({
  initialState: {
    endDate: null,
    startDate: null,
    image: null,
    newsItemTranslations: setTranslations(),
    videoIFrame: null,
  },
  schema: newsArticleFormSchema,
  onSubmit: async (values) => {
    try {
      await newsArticleCreateMutation.execute({ body: values })
      await router.push({ name: 'news-articles-overview' })
    }
    catch (error) {
      apiErrorToast.show(error)
    }
  },
})

function setTranslations(): NewsArticleTranslationForm[] {
  return Object.values(Locale).map((language) => ({
    title: null,
    content: null,
    language,
  }))
}
</script>

<template>
  <AppTeleport to="headerLeft">
    <AppPageReturnLink
      :return-link="{
        label: i18n.t('module.news.update.return_to_overview'),
        to: {
          name: 'news-articles-overview',
        },
      }"
    />
  </AppTeleport>

  <FormPage :title="i18n.t('module.news.article.create.page_title')">
    <NewsArticleForm :form="form" />
  </FormPage>
</template>
