<script setup lang="ts">
import {
  useVcDialog,
  useVcToast,
} from '@wisemen/vue-core-components'
import { useForm } from 'formango'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'

import { Locale } from '@/client/types.gen'
import AppTeleport from '@/components/app/teleport/AppTeleport.vue'
import FormPage from '@/components/form/FormPage.vue'
import AppPageReturnLink from '@/components/layout/page/AppPageReturnLink.vue'
import { useApiErrorToast } from '@/composables/api-error-toast/apiErrorToast.composable'
import { useDocumentTitle } from '@/composables/document-title/documentTitle.composable'
import type {
  NewsAnnouncementDetail,
  NewsAnnouncementDetailTranslation,
} from '@/models/announcement/detail/announcementDetail.model'
import { newsAnnouncementUpdateFormSchema } from '@/models/news/announcement-update/newsAnnouncementUpdateForm.model'
import { useNewsAnnouncementDeleteMutation } from '@/modules/news/api/mutations/newsAnnouncementDelete.mutation'
import { useNewsAnnouncementUpdateMutation } from '@/modules/news/api/mutations/newsAnnouncementUpdate.mutation'
import NewsAnnouncementUpdateForm from '@/modules/news/features/announcement-update/components/NewsAnnouncementUpdateForm.vue'

const props = defineProps<{
  announcement: NewsAnnouncementDetail
}>()

const i18n = useI18n()
const documentTitle = useDocumentTitle()
const newsAnnouncementUpdateMutation = useNewsAnnouncementUpdateMutation()
const newsAnnouncementDeleteMutation = useNewsAnnouncementDeleteMutation()
const confirmDialog = useVcDialog({ component: () => import('@/components/dialog/AppConfirmDialog.vue') })
const apiErrorToast = useApiErrorToast()
const router = useRouter()
const toast = useVcToast()

documentTitle.set(() => i18n.t('module.news.announcement.update.title'))

const form = useForm({
  initialState: {
    endDate: props.announcement.endDate ?? null,
    startDate: props.announcement.startDate ?? null,
    translations: setTranslations(),
    type: props.announcement.type,
  },
  schema: newsAnnouncementUpdateFormSchema,
  onSubmit: async (values) => {
    try {
      await newsAnnouncementUpdateMutation.execute({
        body: values,
        params: { newsAnnouncementUuid: props.announcement.uuid },
      })

      await router.push({ name: 'news-announcements-overview' })
    }
    catch (error) {
      apiErrorToast.show(error)
    }
  },
})

const isDeleting = computed<boolean>(() => newsAnnouncementDeleteMutation.isLoading.value)

function setTranslations(): NewsAnnouncementDetailTranslation[] {
  return Object.values(Locale).map((lang) => {
    const existingTranslation = props.announcement.translations?.find(
      (t) => t.language === lang,
    )

    return existingTranslation
      ? { ...existingTranslation }
      : {
          title: null,
          content: null,
          language: lang,
        }
  }) as NewsAnnouncementDetailTranslation[]
}

function onShowDeleteDialog(): void {
  confirmDialog.open({
    title: i18n.t('module.news.update_announcement.delete_announcement'),
    isDestructive: true,
    isLoading: isDeleting,
    cancelText: i18n.t('shared.cancel'),
    confirmText: i18n.t('shared.delete'),
    description: i18n.t('module.news.update_announcement.delete_message'),
    onConfirm: onConfirmDelete,
  })
}

async function onConfirmDelete(): Promise<void> {
  try {
    await newsAnnouncementDeleteMutation.execute({ params: { newsAnnouncementUuid: props.announcement.uuid } })
    await router.push({ name: 'news-announcements-overview' })
    toast.info({
      title: i18n.t('module.news.announcement.delete.success_message.title'),
      description: i18n.t('module.news.announcement.delete.success_message.description'),
    })

    confirmDialog.close()
  }
  catch (error) {
    apiErrorToast.show(error)
  }
}
</script>

<template>
  <AppTeleport to="headerLeft">
    <AppPageReturnLink
      :return-link="{
        label: i18n.t('module.news.announcements.update.return_to_overview'),
        to: {
          name: 'news-announcements-overview',
        },
      }"
    />
  </AppTeleport>

  <FormPage :title="i18n.t('module.news.announcement.update.title')">
    <NewsAnnouncementUpdateForm
      :form="form"
      @delete="onShowDeleteDialog"
    />
  </FormPage>
</template>
