<script setup lang="ts">
import {
  useVcDialog,
  useVcToast,
} from '@wisemen/vue-core-components'
import { useForm } from 'formango'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'

import { Locale } from '@/client/types.gen'
import AppTeleport from '@/components/app/teleport/AppTeleport.vue'
import FormPage from '@/components/form/FormPage.vue'
import AppPageReturnLink from '@/components/layout/page/AppPageReturnLink.vue'
import { useApiErrorToast } from '@/composables/api-error-toast/apiErrorToast.composable'
import { useDocumentTitle } from '@/composables/document-title/documentTitle.composable'
import { newsArticleFormSchema } from '@/models/news/article-update/newsArticleCreateForm.model'
import type {
  NewsArticleDetail,
  NewsArticleDetailTranslation,
} from '@/models/news/detail/newsArticleDetail.model'
import { useNewsArticleDeleteMutation } from '@/modules/news/api/mutations/newsArticleDelete.mutation'
import { useNewsArticleUpdateMutation } from '@/modules/news/api/mutations/newsArticleUpdate.mutation'
import NewsArticleForm from '@/modules/news/components/article-form/NewsArticleForm.vue'

const props = defineProps<{
  article: NewsArticleDetail
}>()

const i18n = useI18n()
const documentTitle = useDocumentTitle()
const newsArticleUpdateMutation = useNewsArticleUpdateMutation()
const newsArticleDeleteMutation = useNewsArticleDeleteMutation()
const apiErrorToast = useApiErrorToast()
const router = useRouter()
const toast = useVcToast()

documentTitle.set(() => i18n.t('module.news.article.update.page_title'))

const confirmDialog = useVcDialog({ component: () => import('@/components/dialog/AppConfirmDialog.vue') })

const form = useForm({
  initialState: {
    endDate: props.article.endDate ?? null,
    startDate: props.article.startDate ?? null,
    image: props.article.image,
    newsItemTranslations: setTranslations(),
    videoIFrame: props.article.videoIframe ?? null,
  },
  schema: newsArticleFormSchema,
  onSubmit: async (values) => {
    try {
      await newsArticleUpdateMutation.execute({
        body: values,
        params: { newsArticleUuid: props.article.uuid },
      })
      await router.push({ name: 'news-articles-overview' })
    }
    catch (error) {
      apiErrorToast.show(error)
    }
  },
})

const isDeleting = computed<boolean>(() => newsArticleDeleteMutation.isLoading.value)

function setTranslations(): NewsArticleDetailTranslation[] {
  return Object.values(Locale).map((lang) => {
    const existingTranslation = props.article.newItemTranslations?.find(
      (t) => t.language === lang,
    )

    return existingTranslation
      ? { ...existingTranslation }
      : {
          title: null,
          content: null,
          language: lang,
        }
  }) as NewsArticleDetailTranslation[]
}

function onShowDeleteDialog(): void {
  confirmDialog.open({
    title: i18n.t('module.news.update.delete_article'),
    isDestructive: true,
    isLoading: isDeleting,
    cancelText: i18n.t('shared.cancel'),
    confirmText: i18n.t('shared.delete'),
    description: i18n.t('module.news.update.delete_message'),
    onConfirm: onConfirmDelete,
  })
}

async function onConfirmDelete(): Promise<void> {
  try {
    await newsArticleDeleteMutation.execute({ body: { newsArticleUuid: props.article.uuid } })
    await router.push({ name: 'news-overview' })
    toast.info({
      title: i18n.t('module.news.delete.success_message.title'),
      description: i18n.t('module.news.delete.success_message.description'),
    })

    confirmDialog.close()
  }
  catch (error) {
    apiErrorToast.show(error)
  }
}
</script>

<template>
  <AppTeleport to="headerLeft">
    <AppPageReturnLink
      :return-link="{
        label: i18n.t('module.news.update.return_to_overview'),
        to: {
          name: 'news-articles-overview',
        },
      }"
    />
  </AppTeleport>

  <FormPage :title="i18n.t('module.news.article.update.page_title')">
    <NewsArticleForm
      :form="form"
      :is-update="true"
      @delete="onShowDeleteDialog"
    />
  </FormPage>
</template>
