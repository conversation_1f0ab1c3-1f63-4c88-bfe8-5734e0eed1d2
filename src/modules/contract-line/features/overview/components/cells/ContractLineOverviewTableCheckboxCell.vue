<script setup lang="ts">
import {
  VcCheckboxControl,
  VcCheckboxRoot,
} from '@wisemen/vue-core-components'
import {
  ref,
  watch,
} from 'vue'

import DataTableCell from '@/components/table/data-table/DataTableCell.vue'
import type { ContractLineIndex } from '@/models/contract-line/index/contractLineIndex.model'

const props = withDefaults(defineProps<{
  rowId: string
  isDisabled?: boolean
  selectedItems: ContractLineIndex[]
}>(), { isDisabled: false })

const emit = defineEmits<{
  update: [boolean]
}>()

const value = ref<boolean>(props.selectedItems.some((item) => item.contractLineId === props.rowId))

watch(() => props.selectedItems, (newSelectedItems) => {
  value.value = newSelectedItems.some((item) => item.contractLineId === props.rowId)
})
</script>

<template>
  <DataTableCell>
    <VcCheckboxRoot
      v-model="value"
      :is-disabled="props.isDisabled"
      @update:model-value="(value) => emit('update', value)"
    >
      <VcCheckboxControl />
    </VcCheckboxRoot>
  </DataTableCell>
</template>
