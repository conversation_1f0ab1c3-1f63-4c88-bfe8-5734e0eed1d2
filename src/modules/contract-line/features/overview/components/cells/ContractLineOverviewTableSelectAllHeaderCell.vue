<script setup lang="ts">
import {
  VcCheckboxControl,
  VcCheckboxRoot,
} from '@wisemen/vue-core-components'
import { computed } from 'vue'

import DataTableHeaderCell from '@/components/table/data-table/DataTableHeaderCell.vue'

const props = defineProps<{
  hasUnselectedItems: boolean
}>()

const emit = defineEmits<{
  click: []
}>()

const modelValue = defineModel<boolean>({ required: true })

const isIndeterminate = computed<boolean>(() => props.hasUnselectedItems ?? false)
</script>

<template>
  <DataTableHeaderCell>
    <VcCheckboxRoot
      v-model="modelValue"
      :is-indeterminate="isIndeterminate"
      @update:model-value="() => emit('click')"
    >
      <VcCheckboxControl />
    </VcCheckboxRoot>
  </DataTableHeaderCell>
</template>
