<script setup lang="ts">
import {
  useVcToast,
  VcButton,
  VcIcon,
  VcTooltip,
} from '@wisemen/vue-core-components'
import type { VNode } from 'vue'
import {
  computed,
  h,
  ref,
} from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'

import {
  DynamicColumnNames,
  PickUpTransportMode,
} from '@/client/types.gen'
import AppGroup from '@/components/app/AppGroup.vue'
import AppTableBulkActions from '@/components/app/table/AppTableBulkActions.vue'
import FiltersActive from '@/components/filters/active/FiltersActive.vue'
import { createAutocompleteFilter } from '@/components/filters/createFilters'
import FiltersDropdownMenu from '@/components/filters/dropdown-menu/FiltersDropdownMenu.vue'
import { useFilters } from '@/components/filters/filters.composable'
import FiltersRoot from '@/components/filters/FiltersRoot.vue'
import AppPage from '@/components/layout/page/AppPage.vue'
import DataTable from '@/components/table/data-table/DataTable.vue'
import DynamicTableSettings from '@/components/table/dynamic-table-settings/DynamicTableSettings.vue'
import DynamicTableViews from '@/components/table/dynamic-table-views/DynamicTableViews.vue'
import { useApiErrorToast } from '@/composables/api-error-toast/apiErrorToast.composable'
import { useCustomBooleanFilter } from '@/composables/custom-boolean-filter/customBooleanFilter.composable'
import { useDocumentTitle } from '@/composables/document-title/documentTitle.composable'
import { useDynamicTableV2 } from '@/composables/dynamic-table/dynamicTableV2.composable'
import { useEwcFilter } from '@/composables/ewc-filter/ewcFilter.composable'
import { useGlobalCustomer } from '@/composables/global-customer/globalCustomer.composable'
import { useSearch } from '@/composables/search/search.composable'
import { useSearchableTableColumns } from '@/composables/searchable-table-columns/searchableTableColumns.composable'
import { useSort } from '@/composables/sort/sort.composable'
import { useGenericColumn } from '@/composables/table-columns/genericTableColumnsV2.composable'
import { ContractLineToPickupRequestMaterialTransformer } from '@/models/contract-line/contractLine.transformer'
import type { ContractLineIndex } from '@/models/contract-line/index/contractLineIndex.model'
import type { ContractLineIndexQueryParams } from '@/models/contract-line/index/contractLineIndexQueryParams.model'
import { ContractLineIndexQueryParamsTransformer } from '@/models/contract-line/index/contractLineIndexQueryParams.transformer'
import { ContractLinePdfTransformer } from '@/models/contract-line/pdf/contractLinePdf.transformer'
import { ContractLinePackagingTypeEnumUtil } from '@/models/enums/contractLinePackagingType.enum'
import { DynamicTableName } from '@/models/enums/dynamicTableName.enum'
import { PickupRequestFormSteps } from '@/models/enums/formSteps.enum'
import type { PickupRequestUpdateForm } from '@/models/pickup-request/update/pickupRequestUpdateForm.model'
import type { WasteProducerIndexQueryParams } from '@/models/waste-producer/index/wasteProducerIndexQueryParams.model'
import { useContractGenerateBulkPdfMutation } from '@/modules/contract-line/api/mutations/contractGenerateBulkPdf.mutation'
import { useContractLineIndexQuery } from '@/modules/contract-line/api/queries/contractLineIndex.query'
import ContractLineOverviewTableCheckboxCell from '@/modules/contract-line/features/overview/components/cells/ContractLineOverviewTableCheckboxCell.vue'
import ContractLineOverviewTableSelectAllHeaderCell from '@/modules/contract-line/features/overview/components/cells/ContractLineOverviewTableSelectAllHeaderCell.vue'
import { usePickupRequestCreateMutation } from '@/modules/pickup-request/api/mutations/pickupRequestCreate.mutation'
import { usePickupRequestUpdateMutation } from '@/modules/pickup-request/api/mutations/pickupRequestUpdate.mutation'
import { useCustomerIndexQuery } from '@/modules/waste-inquiry/api/queries/customerIndex.query'
import { useWasteProducerIndexQuery } from '@/modules/waste-inquiry/api/queries/wasteProducerIndex.query'
import type { DataTableColumn } from '@/types/table.type'
import { DownloadUtil } from '@/utils/download.util'

const itemsSelectedInBulk = ref<ContractLineIndex[]>([])
const itemsUnselectedFromAll = ref<ContractLineIndex[]>([])
const areAllItemsSelected = ref<boolean>(false)
const isDownloadLoading = ref<boolean>(false)
const isPlanningLoading = ref<boolean>(false)

const i18n = useI18n()
const documentTitle = useDocumentTitle()
const toast = useVcToast()
const apiErrorToast = useApiErrorToast()
const router = useRouter()

documentTitle.set(() => i18n.t('module.contract.overview.title'))

const globalCustomer = useGlobalCustomer()

const customerIndexSearch = useSearch({ persistInUrl: false })

const customerIndexQuery = useCustomerIndexQuery({ params: { search: customerIndexSearch.debouncedSearch } })

const pickupRequestCreateMutation = usePickupRequestCreateMutation()
const pickupRequestUpdateMutation = usePickupRequestUpdateMutation()
const contractGenerateBulkPdfMutation = useContractGenerateBulkPdfMutation()

const search = useSearch({ persistInUrl: false })

const searchableColumns = useSearchableTableColumns({
  keys: [
    'asn',
    'contractNumber',
    'contractItem',
    'customerReference',
    'deliveryInfo',
    'endTreatmentCenterName',
    'esnNumber',
    'installationName',
    'materialAnalysis',
    'materialNumber',
    'materialDescription',
    'processCode',
    'tcNumber',
    'treatmentCenterName',
    'wasteProducerName',
    'wasteMaterial',
  ],
})

const sort = useSort({
  keys: [
    'asn',
    'contractItem',
    'contractNumber',
    'endTreatmentCenterId',
    'endTreatmentCenterName',
    'esnNumber',
    'ewcCode',
    'installationName',
    'pickUpAddressId',
    'pickUpAddressName',
    'processCode',
    'tcNumber',
    'treatmentCenterName',
    'wasteMaterial',
  ],
  persistInUrl: false,
})

const isHazardousFilter = useCustomBooleanFilter(
  'isHazardous',
  i18n.t('enum.dynamic_table_column_name.is_hazardous'),
)

const tfsFilter = useCustomBooleanFilter(
  'tfs',
  i18n.t('enum.dynamic_table_column_name.tfs_number'),
)

const packagedFilter = useCustomBooleanFilter(
  'packaged',
  i18n.t('enum.dynamic_table_column_name.packaged'),
)

const wasteProducerIndexSearch = ref<string>('')
const wasteProducerIndexFilters = ref<WasteProducerIndexQueryParams['filters']>({})

const wasteProducerIndexQuery = useWasteProducerIndexQuery({
  params: {
    filters: wasteProducerIndexFilters,
    search: wasteProducerIndexSearch,
  },
})

const ewcFilter = useEwcFilter()

const filters = useFilters({
  filterGroups: () => [
    {
      filters: [
        createAutocompleteFilter({
          isHidden: globalCustomer.globalCustomer.value !== null,
          isLoading: customerIndexQuery.isLoading.value,
          defaultValue: null,
          displayFn: (option) => option.name,
          key: 'customerId',
          label: i18n.t('enum.dynamic_table_column_name.customer_name'),
          options: customerIndexQuery.data.value?.data.map((customer) => ({
            id: customer.id,
            name: customer.name,
          })) ?? [],
          onSearch: (value) => {
            customerIndexSearch.updateSearch(value)
          },
        }),
        createAutocompleteFilter({
          isLoading: wasteProducerIndexQuery.isLoading.value,
          defaultValue: null,
          displayFn: (option) => option.name,
          key: 'wasteProducerId',
          label: i18n.t('enum.dynamic_table_column_name.waste_producer'),
          options: wasteProducerIndexQuery.data.value?.data.map((wp) => ({
            id: wp.id,
            name: wp.name,
          })) ?? [],
          onSearch: (value) => {
            wasteProducerIndexSearch.value = value
          },
        }),
        ewcFilter,
      ],
    },
    {
      filters: [
        tfsFilter,
        isHazardousFilter,
        packagedFilter,
      ],
    },
  ],
  persistInUrl: false,
})

const dynamicTable = useDynamicTableV2({
  dynamicTableName: DynamicTableName.CONTRACT_OVERVIEW,
  filters,
  sort,
})

const columns = computed<DataTableColumn<ContractLineIndex>[]>(() => {
  const dynamicColumns = dynamicTable.columns.value
    .filter((column) => column.isVisible)
    .map((column) => {
      switch (column.name) {
        case DynamicColumnNames.PACKAGED:
          return useGenericColumn(
            column.name,
            column.label,
            (value: ContractLineIndex) => value.packaged === null
              ? null
              : i18n.t(ContractLinePackagingTypeEnumUtil.getI18nKey(value.packaged)),
          )
        case DynamicColumnNames.PICK_UP_ADDRESS_ID:
          return useGenericColumn(
            column.name,
            column.label,
            (value: ContractLineIndex) => value.pickupAddressId,
          )
        case DynamicColumnNames.PICK_UP_ADDRESS_NAME:
          return useGenericColumn(
            column.name,
            column.label,
            (value: ContractLineIndex) => value.pickupAddressName,
          )
        default:
          return useGenericColumn(column.name, column.label)
      }
    })

  return [
    {
      cell: (row): VNode => h(ContractLineOverviewTableCheckboxCell, {
        rowId: row.contractLineId,
        selectedItems: itemsSelectedInBulk.value,
        onUpdate: (value) => onSelectRowCheckboxClick(row, value),
      }),
      header: (): VNode => h(ContractLineOverviewTableSelectAllHeaderCell, {
        'hasUnselectedItems': itemsUnselectedFromAll.value.length > 0,
        'modelValue': areAllItemsSelected.value,
        'onUpdate:modelValue': onSelectAllCheckboxClick,
      }),
      key: 'checkbox',
    },
    ...dynamicColumns,
  ] as DataTableColumn<ContractLineIndex>[]
})

const contractLineIndexQuery = useContractLineIndexQuery({
  params: {
    filters: computed<ContractLineIndexQueryParams['filters']>(() => ({
      ...filters.values.value,
      ...searchableColumns.values.value,
    })),
    search: search.debouncedSearch,
    sort: sort.values,
  },
})

const itemsSelectedHaveDifferentWasteProducers = computed<boolean>(() => {
  if (itemsSelectedInBulk.value.length === 0) {
    return false
  }

  const wasteProducerIds = new Set(itemsSelectedInBulk.value.map((item) => item.wasteProducerId))

  return wasteProducerIds.size > 1
})

function onSelectAllCheckboxClick(): void {
  areAllItemsSelected.value = !areAllItemsSelected.value

  if (areAllItemsSelected.value === true) {
    itemsSelectedInBulk.value = contractLineIndexQuery.data.value.data

    return
  }

  itemsUnselectedFromAll.value = []
  itemsSelectedInBulk.value = []
}

function onCancelBulkSelection(): void {
  itemsSelectedInBulk.value = []
  itemsUnselectedFromAll.value = []
  areAllItemsSelected.value = false
}

function onSelectRowCheckboxClick(row: ContractLineIndex, value: boolean): void {
  if (!row.contractLineId) {
    return
  }
  if (areAllItemsSelected.value) {
    if (!value) {
      itemsUnselectedFromAll.value.push(row)
    }
    else {
      itemsUnselectedFromAll.value = itemsUnselectedFromAll.value.filter(
        (item) => item.contractLineId !== row.contractLineId,
      )
    }

    const total = contractLineIndexQuery.data.value.data.length

    if (itemsUnselectedFromAll.value.length === total) {
      onCancelBulkSelection()
    }

    return
  }

  if (value) {
    itemsSelectedInBulk.value?.push(row)
  }
  else {
    itemsSelectedInBulk.value = itemsSelectedInBulk.value.filter((item) =>
      item.contractLineId !== row.contractLineId) ?? []
  }
}

async function onBulkDownload(
  items: ContractLineIndex[],
  isSelectAllMode?: boolean,
  excludedItems?: ContractLineIndex[],
): Promise<void> {
  isDownloadLoading.value = true

  try {
    let requestBody

    if (isSelectAllMode) {
      const currentFilters = {
        ...filters.values.value,
        ...searchableColumns.values.value,
      }

      const transformedQueryParams = ContractLineIndexQueryParamsTransformer.toDto({
        filters: currentFilters,
        pagination: {
          limit: 10,
          offset: 0,
        }, // Dummy pagination, not used for PDF
        search: search.debouncedSearch.value,
        sort: sort.values.value,
      })

      requestBody = ContractLinePdfTransformer.contractLinesToQuerySelectionDto(
        transformedQueryParams.filter,
        excludedItems,
      )
    }
    else {
      requestBody = ContractLinePdfTransformer.contractLinesToSelectionDto(items)
    }

    const response = await contractGenerateBulkPdfMutation.execute({ body: requestBody })

    const blob = DownloadUtil.transformBase64ToBlob(response.content, response.mimeType)

    DownloadUtil.openBlobInNewTab(blob, response.name, response.mimeType)
  }
  catch (error) {
    apiErrorToast.show(error)
  }
  finally {
    isDownloadLoading.value = false
  }
}

async function onBulkPlanning(items: ContractLineIndex[]): Promise<void> {
  isPlanningLoading.value = true

  if (items[0].customerId === null) {
    toast.error({
      title: i18n.t('module.contract.overview.bulk.plan_error.no_customer'),
      description: i18n.t('error.default_error.description'),
    })

    return
  }

  try {
    const pickupUuid = await pickupRequestCreateMutation.execute()

    const pickup: Partial<PickupRequestUpdateForm> = {
      customer: {
        id: items[0].customerId,
        name: '',
        address: null,
      },
      materials: items.map((item) => (ContractLineToPickupRequestMaterialTransformer.toPickupMaterial(item))),
      transportMode: PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK,
      wasteProducer: items[0].wasteProducerId !== null
        ? {
            id: items[0].wasteProducerId,
            name: '',
            address: null,
          }
        : null,
    }

    await pickupRequestUpdateMutation.execute({
      body: pickup,
      params: { pickupRequestUuid: pickupUuid },
    })

    router.push({
      name: 'pickup-request-update',
      params: { pickupRequestUuid: pickupUuid },
      query: { step: PickupRequestFormSteps.WASTE },
    })
  }
  catch (error) {
    apiErrorToast.show(error)
  }
  finally {
    isPlanningLoading.value = false
  }
}
</script>

<template>
  <AppPage :title="i18n.t('module.contract.overview.title')">
    <DataTable
      :is-loading="contractLineIndexQuery.isLoading.value"
      :data="contractLineIndexQuery.data.value.data"
      :columns="columns"
      :error="contractLineIndexQuery.error.value"
      :searchable-columns="searchableColumns"
      :get-key="(_, index) => `${index}`"
      :filters="filters"
      :sort="sort"
      :search="search"
      :is-first-column-sticky="true"
      class="pb-xl"
      @next-page="contractLineIndexQuery.fetchNextPage()"
    >
      <template #top>
        <FiltersRoot :filters="filters">
          <AppGroup
            justify="between"
            class="px-xl h-14"
          >
            <FiltersActive />

            <AppGroup>
              <DynamicTableViews :dynamic-table="dynamicTable" />
              <FiltersDropdownMenu />
              <DynamicTableSettings :dynamic-table="dynamicTable" />
            </AppGroup>
          </AppGroup>
        </FiltersRoot>

        <AppTableBulkActions
          :count="itemsSelectedInBulk.length"
          :are-all-items-selected="areAllItemsSelected"
          :unselected-items-count="itemsUnselectedFromAll.length"
          :is-always-visible="false"
          @cancel-bulk-selection="onCancelBulkSelection"
        >
          <template #actions>
            <VcButton
              :is-loading="isDownloadLoading"
              :is-disabled="itemsSelectedInBulk.length === 0 && !areAllItemsSelected"
              variant="secondary"
              @click="onBulkDownload(itemsSelectedInBulk, areAllItemsSelected, itemsUnselectedFromAll)"
            >
              {{ i18n.t('module.contract.overview.bulk.download_pdf') }}
            </VcButton>
            <VcTooltip
              :disable-close-on-trigger-click="true"
              :is-disabled="!itemsSelectedHaveDifferentWasteProducers"
            >
              <template #trigger>
                <VcButton
                  :is-disabled="itemsSelectedInBulk.length === 0 || itemsSelectedHaveDifferentWasteProducers"
                  :is-loading="isPlanningLoading"
                  :icon-left="itemsSelectedHaveDifferentWasteProducers ? 'alertTriangle' : null"
                  @click="onBulkPlanning(itemsSelectedInBulk)"
                >
                  {{ i18n.t('module.contract.overview.bulk.plan') }}
                </VcButton>
              </template>
              <template #content>
                <div class="p-md flex items-center gap-3">
                  <VcIcon
                    icon="alertTriangle"
                    class="text-warning-primary w-6"
                  />
                  <p class="max-w-70 text-sm">
                    {{ i18n.t('module.contract.overview.bulk.plan_error.different_waste_producers') }}
                  </p>
                </div>
              </template>
            </VcTooltip>
          </template>
        </AppTableBulkActions>
      </template>
    </DataTable>
  </AppPage>
</template>
