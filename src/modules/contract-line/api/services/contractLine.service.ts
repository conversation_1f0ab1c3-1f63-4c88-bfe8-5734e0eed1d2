import type {
  PaginatedData,
  PaginationOptions,
} from '@wisemen/vue-core-components'
import { PaginationParamsBuilder } from '@wisemen/vue-core-components'

import {
  generateContractLinesPdfV1,
  viewContractLineIndexV1,
  viewPackagingRequestContractLineIndexV1,
  viewWprContractLineIndexV1,
} from '@/client'
import {
  ContractLineIndexPaginationTransformer,
  ContractLineIndexTransformer,
  ContractLinePackagingRequestIndexPaginationTransformer,
  ContractLinePackagingRequestIndexTransformer,
  ContractLineWeeklyPlanningIndexTransformer,
} from '@/models/contract-line/contractLine.transformer'
import type { ContractLineIndex } from '@/models/contract-line/index/contractLineIndex.model'
import type {
  ContractLineIndexPagination,
  ContractLinePackagingRequestIndexPagination,
} from '@/models/contract-line/index/contractLineIndexPagination.model'
import type { ContractLineIndexQueryParams } from '@/models/contract-line/index/contractLineIndexQueryParams.model'
import { ContractLineIndexQueryParamsTransformer } from '@/models/contract-line/index/contractLineIndexQueryParams.transformer'
import type { ContractLinePackagingRequestIndex } from '@/models/contract-line/packaging-request/contractLinePackagingRequestIndex.model'
import type { ContractLineGeneratePdf } from '@/models/contract-line/pdf/contractLineGeneratePdf.model.ts'
import type { ContractLineGeneratePdfResponse } from '@/models/contract-line/pdf/contractLineGeneratePdfResponse.model'
import { ContractLinePdfTransformer } from '@/models/contract-line/pdf/contractLinePdf.transformer'
import type { ContractLineWeeklyPlanningIndex } from '@/models/contract-line/weekly-planning/contractLineWeeklyPlanningIndex.model'
import type { ContractLineWeeklyPlanningIndexQueryParams } from '@/models/contract-line/weekly-planning/contractLineWeeklyPlanningIndexQueryParams'
import { ContractLineWeeklyPlanningIndexQueryParamsTransformer } from '@/models/contract-line/weekly-planning/contractLineWeeklyPlanningIndexQueryParams.transformer'
import type {
  OffsetPagination,
  OffsetPaginationResponse,
} from '@/types/pagination.type'

export class ContractLineService {
  static async generateBulkPdf(data: ContractLineGeneratePdf): Promise<ContractLineGeneratePdfResponse> {
    const response = await generateContractLinesPdfV1({ body: data })

    return ContractLinePdfTransformer.fromDto(response.data)
  }

  static async getAll(
    params: OffsetPagination<ContractLineIndexQueryParams>,
  ): Promise<OffsetPaginationResponse<ContractLineIndex>> {
    const response = await viewContractLineIndexV1({ query: ContractLineIndexQueryParamsTransformer.toDto(params) })

    return {
      data: response.data.items.map(ContractLineIndexTransformer.fromDto),
      meta: response.data.meta,
    }
  }

  // TODO: @Tanya-Amber-L remove this and refactor to the new getAll method above
  static async getAllOld(
    paginationOptions: PaginationOptions<ContractLineIndexPagination>,
  ): Promise<PaginatedData<ContractLineIndex>> {
    const query = new PaginationParamsBuilder(paginationOptions)
      .build(ContractLineIndexPaginationTransformer.toDto)

    const response = await viewContractLineIndexV1({
      query: {
        ...query,
        sort: [],
      },
    })

    return {
      data: response.data.items.map(ContractLineIndexTransformer.fromDto),
      meta: {
        limit: response.data.meta.limit,
        offset: response.data.meta.offset,
        total: response.data.meta.total,
      },
    }
  }

  static async getAllWeeklyPlanningLines(
    params: OffsetPagination<ContractLineWeeklyPlanningIndexQueryParams>,
  ): Promise<OffsetPaginationResponse<ContractLineWeeklyPlanningIndex>> {
    const query = ContractLineWeeklyPlanningIndexQueryParamsTransformer.toDto(params)
    const response = await viewWprContractLineIndexV1({ query })

    return {
      data: response.data.items.map(ContractLineWeeklyPlanningIndexTransformer.fromDto),
      meta: {
        limit: response.data.meta.limit,
        offset: response.data.meta.offset,
        total: response.data.meta.total,
      },
    }
  }

  static async getPackagingRequestLines(
    paginationOptions: PaginationOptions<ContractLinePackagingRequestIndexPagination>,
  ): Promise<PaginatedData<ContractLinePackagingRequestIndex>> {
    const query = new PaginationParamsBuilder(paginationOptions)
      .build(ContractLinePackagingRequestIndexPaginationTransformer.toDto)

    const response = await viewPackagingRequestContractLineIndexV1({ query })

    return {
      data: response.data.items.map(ContractLinePackagingRequestIndexTransformer.fromDto),
      meta: {
        limit: response.data.meta.limit,
        offset: response.data.meta.offset,
        total: response.data.items.length,
      },
    }
  }

  // TODO: @Tanya-Amber-L remove this and refactor to the new getAllWeeklyPlanningLines method above
  static async getWeeklyPlanningLines(
    paginationOptions: PaginationOptions<ContractLineIndexPagination>,
  ): Promise<PaginatedData<ContractLineWeeklyPlanningIndex>> {
    const query = new PaginationParamsBuilder(paginationOptions).build(ContractLineIndexPaginationTransformer.toDto)

    if (!query.filter || typeof query.filter.customerId !== 'string') {
      throw new Error('Customer ID must be a string for fetching weekly planning lines.')
    }

    const safeQuery = {
      ...query,
      filter: {
        ...query.filter,
        customerId: query.filter.customerId as string,
      },
    }

    const response = await viewWprContractLineIndexV1({ query: safeQuery })

    return {
      data: response.data.items.map(ContractLineWeeklyPlanningIndexTransformer.fromDto),
      meta: response.data.meta,
    }
  }
}
