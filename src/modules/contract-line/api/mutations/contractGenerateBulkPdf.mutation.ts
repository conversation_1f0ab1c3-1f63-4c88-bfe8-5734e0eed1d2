import type { UseMutationReturnType } from '@wisemen/vue-core-query'
import { useMutation } from '@wisemen/vue-core-query'

import type { ContractLineGeneratePdf } from '@/models/contract-line/pdf/contractLineGeneratePdf.model.ts'
import type { ContractLineGeneratePdfResponse } from '@/models/contract-line/pdf/contractLineGeneratePdfResponse.model'
import { ContractLineService } from '@/modules/contract-line/api/services/contractLine.service'

export function useContractGenerateBulkPdfMutation():
UseMutationReturnType<ContractLineGeneratePdf, ContractLineGeneratePdfResponse> {
  return useMutation<ContractLineGeneratePdf, ContractLineGeneratePdfResponse>({
    queryFn: async ({ body }) => {
      return await ContractLineService.generateBulkPdf(body)
    },
    queryKeysToInvalidate: {},
  })
}
