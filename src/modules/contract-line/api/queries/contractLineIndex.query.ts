import { useOffsetInfiniteQuery } from '@/composables/offset-infinite-query/offsetInfiniteQuery.composable.ts'
import type { ContractLineIndexQueryParams } from '@/models/contract-line/index/contractLineIndexQueryParams.model'
import { ContractLineService } from '@/modules/contract-line/api/services/contractLine.service'
import type { InfiniteQueryOptions } from '@/types/query.type'

// eslint-disable-next-line ts/explicit-function-return-type
export function useContractLineIndexQuery(options: InfiniteQueryOptions<ContractLineIndexQueryParams>) {
  return useOffsetInfiniteQuery({
    queryFn: (pagination) => {
      return ContractLineService.getAll({
        filters: options.params.filters.value,
        pagination,
        search: options.params.search.value,
        sort: options.params.sort.value,
      })
    },
    queryKey: { contractLineIndex: { queryParams: options.params } },
  })
}
