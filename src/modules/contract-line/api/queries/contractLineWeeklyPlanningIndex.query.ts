import { useOffsetInfiniteQuery } from '@/composables/offset-infinite-query/offsetInfiniteQuery.composable.ts'
import type { ContractLineWeeklyPlanningIndexQueryParams } from '@/models/contract-line/weekly-planning/contractLineWeeklyPlanningIndexQueryParams'
import { ContractLineService } from '@/modules/contract-line/api/services/contractLine.service'
import type { InfiniteQueryOptions } from '@/types/query.type'

// eslint-disable-next-line ts/explicit-function-return-type
export function useContractLineWeeklyPlanningIndexQuery(
  options: InfiniteQueryOptions<ContractLineWeeklyPlanningIndexQueryParams>,
) {
  return useOffsetInfiniteQuery({
    queryFn: (pagination) => {
      return ContractLineService.getAllWeeklyPlanningLines({
        filters: options.params.filters.value,
        pagination,
      })
    },
    queryKey: { contractLineWeeklyPlanningIndex: { queryParams: options.params } },
  })
}
