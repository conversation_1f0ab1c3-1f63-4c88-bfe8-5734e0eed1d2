import type { PaginationOptions } from '@wisemen/vue-core-components'
import type { UseInfiniteQueryReturnType } from '@wisemen/vue-core-query'
import { useInfiniteQuery } from '@wisemen/vue-core-query'
import type { ComputedRef } from 'vue'

import type { ContractLinePackagingRequestIndexPagination } from '@/models/contract-line/index/contractLineIndexPagination.model'
import type { ContractLinePackagingRequestIndex } from '@/models/contract-line/packaging-request/contractLinePackagingRequestIndex.model'
import { ContractLineService } from '@/modules/contract-line/api/services/contractLine.service'

export function useContractLinePackagingRequestIndexQuery(
  paginationOptions: ComputedRef<PaginationOptions<ContractLinePackagingRequestIndexPagination>>,
): UseInfiniteQueryReturnType<ContractLinePackagingRequestIndex> {
  return useInfiniteQuery<ContractLinePackagingRequestIndex, ContractLinePackagingRequestIndexPagination>({
    paginationOptions,
    queryFn: async (options) => {
      return await ContractLineService.getPackagingRequestLines(options)
    },
    queryKey: { contractLinePackagingRequestIndex: { paginationOptions } },
  })
}
