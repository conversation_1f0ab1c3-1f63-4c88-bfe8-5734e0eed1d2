import type { PaginationOptions } from '@wisemen/vue-core-components'
import type { UseInfiniteQueryReturnType } from '@wisemen/vue-core-query'
import { useInfiniteQuery } from '@wisemen/vue-core-query'
import type { ComputedRef } from 'vue'

import type { ContractLineIndex } from '@/models/contract-line/index/contractLineIndex.model'
import type { ContractLineIndexPagination } from '@/models/contract-line/index/contractLineIndexPagination.model.ts'
import { ContractLineService } from '@/modules/contract-line/api/services/contractLine.service'

export function useContractLineIndexQueryOld(
  paginationOptions: ComputedRef<PaginationOptions<ContractLineIndexPagination>>,
): UseInfiniteQueryReturnType<ContractLineIndex> {
  return useInfiniteQuery<ContractLineIndex, ContractLineIndexPagination>({
    paginationOptions,
    queryFn: async (options) => {
      return await ContractLineService.getAllOld(options)
    },
    queryKey: { contractLineIndexOld: { paginationOptions } },
  })
}
