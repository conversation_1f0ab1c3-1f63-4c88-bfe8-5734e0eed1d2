<script setup lang="ts">
import { useI18n } from 'vue-i18n'

import AppRouterLink from '@/components/app/link/AppRouterLink.vue'

const i18n = useI18n()

const PRIVACY_COOKIES_URL = 'https://indaver.com/privacy-cookies'
const COMPANY_INFO_URL = 'https://indaver.com/company-data'
</script>

<template>
  <div
    class="
      gap-md flex items-center
      [&>*:not(:last-child)]:after:ml-md
      [&>*:not(:last-child)]:after:text-tertiary
      [&>*:not(:last-child)]:after:content-['|']
    "
  >
    <AppRouterLink
      :to="{ name: 'auth-legal-link-disclaimer' }"
      class="
        text-tertiary text-sm transition-colors
        hover:text-primary
      "
    >
      {{ i18n.t('auth.legal_links.disclaimer') }}
    </AppRouterLink>
    <a
      :href="PRIVACY_COOKIES_URL"
      target="_blank"
      rel="noopener noreferrer"
      class="
        text-tertiary text-sm transition-colors
        hover:text-primary
      "
    >
      {{ i18n.t('auth.legal_links.privacy_&_cookies') }}
    </a>
    <a
      :href="COMPANY_INFO_URL"
      target="_blank"
      rel="noopener noreferrer"
      class="
        text-tertiary text-sm transition-colors
        hover:text-primary
      "
    >
      {{ i18n.t('auth.legal_links.company_info') }}
    </a>
  </div>
</template>
