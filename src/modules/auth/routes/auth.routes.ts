import type { Component } from 'vue'
import type { RouteRecordRaw } from 'vue-router'

import { guest } from '@/middlewares/guest.middleware.ts'

export const authRoutes = [
  {
    path: '/auth',
    component: (): Component => import('@/modules/auth/components/AuthLayout.vue'),
    redirect: { name: 'auth-login' },
    children: [
      {
        name: 'auth-login',
        path: 'login',
        component: (): Component => import('@/modules/auth/features/login/views/AuthLoginView.vue'),
        meta: {
          middleware: [
            guest,
          ],
        },
      },
      {
        name: 'auth-callback',
        path: 'callback',
        component: (): Component => import('@/modules/auth/features/callback/views/AuthCallbackView.vue'),
      },
      {
        name: 'auth-logout',
        path: 'logout',
        component: (): Component => import('@/modules/auth/features/logout/views/AuthLogoutView.vue'),
      },
      {
        name: 'auth-legal-link-disclaimer',
        path: 'legal-link-disclaimer',
        component: (): Component => import('@/modules/auth/features/legal-links/views/AuthLegalLinksDisclaimerView.vue'),
      },
    ],
  },
] as const satisfies RouteRecordRaw[]
