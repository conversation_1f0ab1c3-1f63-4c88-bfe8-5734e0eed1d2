<script setup lang="ts">
import { VcButton } from '@wisemen/vue-core-components'
import { Motion } from 'motion-v'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'

import { TEST_ID } from '@/constants/testId.constant.ts'

const i18n = useI18n()
const router = useRouter()
</script>

<template>
  <div class="px-3xl py-3xl size-full overflow-y-auto">
    <div>
      <Motion
        :initial="{ opacity: 0, y: 12, filter: 'blur(8px)' }"
        :animate="{ opacity: 1, y: 0, filter: 'blur(0)' }"
        :transition="{ duration: 0.4, ease: 'easeOut' }"
      >
        <div
          class="
            bg-brand-primary-alt mt-xl border-brand-200 p-2xl sticky top-0 z-10
            rounded-2xl border shadow-sm
          "
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <VcButton
                :label="i18n.t('shared.back')"
                icon-left="chevronLeft"
                size="sm"
                variant="tertiary"
                @click="router.push({ name: 'auth-login' })"
              />
              <img
                src="@/assets/svgs/logo-dark.svg"
                alt="Indaver"
                class="h-8 w-auto"
              >
            </div>
            <span
              class="
                text-brand-700 border-brand-200 bg-brand-500/10 px-md py-xs
                inline-flex items-center rounded-full border text-xs
                font-semibold tracking-wide uppercase
              "
            >
              {{ i18n.t('module.auth.login.customer_zone.label') }}
            </span>
          </div>
          <h1 class="mt-lg text-brand-700 text-display-sm font-semibold">
            {{ i18n.t('auth.legal_links.disclaimer') }}
          </h1>
          <div class="mt-sm bg-brand-500 h-1 w-12 rounded-full" />
        </div>
      </Motion>

      <div
        :data-test-id="TEST_ID.AUTH.LEGAL_LINKS.DISCLAIMER"
        class="mt-xl border-brand-200 bg-primary rounded-xl border shadow-sm"
      >
        <div
          class="p-2xl text-secondary max-w-3xl space-y-4 text-sm leading-6"
        >
          <p>{{ i18n.t('module.auth.legal_links.disclaimer.paragraph_1') }}</p>
          <p>{{ i18n.t('module.auth.legal_links.disclaimer.paragraph_2') }}</p>
          <p>{{ i18n.t('module.auth.legal_links.disclaimer.paragraph_3') }}</p>
          <p>{{ i18n.t('module.auth.legal_links.disclaimer.paragraph_4') }}</p>
          <p>{{ i18n.t('module.auth.legal_links.disclaimer.paragraph_5') }}</p>
          <p>{{ i18n.t('module.auth.legal_links.disclaimer.paragraph_6') }}</p>
          <p>{{ i18n.t('module.auth.legal_links.disclaimer.paragraph_7') }}</p>
          <p>{{ i18n.t('module.auth.legal_links.disclaimer.paragraph_8') }}</p>
        </div>
      </div>
    </div>
  </div>
</template>
