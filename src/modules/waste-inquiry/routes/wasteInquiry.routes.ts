import type { Component } from 'vue'
import type { RouteRecordRaw } from 'vue-router'

export const wasteInquiryRoutes = [
  {
    path: '/waste-inquiries',
    children: [
      {
        name: 'waste-inquiry-overview',
        path: '',
        component: (): Component => import('@/modules/waste-inquiry/features/overview/views/WasteInquiryOverviewView.vue'),
      },
      {
        name: 'waste-inquiry-detail',
        props: true,
        path: ':inquiryNumber',
        component: (): Component => import('@/modules/waste-inquiry/features/detail/views/WasteInquiryDetailDataProviderView.vue'),
      },
      {
        name: 'waste-inquiry-update',
        props: true,
        path: ':wasteInquiryUuid/edit',
        component: (): Component => import('@/modules/waste-inquiry/features/update/views/WasteInquiryUpdateDataProviderView.vue'),
      },
    ],
  },
] as const satisfies RouteRecordRaw[]
