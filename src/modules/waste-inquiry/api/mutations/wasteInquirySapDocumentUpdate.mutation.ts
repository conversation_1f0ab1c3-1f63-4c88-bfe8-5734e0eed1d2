import type { UseMutationReturnType } from '@wisemen/vue-core-query'
import { useMutation } from '@wisemen/vue-core-query'

import type { WasteInquirySapDocumentUpdateForm } from '@/models/waste-inquiry/update/sap/wasteInquirySapDocumentUpdateForm.model'
import { WasteInquiryService } from '@/modules/waste-inquiry/api/services/wasteInquiry.service'

interface Params {
  inquiryNumber: string
}

export function useWasteInquirySapDocumentUpdateMutation():
UseMutationReturnType<WasteInquirySapDocumentUpdateForm, void, Params> {
  return useMutation<WasteInquirySapDocumentUpdateForm, void, Params>({
    queryFn: async ({
      body, params,
    }) => {
      await WasteInquiryService.updateSapDocument(params.inquiryNumber, body)
    },
    queryKeysToInvalidate: { },
  })
}
