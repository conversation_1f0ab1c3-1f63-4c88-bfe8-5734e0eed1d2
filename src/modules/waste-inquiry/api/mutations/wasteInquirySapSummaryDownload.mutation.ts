import type { UseMutationReturnType } from '@wisemen/vue-core-query'
import { useMutation } from '@wisemen/vue-core-query'

import type { FileDownload } from '@/models/file/fileDownload.model'
import { WasteInquiryService } from '@/modules/waste-inquiry/api/services/wasteInquiry.service'

interface Params {
  inquiryNumber: string
}

export function useWasteInquirySapSummaryDownloadMutation(): UseMutationReturnType<Params, FileDownload> {
  return useMutation<Params, FileDownload>({
    queryFn: async ({ body }) => {
      return await WasteInquiryService.downloadSummary(body.inquiryNumber)
    },
    queryKeysToInvalidate: {},
  })
}
