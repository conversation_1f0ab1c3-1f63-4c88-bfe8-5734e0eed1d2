import type { RequestType } from '@/client'
import {
  viewCustomerCountryV1,
  viewCustomerIndexV1,
  viewSuggestedCustomersV1,
} from '@/client'
import { CustomerIndexTransformer } from '@/models/customer/customer.transformer'
import type { CustomerIndex } from '@/models/customer/index/customerIndex.model'
import type { CustomerIndexQueryParams } from '@/models/customer/index/customerIndexQueryParams.model'
import { CustomerIndexQueryParamsTransformer } from '@/models/customer/index/customerIndexQueryParams.transformer'
import type { CustomerCountryCode } from '@/models/enums/customerCountryCode.enum'
import type {
  OffsetPagination,
  OffsetPaginationResponse,
} from '@/types/pagination.type'

export class CustomerService {
  static async getAll(
    options: OffsetPagination<CustomerIndexQueryParams>,
  ): Promise<OffsetPaginationResponse<CustomerIndex>> {
    const response = await viewCustomerIndexV1({ query: CustomerIndexQueryParamsTransformer.toDto(options) })

    return {
      data: response.data.items.map(CustomerIndexTransformer.fromDto),
      meta: {
        limit: options.pagination.limit,
        offset: options.pagination.offset,
        total: response.data.meta.total,
      },
    }
  }

  static async getCustomerCountry(customerId: string): Promise<CustomerCountryCode> {
    const response = await viewCustomerCountryV1({ path: { id: customerId } })

    return response.data.countryCode as CustomerCountryCode
  }

  static async getSuggested(requestType: RequestType): Promise<CustomerIndex[]> {
    const response = await viewSuggestedCustomersV1({ query: { filter: { requestType } } })

    return response.data.items.map(CustomerIndexTransformer.fromDto)
  }
}
