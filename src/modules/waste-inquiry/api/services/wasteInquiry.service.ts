import {
  bulkDeleteWasteInquiryV1,
  copyWasteInquirySapV1,
  createWasteInquiryV1,
  downloadWasteInquirySummarySapV1,
  submitWasteInquiryV1,
  updateWasteInquiryV1,
  uploadDocumentSubmittedWasteInquiryV1,
  viewWasteInquiryIndexV1,
  viewWasteInquirySapV1,
  viewWasteInquiryV1,
} from '@/client'
import type { FileDownload } from '@/models/file/fileDownload.model'
import type { WasteInquiryCreate } from '@/models/waste-inquiry/create/wasteInquiryCreate.model'
import type { WasteInquiryDetail } from '@/models/waste-inquiry/detail/wasteInquiryDetail.model'
import type { WasteInquiryIndex } from '@/models/waste-inquiry/index/wasteInquiryIndex.model'
import type { WasteInquiryIndexQueryParams } from '@/models/waste-inquiry/index/wasteInquiryIndexQueryParams.model'
import { WasteInquiryIndexQueryParamsTransformer } from '@/models/waste-inquiry/index/wasteInquiryIndexQueryParams.transformer'
import type { WasteInquirySapDetail } from '@/models/waste-inquiry/sap-detail/wasteInquirySapDetail.model'
import type { WasteInquirySapDocumentUpdateForm } from '@/models/waste-inquiry/update/sap/wasteInquirySapDocumentUpdateForm.model'
import type { WasteInquirySubmitForm } from '@/models/waste-inquiry/update/submit/wasteInquirySubmitForm.model'
import type { WasteInquirySubmitResponse } from '@/models/waste-inquiry/update/submit/wasteInquirySubmitResponse.model'
import type { WasteInquiryUpdateForm } from '@/models/waste-inquiry/update/wasteInquiryUpdateForm.model'
import {
  WasteInquiryCreateTransformer,
  WasteInquiryDetailTransformer,
  WasteInquiryIndexTransformer,
  WasteInquirySapDetailTransformer,
  WasteInquirySapDocumentUpdateTransformer,
  WasteInquirySubmitFormTransformer,
  WasteInquirySubmitResponseTransformer,
  WasteInquiryUpdateTransformer,
} from '@/models/waste-inquiry/wasteInquiry.transformer'
import type { WasteInquiryUuid } from '@/models/waste-inquiry/wasteInquiryUuid.model'
import type {
  OffsetPagination,
  OffsetPaginationResponse,
} from '@/types/pagination.type'

export class WasteInquiryService {
  static async copy(inquiryNumber: string): Promise<WasteInquiryUuid> {
    const response = await copyWasteInquirySapV1({ path: { inquiryNumber } })

    return response.data.uuid as WasteInquiryUuid
  }

  static async create(): Promise<WasteInquiryCreate> {
    const response = await createWasteInquiryV1({ body: {} })

    return WasteInquiryCreateTransformer.fromDto(response.data)
  }

  static async deleteInBulk(uuids: WasteInquiryUuid[]): Promise<void> {
    await bulkDeleteWasteInquiryV1({ body: { wasteInquiryUuids: uuids } })
  }

  static async downloadSummary(inquiryNumber: string): Promise<FileDownload> {
    const response = await downloadWasteInquirySummarySapV1({ path: { inquiryNumber } })
    const disposition = response.response.headers.get('Content-Disposition')

    return {
      blob: response.data as Blob,
      disposition,
    }
  }

  static async getAll(
    options: OffsetPagination<WasteInquiryIndexQueryParams>,
  ): Promise<OffsetPaginationResponse<WasteInquiryIndex>> {
    const response = await viewWasteInquiryIndexV1({ query: WasteInquiryIndexQueryParamsTransformer.toDto(options) })

    return {
      data: response.data.items.map(WasteInquiryIndexTransformer.fromDto),
      meta: {
        limit: options.pagination.limit,
        offset: options.pagination.offset,
        total: response.data.meta.total,
      },
    }
  }

  static async getByUuid(wasteInquiryUuid: WasteInquiryUuid): Promise<WasteInquiryDetail> {
    const response = await viewWasteInquiryV1({ path: { uuid: wasteInquiryUuid } })

    return WasteInquiryDetailTransformer.fromDto(response.data)
  }

  static async getSapDetailByInquiryNumber(inquiryNumber: string): Promise<WasteInquirySapDetail> {
    const response = await viewWasteInquirySapV1({ path: { inquiryNumber } })

    return WasteInquirySapDetailTransformer.fromDto(response.data)
  }

  static async submit(wasteInquiryUuid: WasteInquiryUuid, form: WasteInquirySubmitForm):
  Promise<WasteInquirySubmitResponse> {
    await updateWasteInquiryV1({
      body: WasteInquirySubmitFormTransformer.toDto(form),
      path: { uuid: wasteInquiryUuid },
    })

    const response = await submitWasteInquiryV1({ path: { uuid: wasteInquiryUuid } })

    return WasteInquirySubmitResponseTransformer.fromDto(response.data)
  }

  static async update(wasteInquiryUuid: WasteInquiryUuid, form: Partial<WasteInquiryUpdateForm>): Promise<void> {
    await updateWasteInquiryV1({
      body: WasteInquiryUpdateTransformer.toDto(form),
      path: { uuid: wasteInquiryUuid },
    })
  }

  static async updateSapDocument(inquiryNumber: string, form: WasteInquirySapDocumentUpdateForm): Promise<void> {
    await uploadDocumentSubmittedWasteInquiryV1({
      body: WasteInquirySapDocumentUpdateTransformer.toDto(form),
      path: { inquiryNumber },
    })
  }
}
