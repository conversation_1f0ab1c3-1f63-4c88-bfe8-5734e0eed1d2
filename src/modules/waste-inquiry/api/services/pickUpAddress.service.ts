import type { RequestType } from '@/client'
import {
  viewPickUpAddressIndexV1,
  viewSuggestedPickUpAddressesV1,
} from '@/client'
import type { PickUpAddressIndex } from '@/models/pick-up-address/index/pickUpAddressIndex.model'
import type { PickUpAddressIndexQueryParams } from '@/models/pick-up-address/index/pickUpAddressIndexQueryParams.model'
import { PickUpAddressIndexQueryParamsTransformer } from '@/models/pick-up-address/index/pickUpAddressIndexQueryParams.transformer'
import { PickUpAddressIndexTransformer } from '@/models/pick-up-address/pickUpAddress.transformer'
import type {
  OffsetPagination,
  OffsetPaginationResponse,
} from '@/types/pagination.type'

export class PickUpAddressService {
  static async getAll(
    options: OffsetPagination<PickUpAddressIndexQueryParams>,
  ): Promise<OffsetPaginationResponse<PickUpAddressIndex>> {
    const response = await viewPickUpAddressIndexV1({ query: PickUpAddressIndexQueryParamsTransformer.toDto(options) })

    return {
      data: response.data.items.map(PickUpAddressIndexTransformer.fromDto),
      meta: {
        limit: options.pagination.limit,
        offset: options.pagination.offset,
        total: response.data.meta.total,
      },
    }
  }

  static async getSuggested(customerId: string, requestType: RequestType): Promise<PickUpAddressIndex[]> {
    const response = await viewSuggestedPickUpAddressesV1(
      {
        query: {
          filter: {
            customerId,
            requestType,
          },
        },
        requestValidator: undefined,
      },
    )

    return response.data.items.map(PickUpAddressIndexTransformer.fromDto)
  }
}
