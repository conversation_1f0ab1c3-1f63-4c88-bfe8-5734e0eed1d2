import {
  RequestType,
  viewSuggestedWasteProducersV1,
  viewWasteProducerIndexV1,
} from '@/client'
import type { WasteProducerIndex } from '@/models/waste-producer/index/wasteProducerIndex.model'
import type { WasteProducerIndexQueryParams } from '@/models/waste-producer/index/wasteProducerIndexQueryParams.model'
import { WasteProducerIndexQueryParamsTransformer } from '@/models/waste-producer/index/wasteProducerIndexQueryParams.transformer'
import { WasteProducerIndexTransformer } from '@/models/waste-producer/wasteProducer.transformer'
import type {
  OffsetPagination,
  OffsetPaginationResponse,
} from '@/types/pagination.type'

export class WasteProducerService {
  static async getAll(
    options: OffsetPagination<WasteProducerIndexQueryParams>,
  ): Promise<OffsetPaginationResponse<WasteProducerIndex>> {
    const response = await viewWasteProducerIndexV1({ query: WasteProducerIndexQueryParamsTransformer.toDto(options) })

    return {
      data: response.data.items.map(WasteProducerIndexTransformer.fromDto),
      meta: {
        limit: options.pagination.limit,
        offset: options.pagination.offset,
        total: response.data.meta.total,
      },
    }
  }

  static async getSuggested(customerId: string): Promise<WasteProducerIndex[]> {
    const response = await viewSuggestedWasteProducersV1(
      {
        query: {
          filter: {
            customerId,
            requestType: RequestType.WASTE,
          },
        },
        requestValidator: undefined,
      },
    )

    return response.data.items.map(WasteProducerIndexTransformer.fromDto)
  }
}
