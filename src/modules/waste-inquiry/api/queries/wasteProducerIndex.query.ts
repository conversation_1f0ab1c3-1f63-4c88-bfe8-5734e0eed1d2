import { useOffsetInfiniteQuery } from '@/composables/offset-infinite-query/offsetInfiniteQuery.composable.ts'
import type { WasteProducerIndexQueryParams } from '@/models/waste-producer/index/wasteProducerIndexQueryParams.model'
import { WasteProducerService } from '@/modules/waste-inquiry/api/services/wasteProducer.service'
import type { InfiniteQueryOptions } from '@/types/query.type'

// eslint-disable-next-line ts/explicit-function-return-type
export function useWasteProducerIndexQuery(options: InfiniteQueryOptions<WasteProducerIndexQueryParams>) {
  return useOffsetInfiniteQuery({
    queryFn: (pagination) => {
      return WasteProducerService.getAll({
        filters: options.params.filters.value,
        pagination,
        search: options.params.search.value,
      })
    },
    queryKey: { wasteProducerIndex: { queryParams: options.params } },
  })
}
