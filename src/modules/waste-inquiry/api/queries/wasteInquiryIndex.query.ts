import { useOffsetInfiniteQuery } from '@/composables/offset-infinite-query/offsetInfiniteQuery.composable.ts'
import type { WasteInquiryIndexQueryParams } from '@/models/waste-inquiry/index/wasteInquiryIndexQueryParams.model'
import { WasteInquiryService } from '@/modules/waste-inquiry/api/services/wasteInquiry.service'
import type { InfiniteQueryOptions } from '@/types/query.type'

// eslint-disable-next-line ts/explicit-function-return-type
export function useWasteInquiryIndexQuery(options: InfiniteQueryOptions<WasteInquiryIndexQueryParams>) {
  return useOffsetInfiniteQuery({
    queryFn: (pagination) => {
      return WasteInquiryService.getAll({
        filters: options.params.filters.value,
        pagination,
        sort: options.params.sort.value,
      })
    },
    queryKey: { wasteInquiryIndexV2: { queryParams: options.params } },
  })
}
