import { useOffsetInfiniteQuery } from '@/composables/offset-infinite-query/offsetInfiniteQuery.composable.ts'
import type { CustomerIndexQueryParams } from '@/models/customer/index/customerIndexQueryParams.model'
import { CustomerService } from '@/modules/waste-inquiry/api/services/customer.service'
import type { InfiniteQueryOptions } from '@/types/query.type'

// eslint-disable-next-line ts/explicit-function-return-type
export function useCustomerIndexQuery(options: InfiniteQueryOptions<CustomerIndexQueryParams>) {
  return useOffsetInfiniteQuery({
    queryFn: (pagination) => {
      return CustomerService.getAll({
        pagination,
        search: options.params.search.value,
      })
    },
    queryKey: { customerIndex: { queryParams: options.params } },
  })
}
