import { useOffsetInfiniteQuery } from '@/composables/offset-infinite-query/offsetInfiniteQuery.composable.ts'
import type { PickUpAddressIndexQueryParams } from '@/models/pick-up-address/index/pickUpAddressIndexQueryParams.model'
import { PickUpAddressService } from '@/modules/waste-inquiry/api/services/pickUpAddress.service'
import type { InfiniteQueryOptions } from '@/types/query.type'

// eslint-disable-next-line ts/explicit-function-return-type
export function usePickUpAddressIndexQuery(options: InfiniteQueryOptions<PickUpAddressIndexQueryParams>) {
  return useOffsetInfiniteQuery({
    queryFn: (pagination) => {
      return PickUpAddressService.getAll({
        filters: options.params.filters.value,
        pagination,
        search: options.params.search.value,
      })
    },
    queryKey: { pickUpAddressIndex: { queryParams: options.params } },
  })
}
