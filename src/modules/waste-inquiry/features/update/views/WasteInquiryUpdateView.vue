<script setup lang="ts">
import { useVcDialog } from '@wisemen/vue-core-components'
import type { Component } from 'vue'
import {
  computed,
  watch,
} from 'vue'
import { useI18n } from 'vue-i18n'

import type { StateOfMatter } from '@/client/types.gen'
import {
  WasteInquiryStatus,
  WasteMeasurementUnit,
  WastePackagingType,
} from '@/client/types.gen'
import AppTeleport from '@/components/app/teleport/AppTeleport.vue'
import WizardForm from '@/components/form/wizard/WizardForm.vue'
import WizardFormContent from '@/components/form/wizard/WizardFormContent.vue'
import WizardFormSidebar from '@/components/form/wizard/WizardFormSidebar.vue'
import WizardFormState from '@/components/form/wizard/WizardFormState.vue'
import AppPage from '@/components/layout/page/AppPage.vue'
import AppPageReturnLink from '@/components/layout/page/AppPageReturnLink.vue'
import { useDocumentTitle } from '@/composables/document-title/documentTitle.composable'
import { useGlobalCustomer } from '@/composables/global-customer/globalCustomer.composable'
import {
  useWizardForm,
  WizardFormLastStepAction,
} from '@/composables/wizard-form/wizardForm.composable'
import { useWizardFormStepState } from '@/composables/wizard-form/wizardFormStepState.composable'
import { WasteInquiryFormSteps } from '@/models/enums/formSteps.enum'
import type { WasteInquiryDetail } from '@/models/waste-inquiry/detail/wasteInquiryDetail.model'
import type { WasteInquiryCollectionForm } from '@/models/waste-inquiry/update/steps/wasteInquiryCollectionForm.model'
import { wasteInquiryCollectionFormSchema } from '@/models/waste-inquiry/update/steps/wasteInquiryCollectionForm.model'
import type { WasteInquiryCompositionForm } from '@/models/waste-inquiry/update/steps/wasteInquiryCompositionForm.model'
import { wasteInquiryCompositionFormSchema } from '@/models/waste-inquiry/update/steps/wasteInquiryCompositionForm.model'
import type { WasteInquiryCustomerAndLocationForm } from '@/models/waste-inquiry/update/steps/wasteInquiryCustomerAndLocationForm.model'
import { wasteInquiryCustomerAndLocationFormSchema } from '@/models/waste-inquiry/update/steps/wasteInquiryCustomerAndLocationForm.model'
import type { WasteInquiryLegislationForm } from '@/models/waste-inquiry/update/steps/wasteInquiryLegislationForm.model'
import { wasteInquiryLegislationFormSchema } from '@/models/waste-inquiry/update/steps/wasteInquiryLegislationForm.model'
import type { WasteInquiryPropertiesForm } from '@/models/waste-inquiry/update/steps/wasteInquiryPropertiesForm.model'
import { wasteInquiryPropertiesFormSchema } from '@/models/waste-inquiry/update/steps/wasteInquiryPropertiesForm.model'
import type { WasteInquiryTransportForm } from '@/models/waste-inquiry/update/steps/wasteInquiryTransportForm.model'
import { wasteInquiryTransportFormSchema } from '@/models/waste-inquiry/update/steps/wasteInquiryTransportForm.model'
import type { WasteInquiryTypeForm } from '@/models/waste-inquiry/update/steps/wasteInquiryTypeForm.model'
import { wasteInquiryTypeFormSchema } from '@/models/waste-inquiry/update/steps/wasteInquiryTypeForm.model'
import { useWasteInquiryUpdateMutation } from '@/modules/waste-inquiry/api/mutations/wasteInquiryUpdate.mutation'
import WasteInquiryDetailConformityAssessmentInfo from '@/modules/waste-inquiry/features/detail/components/WasteInquiryDetailConformityAssessmentInfo.vue'
import WasteInquiryDetailHeader from '@/modules/waste-inquiry/features/detail/components/WasteInquiryDetailHeader.vue'

const props = defineProps<{
  isReadonly?: boolean
  wasteInquiry: WasteInquiryDetail
}>()

const i18n = useI18n()
const documentTitle = useDocumentTitle()
const globalCustomer = useGlobalCustomer()

const wasteInquiryUpdateMutation = useWasteInquiryUpdateMutation()

documentTitle.set(() => i18n.t('module.waste_inquiry.update.page_title'))

const customerAndLocationState = useWizardFormStepState<WasteInquiryCustomerAndLocationForm>({
  isPickupAddressUnknown: props.wasteInquiry.isUnknownPickUpAddress,
  isWasteProducerUnknown: props.wasteInquiry.isUnknownWasteProducer,
  customer: props.wasteInquiry.customer ?? globalCustomer.globalCustomer.value ?? null,
  pickUpAddress: props.wasteInquiry.pickupAddress,
  wasteProducer: props.wasteInquiry.wasteProducer,
})

const typeState = useWizardFormStepState<WasteInquiryTypeForm>({
  name: props.wasteInquiry.wasteStreamName,
  description: props.wasteInquiry.wasteStreamDescription,
  ewcLevel1: props.wasteInquiry.ewcLevel1,
  ewcLevel2: props.wasteInquiry.ewcLevel2,
  ewcLevel3: props.wasteInquiry.ewcLevel3,
  packagingType: props.wasteInquiry.packagingType ?? WastePackagingType.BULK,
  stateOfMatterType: props.wasteInquiry.stateOfMatterType,
})

const compositionState = useWizardFormStepState<WasteInquiryCompositionForm>({
  hasNoAnalysisReport: props.wasteInquiry.hasNoAnalysisReport ?? false,
  hasNoSds: props.wasteInquiry.hasNoSds ?? false,
  isSampleAvailable: props.wasteInquiry.isSampleAvailable ?? false,
  additionalFiles: props.wasteInquiry.additionalFiles ?? [],
  analysisReportFiles: props.wasteInquiry.analysisReportFiles ?? [],
  averageTemperature: props.wasteInquiry.averageTemperature,
  composition: props.wasteInquiry.composition ?? [],
  flashpointType: props.wasteInquiry.flashpointType,
  maximumTemperature: props.wasteInquiry.maximumTemperature,
  minimumTemperature: props.wasteInquiry.minimumTemperature,
  phType: props.wasteInquiry.phType,
  sdsFiles: props.wasteInquiry.sdsFiles ?? [],
  specificGravity: props.wasteInquiry.specificGravity,
  stableTemperatureType: props.wasteInquiry.stableTemperatureType,
})

const legislationState = useWizardFormStepState<WasteInquiryLegislationForm>({
  legislationRemarks: props.wasteInquiry.legislationRemarks ?? null,
  selectedLegislationTypes: props.wasteInquiry.selectedLegislationTypes ?? [],
  svhcExtraType: props.wasteInquiry.svhcExtraType ?? null,
})

const propertiesState = useWizardFormStepState<WasteInquiryPropertiesForm>({
  propertyRemarks: props.wasteInquiry.propertyRemarks ?? null,
  selectedPropertyTypes: props.wasteInquiry.selectedPropertyTypes ?? [],
})

const collectionState = useWizardFormStepState<WasteInquiryCollectionForm>({
  expectedEndDate: props.wasteInquiry.expectedEndDate,
  firstCollectionDate: props.wasteInquiry.firstCollectionDate,
  collectionRemarks: props.wasteInquiry.collectionRemarks,
  dischargeFrequency: props.wasteInquiry.dischargeFrequency,
  expectedPerCollectionQuantity: props.wasteInquiry.expectedPerCollectionQuantity,
  expectedPerCollectionUnit: props.wasteInquiry.expectedPerCollectionUnit ?? WasteMeasurementUnit.TO,
  expectedYearlyVolumeAmount: props.wasteInquiry.expectedYearlyVolumeAmount,
  expectedYearlyVolumeUnit: props.wasteInquiry.expectedYearlyVolumeUnit ?? WasteMeasurementUnit.TO,
})

const transportState = useWizardFormStepState<WasteInquiryTransportForm>({
  isLoadingByIndaver: props.wasteInquiry.isLoadingByIndaver,
  isTankOwnedByCustomer: props.wasteInquiry.isTankOwnedByCustomer,
  isTransportByIndaver: props.wasteInquiry.isTransportByIndaver,
  collectionRequirements: props.wasteInquiry.collectionRequirements,
  containerLoadingType: props.wasteInquiry.containerLoadingType,
  dependencies: {
    packagingType: typeState.value.packagingType,
    stateOfMatter: typeState.value.stateOfMatterType,
  },
  hazardInducer1: props.wasteInquiry.hazardInducer1,
  hazardInducer2: props.wasteInquiry.hazardInducer2,
  hazardInducer3: props.wasteInquiry.hazardInducer3,
  loadingMethod: props.wasteInquiry.loadingMethod,
  packaging: props.wasteInquiry.packaging,
  regulatedTransport: props.wasteInquiry.regulatedTransport,
  transportIn: props.wasteInquiry.transportIn,
  transportType: props.wasteInquiry.transportType,
  transportVolumeAmount: props.wasteInquiry.transportVolumeAmount,
  transportVolumeUnit: props.wasteInquiry.transportVolumeUnit ?? WasteMeasurementUnit.TO,
  unNumbers: props.wasteInquiry.unNumbers.map((unNumber) => ({
    isHazardous: unNumber.isHazardous,
    packingGroup: unNumber.packingGroup,
    unNumber: unNumber.unNumber,
  })),
  wasteLoadingType: props.wasteInquiry.wasteLoadingType,
  wasteStoredIn: props.wasteInquiry.wasteStoredIn,
})

const submitDialog = useVcDialog({ component: () => import('@/modules/waste-inquiry/features/update/components/submit/WasteInquirySubmitDialog.vue') })

const wizardForm = useWizardForm({
  categories: [
    {
      id: 'general',
      name: computed<string>(() => i18n.t('module.waste_inquiry.update.general_info')),
      icon: 'building',
      steps: [
        {
          id: WasteInquiryFormSteps.CUSTOMER_AND_LOCATION,
          isReadonly: props.isReadonly,
          name: computed<string>(() => i18n.t('module.waste_inquiry.update.customer_and_location.title')),
          scheme: wasteInquiryCustomerAndLocationFormSchema,
          state: customerAndLocationState,
          component: (): Promise<Component> => import('@/modules/waste-inquiry/features/update/components/steps/customer-and-location/WasteInquiryCustomerAndLocationForm.vue'),
        },
      ],
    },
    {
      id: 'waste-identification',
      name: computed<string>(() => i18n.t('module.waste_inquiry.update.waste_identification')),
      icon: 'microscope',
      steps: [
        {
          id: WasteInquiryFormSteps.TYPE,
          isReadonly: props.isReadonly,
          name: computed<string>(() => i18n.t('module.waste_inquiry.update.type.title')),
          scheme: wasteInquiryTypeFormSchema,
          state: typeState,
          component: (): Promise<Component> => import('@/modules/waste-inquiry/features/update/components/steps/type/WasteInquiryTypeForm.vue'),
        },
        {
          id: WasteInquiryFormSteps.COMPOSITION,
          isReadonly: props.isReadonly,
          name: computed<string>(() => i18n.t('module.waste_inquiry.update.composition.title')),
          scheme: wasteInquiryCompositionFormSchema,
          state: compositionState,
          component: (): Promise<Component> => import('@/modules/waste-inquiry/features/update/components/steps/composition/WasteInquiryCompositionForm.vue'),
        },
        {
          id: WasteInquiryFormSteps.LEGISLATION,
          isReadonly: props.isReadonly,
          name: computed<string>(() => i18n.t('module.waste_inquiry.update.legislation.title')),
          scheme: wasteInquiryLegislationFormSchema,
          state: legislationState,
          component: (): Promise<Component> => import('@/modules/waste-inquiry/features/update/components/steps/legislation-and-properties/WasteInquiryLegislationForm.vue'),
        },
        {
          id: WasteInquiryFormSteps.PROPERTIES,
          isReadonly: props.isReadonly,
          name: computed<string>(() => i18n.t('module.waste_inquiry.update.properties.title')),
          scheme: wasteInquiryPropertiesFormSchema,
          state: propertiesState,
          component: (): Promise<Component> => import('@/modules/waste-inquiry/features/update/components/steps/legislation-and-properties/WasteInquiryPropertiesForm.vue'),
        },
      ],
    },
    {
      id: 'collection-and-transport',
      name: computed<string>(() => i18n.t('module.waste_inquiry.update.collection_and_transport')),
      icon: 'package',
      steps: [
        {
          id: WasteInquiryFormSteps.COLLECTION,
          isReadonly: props.isReadonly,
          name: computed<string>(() => i18n.t('module.waste_inquiry.update.collection.title')),
          scheme: wasteInquiryCollectionFormSchema,
          state: collectionState,
          component: (): Promise<Component> => import('@/modules/waste-inquiry/features/update/components/steps/collection/WasteInquiryCollectionForm.vue'),
        },
        {
          id: WasteInquiryFormSteps.TRANSPORT,
          isReadonly: props.isReadonly,
          name: computed<string>(() => i18n.t('module.waste_inquiry.update.transport.title')),
          scheme: wasteInquiryTransportFormSchema,
          state: transportState,
          component: (): Promise<Component> => import('@/modules/waste-inquiry/features/update/components/steps/transport/WasteInquiryTransportForm.vue'),
        },
      ],
    },
  ],
  lastStepAction: props.isReadonly ? null : WizardFormLastStepAction.SUBMIT,
  onAutoSave: async (): Promise<void> => {
    if (props.wasteInquiry.uuid === null) {
      return
    }

    await wasteInquiryUpdateMutation.execute({
      body: {
        ...transportState.value,
        ...typeState.value,
        ...customerAndLocationState.value,
        ...compositionState.value,
        ...legislationState.value,
        ...propertiesState.value,
        ...collectionState.value,
      },
      params: { wasteInquiryUuid: props.wasteInquiry.uuid },
    })
  },
  onShowSubmitDialog: (): void => {
    if (props.wasteInquiry.uuid === null) {
      return
    }

    submitDialog.open({ wasteInquiryUuid: props.wasteInquiry.uuid })
  },
})

watch([
  (): StateOfMatter | null => typeState.value.stateOfMatterType,
  (): WastePackagingType | null => typeState.value.packagingType,
], ([
  stateOfMatterType,
  packagingType,
]) => {
  transportState.value.dependencies.packagingType = packagingType
  transportState.value.dependencies.stateOfMatter = stateOfMatterType
})

watch(() => typeState.value.packagingType, () => {
  transportState.value.transportType = null
  transportState.value.wasteLoadingType = null
  transportState.value.wasteStoredIn = null
  transportState.value.loadingMethod = null
  transportState.value.containerLoadingType = null
  transportState.value.isLoadingByIndaver = false
  transportState.value.isTankOwnedByCustomer = false
  transportState.value.isTransportByIndaver = false
  transportState.value.transportIn = null
  transportState.value.regulatedTransport = null
  transportState.value.unNumbers = []
  transportState.value.hazardInducer1 = null
  transportState.value.hazardInducer2 = null
  transportState.value.hazardInducer3 = null
  transportState.value.transportVolumeAmount = null
  transportState.value.transportVolumeUnit = WasteMeasurementUnit.TO
})

if (props.isReadonly) {
  wizardForm.goToStep(WasteInquiryFormSteps.TYPE)
}
</script>

<template>
  <AppTeleport to="headerLeft">
    <AppPageReturnLink
      :return-link="{
        label: i18n.t('module.waste_inquiry.update.return_to_overview'),
        to: {
          name: 'waste-inquiry-overview',
        },
      }"
    />
  </AppTeleport>

  <WizardForm :wizard-form="wizardForm">
    <AppPage :title="typeState.name || i18n.t('module.waste_inquiry.update.page_title')">
      <template
        v-if="props.isReadonly"
        #header
      >
        <WasteInquiryDetailHeader :waste-inquiry="props.wasteInquiry" />
      </template>
      <template #header-actions>
        <WizardFormState
          :created-at="props.wasteInquiry.createdAt"
          :auto-save-error-message="wizardForm.isAutoSaving.value ? null : wizardForm.autoSaveErrorMessage.value"
          :is-auto-saving="wizardForm.isAutoSaving.value"
        />
      </template>

      <template #left-content>
        <WizardFormSidebar>
          <template
            v-if="props.wasteInquiry.status === WasteInquiryStatus.CONFORMITY_CONFIRMED"
            #aside
          >
            <WasteInquiryDetailConformityAssessmentInfo
              :waste-inquiry="props.wasteInquiry"
            />
          </template>
        </WizardFormSidebar>
      </template>

      <template #default>
        <WizardFormContent />
      </template>
    </AppPage>
  </WizardForm>
</template>
