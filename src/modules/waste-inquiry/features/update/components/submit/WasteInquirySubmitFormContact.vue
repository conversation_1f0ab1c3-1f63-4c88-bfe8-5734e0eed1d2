<script setup lang="ts">
import {
  VcI<PERSON><PERSON>utton,
  VcTextField,
} from '@wisemen/vue-core-components'
import type { Field } from 'formango'
import { useI18n } from 'vue-i18n'

import AppGroup from '@/components/app/AppGroup.vue'
import { TEST_ID } from '@/constants/testId.constant.ts'
import type { WasteInquirySubmitFormContact } from '@/models/waste-inquiry/update/submit/wasteInquirySubmitForm.model'
import { toFormField } from '@/utils/formango.util'

const props = defineProps<{
  contact: Field<WasteInquirySubmitFormContact>
  contactCount: number
  contactIndex: number
}>()

const emit = defineEmits<{
  remove: []
}>()

const i18n = useI18n()

const email = props.contact.register('email')
const firstName = props.contact.register('firstName')
const lastName = props.contact.register('lastName')

function onRemove(): void {
  emit('remove')
}
</script>

<template>
  <AppGroup
    align="start"
    class="w-full"
  >
    <VcTextField
      v-bind="toFormField(email)"
      :test-id="TEST_ID.WASTE_INQUIRY.UPDATE.SUBMIT_DIALOG.CONTACT.EMAIL_TEXT_FIELD"
      :label="i18n.t('user.email')"
      :placeholder="i18n.t('user.email_placeholder')"
      class="w-full"
    />

    <VcTextField
      v-bind="toFormField(firstName)"
      :test-id="TEST_ID.WASTE_INQUIRY.UPDATE.SUBMIT_DIALOG.CONTACT.FIRST_NAME_TEXT_FIELD"
      :label="i18n.t('user.first_name')"
      :placeholder="i18n.t('user.first_name')"
      class="w-1/2"
    />

    <VcTextField
      v-bind="toFormField(lastName)"
      :test-id="TEST_ID.WASTE_INQUIRY.UPDATE.SUBMIT_DIALOG.CONTACT.LAST_NAME_TEXT_FIELD"
      :label="i18n.t('user.last_name')"
      :placeholder="i18n.t('user.last_name')"
      class="w-1/2"
    />

    <VcIconButton
      :label="i18n.t('shared.remove')"
      :is-disabled="props.contactCount === 1"
      icon="trash"
      variant="destructive-tertiary"
      size="sm"
      class="mt-[1.75rem] shrink-0"
      @click="onRemove"
    />
  </AppGroup>
</template>
