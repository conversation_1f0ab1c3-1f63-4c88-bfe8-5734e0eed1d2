<script setup lang="ts">
import {
  VcDate<PERSON>ield,
  VcRadioGroup,
  VcTextarea,
} from '@wisemen/vue-core-components'
import {
  computed,
  watch,
} from 'vue'
import { useI18n } from 'vue-i18n'

import { WasteDischargeFrequency } from '@/client/types.gen'
import AppCardWithIcon from '@/components/app/card/AppCardWithIcon.vue'
import FormGrid from '@/components/app/grid/FormGrid.vue'
import FormMeasurementField from '@/components/form/fields/FormMeasurementField.vue'
import FormRadioGroupItem from '@/components/form/FormRadioGroupItem.vue'
import FormRadioGroupLayout from '@/components/form/FormRadioGroupLayout.vue'
import WizardFormStep from '@/components/form/wizard/WizardFormStep.vue'
import type { WizardForm } from '@/composables/wizard-form/wizardForm.composable'
import { useWizardFormStep } from '@/composables/wizard-form/wizardFormStep.composable'
import { TEST_ID } from '@/constants/testId.constant.ts'
import { WasteDischargeFrequencyEnumUtil } from '@/models/enums/wasteDischargeFrequency.enum'
import type { wasteInquiryCollectionFormSchema } from '@/models/waste-inquiry/update/steps/wasteInquiryCollectionForm.model'
import { DateUtil } from '@/utils/date.util'
import { toFormField } from '@/utils/formango.util'

const props = defineProps<{
  isReadonly?: boolean
  wizardForm: WizardForm<typeof wasteInquiryCollectionFormSchema>
}>()

const i18n = useI18n()
const wizardFormStep = useWizardFormStep(props.wizardForm)

const { form } = wizardFormStep

const expectedYearlyVolumeAmount = form.register('expectedYearlyVolumeAmount')
const expectedYearlyVolumeUnit = form.register('expectedYearlyVolumeUnit')
const expectedPerCollectionQuantity = form.register('expectedPerCollectionQuantity')
const expectedPerCollectionUnit = form.register('expectedPerCollectionUnit')
const dischargeFrequency = form.register('dischargeFrequency')
const firstCollectionDate = form.register('firstCollectionDate')
const expectedEndDate = form.register('expectedEndDate')
const collectionRemarks = form.register('collectionRemarks')

const wasteDischargeFrequencyOptions = computed<WasteDischargeFrequency[]>(() => [
  WasteDischargeFrequency.ONCE_OFF_STREAM,
  WasteDischargeFrequency.REGULAR_STREAM,
  WasteDischargeFrequency.ONCE_OFF_CAMPAIGN,
  WasteDischargeFrequency.REGULAR_CAMPAIGN,
])

const isExpectedEndDateDisabled = computed<boolean>(
  () => firstCollectionDate.value.value === null,
)

watch((firstCollectionDate.value), (firstCollectionDate) => {
  if (firstCollectionDate === null) {
    expectedEndDate.setValue(null)
  }
})
</script>

<template>
  <WizardFormStep :wizard-form-step="wizardFormStep">
    <FormGrid :cols="2">
      <FormMeasurementField
        :is-readonly="props.isReadonly"
        v-bind="expectedYearlyVolumeAmount"
        :is-required="true"
        :min="0.01"
        :max="99999999.99"
        :number-field-test-id="TEST_ID.WASTE_INQUIRY.UPDATE.EXPECTED_YEARLY_VOLUME_AMOUNT_NUMBER_FIELD"
        :unit="expectedYearlyVolumeUnit"
        :label="i18n.t('module.waste_inquiry.update.collection.expected_yearly_volume_amount.label')"
      />

      <FormMeasurementField
        :is-readonly="props.isReadonly"
        v-bind="expectedPerCollectionQuantity"
        :is-required="false"
        :min="0.01"
        :max="99999999.99"
        :number-field-test-id="TEST_ID.WASTE_INQUIRY.UPDATE.EXPECTED_PER_COLLECTION_QUANTITY_NUMBER_FIELD"
        :unit="expectedPerCollectionUnit"
        :label="i18n.t('module.waste_inquiry.update.collection.expected_per_collection_quantity.label')"
      />

      <VcRadioGroup
        :is-disabled="props.isReadonly"
        v-bind="toFormField(dischargeFrequency)"
        :is-required="true"
        :label="i18n.t('module.waste_inquiry.update.collection.frequency_of_discharge.label')"
        class="col-span-full"
      >
        <FormRadioGroupLayout :cols="4">
          <FormRadioGroupItem
            v-for="value in wasteDischargeFrequencyOptions"
            :key="value"
            :test-id="TEST_ID.WASTE_INQUIRY.UPDATE.DISCHARGE_FREQUENCY_RADIO_GROUP_ITEM"
            :value="value"
          >
            <AppCardWithIcon
              :is-disabled="props.isReadonly"
              :is-selected="dischargeFrequency.value.value === value"
              :title="i18n.t(WasteDischargeFrequencyEnumUtil.getLabelI18nKey(value))"
              :description="i18n.t(WasteDischargeFrequencyEnumUtil.getDescriptionI18nKey(value))"
              :icon="WasteDischargeFrequencyEnumUtil.getIcon(value)"
              variant="gray-light"
            />
          </FormRadioGroupItem>
        </FormRadioGroupLayout>
      </VcRadioGroup>

      <VcDateField
        :is-disabled="props.isReadonly"
        v-bind="toFormField(firstCollectionDate)"
        :is-date-disabled="(date) => DateUtil.isInPast(date)"
        :label="i18n.t('module.waste_inquiry.update.collection.first_collection_date.label')"
      />

      <VcDateField
        v-bind="toFormField(expectedEndDate)"
        :is-disabled="isExpectedEndDateDisabled || props.isReadonly"
        :is-date-disabled="(date) => DateUtil.isBefore(date, firstCollectionDate.value.value ?? new Date())"
        :label="i18n.t('module.waste_inquiry.update.collection.expected_end_date.label')"
      />

      <VcTextarea
        :is-disabled="props.isReadonly"
        v-bind="toFormField(collectionRemarks)"
        :test-id="TEST_ID.WASTE_INQUIRY.UPDATE.COLLECTION_REMARKS_TEXT_AREA"
        :label="i18n.t('module.waste_inquiry.update.collection.remarks.label')"
        :placeholder="i18n.t('module.waste_inquiry.update.collection.remarks.placeholder')"
        :class-config="{
          input: 'min-h-28',
        }"
        class="col-span-full"
      />
    </FormGrid>
  </WizardFormStep>
</template>
