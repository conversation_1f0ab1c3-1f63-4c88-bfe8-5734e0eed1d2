<script setup lang="ts">
import {
  V<PERSON><PERSON><PERSON><PERSON>,
  VcFormField,
  VcTextField,
} from '@wisemen/vue-core-components'
import {
  AnimatePresence,
  Motion,
} from 'motion-v'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import {
  CollectionRequirementOption,
  ContainerLoadingType,
  RegulatedTransportOption,
  WasteLoadingMethod,
  WasteLoadingType,
  WasteStoredInOption,
  WasteTransportInOption,
  WasteTransportType,
} from '@/client/types.gen'
import AppGrid from '@/components/app/AppGrid.vue'
import AppGroup from '@/components/app/AppGroup.vue'
import AppHeightTransition from '@/components/app/AppHeightTransition.vue'
import AppCard from '@/components/app/card/AppCard.vue'
import FormGrid from '@/components/app/grid/FormGrid.vue'
import FormBooleanRadioGroup from '@/components/form/fields/FormBooleanRadioGroup.vue'
import FormMeasurement<PERSON>ield from '@/components/form/fields/FormMeasurementField.vue'
import type { FormRadioGroupWithCardsOption } from '@/components/form/FormRadioGroupWithCards.vue'
import FormRadioGroupWithCards from '@/components/form/FormRadioGroupWithCards.vue'
import WizardFormStep from '@/components/form/wizard/WizardFormStep.vue'
import type { WizardForm } from '@/composables/wizard-form/wizardForm.composable'
import { useWizardFormStep } from '@/composables/wizard-form/wizardFormStep.composable'
import { TEST_ID } from '@/constants/testId.constant.ts'
import { CollectionRequirementOptionEnumUtil } from '@/models/enums/collectionRequirementOption.enum'
import { ContainerLoadingTypeEnumUtil } from '@/models/enums/containerLoadingType.enum'
import { RegulatedTransportEnumUtil } from '@/models/enums/regulatedTransport.enum'
import { WasteLoadingMethodEnumUtil } from '@/models/enums/wasteLoadingMethod.enum'
import { WasteLoadingTypeEnumUtil } from '@/models/enums/wasteLoadingType.enum'
import { WasteStoredInEnumUtil } from '@/models/enums/wasteStoredIn.enum'
import { WasteTransportInEnumUtil } from '@/models/enums/wasteTransportIn.enum'
import { WasteTransportTypeEnumUtil } from '@/models/enums/wasteTransportType.enum'
import type { wasteInquiryTransportFormSchema } from '@/models/waste-inquiry/update/steps/wasteInquiryTransportForm.model'
import { WasteInquiryTransportValidationUtil } from '@/models/waste-inquiry/wasteInquiryTransportValidation.util'
import WasteInquiryTransportPackagingItem from '@/modules/waste-inquiry/features/update/components/steps/transport/WasteInquiryTransportPackagingItem.vue'
import WasteInquiryTransportUnNumber from '@/modules/waste-inquiry/features/update/components/steps/transport/WasteInquiryTransportUnNumber.vue'
import { toFormField } from '@/utils/formango.util'

const props = defineProps<{
  isReadonly?: boolean
  wizardForm: WizardForm<typeof wasteInquiryTransportFormSchema>
}>()

const i18n = useI18n()
const wizardFormStep = useWizardFormStep(props.wizardForm)

const { form } = wizardFormStep

const wasteInquiryTransportValidation = computed<WasteInquiryTransportValidationUtil>(
  () => {
    return new WasteInquiryTransportValidationUtil(props.wizardForm.state.value)
  },
)

const isTransportByIndaver = form.register('isTransportByIndaver', false)
const regulatedTransport = form.register('regulatedTransport')
const transportType = form.register('transportType')
const containerLoadingType = form.register('containerLoadingType')
const wasteLoadingType = form.register('wasteLoadingType')
const transportIn = form.register('transportIn')
const loadingMethod = form.register('loadingMethod')
const storedIn = form.register('wasteStoredIn')
const transportVolumeAmount = form.register('transportVolumeAmount')
const transportVolumeUnit = form.register('transportVolumeUnit')
const isTankOwnedByCustomer = form.register('isTankOwnedByCustomer', false)
const collectionRequirements = form.register('collectionRequirements')
const isLoadingByIndaver = form.register('isLoadingByIndaver', false)
const unNumbers = form.registerArray('unNumbers', [
  {
    isHazardous: false,
    packingGroup: null,
    unNumber: null,
  },
])

const packaging = form.registerArray('packaging', [
  {
    hasInnerPackaging: null,
    remarks: null,
    size: null,
    type: null,
    weightPerPieceValue: null,
  },
])
const hazardInducer1 = form.register('hazardInducer1')
const hazardInducer2 = form.register('hazardInducer2')
const hazardInducer3 = form.register('hazardInducer3')

const regulatedTransportOptions = computed<FormRadioGroupWithCardsOption[]>(() => [
  RegulatedTransportOption.YES,
  RegulatedTransportOption.NO,
  RegulatedTransportOption.UNKNOWN,
].map((value) => ({
  title: i18n.t(RegulatedTransportEnumUtil.getI18nKey(value)),
  icon: RegulatedTransportEnumUtil.getIcon(value),
  value,
})))

const wasteTransportTypeOptions = computed<FormRadioGroupWithCardsOption[]>(() => [
  WasteTransportType.CONTAINER,
  WasteTransportType.SKIP,
  WasteTransportType.TIPPER_TRUCK,
  WasteTransportType.REL_TRUCK,
  WasteTransportType.OTHER,
].map((value) => ({
  title: i18n.t(WasteTransportTypeEnumUtil.getLabelI18nKey(value)),
  icon: WasteTransportTypeEnumUtil.getIcon(value),
  value,
})))

const containerLoadingTypeOptions = computed<FormRadioGroupWithCardsOption[]>(() => [
  ContainerLoadingType.CHAIN,
  ContainerLoadingType.HOOK,
].map((value) => ({
  title: i18n.t(ContainerLoadingTypeEnumUtil.getLabelI18nKey(value)),
  icon: ContainerLoadingTypeEnumUtil.getIcon(value),
  value,
})))

const wasteLoadingTypeOptions = computed<FormRadioGroupWithCardsOption[]>(() => [
  WasteLoadingType.ON_WASTE_COLLECTION,
  WasteLoadingType.BEFORE_WASTE_COLLECTION,
].map((value) => ({
  title: i18n.t(WasteLoadingTypeEnumUtil.getLabelI18nKey(value)),
  icon: null,
  value,
})))

const wasteTransportInOptions = computed<FormRadioGroupWithCardsOption[]>(() => [
  WasteTransportInOption.TANK_TRAILER,
  WasteTransportInOption.TANK_CONTAINER,
  WasteTransportInOption.OTHER,
  WasteTransportInOption.NO_PREFERENCE,
].map((value) => ({
  title: i18n.t(WasteTransportInEnumUtil.getLabelI18nKey(value)),
  icon: WasteTransportInEnumUtil.getIcon(value),
  value,
})))

const loadingMethodOptions = computed<FormRadioGroupWithCardsOption[]>(() => [
  WasteLoadingMethod.GRAVITATIONAL,
  WasteLoadingMethod.PUMP_FROM_CUSTOMER,
  WasteLoadingMethod.PUMP_FROM_HAULIER,
].map((value) => ({
  title: i18n.t(WasteLoadingMethodEnumUtil.getLabelI18nKey(value)),
  icon: null,
  value,
})))

const storedInOptions = computed<FormRadioGroupWithCardsOption[]>(() => [
  WasteStoredInOption.STORAGE_TANK,
  WasteStoredInOption.TANK_CONTAINER,
  WasteStoredInOption.IBCS,
  WasteStoredInOption.DRUMS,
  WasteStoredInOption.OTHER,
].map((value) => ({
  title: i18n.t(WasteStoredInEnumUtil.getLabelI18nKey(value)),
  icon: WasteStoredInEnumUtil.getIcon(value),
  value,
})))

const collectionRequirementOptions = computed<FormRadioGroupWithCardsOption[]>(() => [
  CollectionRequirementOption.TRACTOR,
  CollectionRequirementOption.TRACTOR_TRAILER,
  CollectionRequirementOption.TRACTOR_TRAILER_TANK,
].map((value) => ({
  title: i18n.t(CollectionRequirementOptionEnumUtil.getI18nKey(value)),
  icon: null,
  value,
})))

function onAddUnNumber(): void {
  unNumbers.append({
    isHazardous: false,
    packingGroup: null,
    unNumber: null,
  })
}

function onRemoveUnNumber(index: number): void {
  if (unNumbers.fields.value.length === 1) {
    unNumbers.setValue([
      {
        isHazardous: false,
        packingGroup: null,
        unNumber: null,
      },
    ])

    return
  }

  unNumbers.remove(index)
}

function onAddPackaging(): void {
  packaging.append()
}

function onRemovePackaging(index: number): void {
  packaging.remove(index)
}

function onClearRegulatedTransport(): void {
  regulatedTransport.setValue(null)
}

function onClearTransportType(): void {
  transportType.setValue(null)
}

function onClearContainerLoadingType(): void {
  containerLoadingType.setValue(null)
}

function onClearLoadingType(): void {
  wasteLoadingType.setValue(null)
}

function onClearTransportIn(): void {
  transportIn.setValue(null)
}

function onClearStoredIn(): void {
  storedIn.setValue(null)
}

function onClearCollectionRequirement(): void {
  collectionRequirements.setValue(null)
}
</script>

<template>
  <WizardFormStep :wizard-form-step="wizardFormStep">
    <FormGrid :cols="1">
      <FormBooleanRadioGroup
        v-bind="isTransportByIndaver"
        :is-readonly="props.isReadonly"
        :is-required="true"
        :test-id="TEST_ID.WASTE_INQUIRY.UPDATE.IS_TRANSPORT_ARRANGED_BY_INDAVER_RADIO_GROUP_ITEM"
        :truthy-label="i18n.t('module.waste_inquiry.update.transport.is_transport_arranged_by_indaver.truthy.label')"
        :falsy-label="i18n.t('module.waste_inquiry.update.transport.is_transport_arranged_by_indaver.falsy.label')"
        :label="i18n.t('module.waste_inquiry.update.transport.is_transport_arranged_by_indaver.label')"
      />

      <FormRadioGroupWithCards
        :field="regulatedTransport"
        :options="regulatedTransportOptions"
        :label="i18n.t('module.waste_inquiry.update.transport.regulated_transport.label')"
        :is-disabled="props.isReadonly"
        :is-required="wasteInquiryTransportValidation.isRegulatedTransportRequired"
        :is-readonly="props.isReadonly"
        :test-id="TEST_ID.WASTE_INQUIRY.UPDATE.REGULATED_TRANSPORT_RADIO_GROUP_ITEM"
        @clear="onClearRegulatedTransport"
      />

      <div v-if="wasteInquiryTransportValidation.isUnNumbersAllowed">
        <VcFormField
          :is-required="wasteInquiryTransportValidation.isUnNumberRequired"
          :label="i18n.t('module.waste_inquiry.update.transport.un_number_and_packing_group.label')"
          for=""
        >
          <AppCard
            :is-disabled="true"
            variant="transparent"
          >
            <AppHeightTransition>
              <AppGroup
                direction="col"
                gap="xl"
                align="start"
              >
                <AnimatePresence
                  :initial="false"
                  mode="popLayout"
                >
                  <Motion
                    v-for="(field, fieldIndex) of unNumbers.fields.value"
                    :key="field"
                    :layout="true"
                    :initial="{
                      opacity: 0,
                      scale: 0.95,
                      filter: 'blur(4px)',
                    }"
                    :animate="{
                      opacity: 1,
                      scale: 1,
                      filter: 'blur(0px)',
                    }"
                    :exit="{
                      opacity: 0,
                      scale: 0.95,
                      filter: 'blur(4px)',
                    }"
                    :transition="{
                      bounce: 0,
                      duration: 0.4,
                      type: 'spring',
                    }"
                    class="w-full p-px"
                    tabindex="-1"
                  >
                    <WasteInquiryTransportUnNumber
                      :is-disabled="props.isReadonly"
                      :un-number-count="unNumbers.fields.value.length"
                      :un-number="unNumbers.register(`${fieldIndex}`)"
                      :wizard-form="props.wizardForm"
                      :is-optional="regulatedTransport.value.value === RegulatedTransportOption.UNKNOWN"
                      @remove="onRemoveUnNumber(fieldIndex)"
                    />
                  </Motion>
                </AnimatePresence>
              </AppGroup>
            </AppHeightTransition>
          </AppCard>
        </VcFormField>

        <VcButton
          v-if="!props.isReadonly"
          icon-left="plus"
          variant="tertiary-color"
          size="sm"
          class="mt-xs"
          @click="onAddUnNumber"
        >
          {{ i18n.t('module.waste_inquiry.update.packaging.add_un_number') }}
        </VcButton>
      </div>

      <FormGrid
        v-if="unNumbers.modelValue.value.some(field => field.isHazardous === true)"
        :cols="3"
      >
        <VcTextField
          v-bind="toFormField(hazardInducer1)"
          :is-disabled="props.isReadonly"
          :label="i18n.t('module.waste_inquiry.update.transport.hazard_inducer_1.label')"
          :is-required="true"
          :placeholder="i18n.t('module.waste_inquiry.update.transport.hazard_inducer_1.label')"
        />
        <VcTextField
          v-bind="toFormField(hazardInducer2)"
          :is-disabled="props.isReadonly"
          :label="i18n.t('module.waste_inquiry.update.transport.hazard_inducer_2.label')"
          :placeholder="i18n.t('module.waste_inquiry.update.transport.hazard_inducer_2.label')"
        />
        <VcTextField
          v-bind="toFormField(hazardInducer3)"
          :is-disabled="props.isReadonly"
          :label="i18n.t('module.waste_inquiry.update.transport.hazard_inducer_3.label')"
          :placeholder="i18n.t('module.waste_inquiry.update.transport.hazard_inducer_3.label')"
        />
      </FormGrid>

      <FormRadioGroupWithCards
        v-if="wasteInquiryTransportValidation.isTransportTypeAllowed"
        :field="transportType"
        :options="wasteTransportTypeOptions"
        :label="i18n.t('module.waste_inquiry.update.transport.transport_type.label')"
        :is-disabled="props.isReadonly"
        :is-required="wasteInquiryTransportValidation.isTransportTypeRequired"
        :is-readonly="props.isReadonly"
        :test-id="TEST_ID.WASTE_INQUIRY.UPDATE.TRANSPORT_TYPE_RADIO_GROUP_ITEM"
        @clear="onClearTransportType"
      />

      <FormBooleanRadioGroup
        v-if="wasteInquiryTransportValidation.isLoadingByIndaverAllowed"
        :is-disabled="props.isReadonly"
        :is-required="wasteInquiryTransportValidation.isLoadingByIndaverRequired"
        v-bind="isLoadingByIndaver"
        :label="i18n.t('module.waste_inquiry.update.transport.loading_by_indaver.label')"
        :truthy-label="i18n.t('shared.yes')"
        :falsy-label="i18n.t('shared.no')"
      />

      <FormRadioGroupWithCards
        v-if="wasteInquiryTransportValidation.isContainerLoadingTypeAllowed"
        :field="containerLoadingType"
        :options="containerLoadingTypeOptions"
        :label="i18n.t('module.waste_inquiry.update.transport.container_loading_type.label')"
        :is-disabled="props.isReadonly"
        :is-required="wasteInquiryTransportValidation.isContainerLoadingTypeRequired"
        :is-readonly="props.isReadonly"
        :test-id="TEST_ID.WASTE_INQUIRY.UPDATE.CONTAINER_LOADING_TYPE_RADIO_GROUP_ITEM"
        @clear="onClearContainerLoadingType"
      />

      <FormRadioGroupWithCards
        v-if="wasteInquiryTransportValidation.isLoadingTypeAllowed"
        :field="wasteLoadingType"
        :options="wasteLoadingTypeOptions"
        :label="i18n.t('module.waste_inquiry.update.transport.loading_type.label')"
        :is-disabled="props.isReadonly"
        :is-required="wasteInquiryTransportValidation.isLoadingTypeRequired"
        :is-readonly="props.isReadonly"
        :test-id="TEST_ID.WASTE_INQUIRY.UPDATE.WASTE_LOADING_TYPE_RADIO_GROUP_ITEM"
        @clear="onClearLoadingType"
      />

      <FormRadioGroupWithCards
        v-if="wasteInquiryTransportValidation.isTransportInAllowed"
        :field="transportIn"
        :options="wasteTransportInOptions"
        :label="i18n.t('module.waste_inquiry.update.transport.transport_in.label')"
        :is-disabled="props.isReadonly"
        :is-required="wasteInquiryTransportValidation.isTransportInRequired"
        :is-readonly="props.isReadonly"
        :test-id="TEST_ID.WASTE_INQUIRY.UPDATE.TRANSPORT_IN_RADIO_GROUP_ITEM"
        @clear="onClearTransportIn"
      />

      <FormBooleanRadioGroup
        v-if="wasteInquiryTransportValidation.isTankOwnedByCustomerAllowed"
        v-bind="isTankOwnedByCustomer"
        :test-id="TEST_ID.WASTE_INQUIRY.UPDATE.IS_TANK_OWNED_BY_CUSTOMER_RADIO_GROUP_ITEM"
        :is-disabled="props.isReadonly"
        :is-required="wasteInquiryTransportValidation.isTankOwnedByCustomerRequired"
        :truthy-label="i18n.t('shared.yes')"
        :falsy-label="i18n.t('shared.no')"
        :label="i18n.t('module.waste_inquiry.update.transport.is_tank_owned_by_customer.label')"
      />

      <FormRadioGroupWithCards
        v-if="wasteInquiryTransportValidation.isCollectionRequirementsAllowed"
        :field="collectionRequirements"
        :options="collectionRequirementOptions"
        :label="i18n.t('module.waste_inquiry.update.transport.collection_requirements.label')"
        :is-disabled="props.isReadonly"
        :is-required="wasteInquiryTransportValidation.isCollectionRequirementsRequired"
        :is-readonly="props.isReadonly"
        :test-id="TEST_ID.WASTE_INQUIRY.UPDATE.COLLECTION_REQUIREMENTS_RADIO_GROUP_ITEM"
        @clear="onClearCollectionRequirement"
      />

      <FormRadioGroupWithCards
        v-if="wasteInquiryTransportValidation.isLoadingMethodAllowed"
        :field="loadingMethod"
        :options="loadingMethodOptions"
        :label="i18n.t('components.waste_inquiry.composition.components.loading_method.label')"
        :is-disabled="props.isReadonly"
        :is-required="wasteInquiryTransportValidation.isLoadingMethodRequired"
        :is-readonly="props.isReadonly"
        :test-id="TEST_ID.WASTE_INQUIRY.UPDATE.LOADING_METHOD_RADIO_GROUP_ITEM"
      />

      <FormRadioGroupWithCards
        v-if="wasteInquiryTransportValidation.isStoredInAllowed"
        :field="storedIn"
        :options="storedInOptions"
        :label="i18n.t('components.waste_inquiry.composition.components.stored_in.label')"
        :is-disabled="props.isReadonly"
        :is-required="wasteInquiryTransportValidation.isStoredInRequired"
        :is-readonly="props.isReadonly"
        :test-id="TEST_ID.WASTE_INQUIRY.UPDATE.STORED_IN_RADIO_GROUP_ITEM"
        @clear="onClearStoredIn"
      />

      <AppGrid
        v-if="wasteInquiryTransportValidation.isTransportVolumeAllowed"
        :cols="2"
      >
        <FormMeasurementField
          v-bind="transportVolumeAmount"
          :is-readonly="props.isReadonly"
          :min="0.01"
          :max="99999999999.99"
          :is-required="wasteInquiryTransportValidation.isTransportVolumeRequired"
          :unit="transportVolumeUnit"
          :label="i18n.t('components.waste_inquiry.composition.components.transport_volume.label')"
        />
      </AppGrid>

      <div v-if="wasteInquiryTransportValidation.isPackagingAllowed">
        <VcFormField
          :is-required="wasteInquiryTransportValidation.isPackagingRequired"
          :label="i18n.t('components.waste_inquiry.composition.components.packaging.label')"
          for=""
        >
          <AppCard
            :is-disabled="true"
            variant="transparent"
          >
            <AppHeightTransition>
              <AppGroup
                direction="col"
                gap="xl"
                align="start"
              >
                <AnimatePresence
                  :initial="false"
                  mode="popLayout"
                >
                  <Motion
                    v-for="(field, fieldIndex) of packaging.fields.value"
                    :key="field"
                    :layout="true"
                    :initial="{
                      opacity: 0,
                      scale: 0.95,
                      filter: 'blur(4px)',
                    }"
                    :animate="{
                      opacity: 1,
                      scale: 1,
                      filter: 'blur(0px)',
                    }"
                    :exit="{
                      opacity: 0,
                      scale: 0.95,
                      filter: 'blur(4px)',
                    }"
                    :transition="{
                      bounce: 0,
                      duration: 0.4,
                      type: 'spring',
                    }"
                    class="w-full p-px"
                    tabindex="-1"
                  >
                    <WasteInquiryTransportPackagingItem
                      :is-disabled="props.isReadonly"
                      :packaging-count="packaging.fields.value.length"
                      :packaging="packaging.register(`${fieldIndex}`)"
                      @remove="onRemovePackaging(fieldIndex)"
                    />
                  </Motion>
                </AnimatePresence>
              </AppGroup>
            </AppHeightTransition>
          </AppCard>
        </VcFormField>

        <VcButton
          v-if="!props.isReadonly"
          icon-left="plus"
          variant="tertiary-color"
          size="sm"
          class="mt-xs"
          @click="onAddPackaging"
        >
          {{ i18n.t('components.waste_inquiry.composition.components.add_packaging') }}
        </VcButton>
      </div>
    </FormGrid>
  </WizardFormStep>
</template>
