<script setup lang="ts">
import {
  usePagination,
  VcAutocomplete,
  VcIconButton,
  VcRadioGroup,
} from '@wisemen/vue-core-components'
import type { Field } from 'formango'
import {
  computed,
  ref,
} from 'vue'
import { useI18n } from 'vue-i18n'

import { PackingGroup } from '@/client/types.gen'
import AppGroup from '@/components/app/AppGroup.vue'
import AppCardWithText from '@/components/app/card/AppCardWithText.vue'
import FormRadioGroupItem from '@/components/form/FormRadioGroupItem.vue'
import type { WizardForm } from '@/composables/wizard-form/wizardForm.composable'
import { TEST_ID } from '@/constants/testId.constant.ts'
import { WastePackagingSizeEnumUtil } from '@/models/enums/wastePackagingSize.enum'
import type { UnNumberIndex } from '@/models/un-number/index/unNumberIndex.model'
import type { UnNumberIndexPagination } from '@/models/un-number/index/unNumberIndexPagination.model'
import type {
  UnNumber,
  wasteInquiryTransportFormSchema,
} from '@/models/waste-inquiry/update/steps/wasteInquiryTransportForm.model'
import { WasteInquiryTransportValidationUtil } from '@/models/waste-inquiry/wasteInquiryTransportValidation.util'
import { useUnNumberIndexQuery } from '@/modules/un-number/api/queries/unNumberIndex.query'
import { toFormField } from '@/utils/formango.util'

const props = defineProps<{
  isDisabled?: boolean
  isOptional: boolean
  unNumber: Field<UnNumber>
  unNumberCount: number
  wizardForm: WizardForm<typeof wasteInquiryTransportFormSchema>
}>()

const emit = defineEmits<{
  remove: []
}>()

const i18n = useI18n()

const unNumberObject = ref<UnNumberIndex>({
  isHazardous: props.unNumber.value.value?.isHazardous ?? false,
  dangerLabel1: null,
  dangerLabel2: null,
  dangerLabel3: null,
  description: null,
  number: props.unNumber.value.value?.unNumber ?? '',
  packingGroup: props.unNumber.value.value?.packingGroup ?? null,
})
const unNumber = props.unNumber.register('unNumber')
const packingGroup = props.unNumber.register('packingGroup')
const isHazardous = props.unNumber.register('isHazardous')

const packagingGroupOptions = computed<PackingGroup[]>(() => [
  PackingGroup.ONE,
  PackingGroup.TWO,
  PackingGroup.THREE,
  PackingGroup.NOT_APPLICABLE,
])

const wasteInquiryTransportValidation = computed<WasteInquiryTransportValidationUtil>(
  () => new WasteInquiryTransportValidationUtil({
    ...props.wizardForm.state.value,
    ...props.wizardForm.state.value.dependencies,
  }),
)

const pagination = usePagination<UnNumberIndexPagination>({ isRouteQueryEnabled: false })

const unNumberIndexQuery = useUnNumberIndexQuery(pagination.paginationOptions)

const unNumbers = computed<UnNumberIndex[]>(() => unNumberIndexQuery.data.value?.data ?? [])

function onRemove(): void {
  emit('remove')
}

function onSearch(search: string): void {
  pagination.handleSearchChange(search)
}

function onUpdateUnNumber(item: UnNumberIndex): void {
  const unNumberDetail = unNumbers.value.find((num) =>
    num.number === item.number && num.packingGroup === item.packingGroup)

  unNumber.setValue(unNumberDetail?.number ?? '')
  isHazardous.setValue(unNumberDetail?.isHazardous ?? null)
  packingGroup.setValue(unNumberDetail?.packingGroup ? unNumberDetail.packingGroup as PackingGroup : null)
}

function displayUnNumber(item: UnNumberIndex): string {
  const unNumberDetail = unNumbers.value.find((num) =>
    num.number === item.number && num.packingGroup === item.packingGroup)

  if (!unNumberDetail) {
    return item.number
  }
  const packingGroup = unNumberDetail.packingGroup
    ? `(${i18n.t(WastePackagingSizeEnumUtil.getI18nKey(unNumberDetail.packingGroup as PackingGroup))})`
    : null

  return `${unNumberDetail.number} - ${unNumberDetail.description} ${packingGroup || ''}`
}
</script>

<template>
  <AppGroup
    align="start"
    class="w-full"
  >
    <VcAutocomplete
      v-model="unNumberObject"
      :items="unNumbers"
      :is-loading="unNumberIndexQuery.isFetching.value"
      :display-fn="(value) => displayUnNumber(value)"
      :label="i18n.t('components.waste_inquiry.composition.components.un_number.label')"
      :is-disabled="props.isDisabled"
      :is-required="wasteInquiryTransportValidation.isUnNumberRequired"
      :data-test-id="TEST_ID.WASTE_INQUIRY.UPDATE.TRANSPORT_UN_NUMBER.UN_NUMBER_SELECT"
      :placeholder="i18n.t('module.pickup_request.update.packaging.placeholder.un_number')"
      :is-search-term-optional="true"
      popover-align="start"
      class="w-full"
      @search="onSearch"
      @update:model-value="onUpdateUnNumber"
    />
    <VcRadioGroup
      :is-disabled="props.isDisabled"
      v-bind="toFormField(packingGroup)"
      :label="i18n.t('components.waste_inquiry.composition.components.packaging_group.label')"
      class="shrink-0"
    >
      <AppGroup>
        <FormRadioGroupItem
          v-for="option of packagingGroupOptions"
          :key="option"
          :test-id="TEST_ID.WASTE_INQUIRY.UPDATE.TRANSPORT_UN_NUMBER.PACKAGING_GROUP_OPTION"
          :value="option"
        >
          <AppCardWithText
            :is-disabled="props.isDisabled"
            :has-clear-button="false"
            :is-selected="packingGroup.value.value === option"
            :title="i18n.t(WastePackagingSizeEnumUtil.getI18nKey(option))"
            class="
              flex h-10 min-w-14 items-center justify-center !rounded-md !py-0
            "
          />
        </FormRadioGroupItem>
      </AppGroup>
    </VcRadioGroup>

    <VcIconButton
      v-if="!props.isDisabled"
      :label="i18n.t('shared.remove')"
      :is-disabled="props.isOptional ? false : props.unNumberCount === 1"
      icon="trash"
      variant="destructive-tertiary"
      size="sm"
      class="mt-[1.75rem] shrink-0"
      @click="onRemove"
    />
  </AppGroup>
</template>
