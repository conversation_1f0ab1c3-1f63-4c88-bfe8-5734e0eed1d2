<script setup lang="ts">
import {
  VcRadioGroup,
  VcSelect,
  VcSelectItem,
} from '@wisemen/vue-core-components'
import type { Form } from 'formango'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import { WastePhOption } from '@/client/types.gen'
import AppGroup from '@/components/app/AppGroup.vue'
import AppCardWithText from '@/components/app/card/AppCardWithText.vue'
import FormGrid from '@/components/app/grid/FormGrid.vue'
import FormTemperatureField from '@/components/form/fields/FormTemperatureField.vue'
import FormUnitValueField from '@/components/form/fields/FormUnitValueField.vue'
import FormRadioGroupItem from '@/components/form/FormRadioGroupItem.vue'
import FormRadioGroupLayout from '@/components/form/FormRadioGroupLayout.vue'
import { TEST_ID } from '@/constants/testId.constant.ts'
import {
  StableTemperatureType,
  StableTemperatureTypeEnumUtil,
} from '@/models/enums/stableTemperatureType.enum'
import {
  WasteFlashpointEnumUtil,
  WasteFlashpointOption,
} from '@/models/enums/wasteFlashpoint.enum'
import { WastePhEnumUtil } from '@/models/enums/wastePh.enum'
import type { wasteInquiryCompositionFormSchema } from '@/models/waste-inquiry/update/steps/wasteInquiryCompositionForm.model'
import { toFormField } from '@/utils/formango.util'

const props = defineProps<{
  isReadonly?: boolean
  form: Form<typeof wasteInquiryCompositionFormSchema>
}>()

const i18n = useI18n()

const specificGravity = props.form.register('specificGravity')
const flashpointType = props.form.register('flashpointType')
const phType = props.form.register('phType')
const stableTemperatureType = props.form.register('stableTemperatureType')
const minimumTemperature = props.form.register('minimumTemperature')
const maximumTemperature = props.form.register('maximumTemperature')
const averageTemperature = props.form.register('averageTemperature')

const temperatures = [
  StableTemperatureType.AMBIENT,
  StableTemperatureType.MIN_MAX,
  StableTemperatureType.AVERAGE,
] as const

const isMinMax = computed<boolean>(
  () => stableTemperatureType.value.value === StableTemperatureType.MIN_MAX,
)

const isAverage = computed<boolean>(
  () => stableTemperatureType.value.value === StableTemperatureType.AVERAGE,
)

function onClearFlashpointType(): void {
  flashpointType.setValue(null)
}

function onClearPhType(): void {
  phType.setValue(null)
}
</script>

<template>
  <FormGrid
    :cols="2"
    class="pt-3xl"
  >
    <VcRadioGroup
      :is-disabled="props.isReadonly"
      v-bind="toFormField(flashpointType)"
      :label="i18n.t('module.waste_inquiry.update.characteristics.flashpoint_type.label')"
      class="
        col-span-full
        lg:col-span-1
      "
    >
      <FormRadioGroupLayout :cols="3">
        <FormRadioGroupItem
          v-for="item of [
            WasteFlashpointOption['<_23°'],
            WasteFlashpointOption['23°_60°'],
            WasteFlashpointOption['>_60°'],
          ]"
          :key="item"
          :test-id="TEST_ID.WASTE_INQUIRY.UPDATE.WASTE_FLASH_POINT_RADIO_GROUP_ITEM"
          :value="item"
        >
          <AppCardWithText
            :is-disabled="props.isReadonly"
            :title="i18n.t(WasteFlashpointEnumUtil.getI18nKey(item))"
            :is-selected="item === flashpointType.value.value"
            :has-clear-button="!props.isReadonly"
            @clear="onClearFlashpointType"
          />
        </FormRadioGroupItem>
      </FormRadioGroupLayout>
    </VcRadioGroup>

    <VcRadioGroup
      :is-disabled="props.isReadonly"
      v-bind="toFormField(phType)"
      :label="i18n.t('module.waste_inquiry.update.characteristics.ph_type.label')"
      class="
        col-span-full
        lg:col-span-1
      "
    >
      <FormRadioGroupLayout :cols="4">
        <FormRadioGroupItem
          v-for="item of [
            WastePhOption['<_2'],
            WastePhOption['2_4'],
            WastePhOption['4_10'],
            WastePhOption['>_10'],
          ]"
          :key="item"
          :test-id="TEST_ID.WASTE_INQUIRY.UPDATE.WASTE_PH_RADIO_GROUP_ITEM"
          :value="item"
        >
          <AppCardWithText
            :is-disabled="props.isReadonly"
            :title="i18n.t(WastePhEnumUtil.getI18nKey(item))"
            :is-selected="item === phType.value.value"
            :has-clear-button="!props.isReadonly"
            @clear="onClearPhType"
          />
        </FormRadioGroupItem>
      </FormRadioGroupLayout>
    </VcRadioGroup>

    <FormUnitValueField
      v-bind="specificGravity"
      :is-required="false"
      :is-readonly="props.isReadonly"
      :test-id="TEST_ID.WASTE_INQUIRY.UPDATE.SPECIFIC_GRAVITY_TEXT_FIELD"
      :label="i18n.t('module.waste_inquiry.update.characteristics.specific_gravity.label')"
      :max="9999999.9"
      :min="-9999999.9"
      :step="0.1"
      :format-options="{
        style: 'decimal',
        maximumFractionDigits: 1,
      }"
      unit="kg/l"
    />

    <VcSelect
      v-bind="toFormField(stableTemperatureType)"
      :test-id="TEST_ID.WASTE_INQUIRY.UPDATE.TEMPERATURE.SELECT"
      :display-fn="(value) => i18n.t(StableTemperatureTypeEnumUtil.getI18nKey(value))"
      :label="i18n.t('module.waste_inquiry.update.characteristics.stable_temperature.label')"
      :placeholder="i18n.t('module.waste_inquiry.update.characteristics.stable_temperature.placeholder')"
      :is-disabled="props.isReadonly"
      popover-align="start"
    >
      <VcSelectItem
        v-for="temperature of temperatures"
        :key="temperature"
        :value="temperature"
        :test-id="TEST_ID.WASTE_INQUIRY.UPDATE.TEMPERATURE.OPTION"
      >
        {{ i18n.t(StableTemperatureTypeEnumUtil.getI18nKey(temperature)) }}
      </VcSelectItem>
    </VcSelect>

    <AppGroup
      v-if="isMinMax"
      align="start"
    >
      <FormTemperatureField
        :is-readonly="props.isReadonly"
        v-bind="minimumTemperature"
        :is-required="true"
        :label="i18n.t('shared.min_temperature')"
        :max="maximumTemperature.modelValue.value ?? 99999"
        :min="-99999"
        class="w-full"
      />

      <FormTemperatureField
        :is-readonly="props.isReadonly"
        v-bind="maximumTemperature"
        :is-required="true"
        :label="i18n.t('shared.max_temperature')"
        :max="99999"
        :min="minimumTemperature.modelValue.value ?? -99999"
        class="w-full"
      />
    </AppGroup>

    <FormTemperatureField
      v-else-if="isAverage"
      :is-readonly="props.isReadonly"
      v-bind="averageTemperature"
      :is-required="true"
      :label="i18n.t('shared.average_temperature')"
      :max="99999"
      :min="-99999"
      class="w-full"
    />
  </FormGrid>
</template>
