<script setup lang="ts">
import {
  computed,
  ref,
  watch,
} from 'vue'
import { useI18n } from 'vue-i18n'

import { RequestType } from '@/client/types.gen'
import AppCustomerAutocomplete from '@/components/app/autocomplete/AppCustomerAutocomplete.vue'
import AppPickUpAddressAutocomplete from '@/components/app/autocomplete/AppPickUpAddressAutocomplete.vue'
import AppWasteProducerAutocomplete from '@/components/app/autocomplete/AppWasteProducerAutocomplete.vue'
import FormLayout from '@/components/form/FormLayout.vue'
import WizardFormSingleChoiceSection from '@/components/form/wizard/components/WizardFormSingleChoiceSection.vue'
import WizardFormStep from '@/components/form/wizard/WizardFormStep.vue'
import { useGlobalCustomer } from '@/composables/global-customer/globalCustomer.composable'
import type { WizardForm } from '@/composables/wizard-form/wizardForm.composable'
import { useWizardFormStep } from '@/composables/wizard-form/wizardFormStep.composable'
import { TEST_ID } from '@/constants/testId.constant.ts'
import { AddressUtil } from '@/models/address/address.util'
import type { CustomerIndex } from '@/models/customer/index/customerIndex.model'
import type { PickUpAddressIndex } from '@/models/pick-up-address/index/pickUpAddressIndex.model'
import type { wasteInquiryCustomerAndLocationFormSchema } from '@/models/waste-inquiry/update/steps/wasteInquiryCustomerAndLocationForm.model'
import type { WasteProducerIndex } from '@/models/waste-producer/index/wasteProducerIndex.model'
import { useSuggestedCustomerIndexQuery } from '@/modules/waste-inquiry/api/queries/suggestedCustomerIndex.query'
import { useSuggestedPickUpAddressIndexQuery } from '@/modules/waste-inquiry/api/queries/suggestedPickUpAddressIndex.query'
import { useSuggestedWasteProducerIndexQuery } from '@/modules/waste-inquiry/api/queries/suggestedWasteProducerIndex.query'

const props = defineProps<{
  isReadonly?: boolean
  wizardForm: WizardForm<typeof wasteInquiryCustomerAndLocationFormSchema>
}>()

const i18n = useI18n()
const globalCustomer = useGlobalCustomer()
const wizardFormStep = useWizardFormStep(props.wizardForm)
const { form } = wizardFormStep

const customer = form.register('customer')
const customerAutocompleteValue = ref<CustomerIndex | null>(null)
const suggestedCustomerIndexQuery = useSuggestedCustomerIndexQuery(RequestType.WASTE)
const selectedCustomer = computed<CustomerIndex | null>(() => customer.value.value)

const isWasteProducerUnknown = form.register('isWasteProducerUnknown', false)
const wasteProducer = form.register('wasteProducer')
const wasteProducerAutocompleteValue = ref<WasteProducerIndex | null>(null)
const suggestedWasteProducerIndexQuery = useSuggestedWasteProducerIndexQuery(
  computed<string | null>(() => selectedCustomer.value?.id ?? null),
)

const isPickupAddressUnknown = form.register('isPickupAddressUnknown', false)
const pickUpAddress = form.register('pickUpAddress')
const pickUpAddressAutocompleteValue = ref<PickUpAddressIndex | null>(null)
const suggestedPickUpAddressIndexQuery = useSuggestedPickUpAddressIndexQuery(
  computed<string | null>(() => selectedCustomer.value?.id ?? null),
  RequestType.WASTE,
)

watch(selectedCustomer, () => {
  wasteProducer.setValue(null)
  pickUpAddress.setValue(null)

  pickUpAddressAutocompleteValue.value = null
  pickUpAddressAutocompleteValue.value = null
})
</script>

<template>
  <WizardFormStep :wizard-form-step="wizardFormStep">
    <FormLayout>
      <WizardFormSingleChoiceSection
        id="customer"
        :is-visible="true"
        :autocomplete-value="customerAutocompleteValue"
        :field="customer"
        :test-id="TEST_ID.WASTE_INQUIRY.UPDATE.CUSTOMER_RADIO_GROUP_ITEM"
        :suggested-resources="globalCustomer.globalCustomer.value === null
          ? suggestedCustomerIndexQuery.data.value ?? []
          : []"
        :is-loading-suggested-resources="suggestedCustomerIndexQuery.isLoading.value"
        :title="i18n.t('module.waste_inquiry.update.customer_and_location.customer.title')"
        :card-title="(customer) => customer.name"
        :card-description="(customer) => customer.address === null ? '-' : AddressUtil.format(customer.address)"
        :is-required="true"
        :is-readonly="props.isReadonly || globalCustomer.globalCustomer.value !== null"
        card-icon="building"
      >
        <template #autocomplete>
          <AppCustomerAutocomplete v-model="customerAutocompleteValue" />
        </template>
      </WizardFormSingleChoiceSection>

      <WizardFormSingleChoiceSection
        id="wasteProducer"
        :is-visible="selectedCustomer !== null"
        :autocomplete-value="wasteProducerAutocompleteValue"
        :field="wasteProducer"
        :test-id="TEST_ID.WASTE_INQUIRY.UPDATE.WASTE_PRODUCER_RADIO_GROUP_ITEM"
        :suggested-resources="suggestedWasteProducerIndexQuery.data.value ?? []"
        :is-loading-suggested-resources="suggestedWasteProducerIndexQuery.isLoading.value"
        :title="i18n.t('module.waste_inquiry.update.customer_and_location.waste_producer.title')"
        :card-description="(wasteProducer) => wasteProducer.address === null ? '-' : AddressUtil.format(wasteProducer.address)"
        :card-title="(wasteProducer) => wasteProducer.name"
        :is-required="true"
        :is-unknown="{
          field: isWasteProducerUnknown,
          label: i18n.t('module.waste_inquiry.update.customer_and_location.waste_producer.unknown_label'),
        }"
        :is-readonly="props.isReadonly"
        card-icon="locationPin"
      >
        <template #autocomplete>
          <AppWasteProducerAutocomplete
            v-model="wasteProducerAutocompleteValue"
            :customer-id="selectedCustomer!.id"
          />
        </template>
      </WizardFormSingleChoiceSection>

      <WizardFormSingleChoiceSection
        id="pickUpAddress"
        :is-visible="selectedCustomer !== null"
        :autocomplete-value="pickUpAddressAutocompleteValue"
        :field="pickUpAddress"
        :test-id="TEST_ID.WASTE_INQUIRY.UPDATE.PICK_UP_ADDRESS_RADIO_GROUP_ITEM"
        :suggested-resources="suggestedPickUpAddressIndexQuery.data.value ?? []"
        :is-loading-suggested-resources="suggestedPickUpAddressIndexQuery.isLoading.value"
        :title="i18n.t('module.waste_inquiry.update.customer_and_location.pick_up_address.title')"
        :card-description="(pickUpAddress) => pickUpAddress.address === null ? '-' : AddressUtil.format(pickUpAddress.address)"
        :card-title="(pickUpAddress) => pickUpAddress.name"
        :is-required="true"
        :is-readonly="props.isReadonly"
        :is-unknown="{
          field: isPickupAddressUnknown,
          label: i18n.t('module.waste_inquiry.update.customer_and_location.pick_up_address.unknown_label'),
        }"
        card-icon="locationPin"
      >
        <template #autocomplete>
          <AppPickUpAddressAutocomplete
            v-model="pickUpAddressAutocompleteValue"
            :customer-id="selectedCustomer!.id"
          />
        </template>
      </WizardFormSingleChoiceSection>
    </FormLayout>
  </WizardFormStep>
</template>
