<script setup lang="ts">
import {
  VcIconButton,
  VcNumberField,
  VcTextField,
} from '@wisemen/vue-core-components'
import type { Field } from 'formango'
import { useI18n } from 'vue-i18n'

import AppGroup from '@/components/app/AppGroup.vue'
import { TEST_ID } from '@/constants/testId.constant.ts'
import type { CompositionComponent } from '@/models/waste-inquiry/update/steps/wasteInquiryCompositionForm.model'
import { toFormField } from '@/utils/formango.util'

const props = defineProps<{
  isReadonly?: boolean
  componentCount: number
  componentIndex: number
  component: Field<CompositionComponent>
}>()

const emit = defineEmits<{
  remove: []
}>()

const i18n = useI18n()

const name = props.component.register('name')
const minWeight = props.component.register('minWeight')
const maxWeight = props.component.register('maxWeight')

function onRemove(): void {
  emit('remove')
}
</script>

<template>
  <AppGroup
    align="start"
    class="w-full"
  >
    <VcTextField
      v-bind="toFormField(name)"
      :is-disabled="props.isReadonly"
      :test-id="TEST_ID.WASTE_INQUIRY.UPDATE.COMPOSITION_COMPONENT.NAME_TEXT_FIELD"
      :label="`${i18n.t('components.waste_inquiry.composition.components.component_name.label')} ${props.componentIndex + 1}`"
      :placeholder="i18n.t('components.waste_inquiry.composition.components.component_name.placeholder')"
      class="w-full"
    />

    <VcNumberField
      v-bind="toFormField(minWeight)"
      :hide-controls="true"
      :is-disabled="props.isReadonly"
      :min="0"
      :test-id="TEST_ID.WASTE_INQUIRY.UPDATE.COMPOSITION_COMPONENT.MIN_WEIGHT_NUMBER_FIELD"
      :max="maxWeight.value.value ?? 100"
      :step="0.01"
      :format-options="{
        maximumFractionDigits: 2,
      }"
      :label="i18n.t('components.waste_inquiry.composition.components.component.min_weight.label')"
      placeholder="0"
      class="w-24 shrink-0"
    >
      <template #right>
        <span class="text-secondary mr-md text-sm">
          %
        </span>
      </template>
    </VcNumberField>

    <VcNumberField
      v-bind="toFormField(maxWeight)"
      :hide-controls="true"
      :is-disabled="props.isReadonly"
      :min="minWeight.value.value ?? 0"
      :max="100"
      :step="0.01"
      :test-id="TEST_ID.WASTE_INQUIRY.UPDATE.COMPOSITION_COMPONENT.MAX_WEIGHT_NUMBER_FIELD"
      :format-options="{
        maximumFractionDigits: 2,
      }"
      :label="i18n.t('components.waste_inquiry.composition.components.component.max_weight.label')"
      placeholder="0"
      class="w-24 shrink-0"
    >
      <template #right>
        <span class="text-secondary mr-md text-sm">
          %
        </span>
      </template>)
    </VcNumberField>

    <VcIconButton
      :label="i18n.t('shared.remove')"
      :is-disabled="props.componentCount === 1 || props.isReadonly"
      icon="trash"
      variant="destructive-tertiary"
      size="sm"
      class="mt-[1.75rem] shrink-0"
      @click="onRemove"
    />
  </AppGroup>
</template>
