<script setup lang="ts">
import { useRouteParams } from '@vueuse/router'
import {
  VcButton,
  VcCheckbox,
  VcFormField,
} from '@wisemen/vue-core-components'
import { useForm } from 'formango'
import {
  AnimatePresence,
  Motion,
} from 'motion-v'
import { watch } from 'vue'
import { useI18n } from 'vue-i18n'

import AppGroup from '@/components/app/AppGroup.vue'
import AppHeightTransition from '@/components/app/AppHeightTransition.vue'
import AppCard from '@/components/app/card/AppCard.vue'
import FormGrid from '@/components/app/grid/FormGrid.vue'
import FormBooleanRadioGroup from '@/components/form/fields/FormBooleanRadioGroup.vue'
import FormFileUpload from '@/components/form/file-upload/FormFileUpload.vue'
import WizardFormStep from '@/components/form/wizard/WizardFormStep.vue'
import type { WizardForm } from '@/composables/wizard-form/wizardForm.composable'
import { useWizardFormStep } from '@/composables/wizard-form/wizardFormStep.composable'
import { TEST_ID } from '@/constants/testId.constant.ts'
import { wasteInquirySapDocumentUpdateFormSchema } from '@/models/waste-inquiry/update/sap/wasteInquirySapDocumentUpdateForm.model'
import type { wasteInquiryCompositionFormSchema } from '@/models/waste-inquiry/update/steps/wasteInquiryCompositionForm.model'
import { useWasteInquirySapDocumentUpdateMutation } from '@/modules/waste-inquiry/api/mutations/wasteInquirySapDocumentUpdate.mutation'
import WasteInquiryCharacteristicsForm from '@/modules/waste-inquiry/features/update/components/steps/characteristics/WasteInquiryCharacteristicsForm.vue'
import WasteInquiryCompositionComponent from '@/modules/waste-inquiry/features/update/components/steps/composition/WasteInquiryCompositionComponent.vue'
import { FileUtil } from '@/utils/file.util'
import { toFormField } from '@/utils/formango.util'

const props = defineProps<{
  isReadonly?: boolean
  wizardForm: WizardForm<typeof wasteInquiryCompositionFormSchema>
}>()

const i18n = useI18n()
const wizardFormStep = useWizardFormStep(props.wizardForm)
const documentsUpdateMutation = useWasteInquirySapDocumentUpdateMutation()

const inquiryNumber = useRouteParams<string | null>('inquiryNumber')

const { form } = wizardFormStep

const sdsFiles = form.register('sdsFiles', [])
const hasNoSds = form.register('hasNoSds', false)

const analysisReportFiles = form.register('analysisReportFiles', [])
const hasNoAnalysisReport = form.register('hasNoAnalysisReport', false)

const composition = form.registerArray('composition', [
  {
    name: null,
    maxWeight: null,
    minWeight: null,
  },
])

const isSampleAvailable = form.register('isSampleAvailable', false)

function onAddComponent(): void {
  composition.append()
}

function onRemoveComponent(index: number): void {
  composition.remove(index)
}

const sapDocumentsForm = useForm({
  initialState: {
    additionalFiles: props.wizardForm.state.value.additionalFiles ?? [],
    analysisReportFiles: props.wizardForm.state.value.analysisReportFiles ?? [],
    sdsFiles: props.wizardForm.state.value.sdsFiles ?? [],
  },
  schema: wasteInquirySapDocumentUpdateFormSchema,
  onSubmit: async (values) => {
    if (inquiryNumber.value === null) {
      return
    }

    await documentsUpdateMutation.execute({
      body: values,
      params: { inquiryNumber: inquiryNumber.value },
    })
  },
})

const sapSdsFiles = sapDocumentsForm.register('sdsFiles', [])
const sapAnalysisReportFiles = sapDocumentsForm.register('analysisReportFiles', [])

watch(sapDocumentsForm.state, () => {
  sapDocumentsForm.submit()
}, { deep: true })
</script>

<template>
  <WizardFormStep :wizard-form-step="wizardFormStep">
    <FormGrid :cols="2">
      <AppGroup
        direction="col"
        align="start"
      >
        <FormFileUpload
          v-if="!props.isReadonly"
          :is-required="true"
          v-bind="toFormField(sdsFiles)"
          :is-disabled="hasNoSds.value.value"
          :mime-types="FileUtil.getDefaultAllowedExtensions()"
          :label="i18n.t('module.waste_inquiry.update.composition.sds.label')"
          :max-file-size-mb="100"
        />
        <FormFileUpload
          v-else
          v-bind="toFormField(sapSdsFiles)"
          :is-disabled="hasNoSds.value.value"
          :mime-types="FileUtil.getDefaultAllowedExtensions()"
          :label="i18n.t('module.waste_inquiry.update.composition.sds.label')"
          :max-file-size-mb="100"
        />

        <VcCheckbox
          v-bind="toFormField(hasNoSds)"
          :test-id="TEST_ID.WASTE_INQUIRY.UPDATE.COMPOSITION_NO_SDS_CHECKBOX"
          :is-disabled="sdsFiles.value.value.length > 0 || props.isReadonly"
          :label="i18n.t('module.waste_inquiry.update.composition.sds.no_sds')"
          :variant="props.isReadonly ? 'readonly' : undefined"
        />
      </AppGroup>

      <AppGroup
        direction="col"
        align="start"
      >
        <FormFileUpload
          v-if="!props.isReadonly"
          :is-required="true"
          v-bind="toFormField(analysisReportFiles)"
          :is-disabled="hasNoAnalysisReport.value.value"
          :mime-types="FileUtil.getDefaultAllowedExtensions()"
          :label="i18n.t('module.waste_inquiry.update.composition.analysis_report.label')"
          :max-file-size-mb="100"
        />
        <FormFileUpload
          v-else
          v-bind="toFormField(sapAnalysisReportFiles)"
          :is-disabled="hasNoAnalysisReport.value.value"
          :mime-types="FileUtil.getDefaultAllowedExtensions()"
          :label="i18n.t('module.waste_inquiry.update.composition.analysis_report.label')"
          :max-file-size-mb="100"
        />

        <VcCheckbox
          v-bind="toFormField(hasNoAnalysisReport)"
          :is-disabled="analysisReportFiles.value.value.length > 0 || props.isReadonly"
          :test-id="TEST_ID.WASTE_INQUIRY.UPDATE.NO_ANALYSIS_REPORT_CHECKBOX"
          :label="i18n.t('module.waste_inquiry.update.composition.analysis_report.no_report')"
          :variant="props.isReadonly ? 'readonly' : undefined"
        />
      </AppGroup>

      <div class="col-span-full">
        <VcFormField
          :label="i18n.t('module.waste_inquiry.update.composition.components.label')"
          :is-required="true"
          for=""
        >
          <AppCard
            :is-disabled="true"
            variant="transparent"
          >
            <AppHeightTransition>
              <AppGroup
                direction="col"
                gap="xl"
                align="start"
              >
                <AnimatePresence
                  :initial="false"
                  mode="popLayout"
                >
                  <Motion
                    v-for="(field, fieldIndex) of composition.fields.value"
                    :key="field"
                    :layout="true"
                    :initial="{
                      opacity: 0,
                      scale: 0.95,
                      filter: 'blur(4px)',
                    }"
                    :animate="{
                      opacity: 1,
                      scale: 1,
                      filter: 'blur(0px)',
                    }"
                    :exit="{
                      opacity: 0,
                      scale: 0.95,
                      filter: 'blur(4px)',
                    }"
                    :transition="{
                      bounce: 0,
                      duration: 0.4,
                      type: 'spring',
                    }"
                    class="w-full p-px"
                    tabindex="-1"
                  >
                    <WasteInquiryCompositionComponent
                      :is-readonly="props.isReadonly"
                      :component="composition.register(`${fieldIndex}`)"
                      :component-index="fieldIndex"
                      :component-count="composition.fields.value.length"
                      @remove="onRemoveComponent(fieldIndex)"
                    />
                  </Motion>
                </AnimatePresence>
              </AppGroup>
            </AppHeightTransition>
          </AppCard>
        </VcFormField>

        <VcButton
          v-if="!props.isReadonly"
          icon-left="plus"
          variant="tertiary-color"
          size="sm"
          class="mt-xs"
          @click="onAddComponent"
        >
          {{ i18n.t('components.waste_inquiry.composition.components.add') }}
        </VcButton>
      </div>

      <FormBooleanRadioGroup
        v-bind="isSampleAvailable"
        :is-readonly="props.isReadonly"
        :is-required="true"
        :test-id="TEST_ID.WASTE_INQUIRY.UPDATE.WASTE_SAMPLE_RADIO_GROUP_ITEM"
        :label="i18n.t('module.waste_inquiry.update.composition.sample.label')"
        :truthy-label="i18n.t('module.waste_inquiry.update.composition.sample.available')"
        :falsy-label="i18n.t('module.waste_inquiry.update.composition.sample.not_available')"
      />
    </FormGrid>

    <WasteInquiryCharacteristicsForm
      :is-readonly="props.isReadonly"
      :form="form"
    />
  </WizardFormStep>
</template>
