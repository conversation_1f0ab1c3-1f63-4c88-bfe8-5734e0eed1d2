<script setup lang="ts">
import type { FileUploadInfo } from '@wisemen/vue-core-components'
import {
  VcFileUploadRoot,
  VcFileUploadTrigger,
  VcFormField,
  VcIcon,
} from '@wisemen/vue-core-components'
import { useId } from 'vue'

import { FileService } from '@/api/services/file.service'
import type { MimeType } from '@/client/types.gen'
import FormFileUploadItem from '@/components/form/file-upload/FormFileUploadItem.vue'
import FormFileUploadTriggerCard from '@/components/form/file-upload/FormFileUploadTriggerCard.vue'
import type { S3File } from '@/models/s3-file/s3File.model'
import { FileUtil } from '@/utils/file.util'

const props = withDefaults(defineProps<{
  id: string
  title: string
  isRequired?: boolean
  isTouched?: boolean
  errors?: string[]
  hideDeleteButton?: boolean
  hint?: string | null
  label?: string | null
}>(), {
  isDisabled: false,
  isRequired: false,
  isTouched: false,
  errors: () => [],
  hint: null,
  label: null,
})

const inputId = props.id ?? useId()

const value = defineModel<S3File[]>({ required: true })

async function getFileInfo(name: string, mimeType: string): Promise<FileUploadInfo> {
  const response = await FileService.upload(name, mimeType as MimeType)

  if (!response.uuid || !response.url) {
    throw new Error('Failed to get file upload info')
  }

  return {
    uuid: response.uuid,
    uploadUrl: response.url,
  }
}

async function confirmUpload(): Promise<void> {}
</script>

<template>
  <VcFormField
    :for="inputId"
    :is-required="props.isRequired"
    :is-touched="props.isTouched"
    :error-message="props.errors?.[0]"
    :hint="props.hint"
    :label="props.label"
    class="w-full"
  >
    <VcFileUploadRoot
      v-slot="{ items }"
      v-model="value"
      :accept="FileUtil.getDefaultAllowedExtensions()"
      :get-file-info="getFileInfo"
      :confirm-upload="confirmUpload"
      :is-valid-file="(file) => file.size <= FileUtil.mbToBytes(100)"
    >
      <div>
        <div class="flex items-center justify-between pb-1">
          <p class="text-secondary w-full text-sm font-medium">
            {{ props.title }}
          </p>
          <VcFileUploadTrigger
            :id="props.id"
            class="!w-6"
          >
            <VcIcon
              icon="plus"
              class="
                text-brand-tertiary size-7 cursor-pointer rounded-full p-1
                duration-200
                hover:bg-disabled
              "
            />
          </VcFileUploadTrigger>
        </div>
        <div class="max-h-50 overflow-y-auto">
          <FormFileUploadTriggerCard
            v-if="items.length === 0"
            :id="props.id"
            :mime-types="FileUtil.getDefaultAllowedExtensions()"
            size="sm"
          />
          <div class="flex flex-col gap-2">
            <FormFileUploadItem
              v-for="item of items"
              :key="item.key"
              :item="item"
              :hide-delete-button="true"
              size="sm"
            />
          </div>
        </div>
      </div>
    </VcFileUploadRoot>
  </VcFormField>
</template>
