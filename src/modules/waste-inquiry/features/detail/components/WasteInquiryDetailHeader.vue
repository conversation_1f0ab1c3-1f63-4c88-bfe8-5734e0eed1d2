<script setup lang="ts">
import {
  V<PERSON><PERSON>utton,
  VcI<PERSON>,
  VcPopover,
  VcTooltip,
} from '@wisemen/vue-core-components'
import { useForm } from 'formango'
import {
  computed,
  ref,
  watch,
} from 'vue'
import { useI18n } from 'vue-i18n'

import type { WasteInquiryStatus } from '@/client'
import AppGroup from '@/components/app/AppGroup.vue'
import { useLocalizedDateFormat } from '@/composables/localized-date-format/localizedDateFormat.composable'
import { WasteInquiryStatusEnumUtil } from '@/models/enums/wasteInquiryStatus.enum'
import type { WasteInquiryDetail } from '@/models/waste-inquiry/detail/wasteInquiryDetail.model'
import { wasteInquirySapDocumentUpdateFormSchema } from '@/models/waste-inquiry/update/sap/wasteInquirySapDocumentUpdateForm.model'
import { useWasteInquirySapDocumentUpdateMutation } from '@/modules/waste-inquiry/api/mutations/wasteInquirySapDocumentUpdate.mutation'
import { useWasteInquirySapSummaryDownloadMutation } from '@/modules/waste-inquiry/api/mutations/wasteInquirySapSummaryDownload.mutation'
import WasteInquiryDetailHeaderFileUpload from '@/modules/waste-inquiry/features/detail/components/WasteInquiryDetailHeaderFileUpload.vue'
import { DateUtil } from '@/utils/date.util'
import { DownloadUtil } from '@/utils/download.util'
import { toFormField } from '@/utils/formango.util'
import { WasteInquiryUtil } from '@/utils/wasteInquiry.util'

const props = defineProps<{
  wasteInquiry: WasteInquiryDetail
}>()

const i18n = useI18n()
const localizedDateFormat = useLocalizedDateFormat()
const documentsUpdateMutation = useWasteInquirySapDocumentUpdateMutation()
const summaryDownloadMutation = useWasteInquirySapSummaryDownloadMutation()

const initialFileCounts = ref<{
  additionalFiles: number
  analysisReportFiles: number
  sdsFiles: number
}>({
  additionalFiles: props.wasteInquiry.additionalFiles?.length ?? 0,
  analysisReportFiles: props.wasteInquiry.analysisReportFiles?.length ?? 0,
  sdsFiles: props.wasteInquiry.sdsFiles?.length ?? 0,
})

const wasteInquiryStatus = computed<WasteInquiryStatus | null>(() => {
  return props.wasteInquiry.status
    ? props.wasteInquiry.status as WasteInquiryStatus
    : null
})

const form = useForm({
  initialState: {
    additionalFiles: props.wasteInquiry.additionalFiles ?? [],
    analysisReportFiles: props.wasteInquiry.analysisReportFiles ?? [],
    sdsFiles: props.wasteInquiry.sdsFiles ?? [],
  },
  schema: wasteInquirySapDocumentUpdateFormSchema,
  onSubmit: async (values) => {
    if (props.wasteInquiry.inquiryNumber === null) {
      return
    }

    const newFiles = {
      additionalFiles: values.additionalFiles?.slice(initialFileCounts.value.additionalFiles) ?? [],
      analysisReportFiles: values.analysisReportFiles?.slice(initialFileCounts.value.analysisReportFiles) ?? [],
      sdsFiles: values.sdsFiles?.slice(initialFileCounts.value.sdsFiles) ?? [],
    }

    if (
      newFiles.sdsFiles.length > 0
      || newFiles.analysisReportFiles.length > 0
      || newFiles.additionalFiles.length > 0
    ) {
      await documentsUpdateMutation.execute({
        body: newFiles,
        params: { inquiryNumber: props.wasteInquiry.inquiryNumber },
      })

      initialFileCounts.value = {
        additionalFiles: values.additionalFiles?.length ?? 0,
        analysisReportFiles: values.analysisReportFiles?.length ?? 0,
        sdsFiles: values.sdsFiles?.length ?? 0,
      }
    }
  },
})

const sdsFiles = form.register('sdsFiles', props.wasteInquiry.sdsFiles ?? [])
const analysisReportFiles = form.register('analysisReportFiles', props.wasteInquiry.analysisReportFiles ?? [])
const additionalFiles = form.register('additionalFiles', props.wasteInquiry.additionalFiles ?? [])

const filesCount = computed<number>(() => {
  return (form.state.value.sdsFiles?.length ?? 0)
    + (form.state.value.analysisReportFiles?.length ?? 0)
    + (form.state.value.additionalFiles?.length ?? 0)
})

async function downloadSummary(): Promise<void> {
  if (props.wasteInquiry.inquiryNumber === null) {
    return
  }

  const result = await summaryDownloadMutation.execute({ body: { inquiryNumber: props.wasteInquiry.inquiryNumber } })

  DownloadUtil.downloadBlob(result.blob, result.disposition)
}

watch(form.state, () => {
  form.submit()
}, { deep: true })
</script>

<template>
  <header class="p-3xl pb-md flex items-center justify-between">
    <div>
      <AppGroup>
        <VcTooltip popover-side="bottom">
          <template #trigger>
            <VcIcon
              v-if="wasteInquiryStatus !== null"
              :class="WasteInquiryUtil.getIconStyleFromStatus(wasteInquiryStatus)"
              icon="microscope"
              class="p-md size-9 rounded-lg"
            />
          </template>
          <template #content>
            <p
              v-if="wasteInquiryStatus !== null"
              class="px-lg my-md text-sm"
            >
              {{ i18n.t(WasteInquiryStatusEnumUtil.getI18nKey(wasteInquiryStatus)) }}
            </p>
          </template>
        </VcTooltip>
        <h1 class="text-primary text-display-xs font-semibold">
          {{ props.wasteInquiry.wasteStreamName }}
        </h1>
      </AppGroup>

      <AppGroup
        class="pt-1.5"
        gap="sm"
      >
        <span class="text-secondary text-xs">
          {{ i18n.t('module.waste_inquiry.update.submitted_id', { id: props.wasteInquiry.inquiryNumber }) }}
        </span>

        <span class="text-fg-brand-primary">
          &bull;
        </span>

        <span class="text-secondary text-xs">
          {{ i18n.t('module.waste_inquiry.update.submitted_on') }}

          <time
            v-if="props.wasteInquiry.submittedOn"
            :datetime="DateUtil.toISODate(props.wasteInquiry.submittedOn)"
          >
            {{ localizedDateFormat.toDate(props.wasteInquiry.submittedOn) }}
          </time>
        </span>

        <span class="text-fg-brand-primary">
          &bull;
        </span>

        <span class="text-secondary text-xs">
          {{ i18n.t('module.waste_inquiry.update.submitted_by', { user: props.wasteInquiry.createdBy }) }}
        </span>
      </AppGroup>
    </div>
    <AppGroup>
      <VcPopover :is-popover-arrow-hidden="true">
        <template #trigger>
          <VcButton
            variant="secondary"
            icon-left="paperClip"
          >
            {{ filesCount }}
          </VcButton>
        </template>
        <template #content>
          <div class="px-lg py-md gap-xl flex w-90 flex-col">
            <WasteInquiryDetailHeaderFileUpload
              id="sds-files-upload"
              v-bind="toFormField(sdsFiles)"
              :title="i18n.t('module.waste_inquiry.detail.sds')"
            />
            <WasteInquiryDetailHeaderFileUpload
              id="analysis-report-files-upload"
              v-bind="toFormField(analysisReportFiles)"
              :title="i18n.t('module.waste_inquiry.detail.analysis_report')"
            />
            <WasteInquiryDetailHeaderFileUpload
              id="additional-files-upload"
              v-bind="toFormField(additionalFiles)"
              :title="i18n.t('module.waste_inquiry.detail.additional_files')"
            />
          </div>
        </template>
      </VcPopover>
      <VcButton
        variant="secondary"
        icon-left="download"
        @click="downloadSummary"
      >
        {{ i18n.t('module.waste_inquiry.detail.download_summary') }}
      </VcButton>
    </AppGroup>
  </header>
</template>
