import { h } from 'vue'

import { DynamicColumnNames } from '@/client'
import { TEST_ID } from '@/constants/testId.constant'
import type { WasteInquiryIndex } from '@/models/waste-inquiry/index/wasteInquiryIndex.model'
import WasteInquiryOverviewTableEwcCell from '@/modules/waste-inquiry/features/overview/components/cells/WasteInquiryOverviewTableEwcCell.vue'
import WasteInquiryOverviewTableStatusCell from '@/modules/waste-inquiry/features/overview/components/cells/WasteInquiryOverviewTableStatusCell.vue'
import type { DataTableColumn } from '@/types/table.type'

// eslint-disable-next-line ts/explicit-function-return-type
export function useWasteInquiryTableColumns() {
  return {
    [DynamicColumnNames.EWC_CODE]: useEwcCodeColumn,
    [DynamicColumnNames.STATUS]: useStatusColumn,
  }
}

function useEwcCodeColumn(
  key: string,
  label: string,
): DataTableColumn<WasteInquiryIndex> {
  return {
    cell: (row) => h(WasteInquiryOverviewTableEwcCell, {
      ewcLevel1: row.ewcLevel1,
      ewcLevel2: row.ewcLevel2,
      ewcLevel3: row.ewcLevel3,
    }),
    headerLabel: label,
    key,
  }
}

function useStatusColumn(
  key: string,
  label: string,
): DataTableColumn<WasteInquiryIndex> {
  return {
    testId: TEST_ID.WASTE_INQUIRY.OVERVIEW.TABLE.STATUS_COLUMN,
    cell: (row) => (
      h(WasteInquiryOverviewTableStatusCell, { status: row.status })
    ),
    headerLabel: label,
    key,
  }
}
