<script setup lang="ts">
import { VcIconButton } from '@wisemen/vue-core-components'
import { useI18n } from 'vue-i18n'

import type { WasteInquiryStatus } from '@/client/types.gen'
import AppGroup from '@/components/app/AppGroup.vue'
import DataTableCell from '@/components/table/data-table/DataTableCell.vue'
import { TEST_ID } from '@/constants/testId.constant'

const props = withDefaults(defineProps<{
  isCopying?: boolean
  isDownloading?: boolean
  hideCopy?: boolean
  hideDownload?: boolean
  status: WasteInquiryStatus
}>(), {
  isCopying: false,
  isDownloading: false,
  hideCopy: false,
  hideDownload: false,
})

const emit = defineEmits<{
  copy: []
  download: []
  edit: []
}>()

const i18n = useI18n()

function onDownload(): void {
  emit('download')
}

function onEdit(): void {
  emit('edit')
}

function onCopy(): void {
  emit('copy')
}
</script>

<template>
  <DataTableCell
    :has-interactive-content="true"
    class="!px-lg"
  >
    <AppGroup gap="xs">
      <VcIconButton
        v-if="!props.hideCopy"
        :label="i18n.t('shared.copy')"
        :class-config="{
          icon: 'text-fg-brand-primary',
        }"
        :is-loading="props.isCopying"
        variant="tertiary"
        size="sm"
        icon="copy"
        @click="onCopy"
      />

      <VcIconButton
        v-if="!props.hideDownload"
        :label="i18n.t('shared.download')"
        :class-config="{
          icon: 'text-fg-brand-primary',
        }"
        :is-loading="props.isDownloading"
        :is-disabled="props.isDownloading"
        icon="download"
        variant="tertiary"
        size="sm"
        @click="onDownload"
      />

      <VcIconButton
        :label="i18n.t('shared.edit')"
        :class-config="{
          icon: 'text-fg-brand-primary',
        }"
        :test-id="TEST_ID.WASTE_INQUIRY.OVERVIEW.TABLE.GO_TO_DETAIL_ACTION"
        variant="tertiary"
        size="sm"
        icon="chevronRight"
        @click="onEdit"
      />
    </AppGroup>
  </DataTableCell>
</template>
