<script setup lang="ts">
import '@/icons/icons.ts'

import {
  VcIconButton,
  VcTooltip,
} from '@wisemen/vue-core-components'
import { useI18n } from 'vue-i18n'

import AppGroup from '@/components/app/AppGroup.vue'
import DataTableCell from '@/components/table/data-table/DataTableCell.vue'
import type { UserIndex } from '@/models/user/index/userIndex.model'

const props = defineProps<{
  hasUserImpersonatePermission: boolean
  hasUserManagePermission: boolean
  user: UserIndex
}>()

const emit = defineEmits<{
  impersonate: []
}>()

const i18n = useI18n()

function onImpersonate(): void {
  emit('impersonate')
}
</script>

<template>
  <DataTableCell :has-interactive-content="true">
    <AppGroup
      align="center"
      justify="end"
      class="w-full"
    >
      <VcTooltip
        v-if="props.hasUserImpersonatePermission"
        :delay-duration="500"
      >
        <template #trigger>
          <VcIconButton
            :label="i18n.t('module.user.overview.actions.impersonate')"
            :class-config="{
              root: 'size-8 p-md hover:bg-brand-primary rounded-md',
              icon: 'size-4 text-brand-primary',
            }"
            variant="tertiary"
            size="sm"
            icon="impersonate"
            @click="onImpersonate"
          />
        </template>
        <template #content>
          <span class="p-md text-xs">{{ i18n.t('module.user.overview.actions.impersonate') }}</span>
        </template>
      </VcTooltip>
    </AppGroup>
  </DataTableCell>
</template>
