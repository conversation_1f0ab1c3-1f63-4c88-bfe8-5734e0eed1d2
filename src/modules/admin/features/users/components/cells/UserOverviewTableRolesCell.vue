<script setup lang="ts">
import {
  VcBadge,
  VcTooltip,
} from '@wisemen/vue-core-components'
import { useI18n } from 'vue-i18n'

import AppGroup from '@/components/app/AppGroup.vue'
import DataTableCell from '@/components/table/data-table/DataTableCell.vue'
import type { UserIndex } from '@/models/user/index/userIndex.model'

const props = defineProps<{
  roles: UserIndex['roles']
}>()

const i18n = useI18n()

const ROLE_LIMIT = 2
</script>

<template>
  <DataTableCell>
    <AppGroup v-if="props.roles.length > 0">
      <VcBadge
        v-for="role of props.roles.slice(0, ROLE_LIMIT)"
        :key="role.uuid"
        color="brand"
        variant="translucent"
        size="sm"
        class="capitalize"
      >
        {{ role.name }}
      </VcBadge>

      <VcTooltip
        v-if="props.roles.length > ROLE_LIMIT"
        :delay-duration="500"
      >
        <template #trigger>
          <VcBadge
            color="brand"
            variant="translucent"
            size="sm"
          >
            +{{ props.roles.length - ROLE_LIMIT }}
          </VcBadge>
        </template>
        <template #content>
          <div class="p-md">
            <div
              v-for="role of props.roles.slice(ROLE_LIMIT)"
              :key="role.uuid"
              class="text-xs capitalize"
            >
              • {{ role.name }}
            </div>
          </div>
        </template>
      </VcTooltip>
    </AppGroup>

    <span
      v-else
      class="text-disabled text-xs"
    >
      {{ i18n.t('shared.no_roles_assigned') }}
    </span>
  </DataTableCell>
</template>
