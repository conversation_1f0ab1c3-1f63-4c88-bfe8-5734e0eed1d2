<script setup lang="ts">
import { VcAvatar } from '@wisemen/vue-core-components'

import AppGroup from '@/components/app/AppGroup.vue'
import DataTableCell from '@/components/table/data-table/DataTableCell.vue'

const props = defineProps<{
  firstName: string
  lastName: string
}>()
</script>

<template>
  <DataTableCell :is-primary-cell="true">
    <span
      v-if="props.firstName === ''"
      class="text-disabled"
    >
      -
    </span>

    <AppGroup
      v-else
      gap="lg"
    >
      <VcAvatar
        :name="`${props.firstName} ${props.lastName}`"
        :class-config="{
          root: 'size-9',
          fallback: 'text-xs font-semibold',
        }"
      />
      {{ props.firstName }} {{ props.lastName }}
    </AppGroup>
  </DataTableCell>
</template>
