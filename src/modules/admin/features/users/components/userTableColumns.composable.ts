import type { TableColumn } from '@wisemen/vue-core-components'
import { VcTableCell } from '@wisemen/vue-core-components'
import type { VNode } from 'vue'
import { h } from 'vue'

import type { UserIndexView } from '@/client'
import { DynamicColumnNames } from '@/client'
import { i18nPlugin } from '@/plugins/i18n.plugin'

export function useUserTableColumns(): Partial<{
  [K in DynamicColumnNames]: (
  label: string,
  isSortable: boolean,
  ) => TableColumn<UserIndexView>
}> {
  return { [DynamicColumnNames.TYPE]: useTypeColumn }
}

export function useEmailColumn(
  label: string,
  isSortable: boolean,
): TableColumn<UserIndexView> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, { isPrimaryCell: true }, () => row.email),
    headerLabel: label,
    key: 'email',
  }
}

export function useFirstNameColumn(
  label: string,
  isSortable: boolean,
): TableColumn<UserIndexView> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.firstName ?? '-'),
    headerLabel: label,
    key: 'firstName',
  }
}

export function useLastNameColumn(
  label: string,
  isSortable: boolean,
): TableColumn<UserIndexView> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.lastName ?? '-'),
    headerLabel: label,
    key: 'lastName',
  }
}

export function useRolesColumn(
  label: string,
  isSortable: boolean,
): TableColumn<UserIndexView> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.roles.map((role) => role.name).join(', ') || '-'),
    headerLabel: label,
    key: 'roles',
  }
}

const EMAIL_DOMAIN = '@indaver.com'

export function useTypeColumn(
  label: string,
  isSortable: boolean,
): TableColumn<UserIndexView> {
  return {
    isSortable,
    cell: (row): VNode => {
      const isInternal = row.email.endsWith(EMAIL_DOMAIN)

      return h(VcTableCell, () => isInternal
        ? i18nPlugin.global.t('module.user.overview.table.internal')
        : i18nPlugin.global.t('module.user.overview.table.external'))
    },
    headerLabel: label,
    key: 'type',
  }
}
