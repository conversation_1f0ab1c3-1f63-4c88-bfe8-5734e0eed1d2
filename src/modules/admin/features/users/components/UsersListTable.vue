<script setup lang="ts">
import type { TableColumn } from '@wisemen/vue-core-components'
import type { VNode } from 'vue'
import {
  computed,
  h,
} from 'vue'
import { useI18n } from 'vue-i18n'

import SearchField from '@/components/search-field/SearchField.vue'
import DataTable from '@/components/table/data-table/DataTable.vue'
import DataTableCell from '@/components/table/data-table/DataTableCell.vue'
import type { UserIndex } from '@/models/user/index/userIndex.model.ts'
import UserOverviewTableEmailCell from '@/modules/admin/features/users/components/cells/UserOverviewTableEmailCell.vue'
import UserOverviewTableNameCell from '@/modules/admin/features/users/components/cells/UserOverviewTableNameCell.vue'
import UserOverviewTableRolesCell from '@/modules/admin/features/users/components/cells/UserOverviewTableRolesCell.vue'
import UsersListTableActionsCell from '@/modules/admin/features/users/components/cells/UsersListTableActionsCell.vue'
import type { TableProps } from '@/types/table.type.ts'

const props = defineProps<TableProps<UserIndex> & {
  hasUserImpersonatePermission?: boolean
  hasUserManagePermission?: boolean
}>()

const emit = defineEmits<{
  userEdit: [user: UserIndex]
  userImpersonate: [user: UserIndex]
}>()

const i18n = useI18n()

const columns = computed<TableColumn<UserIndex>[]>(() =>
  [
    {
      cell: (user): VNode => h(DataTableCell, () => user.upn),
      headerLabel: i18n.t('module.user.columns.upn'),
      key: 'upn',
    },
    {
      cell: (user): VNode => h(UserOverviewTableNameCell, {
        firstName: user.firstName ?? '',
        lastName: user.lastName ?? '',
      }),
      headerLabel: i18n.t('module.user.name'),
      key: 'name',
    },
    {
      cell: (user): VNode => h(UserOverviewTableEmailCell, { email: user.email }),
      headerLabel: i18n.t('module.user.email'),
      key: 'email',
    },
    {
      cell: (user): VNode => h(UserOverviewTableRolesCell, { roles: user.roles }),
      headerLabel: i18n.t('module.user.roles'),
      key: 'roles',
    },
    {
      cell: (user): VNode => h(UsersListTableActionsCell, {
        hasUserImpersonatePermission: props.hasUserImpersonatePermission ?? false,
        hasUserManagePermission: props.hasUserManagePermission ?? false,
        user,
        onImpersonate: () => emit('userImpersonate', user),
      }),
      headerLabel: i18n.t('shared.actions'),
      key: 'actions',
    },
  ])
</script>

<template>
  <DataTable
    v-bind="props"
    :columns="columns"
    :is-first-column-sticky="true"
    :get-key="(user) => user.uuid"
  >
    <template #top>
      <SearchField
        v-if="props.search"
        :search="props.search"
        class="p-md"
      />
    </template>
  </DataTable>
</template>
