<script setup lang="ts">
import {
  useVcDialog,
  useVcToast,
  Vc<PERSON>utton,
} from '@wisemen/vue-core-components'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'

import { Permission } from '@/client'
import AppPage from '@/components/layout/page/AppPage.vue'
import { useApiErrorToast } from '@/composables/api-error-toast/apiErrorToast.composable.ts'
import { useDocumentTitle } from '@/composables/document-title/documentTitle.composable'
import { useSearch } from '@/composables/search/search.composable'
import type { UserIndex } from '@/models/user/index/userIndex.model'
import { useSyncEntraUsersMutation } from '@/modules/admin/api/mutations/syncEntraUsers.mutation'
import { useUserIndexQuery } from '@/modules/admin/api/queries/userIndex.query.ts'
import UsersListTable from '@/modules/admin/features/users/components/UsersListTable.vue'
import { useAuthStore } from '@/stores/auth.store'
import { useImpersonationStore } from '@/stores/impersonation.store'

const i18n = useI18n()
const router = useRouter()
const authStore = useAuthStore()
const impersonationStore = useImpersonationStore()

const documentTitle = useDocumentTitle()

const confirmDialog = useVcDialog({ component: () => import('@/components/dialog/AppConfirmDialog.vue') })

function onShowImpersonationDialog(user: UserIndex): void {
  confirmDialog.open({
    title: i18n.t('module.user.impersonation.title'),
    isDestructive: false,
    cancelText: i18n.t('shared.cancel'),
    confirmText: i18n.t('shared.start_impersonation'),
    description: i18n.t('module.user.impersonation.description', { user: user.firstName }),
    onConfirm: () => handleUserImpersonate(user),
  })
}

documentTitle.set(() => i18n.t('module.user.overview.title'))

const search = useSearch()
const toast = useVcToast()
const apiErrorToast = useApiErrorToast()

const {
  isLoading,
  data,
  error,
  fetchNextPage,
} = useUserIndexQuery({ params: { search: search.debouncedSearch } })

const syncEntraUsersMutation = useSyncEntraUsersMutation()

async function handleUserImpersonate(user: UserIndex): Promise<void> {
  try {
    await impersonationStore.startImpersonation(user)
    confirmDialog.close()
    await router.push({ name: 'dashboard-overview' })
    window.location.reload()
  }
  catch (error) {
    apiErrorToast.show(error)
  }
}

async function onSyncUsers(): Promise<void> {
  try {
    await syncEntraUsersMutation.execute()
    toast.success({
      title: i18n.t('module.user.overview.sync_users_success_title'),
      description: i18n.t('module.user.overview.sync_users_success_message'),
    })
  }
  catch (error) {
    apiErrorToast.show(error)
  }
}
</script>

<template>
  <AppPage
    :no-content-padding="true"
    :title="i18n.t('module.user.overview.title')"
  >
    <template #header-actions>
      <VcButton
        :is-loading="syncEntraUsersMutation.isLoading.value"
        @click="onSyncUsers"
      >
        {{ i18n.t('module.user.overview.sync_users') }}
      </VcButton>
    </template>

    <template #default>
      <UsersListTable
        :search="search"
        :is-loading="isLoading"
        :data="data.data"
        :error="error"
        :has-user-impersonate-permission="authStore.hasPermission(Permission.USER_IMPERSONATE)"
        :has-user-manage-permission="authStore.hasPermission(Permission.USER_MANAGE)"
        @next-page="fetchNextPage"
        @user-impersonate="onShowImpersonationDialog"
      />
    </template>
  </AppPage>
</template>
