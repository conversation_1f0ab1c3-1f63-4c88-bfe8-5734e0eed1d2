import { useOffsetInfiniteQuery } from '@/composables/offset-infinite-query/offsetInfiniteQuery.composable'
import type { UserIndexQueryParams } from '@/models/user/index/userIndexQueryParams.model.ts'
import { UserService } from '@/modules/admin/api/services/user.service.ts'
import type { InfiniteQueryOptions } from '@/types/query.type'

// eslint-disable-next-line ts/explicit-function-return-type
export function useUserIndexQuery(options: InfiniteQueryOptions<UserIndexQueryParams>) {
  return useOffsetInfiniteQuery({
    queryFn: (pagination) => {
      return UserService.getAll({
        pagination,
        search: options.params.search.value,
      })
    },
    queryKey: { userIndex: { queryParams: options.params } },
  })
}
