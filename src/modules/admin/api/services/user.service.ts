import {
  syncEntraUsersV1,
  viewUserIndexV1,
} from '@/client'
import type { UserIndex } from '@/models/user/index/userIndex.model.ts'
import { UserIndexTransformer } from '@/models/user/index/userIndex.transformer.ts'
import type { UserIndexQueryParams } from '@/models/user/index/userIndexQueryParams.model.ts'
import { UserIndexQueryParamsTransformer } from '@/models/user/index/userIndexQueryParams.transformer.ts'
import type {
  OffsetPagination,
  OffsetPaginationResponse,
} from '@/types/pagination.type.ts'

export class UserService {
  static async getAll(
    params: OffsetPagination<UserIndexQueryParams>,
  ): Promise<OffsetPaginationResponse<UserIndex>> {
    const response = await viewUserIndexV1({ query: UserIndexQueryParamsTransformer.toDto(params) })

    return {
      data: response.data.items.map(UserIndexTransformer.fromDto),
      meta: response.data.meta,
    }
  }

  static async sync(): Promise<void> {
    await syncEntraUsersV1()
  }
}
