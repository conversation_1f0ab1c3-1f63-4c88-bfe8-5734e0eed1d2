import type { UseMutationReturnType } from '@wisemen/vue-core-query'
import { useMutation } from '@wisemen/vue-core-query'

import { UserService } from '@/modules/admin/api/services/user.service'

export function useSyncEntraUsersMutation(): UseMutationReturnType<void, void> {
  return useMutation<void, void>({
    queryFn: async () => {
      await UserService.sync()
    },
    queryKeysToInvalidate: { userIndex: {} },
  })
}
