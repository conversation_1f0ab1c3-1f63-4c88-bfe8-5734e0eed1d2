<script setup lang="ts">
import type { Icon } from '@wisemen/vue-core-components'
import { computed } from 'vue'

import AppGroup from '@/components/app/AppGroup.vue'
import type {
  IconTileFill,
  IconTileVariant,
} from '@/components/app/AppIconTile.vue'
import AppIconTile from '@/components/app/AppIconTile.vue'
import type { CardVariant } from '@/components/app/card/AppCard.vue'
import AppCard from '@/components/app/card/AppCard.vue'
import AppCardClearButton from '@/components/app/card/AppCardClearButton.vue'

const props = withDefaults(defineProps<{
  title: string
  hasClearButton?: boolean
  isDisabled?: boolean
  isSelected: boolean
  description?: string | null
  icon: Icon | null
  variant: IconTileVariant
}>(), {
  hasClearButton: false,
  description: null,
})

const emit = defineEmits<{
  clear: []
}>()

const cardVariant = computed<CardVariant>(() => {
  if (props.isSelected) {
    return 'brand'
  }
  if (props.isDisabled) {
    return 'disabled'
  }

  return 'transparent'
})

const iconVariant = computed<IconTileVariant>(() => {
  if (props.isSelected) {
    return 'brand'
  }

  return props.variant
})

const iconTileFill = computed<IconTileFill>(() => {
  if (props.isSelected) {
    return 'solid'
  }

  return 'tint'
})

function onClear(): void {
  emit('clear')
}
</script>

<template>
  <AppCard
    :variant="cardVariant"
    :is-disabled="props.isDisabled"
    class="relative flex overflow-hidden"
  >
    <AppGroup
      justify="between"
      class="w-full overflow-hidden"
    >
      <AppGroup
        :align="props.description === null ? 'center' : 'start'"
        gap="lg"
        class="overflow-hidden"
      >
        <AppIconTile
          v-if="props.icon !== null"
          :icon="props.icon"
          :variant="iconVariant"
          :fill="iconTileFill"
          :class="{
            'mt-xs': props.description !== null,
          }"
          class="
            duration-200
            group-hover:brightness-98
          "
        />

        <AppGroup
          :align="props.description === null ? 'center' : 'start'"
          gap="none"
          direction="col"
          class="overflow-hidden"
        >
          <span
            :class="{
              'text-secondary': cardVariant !== 'disabled',
              'text-disabled': cardVariant === 'disabled',
            }"
            class="w-full truncate text-left text-sm font-semibold"
          >
            {{ props.title }}
          </span>

          <span
            v-if="props.description !== null"
            :class="{
              'text-tertiary': cardVariant !== 'disabled',
              'text-disabled': cardVariant === 'disabled',
            }"
            class="line-clamp-2 text-start text-sm"
          >
            {{ props.description }}
          </span>
        </AppGroup>
      </AppGroup>

      <slot name="right" />
    </AppGroup>

    <AppCardClearButton
      v-if="props.hasClearButton && props.isSelected"
      @click="onClear"
    />
  </AppCard>
</template>
