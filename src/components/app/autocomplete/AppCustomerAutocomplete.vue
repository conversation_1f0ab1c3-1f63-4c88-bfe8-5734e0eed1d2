<script setup lang="ts">
import {
  VcAutocomplete,
  VcSelectItem,
  VcTooltip,
} from '@wisemen/vue-core-components'
import {
  computed,
  ref,
} from 'vue'
import { useI18n } from 'vue-i18n'

import AppGroup from '@/components/app/AppGroup.vue'
import { AddressUtil } from '@/models/address/address.util'
import type { CustomerIndex } from '@/models/customer/index/customerIndex.model'
import { useCustomerIndexQuery } from '@/modules/waste-inquiry/api/queries/customerIndex.query'

const props = withDefaults(defineProps<{
  isRequired?: boolean
  isTouched?: boolean
  errors?: string[]
  label?: string | null
}>(), {
  isRequired: false,
  isTouched: false,
  errors: () => [],
  label: null,
})

const modelValue = defineModel<CustomerIndex | null>({ required: true })

const i18n = useI18n()
const search = ref<string>('')

const customerIndexQuery = useCustomerIndexQuery({ params: { search } })

const customers = computed<CustomerIndex[]>(() => customerIndexQuery.data?.value?.data ?? [])
const isFetching = computed<boolean>(() => customerIndexQuery.isFetching.value)

function onCustomerSearch(value: string): void {
  search.value = value
}
</script>

<template>
  <VcAutocomplete
    v-model="modelValue"
    :display-fn="(value) => value.name"
    :is-loading="isFetching"
    :items="customers"
    :is-required="props.isRequired"
    :is-touched="props.isTouched"
    :errors="props.errors"
    :label="props.label"
    :placeholder="i18n.t('component.customer_autocomplete.placeholder')"
    icon-left="search"
    @search="onCustomerSearch"
  >
    <template #item="{ value }">
      <VcSelectItem :value="value">
        <VcTooltip>
          <template #trigger>
            <AppGroup
              class="truncate"
              gap="xs"
            >
              <span>
                {{ value.name }}
              </span>
              <span class="text-tertiary">
                {{ value.id }}
              </span>
              <span
                v-if="value.address !== null"
                class="text-tertiary truncate"
              >
                - {{ AddressUtil.format(value.address) }}
              </span>
            </AppGroup>
          </template>
          <template #content>
            <div class="gap-xs py-sm px-md flex max-w-75 flex-col text-sm">
              <p> {{ value.name }} </p>
              <p class="text-tertiary">
                {{ value.id }}
              </p>
              <p
                v-if="value.address !== null"
                class="text-tertiary"
              >
                {{ AddressUtil.format(value.address) }}
              </p>
            </div>
          </template>
        </VcTooltip>
      </VcSelectItem>
    </template>
  </VcAutocomplete>
</template>
