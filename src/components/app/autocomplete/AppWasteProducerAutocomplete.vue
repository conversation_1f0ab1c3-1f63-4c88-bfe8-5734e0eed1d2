<script setup lang="ts">
import {
  VcAutocomplete,
  VcSelectItem,
  VcTooltip,
} from '@wisemen/vue-core-components'
import {
  computed,
  ref,
} from 'vue'
import { useI18n } from 'vue-i18n'

import AppGroup from '@/components/app/AppGroup.vue'
import { AddressUtil } from '@/models/address/address.util'
import type { WasteProducerIndex } from '@/models/waste-producer/index/wasteProducerIndex.model'
import type { WasteProducerIndexQueryParams } from '@/models/waste-producer/index/wasteProducerIndexQueryParams.model.ts'
import { useWasteProducerIndexQuery } from '@/modules/waste-inquiry/api/queries/wasteProducerIndex.query'

const props = withDefaults(defineProps<{
  customerId: string
  isRequired?: boolean
  isTouched?: boolean
  errors?: string[]
  label?: string | null
}>(), {
  isRequired: false,
  isTouched: false,
  errors: () => [],
  label: null,
})

const modelValue = defineModel<WasteProducerIndex | null>({ required: true })

const i18n = useI18n()
const search = ref<string>('')

const filters = computed<WasteProducerIndexQueryParams['filters']>(() => ({ customerId: props.customerId }))

const wasteProducerIndexQuery = useWasteProducerIndexQuery({
  params: {
    filters,
    search,
  },
})

const wasteProducers = computed<WasteProducerIndex[]>(() => wasteProducerIndexQuery.data?.value?.data ?? [])
const isFetching = computed<boolean>(() => wasteProducerIndexQuery.isFetching.value)
const isDebouncing = ref<boolean>(false)

function onWasteProducerSearch(value: string): void {
  search.value = value
}
</script>

<template>
  <VcAutocomplete
    v-model="modelValue"
    :display-fn="(value) => value.name"
    :is-loading="isFetching || isDebouncing"
    :items="wasteProducers"
    :is-required="props.isRequired"
    :is-touched="props.isTouched"
    :errors="props.errors"
    :label="props.label"
    :clear-search-on-selection="true"
    :placeholder="i18n.t('component.waste_producer_autocomplete.placeholder')"
    popover-align="end"
    icon-left="search"
    @search="onWasteProducerSearch"
  >
    <template #item="{ value }">
      <VcSelectItem :value="value">
        <VcTooltip>
          <template #trigger>
            <AppGroup
              class="truncate"
              gap="xs"
            >
              <span>
                {{ value.name }}
              </span>
              <span class="text-tertiary">
                {{ value.id }}
              </span>
              <span
                v-if="value.address !== null"
                class="text-tertiary truncate"
              >
                - {{ AddressUtil.format(value.address) }}
              </span>
            </AppGroup>
          </template>
          <template #content>
            <div class="gap-xs py-sm px-md flex max-w-75 flex-col text-sm">
              <p> {{ value.name }} </p>
              <p class="text-tertiary">
                {{ value.id }}
              </p>
              <p
                v-if="value.address !== null"
                class="text-tertiary"
              >
                {{ AddressUtil.format(value.address) }}
              </p>
            </div>
          </template>
        </VcTooltip>
      </VcSelectItem>
    </template>
  </VcAutocomplete>
</template>
