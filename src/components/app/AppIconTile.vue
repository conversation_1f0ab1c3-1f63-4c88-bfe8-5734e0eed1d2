<script setup lang="ts">
import type { Icon } from '@wisemen/vue-core-components'
import { VcIcon } from '@wisemen/vue-core-components'

import { UuidToColorUtil } from '@/utils/uuidToColor.util'

export type IconTileVariant = 'brand' | 'error' | 'gray-darker' | 'gray-light' | 'light-blue' | 'moss' | 'purple' | 'success' | 'teal' | 'warning'
export type IconTileFill = 'solid' | 'tint'

const props = defineProps<{
  fill: IconTileFill
  icon: Icon
  variant: IconTileVariant
}>()
</script>

<template>
  <div
    :class="UuidToColorUtil.getBackgroundColorClassFromColorVariant(props.variant, props.fill)"
    class="flex size-10 shrink-0 items-center justify-center rounded-lg"
  >
    <VcIcon
      :icon="props.icon"
      :class="UuidToColorUtil.getForegroundColorClassFromColor(props.variant, props.fill)"
      class="size-7"
    />
  </div>
</template>
