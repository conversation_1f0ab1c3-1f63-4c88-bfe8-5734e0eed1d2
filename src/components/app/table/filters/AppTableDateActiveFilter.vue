<script setup lang="ts" generic="TPagination extends BasePagination">
import type {
  BasePagination,
  FilterChangeEvent,
  Pagination,
} from '@wisemen/vue-core-components'
import {
  VcDateField,
  VcIconButton,
} from '@wisemen/vue-core-components'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import type { DynamicTable } from '@/composables/dynamic-table/dynamicTable.composable'
import type { TableDateFilter } from '@/types/tableFilter.type.ts'

const props = defineProps<{
  dynamicTable: DynamicTable | null
  filter: TableDateFilter<TPagination['filter']>
  pagination: Pagination<TPagination>
}>()

const i18n = useI18n()

const modelValue = computed<Date | null>(() => {
  const filters = props.pagination.paginationOptions.value.filter
  const value = filters?.[props.filter.key] as Date | undefined

  return value ?? null
})

function onUpdateModelValue(value: Date | null): void {
  const event = { [props.filter.key]: value } as FilterChangeEvent<TPagination['filter']>

  if (props.filter.onUpdate) {
    props.filter.onUpdate(value)
  }

  props.pagination.handleFilterChange(event)

  if (props.filter.dynamicTableColumnUuid === null || value === null || props.dynamicTable === null) {
    return
  }

  props.dynamicTable.updateFilter({
    uuid: props.filter.dynamicTableColumnUuid,
    value: value.toISOString(),
  })
}

function onClear(): void {
  const event = { [props.filter.key]: undefined } as FilterChangeEvent<TPagination['filter']>

  props.pagination.handleFilterChange(event)

  if (props.filter.dynamicTableColumnUuid === null) {
    return
  }

  if (props.dynamicTable === null) {
    return
  }

  props.dynamicTable.updateFilter({
    uuid: props.filter.dynamicTableColumnUuid,
    value: '',
  })
}
</script>

<template>
  <div class="flex items-center">
    <VcDateField
      :model-value="modelValue"
      :placeholder="props.filter.placeholder"
      :class-config="{
        root: 'rounded-r-none rounded-l-full w-34',
      }"
      @update:model-value="onUpdateModelValue"
      @clear="onClear"
    />
    <VcIconButton
      :label="i18n.t('shared.close')"
      :class-config="{
        icon: 'size-4',
        root: 'rounded-l-none rounded-r-full w-8 border-secondary border-l-0',
      }"
      variant="secondary"
      icon="close"
      @click="onClear"
    />
  </div>
</template>
