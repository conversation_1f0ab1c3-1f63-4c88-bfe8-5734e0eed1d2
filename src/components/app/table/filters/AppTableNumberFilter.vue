<script setup lang="ts" generic="TPagination extends BasePagination">
import type {
  BasePagination,
  FilterChangeEvent,
  Pagination,
} from '@wisemen/vue-core-components'
import {
  VcIconButton,
  VcNumberField,
} from '@wisemen/vue-core-components'
import {
  computed,
  useId,
} from 'vue'
import { useI18n } from 'vue-i18n'

import type { DynamicTable } from '@/composables/dynamic-table/dynamicTable.composable'
import type { TableNumberFilter } from '@/types/tableFilter.type.ts'

const props = defineProps<{
  dynamicTable: DynamicTable | null
  filter: TableNumberFilter<TPagination['filter']>
  pagination: Pagination<TPagination>
}>()

const id = useId()
const i18n = useI18n()

const modelValue = computed<number | null>(() => {
  const filters = props.pagination.paginationOptions.value.filter
  const value = filters?.[props.filter.key] as number | undefined

  return value ?? null
})

function onUpdateModelValue(value: number | null): void {
  const event = { [props.filter.key]: value } as FilterChangeEvent<TPagination['filter']>

  if (props.filter.onUpdate) {
    props.filter.onUpdate(value)
  }

  props.pagination.handleFilterChange(event)

  if (props.filter.dynamicTableColumnUuid === null || value === null || props.dynamicTable === null) {
    return
  }

  props.dynamicTable.updateFilter({
    uuid: props.filter.dynamicTableColumnUuid,
    value: value.toString(),
  })
}

function onClear(): void {
  const event = { [props.filter.key]: undefined } as FilterChangeEvent<TPagination['filter']>

  props.pagination.handleFilterChange(event)

  if (props.filter.dynamicTableColumnUuid === null) {
    return
  }

  if (props.dynamicTable === null) {
    return
  }

  props.dynamicTable.updateFilter({
    uuid: props.filter.dynamicTableColumnUuid,
    value: '',
  })
}
</script>

<template>
  <div>
    <label
      :for="id"
      class="px-lg text-sm font-semibold"
    >
      {{ props.filter.label }}
    </label>

    <div class="flex items-center">
      <VcNumberField
        :id="id"
        :model-value="modelValue"
        :placeholder="props.filter.placeholder"
        :class-config="{
          root: 'rounded-r-none rounded-l-xl w-full mx-lg my-xs',
        }"
        :min="props.filter.min"
        :max="props.filter.max"
        :format-options="props.filter.formatOptions"
        :hide-controls="props.filter.hideControls"
        class="w-full"
        @update:model-value="onUpdateModelValue"
        @clear="onClear"
      />
      <VcIconButton
        :label="i18n.t('shared.close')"
        :class-config="{
          icon: 'size-4',
          root: 'rounded-l-none rounded-r-xl w-8 border-secondary border-l-0 mx-3',
        }"
        variant="secondary"
        icon="close"
        @click="onClear"
      />
    </div>
  </div>
</template>
