<script setup lang="ts">
import { VcIcon } from '@wisemen/vue-core-components'
import { useI18n } from 'vue-i18n'

const i18n = useI18n()
</script>

<template>
  <div
    class="
      absolute top-1/2 left-1/2 z-12 w-75 -translate-x-1/2 -translate-y-1/2
      text-center
    "
  >
    <div
      aria-hidden="true"
      class="text-secondary mx-auto size-6"
    >
      <VcIcon icon="search" />
    </div>
    <p class="text-primary mt-xl text-lg font-semibold">
      {{ i18n.t('component.table.no_results.title') }}
    </p>
    <p class="text-secondary pb-xl pt-md mx-auto max-w-92 text-sm">
      {{ i18n.t('component.table.no_results.description') }}
    </p>
  </div>
</template>
