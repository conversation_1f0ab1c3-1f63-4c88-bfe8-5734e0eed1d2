<script setup lang="ts">
import {
  useVcToast,
  VcCheckbox,
  VcDialog,
  VcTextField,
} from '@wisemen/vue-core-components'
import { useForm } from 'formango'
import { useI18n } from 'vue-i18n'

import { useDynamicTableUpdateViewMutation } from '@/api/mutations/dynamicTableUpdateView.mutation'
import { Permission } from '@/client/types.gen'
import AppGrid from '@/components/app/AppGrid.vue'
import AppHeightTransition from '@/components/app/AppHeightTransition.vue'
import AppDialogActionCancel from '@/components/app/dialog/AppDialogActionCancel.vue'
import AppDialogActionPrimary from '@/components/app/dialog/AppDialogActionPrimary.vue'
import AppDialogActions from '@/components/app/dialog/AppDialogActions.vue'
import AppDialogContent from '@/components/app/dialog/AppDialogContent.vue'
import AppDialogHeader from '@/components/app/dialog/AppDialogHeader.vue'
import AppForm from '@/components/form/AppForm.vue'
import { useApiErrorToast } from '@/composables/api-error-toast/apiErrorToast.composable'
import { dynamicTableUpdateViewFormSchema } from '@/models/dynamic-table/update/dynamicTableUpdateViewForm.model'
import type { DynamicTableViewIndex } from '@/models/dynamic-table/view/dynamicTableViewIndex.model'
import type { DynamicTableName } from '@/models/enums/dynamicTableName.enum'
import { useAuthStore } from '@/stores/auth.store'
import { toFormField } from '@/utils/formango.util'

const props = withDefaults(defineProps<{
  disableUncheckGlobalDefault: boolean
  tableName: DynamicTableName
  view: DynamicTableViewIndex
}>(), { disableUncheckGlobalDefault: false })

const emit = defineEmits<{
  saveView: [ viewName: DynamicTableName ]
}>()

const i18n = useI18n()
const toast = useVcToast()
const apiErrorToast = useApiErrorToast()
const authStore = useAuthStore()

const isUserAdmin = authStore.isSystemAdmin()
const hasManagePermission = authStore.hasPermission(Permission.DYNAMIC_TABLE_VIEW_MANAGE)

const dynamicTableUpdateViewMutation = useDynamicTableUpdateViewMutation()

const form = useForm({
  initialState: {
    isDefault: props.view.isUserDefault,
    isGlobal: props.view.isGlobal,
    isGlobalDefault: props.view.isDefaultGlobal,
    filters: props.view.filters,
    sorts: props.view.sorts.map((sort) => ({
      columnUuid: sort.columnUuid,
      direction: sort.direction,
    })),
    viewName: props.view.name,
    visibleColumns: props.view.visibleColumns.map((column) => ({ columnUuid: column.columnUuid })),
  },
  schema: dynamicTableUpdateViewFormSchema,
  onSubmit: async (values) => {
    try {
      await dynamicTableUpdateViewMutation.execute({
        body: values,
        params: {
          viewUuid: props.view.uuid,
          tableName: props.tableName,
        },
      })

      toast.info({
        title: i18n.t('component.table.dynamic_view.view_saved'),
        description: i18n.t('component.table.dynamic_view.view_updated_description', { name: values.viewName }),
      })

      onSave(values.viewName as DynamicTableName)
    }
    catch (error) {
      apiErrorToast.show(error)
    }
  },
})

const name = form.register('viewName')
const isGlobal = form.register('isGlobal', false)
const isGlobalDefault = form.register('isGlobalDefault', false)
const isDefault = form.register('isDefault', false)

function onSave(viewName: DynamicTableName): void {
  emit('saveView', viewName)
}

function onSubmit(): void {
  form.submit()
}

function onUpdateGlobal(value: boolean): void {
  if (value === false) {
    isGlobalDefault.setValue(false)
  }
}
</script>

<template>
  <VcDialog
    class="w-dialog-md"
  >
    <AppDialogContent>
      <AppDialogHeader
        :is-description-hidden="true"
        :title="i18n.t('component.table.dynamic_view.update_filter_view')"
        :description="i18n.t('shared.update')"
      />

      <AppForm
        :form="form"
        class="mt-lg"
      >
        <AppGrid :cols="1">
          <VcTextField
            v-bind="toFormField(name)"
            :is-disabled="view.isGlobal && !hasManagePermission"
            :label="i18n.t('component.table.dynamic_view.view_name')"
            :placeholder="i18n.t('component.table.dynamic_view.view_name_placeholder')"
          />
          <div
            v-if="isUserAdmin"
            class="px-xl py-lg border-secondary rounded-md border"
          >
            <VcCheckbox
              v-bind="toFormField(isGlobal)"
              :is-disabled="isGlobalDefault.modelValue.value && props.disableUncheckGlobalDefault"
              :label="i18n.t('component.table.dynamic_view.save_as_global_view')"
              @update:model-value="onUpdateGlobal"
            />
            <AppHeightTransition>
              <VcCheckbox
                v-if="isGlobal.modelValue.value"
                v-bind="toFormField(isGlobalDefault)"
                :is-disabled="isGlobalDefault.modelValue.value && props.disableUncheckGlobalDefault"
                :label="i18n.t('component.table.dynamic_view.save_as_default_view_globally')"
                class="pt-sm pl-3xl"
              />
            </AppHeightTransition>
          </div>
          <div class="px-xl py-lg border-secondary rounded-md border">
            <VcCheckbox
              v-bind="toFormField(isDefault)"
              :label="i18n.t('component.table.dynamic_view.save_as_default_view_for_me')"
            />
          </div>
        </AppGrid>
      </AppForm>

      <AppDialogActions>
        <AppDialogActionCancel :label="i18n.t('shared.cancel')" />
        <AppDialogActionPrimary
          :is-loading="form.isSubmitting.value"
          :label="i18n.t('component.table.dynamic_view.update')"
          @confirm="onSubmit"
        />
      </AppDialogActions>
    </AppDialogContent>
  </VcDialog>
</template>
