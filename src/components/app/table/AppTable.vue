<script setup lang="ts" generic="TSchema, TPagination extends BasePagination">
import type {
  BasePagination,
  PaginatedData,
  Pagination,
  TableColumn,
} from '@wisemen/vue-core-components'
import {
  VcTableBody,
  VcTableContent,
  VcTableEmptyOrLoadingState,
  VcTableHeader,
  VcTableHiddenResultsHint,
  VcTableRoot,
  VcTableScrollContainer,
} from '@wisemen/vue-core-components'
import type { VNode } from 'vue'

const props = defineProps<{
  /**
   * wether the table has a border
   * @default false
   */
  hasBorder?: boolean
  /**
   * Whether the first column is sticky
   * @default false
   */
  isFirstColumnSticky?: boolean
  /**
   * Whether the last column is sticky
   * @default false
   */
  isLastColumnSticky?: boolean
  /**
   * Whether the table is loading
   */
  isLoading: boolean
  /**
   * Whether the table results hint is hidden
   * @default false
   */
  isTableResultsHintHidden?: boolean
  /**
   * The columns of the table
   */
  columns: TableColumn<TSchema>[]
  /**
   * The data of the table
   */
  data: PaginatedData<TSchema> | null
  /**
   * The content to show when a row is expanded
   * @default null
   */
  expandedRowContent?: ((row: TSchema) => VNode) | null

  infiniteScroll?: {
    distance?: number
    onNext: () => Promise<void>
  }
  /**
   * The pagination of the table
   */
  pagination: Pagination<TPagination>

  /**
   * The class to apply to the row
   * @default null
   */
  rowClass?: ((row: TSchema, rowIndex: number) => string) | null
}>()
</script>

<template>
  <VcTableRoot
    :is-first-column-sticky="props.isFirstColumnSticky"
    :is-last-column-sticky="props.isLastColumnSticky"
    :is-loading="props.isLoading"
    :columns="props.columns"
    :data="props.data"
    :expanded-row-content="props.expandedRowContent"
    :pagination="props.pagination"
    :row-class="props.rowClass"
    @next-page="props.infiniteScroll?.onNext"
  >
    <slot name="top" />

    <VcTableScrollContainer>
      <VcTableContent>
        <VcTableHeader />
        <VcTableBody />
      </VcTableContent>

      <VcTableHiddenResultsHint v-if="!props.isTableResultsHintHidden" />
    </VcTableScrollContainer>

    <VcTableEmptyOrLoadingState>
      <template #empty-state="{ activeFilterCount }">
        <slot
          :active-filter-count="activeFilterCount"
          name="empty-state"
        />
      </template>
    </VcTableEmptyOrLoadingState>

    <slot name="bottom" />
  </VcTableRoot>
</template>
