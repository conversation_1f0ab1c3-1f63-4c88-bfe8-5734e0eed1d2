<script setup lang="ts">
import {
  VcIcon,
  VcTooltip,
} from '@wisemen/vue-core-components'
import {
  computed,
  useSlots,
} from 'vue'

const props = withDefaults(defineProps<{
  title: string
  isHeaderHidden?: boolean
  isHeaderSeparatorHidden?: boolean
  isTitleHidden?: boolean
  removeContentPadding?: boolean
  subtitle?: string | null
  tooltip?: string | null
}>(), {
  isHeaderHidden: false,
  isHeaderSeparatorHidden: false,
  isTitleHidden: false,
  removeContentPadding: false,
  tooltip: null,
})

const slots = useSlots()

const hasLeftContentSlot = computed<boolean>(() => slots['left-content'] !== undefined)
</script>

<template>
  <main class="flex size-full flex-col overflow-hidden">
    <slot name="header">
      <header
        v-if="!props.isHeaderHidden"
        class="p-3xl pb-xl flex items-center justify-between"
      >
        <div>
          <slot name="backButton" />
          <div class="flex items-center gap-2">
            <h1
              :class="{
                'sr-only': props.isTitleHidden,
              }"
              class="text-primary text-display-xs font-semibold"
            >
              {{ props.title }}
            </h1>

            <slot name="title-right" />

            <VcTooltip v-if="props.tooltip">
              <template #trigger>
                <VcIcon
                  icon="infoCircle"
                  class="mt-xs text-fg-brand-primary size-4"
                />
              </template>
              <template #content>
                <div
                  class="
                    text-secondary px-lg py-md max-w-80 rounded-xl text-center
                    text-xs font-medium
                  "
                >
                  {{ props.tooltip }}
                </div>
              </template>
            </VcTooltip>
          </div>

          <slot name="subtitle" />
        </div>

        <div id="header-actions">
          <slot name="header-actions" />
        </div>
      </header>
    </slot>

    <slot name="tabs" />

    <div class="flex size-full overflow-hidden">
      <div
        v-if="hasLeftContentSlot"
        class="px-2xl -mr-2xl shrink-0 overflow-auto"
      >
        <slot name="left-content" />
      </div>

      <div
        id="page-scroll-container"
        :class="{
          'px-3xl': !props.removeContentPadding,
          'px-0': props.removeContentPadding,
        }"
        class="flex size-full flex-col overflow-y-auto"
      >
        <slot />
      </div>
    </div>

    <div id="page-footer" />
  </main>
</template>
