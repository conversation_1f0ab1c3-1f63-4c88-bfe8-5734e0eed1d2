<script setup lang="ts">
import { RouterView } from 'vue-router'

import { useImpersonationStore } from '@/stores/impersonation.store'

const impersonationStore = useImpersonationStore()
</script>

<template>
  <div
    :class="[
      impersonationStore.isImpersonating
        ? 'from-[#21581C] to-[#072911]'
        : `from-[#178D81] to-[#005048]`,
    ]"
    class="flex size-full flex-1 bg-gradient-to-br to-50%"
  >
    <slot name="sidebar" />

    <div class="flex size-full flex-1 flex-col overflow-hidden">
      <slot name="header" />

      <div
        id="page-content"
        class="relative size-full flex-1 overflow-hidden"
      >
        <div
          id="page-inner-content"
          class="
            bg-primary flex size-full flex-1 origin-top-right overflow-hidden
            rounded-tl-4xl duration-500
          "
        >
          <RouterView />
        </div>
      </div>
    </div>
  </div>
</template>
