<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import { Permission } from '@/client'
import AppDashboardLayoutFloating from '@/components/layout/dashboard/AppDashboardLayoutFloating.vue'
import AppHeader from '@/components/layout/header/AppHeader.vue'
import AppSidebar from '@/components/layout/sidebar/AppSidebar.vue'
import { useGlobalCustomer } from '@/composables/global-customer/globalCustomer.composable'
import { useImpersonationWarning } from '@/composables/impersonation-warning/impersonationWarning.composable.ts'
import type { UserDetail } from '@/models/user/detail/userDetail.model'
import { usePreferencesSync } from '@/modules/settings/composables/preferences.composable'
import { useAuthStore } from '@/stores/auth.store'
import type { NavigationGroup } from '@/types/navigationItem.type'

const i18n = useI18n()
const authStore = useAuthStore()

usePreferencesSync()
useGlobalCustomer()

const impersonationWarning = useImpersonationWarning()

impersonationWarning.startTracking()

const authUser = computed<UserDetail | null>(() => authStore.authUser)

const mainNavigationItems = computed<NavigationGroup[]>(() => [
  {
    items: [
      {
        icon: 'truck',
        label: i18n.t('module.pickup_request.sidebar.title'),
        permissions: [
          Permission.PICK_UP_REQUEST_READ,
          Permission.PICK_UP_REQUEST_MANAGE,
          Permission.WEEKLY_PLANNING_REQUEST_MANAGE,
        ],
        to: { name: 'pickup-request-overview' },
      },
      {
        icon: 'wasteRequest',
        label: i18n.t('module.waste_inquiry.overview.title'),
        permissions: [
          Permission.WASTE_INQUIRY_READ,
          Permission.WASTE_INQUIRY_MANAGE,
        ],
        to: { name: 'waste-inquiry-overview' },
      },
      {
        icon: 'checkVerified',
        label: i18n.t('module.contract.overview.title'),
        permissions: [
          Permission.CONTRACT_LINE_READ,
          Permission.CONTRACT_LINE_MANAGE,
        ],
        to: { name: 'contracts-overview' },
      },
    ],
    label: i18n.t('component.sidebar.group.waste_management'),
  },
  {
    items: [
      {
        icon: 'receipt',
        label: i18n.t('module.invoice.overview.title'),
        permissions: [
          Permission.INVOICE_READ,
          Permission.INVOICE_MANAGE,
        ],
        to: { name: 'invoices-overview' },
      },
      {
        icon: 'certificate',
        label: i18n.t('module.certificate.overview.title'),
        permissions: [
          Permission.CERTIFICATE_READ,
        ],
        to: { name: 'certificates-overview' },
      },
      {
        icon: 'clipboard',
        label: i18n.t('module.guidance_letter.overview.title'),
        permissions: [
          Permission.GUIDANCE_LETTER_READ,
        ],
        to: { name: 'guidance-letters-overview' },
      },
      {
        icon: 'fileAttachement',
        label: i18n.t('module.document.overview.title'),
        permissions: [
          Permission.DOCUMENT_BSC,
          Permission.DOCUMENT_CONTRACT,
          Permission.DOCUMENT_MANUAL,
          Permission.DOCUMENT_MASTER_TABLE,
          Permission.DOCUMENT_MINUTES_AND_PRESENTATIONS,
          Permission.DOCUMENT_QUOTATION,
          Permission.DOCUMENT_TFS,
          Permission.DOCUMENT_TRANSPORT,
        ],
        to: { name: 'documents-overview' },
      },
      {
        icon: 'barChartSquare',
        label: i18n.t('module.reporting.overview.title'),
        link: 'https://app.powerbi.com/groups/me/apps/7be3c9e6-55f1-4b30-afd7-2390911d4d20',
        permissions: [
          Permission.USEFUL_LINK_REPORTING,
        ],
      },
    ],
    label: i18n.t('component.sidebar.group.administration'),
  },
  {
    items: [
      {
        icon: 'settingsSlider',
        label: i18n.t('module.admin.overview.title'),
        permissions: [
          Permission.ROLE_READ,
          Permission.ROLE_MANAGE,
          Permission.ANNOUNCEMENT_MANAGE,
          Permission.NEWS_ITEM_MANAGE,
          Permission.USER_READ,
          Permission.USER_MANAGE,
          Permission.USER_IMPERSONATE,
        ],
        to: { name: 'admin-overview' },
      },
    ],
    label: i18n.t('component.sidebar.group.system_administration'),
  },
])

function onSignOut(): void {
  const logoutUrl = authStore.getLogoutUrl()

  window.location.replace(logoutUrl)
}
</script>

<template>
  <AppDashboardLayoutFloating v-if="authUser !== null">
    <template #sidebar>
      <AppSidebar
        :user="authUser"
        :main-items="mainNavigationItems"
        variant="floating-content"
      />
    </template>

    <template #header>
      <AppHeader
        :user="authUser"
        @sign-out="onSignOut"
      />
    </template>
  </AppDashboardLayoutFloating>
</template>
