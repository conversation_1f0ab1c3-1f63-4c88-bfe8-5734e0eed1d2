<script setup lang="ts">
import { RouterLink } from 'vue-router'

const props = defineProps<{
  isCollapsed: boolean
}>()
</script>

<template>
  <RouterLink
    :to="{ name: 'dashboard-overview' }"
    class="
      relative flex min-h-20 w-full items-center justify-center overflow-hidden
    "
  >
    <img
      :class="[
        props.isCollapsed ? 'max-w-20' : 'py-3xl max-w-25',
      ]"
      class="size-full transition-all duration-200 ease-in-out"
      src="@/assets/svgs/logo-white.svg"
      alt="Logo"
    >
  </RouterLink>
</template>
