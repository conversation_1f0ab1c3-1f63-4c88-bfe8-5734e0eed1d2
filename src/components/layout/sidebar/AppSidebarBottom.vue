<script setup lang="ts">
import {
  VcDropdownMenu,
  VcDropdownMenuGroup,
  VcDropdownMenuItem,
  VcIconButton,
} from '@wisemen/vue-core-components'
import {
  computed,
  ref,
} from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'

import { Permission } from '@/client'
import AppSeparator from '@/components/app/AppSeparator.vue'
import { useApiErrorToast } from '@/composables/api-error-toast/apiErrorToast.composable'
import { usePickupRequestCreateMutation } from '@/modules/pickup-request/api/mutations/pickupRequestCreate.mutation'
import { useWasteInquiryCreateMutation } from '@/modules/waste-inquiry/api/mutations/wasteInquiryCreate.mutation'
import { useAuthStore } from '@/stores/auth.store'

const i18n = useI18n()
const router = useRouter()
const apiErrorToast = useApiErrorToast()

const isCreatingPickup = ref<boolean>(false)
const pickupRequestCreateMutation = usePickupRequestCreateMutation()
const isCreatingWasteInquiry = ref<boolean>(false)
const wasteInquiryCreateMutation = useWasteInquiryCreateMutation()

const authStore = useAuthStore()

const hasPickupRequestPermission = computed<boolean>(() => authStore.hasPermission(Permission.PICK_UP_REQUEST_MANAGE))
const hasWasteInquiryPermission = computed<boolean>(() => authStore.hasPermission(Permission.WASTE_INQUIRY_MANAGE))

async function onNewPickup(): Promise<void> {
  isCreatingPickup.value = true

  try {
    const pickupRequestUuid = await pickupRequestCreateMutation.execute()

    await router.push({
      name: 'pickup-request-update',
      params: { pickupRequestUuid },
    })
  }
  catch (error) {
    isCreatingPickup.value = false
    apiErrorToast.show(error)
  }
}

async function onNewWasteInquiry(): Promise<void> {
  isCreatingWasteInquiry.value = true

  try {
    const wasteInquiry = await wasteInquiryCreateMutation.execute()

    await router.push({
      name: 'waste-inquiry-update',
      params: { wasteInquiryUuid: wasteInquiry.uuid },
    })
  }
  catch (error) {
    isCreatingWasteInquiry.value = false
    apiErrorToast.show(error)
  }
}
</script>

<template>
  <div
    v-if="hasPickupRequestPermission || hasWasteInquiryPermission"
    class="mb-3xl mt-auto"
  >
    <AppSeparator class="my-xl opacity-20" />
    <VcDropdownMenu>
      <template #trigger>
        <div class="flex justify-center">
          <VcIconButton
            :label="i18n.t('shared.actions')"
            icon="plus"
          />
        </div>
      </template>

      <template #content>
        <VcDropdownMenuGroup>
          <VcDropdownMenuItem
            v-if="hasPickupRequestPermission"
            :label="i18n.t('module.pickup_request.update.page_title')"
            icon="plus"
            @click="onNewPickup"
          />
          <VcDropdownMenuItem
            v-if="hasWasteInquiryPermission"
            :label="i18n.t('module.waste_inquiry.update.page_title')"
            icon="plus"
            @click="onNewWasteInquiry"
          />
        </VcDropdownMenuGroup>
      </template>
    </VcDropdownMenu>
  </div>
</template>
