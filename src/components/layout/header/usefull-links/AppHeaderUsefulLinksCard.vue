<script setup lang="ts">
import { VcIcon } from '@wisemen/vue-core-components'

import AppGroup from '@/components/app/AppGroup.vue'
import AppUnstyledButton from '@/components/app/button/AppUnstyledButton.vue'
import AppCard from '@/components/app/card/AppCard.vue'
import type { LinkItem } from '@/components/layout/header/AppHeaderUsefulLinks.vue'

const props = defineProps<{
  linkItem: LinkItem
}>()

function navigateToExternalLink(url: string): void {
  window.open(url, '_blank')?.focus()
}
</script>

<template>
  <AppUnstyledButton
    class="group w-full"
    @click="navigateToExternalLink(props.linkItem.url)"
  >
    <AppCard
      variant="transparent"
      class="
        hover:bg-gray-25
        group-hover:shadow-sm
      "
    >
      <AppGroup
        gap="xl"
        align="center"
      >
        <VcIcon
          v-if="props.linkItem.icon"
          :icon="props.linkItem.icon"
          :aria-label="props.linkItem.title"
          class="min-h-12 min-w-12"
        />
        <div
          v-else
          class="
            bg from-fg-brand-primary to-fg-brand-secondary min-h-10 min-w-10
            rounded-lg bg-radial-[at_25%_25%] to-75%
          "
        />
        <AppGroup
          direction="col"
          gap="md"
          align="start"
          class="w-full"
        >
          <p class="text-primary text-sm font-medium">
            {{ props.linkItem.title }}
          </p>
          <p class="text-tertiary font-regular text-left text-sm">
            {{ props.linkItem.label }}
          </p>
        </AppGroup>
        <VcIcon
          icon="externalLink"
          class="text-tertiary size-5"
        />
      </AppGroup>
    </AppCard>
  </AppUnstyledButton>
</template>
