<script setup lang="ts">
import { VcIcon } from '@wisemen/vue-core-components'
import { useI18n } from 'vue-i18n'

const i18n = useI18n()

const MANUAL_LINK
    = 'https://www.portal.indaver.com/irj/portal?NavigationTarget=ROLES%3A%2F%2Fportal_content%2Fz.ind.indaver%2Fz.ind.customer_zone%2Fz.ind.pages%2Fz.ind.help_help&PrevNavTarget=navurl%3A%2F%2Fba3a3a174d29ce10ab4a3e7cdd9a5697&NavMode=4&CurrentWindowId=WID1756106255430#Welcome/Welcome'
</script>

<template>
  <a
    :href="MANUAL_LINK"
    :aria-label="i18n.t('shared.cz_manual.open_link')"
    target="_blank"
    rel="noopener noreferrer"
    class="inline-flex"
  >
    <VcIcon
      :label="i18n.t('shared.cz_manual.open_link')"
      icon="bookOpen"
      class="text-primary-on-brand/70 size-5"
    />
  </a>
</template>
