<script setup lang="ts">
import type { Icon } from '@wisemen/vue-core-components'
import {
  VcDropdownMenu,
  VcIconButton,
} from '@wisemen/vue-core-components'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import { Permission } from '@/client'
import AppHeaderUsefulLinksCard from '@/components/layout/header/usefull-links/AppHeaderUsefulLinksCard.vue'
import { useAuthStore } from '@/stores/auth.store.ts'

const i18n = useI18n()
const authStore = useAuthStore()

const hasEcmrPermission = computed<boolean>(() => authStore.hasPermission(Permission.USEFUL_LINK_ECMR))
const hasReportingPermission = computed<boolean>(() => authStore.hasPermission(Permission.USEFUL_LINK_REPORTING))
const hasPermitsPermission = computed<boolean>(() => authStore.hasPermission(Permission.USEFUL_LINK_PERMITS))

export interface LinkItem {
  title: string
  isVisible: boolean
  icon: Icon
  label: string
  url: string
}

const linkItems = computed<LinkItem[]>(() => [
  {
    title: i18n.t('shared.useful_link.ewastra_portal_title'),
    isVisible: hasEcmrPermission.value,
    icon: 'ewastra',
    label: i18n.t('shared.useful_link.ewastra_portal_description'),
    url: 'https://portal.ewastra.com/Account/LogOn',
  },
  {
    title: i18n.t('shared.useful_link.indaver_report_title'),
    isVisible: hasReportingPermission.value,
    icon: 'reporting',
    label: i18n.t('shared.useful_link.indaver_report_description'),
    url: 'https://reports.portal.indaver.com/analytics/',
  },
  {
    title: i18n.t('shared.useful_link.waste_facilities_title'),
    isVisible: hasPermitsPermission.value,
    icon: 'permit',
    label: i18n.t('shared.useful_link.waste_facilities_description'),
    url: 'https://indaver.com/waste-facilities',
  },
  {
    title: i18n.t('shared.useful_link.waste_collectors_title'),
    isVisible: hasPermitsPermission.value,
    icon: 'permit',
    label: i18n.t('shared.useful_link.waste_collectors_description'),
    url: 'https://indaver.com/waste-collectors',
  },
])
</script>

<template>
  <VcDropdownMenu
    :is-popover-arrow-hidden="true"
    :popover-side-offset="6"
    popover-align="center"
    class="rounded-xl"
  >
    <template #trigger>
      <VcIconButton
        :label="i18n.t('shared.open_useful_links')"
        :class-config="{
          icon: 'text-primary-on-brand/70',
          root: 'hover:!bg-transparent',
        }"
        variant="tertiary"
        icon="externalLink"
      />
    </template>
    <template #content>
      <div class="px-lg py-lg max-w-200 min-w-200">
        <template
          v-for="linkItem in linkItems"
        >
          <div
            v-if="linkItem.isVisible"
            :key="linkItem.title"
            class="py-xs"
          >
            <AppHeaderUsefulLinksCard
              :link-item="linkItem"
            />
          </div>
        </template>
      </div>
    </template>
  </VcDropdownMenu>
</template>
