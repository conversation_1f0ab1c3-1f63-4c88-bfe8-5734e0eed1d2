<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'

import { Permission } from '@/client'
import AppGroup from '@/components/app/AppGroup.vue'
import AppSeparator from '@/components/app/AppSeparator.vue'
import AppHeaderCZManual from '@/components/layout/header/AppHeaderCZManual.vue'
import AppHeaderSettings from '@/components/layout/header/AppHeaderSettings.vue'
import AppHeaderUsefulLinks from '@/components/layout/header/AppHeaderUsefulLinks.vue'
import AppHeaderUserProfile from '@/components/layout/header/AppHeaderUserProfile.vue'
import type { UserDetail } from '@/models/user/detail/userDetail.model'
import { useAuthStore } from '@/stores/auth.store'
import { useImpersonationStore } from '@/stores/impersonation.store'

const props = defineProps<{
  user: UserDetail
}>()

const emit = defineEmits<{
  signOut: []
}>()

const authStore = useAuthStore()

const impersonationStore = useImpersonationStore()
const router = useRouter()

function onSignOut(): void {
  emit('signOut')
}

async function onStopImpersonation(): Promise<void> {
  impersonationStore.stopImpersonation()
  await router.push({ name: 'users-overview' })
  window.location.reload()
}

const hasEcmrPermission = computed<boolean>(() => authStore.hasPermission(Permission.USEFUL_LINK_ECMR))
const hasReportingPermission = computed<boolean>(() => authStore.hasPermission(Permission.USEFUL_LINK_REPORTING))
const hasPermitsPermission = computed<boolean>(() => authStore.hasPermission(Permission.USEFUL_LINK_PERMITS))

const areUsefullLinksVisible = computed<boolean>(() => {
  return (
    hasEcmrPermission.value
    || hasReportingPermission.value
    || hasPermitsPermission.value
  )
})
</script>

<template>
  <AppGroup
    justify="between"
    class="p-md relative min-h-12"
  >
    <div
      id="header-left"
      class="z-1"
    />

    <AppGroup
      gap="lg"
      class="z-1"
    >
      <AppHeaderUsefulLinks v-if="areUsefullLinksVisible" />
      <AppHeaderCZManual />
      <AppHeaderSettings />

      <AppSeparator
        class="mr-sm !bg-primary/20 !h-7"
        direction="vertical"
      />

      <AppHeaderUserProfile
        :user="props.user"
        :is-impersonating="impersonationStore.isImpersonating"
        @sign-out="onSignOut"
        @stop-impersonation="onStopImpersonation"
      />
    </AppGroup>

    <div
      v-if="impersonationStore.isImpersonating"
      class="
        absolute top-0 right-0 h-full w-[50vw] bg-gradient-to-r from-transparent
        to-[#79b3ad]
      "
    />
  </AppGroup>
</template>
