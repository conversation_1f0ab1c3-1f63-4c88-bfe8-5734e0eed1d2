<script setup lang="ts">
import {
  useKeyboardShortcut,
  VcAvatar,
  VcButton,
  VcDropdownMenu,
  VcDropdownMenuGroup,
  VcDropdownMenuItem,
  VcDropdownMenuSeparator,
  VcIcon,
  VcKeyboardShortcut,
} from '@wisemen/vue-core-components'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import AppGroup from '@/components/app/AppGroup.vue'
import AppUnstyledButton from '@/components/app/button/AppUnstyledButton.vue'
import AppHeaderUserProfileGlobalCustomer from '@/components/layout/header/global-customer/UserProfileGlobalCustomer.vue'
import { useGlobalCustomer } from '@/composables/global-customer/globalCustomer.composable'
import {
  CURRENT_BUILD_NUMBER,
  CURRENT_ENVIRONMENT,
} from '@/constants/environment.constant'
import { TEST_ID } from '@/constants/testId.constant'
import type { UserDetail } from '@/models/user/detail/userDetail.model.ts'
import { UserUtil } from '@/models/user/user.util'
import { useImpersonationStore } from '@/stores/impersonation.store'
import { UuidToColorUtil } from '@/utils/uuidToColor.util'

const props = defineProps<{
  isImpersonating?: boolean
  user: UserDetail | null
}>()

const emit = defineEmits<{
  signOut: []
  stopImpersonation: []
}>()

const i18n = useI18n()
const { globalCustomer } = useGlobalCustomer()
const impersonationStore = useImpersonationStore()

useKeyboardShortcut({
  isDisabled: computed<boolean>(() => props.isImpersonating),
  keys: [
    's',
    'o',
  ],
  onTrigger: onSignOut,
})

const fullName = computed<string | null>(() => {
  if (props.isImpersonating) {
    const impersonatedUser = impersonationStore.impersonatedUser

    if (!impersonatedUser) {
      return null
    }
    const firstName = impersonatedUser.firstName ?? ''
    const lastName = impersonatedUser.lastName ?? ''

    return `${firstName} ${lastName}`.trim() || null
  }

  if (props.user === null) {
    return null
  }

  return UserUtil.getFullName(props.user)
})

function onSignOut(): void {
  emit('signOut')
}

function onStopImpersonation(): void {
  emit('stopImpersonation')
}
</script>

<template>
  <VcDropdownMenu
    :is-popover-arrow-hidden="true"
    :popover-side-offset="6"
    popover-align="end"
  >
    <template #trigger>
      <!-- Impersonation trigger -->
      <AppUnstyledButton
        v-if="isImpersonating"
        class="
          p-xxs pl-sm w-80 rounded-l-full bg-gradient-to-r from-[#178D81]
          to-transparent to-80%
        "
      >
        <AppGroup justify="between">
          <div class="relative">
            <AppGroup
              align="center"
              spacing="sm"
            >
              <VcAvatar
                :name="fullName ?? ''"
                :class-config="{
                  root: 'size-8',
                  fallback: 'text-xs',
                }"
              />
              <AppGroup
                direction="col"
                align="start"
                gap="xxs"
              >
                <p class="text-primary-on-brand font-regular text-xs">
                  {{
                    i18n.t('shared.working_as')
                  }}
                </p>
                <p class="text-primary-on-brand text-xs font-medium">
                  {{ fullName ?? '-' }}
                </p>
              </AppGroup>
            </AppGroup>
          </div>

          <VcIcon
            icon="chevronRight"
            class="size-5 rotate-90 text-white"
          />
        </AppGroup>
      </AppUnstyledButton>

      <VcButton
        v-else
        :test-id="TEST_ID.APP_PAGE.USER_BUTTON"
        variant="unstyled"
      >
        <AppGroup>
          <div class="relative">
            <VcAvatar
              :name="fullName ?? ''"
              :class-config="{
                root: 'size-8',
                fallback: 'text-xs',
              }"
            />

            <div
              v-if="globalCustomer !== null"
              :class="UuidToColorUtil.getBackgroundColorClassFromColorVariant(
                UuidToColorUtil.getColorVariantFromUuid(globalCustomer.id),
                'solid',
              )"
              class="
                absolute right-0 bottom-0 flex size-3.5 translate-x-1
                translate-y-0.5 items-center justify-center rounded-full
              "
            >
              <VcIcon
                :class="UuidToColorUtil.getForegroundColorClassFromColor(
                  UuidToColorUtil.getColorVariantFromUuid(globalCustomer.id),
                  'solid',
                )"
                icon="building"
                class="size-2"
              />
            </div>
          </div>

          <VcIcon
            icon="chevronRight"
            class="size-5 rotate-90 text-white"
          />
        </AppGroup>
      </VcButton>
    </template>

    <template #content>
      <div class="min-w-64">
        <div class="px-lg py-lg">
          <span
            :data-test-id="TEST_ID.APP_PAGE.USER_NAME"
            class="text-primary text-sm"
          >
            {{ fullName ?? '-' }}
          </span>
        </div>

        <div class="px-xs">
          <VcDropdownMenuSeparator />
        </div>

        <AppHeaderUserProfileGlobalCustomer />

        <div class="px-xs">
          <VcDropdownMenuSeparator />
        </div>

        <AppGroup
          class="px-lg py-lg"
          direction="col"
          align="start"
        >
          <span class="text-tertiary block text-sm">
            {{ `${i18n.t('component.sidebar.footer.version')}: ${CURRENT_BUILD_NUMBER}` }}
          </span>

          <span class="text-tertiary block text-sm">
            {{ `${i18n.t('component.sidebar.footer.environment')}: ${CURRENT_ENVIRONMENT}` }}
          </span>
        </AppGroup>

        <div class="px-xs">
          <VcDropdownMenuSeparator />
        </div>

        <VcDropdownMenuGroup>
          <VcDropdownMenuItem
            v-if="isImpersonating"
            :label="i18n.t('component.sidebar.footer.stop_impersonation')"
            :is-destructive="true"
            icon="logout"
            @select="onStopImpersonation"
          >
            <template #right>
              <VcKeyboardShortcut
                :keyboard-keys="['s', 'o']"
                :class-config="{
                  keyboardKey: {
                    key: 'shadow-none bg-error-primary text-error-primary group-data-highlighted/dropdown-menu-item:bg-error-secondary duration-200',
                  },
                  thenLabel: 'text-error-primary',
                }"
              />
            </template>
          </VcDropdownMenuItem>

          <VcDropdownMenuItem
            v-else
            :label="i18n.t('component.sidebar.footer.sign_out')"
            :is-destructive="true"
            icon="logout"
            @select="onSignOut"
          >
            <template #right>
              <VcKeyboardShortcut
                :keyboard-keys="['s', 'o']"
                :class-config="{
                  keyboardKey: {
                    key: 'shadow-none bg-error-primary text-error-primary group-data-highlighted/dropdown-menu-item:bg-error-secondary duration-200',
                  },
                  thenLabel: 'text-error-primary',
                }"
              />
            </template>
          </VcDropdownMenuItem>
        </VcDropdownMenuGroup>
      </div>
    </template>
  </VcDropdownMenu>
</template>
