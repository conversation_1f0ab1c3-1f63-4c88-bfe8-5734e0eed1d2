<script setup lang="ts">
import {
  VcDropdownMenuGroup,
  VcDropdownMenuSubMenu,
  VcIcon,
  VcSpinner,
} from '@wisemen/vue-core-components'
import {
  ListboxContent,
  ListboxFilter,
  ListboxItem,
  ListboxRoot,
} from 'reka-ui'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import AppGroup from '@/components/app/AppGroup.vue'
import AppIconTile from '@/components/app/AppIconTile.vue'
import InfiniteScrollContainer from '@/components/utility/InfiniteScrollContainer.vue'
import { useGlobalCustomer } from '@/composables/global-customer/globalCustomer.composable'
import { AddressUtil } from '@/models/address/address.util'
import { useAuthStore } from '@/stores/auth.store'
import { UuidToColorUtil } from '@/utils/uuidToColor.util'

const authStore = useAuthStore()

const i18n = useI18n()

const {
  isDebouncing,
  isFetching,
  isLoading,
  customers,
  debouncedSearch,
  fetchNextPage,
  globalCustomer,
  search,
} = useGlobalCustomer()

const hasOnlyOneCustomer = computed<boolean>(() => {
  return customers.value.length === 1 && debouncedSearch.value.length === 0 && !isFetching.value
})

// for internal users, selecting a global customer is optional
const isInternalUser = computed<boolean>(() => authStore.authUser?.isInternalUser ?? false)
</script>

<template>
  <VcDropdownMenuGroup>
    <div
      v-if="hasOnlyOneCustomer && globalCustomer !== null"
      class="
        gap-x-lg p-md flex w-64 items-center justify-between overflow-hidden
      "
    >
      <AppGroup class="overflow-hidden">
        <AppIconTile
          :variant="UuidToColorUtil.getColorVariantFromUuid(globalCustomer.id)"
          icon="building"
          fill="solid"
        />

        <AppGroup
          gap="none"
          direction="col"
          align="start"
          class="overflow-hidden"
        >
          <span
            class="
              text-primary max-w-full truncate text-sm font-semibold
              duration-100
            "
          >
            {{ globalCustomer.name }}
          </span>

          <span
            v-if="globalCustomer.address !== null"
            class="text-tertiary max-w-full truncate text-xs"
          >
            {{ globalCustomer.id }} - {{ AddressUtil.format(globalCustomer.address) }}
          </span>
        </AppGroup>
      </AppGroup>
    </div>

    <VcDropdownMenuSubMenu
      v-else
      label=""
    >
      <template #item>
        <div
          class="
            gap-x-lg flex w-64 items-center justify-between overflow-hidden
          "
        >
          <AppGroup
            v-if="globalCustomer !== null"
            class="overflow-hidden"
          >
            <AppIconTile
              :variant="UuidToColorUtil.getColorVariantFromUuid(globalCustomer.id)"
              icon="building"
              fill="solid"
            />

            <AppGroup
              gap="none"
              direction="col"
              align="start"
              class="overflow-hidden"
            >
              <span
                class="
                  text-primary max-w-full truncate text-sm font-semibold
                  duration-100
                "
              >
                {{ globalCustomer.name }}
              </span>

              <span
                v-if="globalCustomer.address !== null"
                class="text-tertiary max-w-full truncate text-xs"
              >
                {{ globalCustomer.id }} - {{ AddressUtil.format(globalCustomer.address) }}
              </span>
            </AppGroup>
          </AppGroup>

          <span
            v-else
            class="text-secondary text-sm"
          >
            {{ i18n.t('component.global_customer.select_global_customer') }}
          </span>

          <VcIcon
            icon="switchHorizontal"
            class="text-primary size-4 shrink-0"
          />
        </div>
      </template>

      <template #default>
        <ListboxRoot
          v-model="globalCustomer"
          :selection-behavior="isInternalUser ? 'toggle' : 'replace'"
        >
          <div
            class="border-secondary flex w-full items-center border-b"
          >
            <ListboxFilter
              v-model="search"
              :auto-focus="true"
              :placeholder="i18n.t('shared.search')"
              class="text-secondary p-lg size-full text-sm outline-none"
            />

            <VcSpinner
              v-if="isFetching || isDebouncing"
              class="text-tertiary mr-md size-4"
            />
          </div>

          <ListboxContent
            class="p-md gap-xs flex max-h-96 w-96 flex-col overflow-hidden"
          >
            <InfiniteScrollContainer @next="fetchNextPage">
              <ListboxItem
                v-for="customer of customers"
                :key="customer.id"
                :value="customer"
                class="
                  group/listbox-item gap-x-md p-md flex cursor-default
                  items-center justify-between rounded-md text-sm duration-100
                  data-highlighted:bg-primary-hover
                  data-[state=checked]:bg-light-blue-25
                "
              >
                <AppGroup>
                  <AppIconTile
                    :fill="customer.id === globalCustomer?.id ? 'solid' : 'tint'"
                    :variant="UuidToColorUtil.getColorVariantFromUuid(customer.id)"
                    icon="building"
                  />

                  <AppGroup
                    gap="none"
                    direction="col"
                    align="start"
                  >
                    <span
                      class="
                        text-secondary text-sm duration-100
                        group-data-[state=checked]/listbox-item:text-primary
                        group-data-[state=checked]/listbox-item:font-semibold
                      "
                    >
                      {{ customer.name }}
                    </span>

                    <span
                      v-if="customer.address !== null"
                      class="text-tertiary text-xs"
                    >
                      {{ customer.id }} - {{ AddressUtil.format(customer.address) }}
                    </span>
                  </AppGroup>
                </AppGroup>

                <VcIcon
                  class="
                    text-primary ml-md size-4 shrink-0 opacity-0 duration-100
                    group-data-[state=checked]/listbox-item:opacity-100
                  "
                  icon="check"
                />
              </ListboxItem>

              <div
                v-if="customers.length === 0 && !isLoading"
                class="px-md py-sm"
              >
                <span class="text-tertiary text-sm">
                  {{ i18n.t('component.global_customer.no_results') }}
                </span>
              </div>
            </InfiniteScrollContainer>
          </ListboxContent>
        </ListboxRoot>
      </template>
    </VcDropdownMenuSubMenu>
  </VcDropdownMenuGroup>
</template>
