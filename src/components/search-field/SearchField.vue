<script setup lang="ts">
import {
  Vc<PERSON>utton,
  VcKeyboardShortcut,
  VcKeyboardShortcutProvider,
  VcTextField,
} from '@wisemen/vue-core-components'
import {
  AnimatePresence,
  Motion,
  MotionConfig,
} from 'motion-v'
import { useId } from 'vue'
import { useI18n } from 'vue-i18n'

import type { useSearch } from '@/composables/search/search.composable'
import { SearchUtil } from '@/utils/search.util'

const props = withDefaults(defineProps<{
  isKeyboardShortcutDisabled?: boolean
  search: ReturnType<typeof useSearch>
}>(), { isKeyboardShortcutDisabled: false })

const id = useId()
const i18n = useI18n()
</script>

<template>
  <div>
    <VcKeyboardShortcutProvider
      :keyboard-keys="props.isKeyboardShortcutDisabled ? [] : ['meta', 'f']"
      :prevent-default="true"
      :is-disabled="props.isKeyboardShortcutDisabled"
    >
      <VcTextField
        :id="id"
        :model-value="props.search.search.value"
        :class-config="{
          root: 'rounded-full',
        }"
        :placeholder="i18n.t('component.search_input.placeholder')"
        class="w-64"
        icon-left="search"
        @update:model-value="(value) => props.search.updateSearch(value ?? '')"
      >
        <template #right>
          <MotionConfig
            :transition="{
              duration: 0.2,
              type: 'spring',
              bounce: 0,
            }"
          >
            <AnimatePresence mode="popLayout">
              <Motion
                v-if="SearchUtil.isEmpty(props.search.search.value) && !props.isKeyboardShortcutDisabled"
                :initial="{ opacity: 0 }"
                :animate="{ opacity: 1 }"
                :exit="{ opacity: 0 }"
              >
                <VcKeyboardShortcut
                  :keyboard-keys="['meta', 'f']"
                  :class-config="{
                    keyboardKey: {
                      key: 'bg-tertiary shadow-none',
                    },
                  }"
                  class="mr-md"
                />
              </Motion>

              <Motion
                v-else-if="!SearchUtil.isEmpty(props.search.search.value)"
                :initial="{ opacity: 0 }"
                :animate="{ opacity: 1 }"
                :exit="{ opacity: 0 }"
              >
                <VcButton
                  :class-config="{
                    content: 'text-xs',
                    root: 'h-6.5 !px-md rounded-full',
                  }"
                  class="mr-xs"
                  size="xs"
                  variant="tertiary"
                  @click="props.search.clearSearch()"
                >
                  {{ i18n.t('component.search_input.clear') }}
                </VcButton>
              </Motion>
            </AnimatePresence>
          </MotionConfig>
        </template>
      </VcTextField>
    </VcKeyboardShortcutProvider>

    <label
      :for="id"
      class="sr-only"
    >
      {{ i18n.t('shared.search') }}
    </label>
  </div>
</template>
