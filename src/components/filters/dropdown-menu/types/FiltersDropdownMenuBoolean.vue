<script setup lang="ts">
import {
  VcDropdownMenuItem,
  VcSwitchRoot,
  VcSwitchThumb,
} from '@wisemen/vue-core-components'

import type { BooleanFilter } from '@/components/filters/filters.type'
import { useFilterValue } from '@/components/filters/filterValue.composable'

const props = defineProps<{
  filter: BooleanFilter
}>()

const value = useFilterValue<boolean>(props.filter.key)

function onToggle(): void {
  value.value = !value.value
}
</script>

<template>
  <VcDropdownMenuItem
    :label="props.filter.label"
    :icon="props.filter.icon"
    @select.prevent="onToggle"
  >
    <template #right>
      <VcSwitchRoot
        v-model="value"
        :tabindex="-1"
        :aria-hidden="true"
        size="sm"
        class="pointer-events-none"
      >
        <VcSwitchThumb />
      </VcSwitchRoot>
    </template>
  </VcDropdownMenuItem>
</template>
