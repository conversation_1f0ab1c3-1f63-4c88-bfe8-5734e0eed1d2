<script setup lang="ts">
import {
  useKeyboardShortcut,
  VcDropdownMenuItem,
  VcIcon,
} from '@wisemen/vue-core-components'

import { useFiltersDateDialog } from '@/components/filters/dialog/filtersDateDialog.composable'
import type { DateFilter } from '@/components/filters/filters.type'

const props = defineProps<{
  filter: DateFilter
}>()

const dateDialog = useFiltersDateDialog()

if (props.filter.keyboardShortcutKeys !== undefined) {
  useKeyboardShortcut({
    keys: props.filter.keyboardShortcutKeys,
    onTrigger: (event) => {
      event.preventDefault()
      dateDialog.open(props.filter)
    },
  })
}
</script>

<template>
  <VcDropdownMenuItem
    :filter="props.filter"
    :label="props.filter.label"
    :icon="props.filter.icon"
    @select="dateDialog.open(props.filter)"
  >
    <template #right>
      <VcIcon
        icon="calendarIcon"
        class="text-disabled size-4"
      />
    </template>
  </VcDropdownMenuItem>
</template>
