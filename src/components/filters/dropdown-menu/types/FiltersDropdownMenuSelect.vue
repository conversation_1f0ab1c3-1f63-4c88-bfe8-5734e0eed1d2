<script setup lang="ts">
import { VcDropdownMenuSubMenu } from '@wisemen/vue-core-components'

import type {
  AutocompleteFilter,
  MultiAutocompleteFilter,
  MultiSelectFilter,
  SelectFilter,
} from '@/components/filters/filters.type'
import FiltersListbox from '@/components/filters/FiltersListbox.vue'

const props = defineProps<{
  filter: AutocompleteFilter | MultiAutocompleteFilter | MultiSelectFilter | SelectFilter
}>()
</script>

<template>
  <VcDropdownMenuSubMenu
    :label="props.filter.label"
    :icon="props.filter.icon"
  >
    <FiltersListbox :filter="props.filter" />
  </VcDropdownMenuSubMenu>
</template>
