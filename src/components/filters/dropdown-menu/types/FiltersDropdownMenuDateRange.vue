<script setup lang="ts">
import {
  useKeyboardShortcut,
  VcDropdownMenuItem,
  VcIcon,
} from '@wisemen/vue-core-components'

import { useFiltersDateRangeDialog } from '@/components/filters/dialog/filtersDateRangeDialog.composable'
import type { DateRangeFilter } from '@/components/filters/filters.type'

const props = defineProps<{
  filter: DateRangeFilter
}>()

const dateRangeDialog = useFiltersDateRangeDialog()

if (props.filter.keyboardShortcutKeys !== undefined) {
  useKeyboardShortcut({
    keys: props.filter.keyboardShortcutKeys,
    onTrigger: (event) => {
      event.preventDefault()
      dateRangeDialog.open(props.filter)
    },
  })
}
</script>

<template>
  <VcDropdownMenuItem
    :filter="props.filter"
    :label="props.filter.label"
    :icon="props.filter.icon"
    @select="dateRangeDialog.open(props.filter)"
  >
    <template #right>
      <VcIcon
        icon="calendarIcon"
        class="text-disabled size-4"
      />
    </template>
  </VcDropdownMenuItem>
</template>
