<script setup lang="ts">
import NumberFlow from '@number-flow/vue'
import {
  useKeyboardShortcut,
  VcBadge,
  VcButton,
  VcDropdownMenuContent,
  VcDropdownMenuContentTransition,
  VcDropdownMenuGroup,
  VcDropdownMenuItem,
  VcDropdownMenuPortal,
  VcDropdownMenuRoot,
  VcDropdownMenuSeparator,
  VcDropdownMenuTrigger,
  VcKeyboardShortcutProvider,
} from '@wisemen/vue-core-components'
import {
  AnimatePresence,
  Motion,
} from 'motion-v'
import {
  computed,
  ref,
} from 'vue'
import { useI18n } from 'vue-i18n'

import AnimateWidth from '@/components/app/transition/AnimateWidth.vue'
import FiltersDropdownMenuBoolean from '@/components/filters/dropdown-menu/types/FiltersDropdownMenuBoolean.vue'
import FiltersDropdownMenuDate from '@/components/filters/dropdown-menu/types/FiltersDropdownMenuDate.vue'
import FiltersDropdownMenuDateRange from '@/components/filters/dropdown-menu/types/FiltersDropdownMenuDateRange.vue'
import FiltersDropdownMenuSelect from '@/components/filters/dropdown-menu/types/FiltersDropdownMenuSelect.vue'
import { useInjectFiltersContext } from '@/components/filters/filters.context'
import type { FilterGroup } from '@/components/filters/filters.type'

const {
  activeFilters,
  clearAllFilters,
  filterGroups,
} = useInjectFiltersContext()

const isDropdownMenuOpen = ref<boolean>(false)

const activeNonStaticFilterCount = computed<number>(() => activeFilters.value.filter((f) => !f.isStatic).length)

const filterGroupsWithVisibleNonStaticFilters = computed<FilterGroup[]>(() => {
  return filterGroups.value.map((group) => ({
    ...group,
    filters: group.filters.filter((filter) => !filter.isStatic && !filter.isHidden),
  })).filter((group) => group.filters.length > 0)
})

useKeyboardShortcut({
  keys: [
    'f',
  ],
  onTrigger: () => {
    isDropdownMenuOpen.value = true
  },
})

const i18n = useI18n()
</script>

<template>
  <VcDropdownMenuRoot
    v-model:is-open="isDropdownMenuOpen"
    :is-popover-arrow-hidden="true"
    popover-align="end"
  >
    <div class="relative">
      <VcKeyboardShortcutProvider :keyboard-keys="['f']">
        <VcDropdownMenuTrigger>
          <VcButton
            :class-config="{
              root: 'border-secondary',
            }"
            icon-left="filterLines"
            variant="secondary"
            class="shrink-0"
            size="sm"
          >
            {{ i18n.t('shared.filters') }}
          </VcButton>

          <AnimatePresence :initial="false">
            <Motion
              v-if="activeNonStaticFilterCount > 0"
              :initial="{
                opacity: 0,
                scale: 0.8,
              }"
              :animate="{
                opacity: 1,
                scale: 1,
              }"
              :exit="{
                opacity: 0,
                scale: 0.8,
              }"
              :transition="{
                duration: 0.2,
                type: 'spring',
                bounce: 0,
              }"
              :as-child="true"
            >
              <VcBadge
                :class-config="{
                  root: '!border-primary size-5 flex items-center justify-center !bg-primary',
                }"
                color="gray"
                size="sm"
                variant="translucent"
                class="absolute top-0 right-0 translate-x-1/3 -translate-y-1/3"
              >
                <AnimateWidth :duration-in-ms="200">
                  <NumberFlow
                    :value="activeNonStaticFilterCount"
                    :spin-timing="{
                      duration: 200,
                      easing: 'ease-in-out',
                    }"
                    class="tabular-nums"
                  />
                </AnimateWidth>
              </VcBadge>
            </Motion>
          </AnimatePresence>
        </VcDropdownMenuTrigger>
      </VcKeyboardShortcutProvider>
    </div>

    <VcDropdownMenuPortal>
      <VcDropdownMenuContent>
        <VcDropdownMenuContentTransition>
          <div class="w-54">
            <template
              v-for="(filterGroup, filterGroupIndex) of filterGroupsWithVisibleNonStaticFilters"
              :key="filterGroupIndex"
            >
              <VcDropdownMenuGroup>
                <span
                  v-if="filterGroup.label !== undefined"
                  class="
                    mb-xs px-md pt-xs pb-xxs text-disabled block text-xs
                    font-semibold
                  "
                >
                  {{ filterGroup.label }}
                </span>

                <template
                  v-for="filter of filterGroup.filters"
                  :key="filter.key"
                >
                  <FiltersDropdownMenuSelect
                    v-if="filter.type === 'select' || filter.type === 'multi-select' || filter.type === 'autocomplete' || filter.type === 'multi-autocomplete'"
                    :filter="filter"
                  />

                  <FiltersDropdownMenuBoolean
                    v-else-if="filter.type === 'boolean'"
                    :filter="filter"
                  />

                  <FiltersDropdownMenuDate
                    v-else-if="filter.type === 'date'"
                    :filter="filter"
                  />

                  <FiltersDropdownMenuDateRange
                    v-else-if="filter.type === 'date-range'"
                    :filter="filter"
                  />
                </template>
              </VcDropdownMenuGroup>

              <VcDropdownMenuSeparator v-if="filterGroupIndex < filterGroups.length - 1" />
            </template>

            <VcDropdownMenuSeparator />

            <VcDropdownMenuGroup>
              <VcDropdownMenuItem
                :is-disabled="activeNonStaticFilterCount === 0"
                :label="i18n.t('component.filters.clear_all')"
                @click="clearAllFilters()"
              />
            </VcDropdownMenuGroup>
          </div>
        </VcDropdownMenuContentTransition>
      </VcDropdownMenuContent>
    </VcDropdownMenuPortal>
  </VcDropdownMenuRoot>
</template>
