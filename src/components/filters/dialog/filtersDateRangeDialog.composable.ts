import { useVcDialog } from '@wisemen/vue-core-components'
import type { ComputedRef } from 'vue'
import { computed } from 'vue'

import { useInjectFiltersContext } from '@/components/filters/filters.context'
import type { DateRangeFilter } from '@/components/filters/filters.type'

interface UseFilterDateDialog {
  isOpen: ComputedRef<boolean>
  open: (filter: DateRangeFilter) => void
}

export function useFiltersDateRangeDialog(): UseFilterDateDialog {
  const dialog = useVcDialog({ component: () => import('@/components/filters/dialog/FiltersDateRangeDialog.vue') })

  const {
    clearFilter,
    openFilterKey,
    values,
  } = useInjectFiltersContext()

  function open(filter: DateRangeFilter): void {
    const value = computed<{
      from: Date | null
      until: Date | null
    }>(() => values.value[filter.key])

    openFilterKey.value = filter.key

    dialog.open({
      filter,
      'modelValue': value,
      'onClosed': () => {
        openFilterKey.value = null
        clearFilter(filter.key, true)
      },
      'onUpdate:modelValue': (updatedValue) => {
        values.value[filter.key] = updatedValue
      },
    })
  }

  return {
    isOpen: computed<boolean>(() => dialog.isOpen()),
    open,
  }
}
