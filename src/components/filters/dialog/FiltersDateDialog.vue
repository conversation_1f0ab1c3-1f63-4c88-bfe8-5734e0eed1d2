<script setup lang="ts">
import {
  VcDatePicker,
  VcDialog,
  VcDialogDescription,
  VcDialogTitle,
} from '@wisemen/vue-core-components'
import { useI18n } from 'vue-i18n'

import type { DateFilter } from '@/components/filters/filters.type'

const props = defineProps<{
  filter: DateFilter
}>()

const emit = defineEmits<{
  close: []
  closed: []
}>()

const value = defineModel<Date | null>({ required: true })

const i18n = useI18n()

function onClose(): void {
  emit('close')
  // Not sure why, but onClose does not work in dialog.open()
  emit('closed')
}
</script>

<template>
  <VcDialog
    :class-config="{
      overlay: 'backdrop-blur-none',
    }"
    class="p-xl"
    @close="onClose"
  >
    <VcDialogTitle class="text-primary sr-only text-sm font-medium">
      <h1>
        {{ props.filter.label }}
      </h1>
    </VcDialogTitle>

    <VcDialogDescription class="sr-only">
      <p>
        {{ i18n.t('component.filters.date_dialog.description', { label: props.filter.label }) }}
      </p>
    </VcDialogDescription>

    <VcDatePicker
      v-model="value"
      :label="props.filter.label"
      :allow-deselect="!props.filter.isStatic"
    />
  </VcDialog>
</template>
