import { useVcDialog } from '@wisemen/vue-core-components'
import type { ComputedRef } from 'vue'
import { computed } from 'vue'

import { useInjectFiltersContext } from '@/components/filters/filters.context'
import type { DateFilter } from '@/components/filters/filters.type'

interface UseFilterDateDialog {
  isOpen: ComputedRef<boolean>
  open: (filter: DateFilter) => void
}

export function useFiltersDateDialog(): UseFilterDateDialog {
  const dialog = useVcDialog({ component: () => import('@/components/filters/dialog/FiltersDateDialog.vue') })

  const {
    clearFilter,
    openFilterKey,
    values,
  } = useInjectFiltersContext()

  function open(filter: DateFilter): void {
    const value = computed<Date | null>(() => values.value[filter.key] as Date | null)

    openFilterKey.value = filter.key

    dialog.open({
      filter,
      'modelValue': value,
      'onClosed': () => {
        openFilterKey.value = null
        clearFilter(filter.key, true)
      },
      'onUpdate:modelValue': (updatedValue) => {
        values.value[filter.key] = updatedValue
      },
    })
  }

  return {
    isOpen: computed<boolean>(() => dialog.isOpen()),
    open,
  }
}
