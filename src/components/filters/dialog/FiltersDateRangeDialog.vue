<script setup lang="ts">
import {
  VcDateRangePicker,
  VcDialog,
  VcDialogDescription,
  VcDialogTitle,
} from '@wisemen/vue-core-components'
import { useI18n } from 'vue-i18n'

import type { DateRangeFilter } from '@/components/filters/filters.type'

const props = defineProps<{
  filter: DateRangeFilter
}>()

const emit = defineEmits<{
  close: []
  closed: []
}>()

const value = defineModel<{
  from: Date | null
  until: Date | null
}>({ required: true })

const i18n = useI18n()

function onClose(): void {
  emit('close')
  // Not sure why, but onClose does not work in dialog.open()
  emit('closed')
}
</script>

<template>
  <VcDialog
    :class-config="{
      overlay: 'backdrop-blur-none',
    }"
    class="p-xl"
    @close="onClose"
  >
    <VcDialogTitle class="text-primary sr-only text-sm font-medium">
      <h1>
        {{ props.filter.label }}
      </h1>
    </VcDialogTitle>

    <VcDialogDescription class="sr-only">
      <p>
        {{ i18n.t('component.filters.date_range_dialog.description', { label: props.filter.label }) }}
      </p>
    </VcDialogDescription>

    <VcDateRangePicker
      v-model="value"
      :label="props.filter.label"
      :allow-deselect="!props.filter.isStatic"
      :show-two-months="true"
    />
  </VcDialog>
</template>
