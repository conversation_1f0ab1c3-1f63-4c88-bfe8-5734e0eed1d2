import type { Icon } from '@wisemen/vue-core-components'
import type { KeyboardShortcut } from 'node_modules/@wisemen/vue-core-components/dist/types/keyboard.type'
import type { MaybeRefOrGetter } from 'vue'

export type SelectFilterValue = number | string | Record<string, any>

interface BaseFilter<TKey extends string> {
  isHidden?: boolean
  isStatic?: boolean
  icon?: Icon
  key: TKey
  keyboardShortcutKeys?: KeyboardShortcut['keys']
  label: string
}

interface BaseSelectFilter<TValue extends SelectFilterValue> {
  displayFn: (value: TValue) => string
  // Ref or getter to allow dynamic options (e.g., based on other filters)
  options: MaybeRefOrGetter<TValue[]>
}

interface BaseAutocompleteFilter {
  isLoading: boolean
  onSearch: (search: string) => void
}

export interface SelectFilter<
  TKey extends string = string,
  TValue extends SelectFilterValue = SelectFilterValue,
> extends BaseFilter<TKey>, BaseSelectFilter<TValue> {
  defaultValue: TValue | null
  type: 'select'
}

export interface MultiSelectFilter<
  TKey extends string = string,
  TValue extends SelectFilterValue = SelectFilterValue,
> extends BaseFilter<TKey>, BaseSelectFilter<TValue> {
  defaultValue: TValue[]
  type: 'multi-select'
}

export interface AutocompleteFilter<
  TKey extends string = string,
  TValue extends SelectFilterValue = SelectFilterValue,
> extends BaseFilter<TKey>, BaseSelectFilter<TValue>, BaseAutocompleteFilter {
  defaultValue: TValue | null
  type: 'autocomplete'
}

export interface MultiAutocompleteFilter<
  TKey extends string = string,
  TValue extends SelectFilterValue = SelectFilterValue,
> extends BaseFilter<TKey>, BaseSelectFilter<TValue>, BaseAutocompleteFilter {
  defaultValue: TValue[]
  type: 'multi-autocomplete'
}

export interface BooleanFilter<TKey extends string = string> extends BaseFilter<TKey> {
  defaultValue: boolean
  type: 'boolean'
}

export interface DateFilter<TKey extends string = string, TValue extends Date = Date> extends BaseFilter<TKey> {
  defaultValue: TValue | null
  type: 'date'
}

export interface DateRangeFilter<TKey extends string = string, TValue extends Date = Date> extends BaseFilter<TKey> {
  defaultValue: {
    from: TValue | null
    until: TValue | null
  }
  type: 'date-range'
}

export type FilterKeys<TFilters extends readonly Filter[]> = TFilters[number]['key']

export interface FilterGroup<TFilters extends readonly Filter[] = readonly Filter[]> {
  filters: TFilters
  label?: string
}

export type GetFiltersFromGroups<TGroups extends readonly FilterGroup<readonly Filter[]>[]>
  = TGroups extends readonly FilterGroup<infer Filters>[] ? Filters[number][] : never

export type Filter
  = | AutocompleteFilter<string, any>
    | BooleanFilter<string>
    | DateFilter<string, any>
    | DateRangeFilter<string, any>
    | MultiAutocompleteFilter<string, any>
    | MultiSelectFilter<string, any>
    | SelectFilter<string, any>

export type FilterValues<TF extends readonly Filter[]> = {
  [F in TF[number] as F['key']]: F extends { defaultValue: infer V } ? V : never
}
