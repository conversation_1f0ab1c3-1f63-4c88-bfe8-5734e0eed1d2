import type { ComputedRef } from 'vue'

import type { Filters } from '@/components/filters/filters.composable'
import type {
  Filter,
  FilterGroup,
} from '@/components/filters/filters.type'
import { useContext } from '@/composables/context/context.composable'

interface FiltersContext extends Filters<FilterGroup[], Filter[]> {
  hasInteractedViaKeyboard: ComputedRef<boolean>
}

export const [
  useProvideFiltersContext,
  useInjectFiltersContext,
] = useContext<FiltersContext>('filtersContext')
