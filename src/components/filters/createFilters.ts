import type {
  <PERSON>comple<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Date<PERSON><PERSON><PERSON><PERSON><PERSON>er,
  MultiAutocompleteFilter,
  MultiSelectFilter,
  SelectFilter,
  SelectFilterValue,
} from '@/components/filters/filters.type'
import type { CalendarDate } from '@/models/date/calendarDate.model'

export function createSelectFilter<
  const TKey extends string,
  const TValue extends SelectFilterValue,
>(
  options: Omit<SelectFilter<TKey, TValue>, 'type'>,
): SelectFilter<TKey, TValue> {
  return {
    ...options,
    type: 'select',
  }
}

export function createMultiSelectFilter<
  const TKey extends string,
  const TValue extends SelectFilterValue,
>(
  options: Omit<MultiSelectFilter<TKey, TValue>, 'type'>,
): MultiSelectFilter<TKey, TValue> {
  return {
    ...options,
    type: 'multi-select',
  }
}

export function createAutocompleteFilter<
  const TKey extends string,
  const TValue extends SelectFilterValue,
>(
  options: Omit<AutocompleteFilter<TKey, TValue>, 'type'>,
): AutocompleteFilter<TKey, TValue> {
  return {
    ...options,
    type: 'autocomplete',
  }
}

export function createMultiAutocompleteFilter<
  const TKey extends string,
  const TValue extends SelectFilterValue,
>(
  options: Omit<MultiAutocompleteFilter<TKey, TValue>, 'type'>,
): MultiAutocompleteFilter<TKey, TValue> {
  return {
    ...options,
    type: 'multi-autocomplete',
  }
}

export function createDateFilter<const TKey extends string, const TValue extends CalendarDate>(
  options: Omit<DateFilter<TKey, TValue>, 'type'>,
): DateFilter<TKey, TValue> {
  return {
    ...options,
    type: 'date',
  }
}

export function createDateRangeFilter<const TKey extends string, const TValue extends CalendarDate>(
  options: Omit<DateRangeFilter<TKey, TValue>, 'type'>,
): DateRangeFilter<TKey, TValue> {
  return {
    ...options,
    type: 'date-range',
  }
}

export function createBooleanFilter<const TKey extends string>(
  options: Omit<BooleanFilter<TKey>, 'type'>,
): BooleanFilter<TKey> {
  return {
    ...options,
    type: 'boolean',
  }
}
