<script setup lang="ts">
import {
  AnimatePresence,
  Motion,
} from 'motion-v'
import { useI18n } from 'vue-i18n'

import AppGroup from '@/components/app/AppGroup.vue'
import FiltersActiveBoolean from '@/components/filters/active/types/FiltersActiveBoolean.vue'
import FiltersActiveDate from '@/components/filters/active/types/FiltersActiveDate.vue'
import FiltersActiveDateRange from '@/components/filters/active/types/FiltersActiveDateRange.vue'
import FiltersActiveMultiSelect from '@/components/filters/active/types/FiltersActiveMultiSelect.vue'
import FiltersActiveSelect from '@/components/filters/active/types/FiltersActiveSelect.vue'
import { useInjectFiltersContext } from '@/components/filters/filters.context'

const { activeFilters } = useInjectFiltersContext()
const i18n = useI18n()
</script>

<template>
  <AppGroup class="-mx-xs p-xs overflow-x-auto">
    <AnimatePresence
      :initial="false"
      mode="popLayout"
    >
      <Motion
        v-for="filter of activeFilters"
        :key="filter.key"
        :initial="{
          opacity: 0,
          x: -5,
          filter: 'blur(2px)',
        }"
        :animate="{
          opacity: 1,
          x: 0,
          filter: 'blur(0px)',
        }"
        :exit="{
          opacity: 0,
          x: -5,
          filter: 'blur(2px)',
        }"
        :layout="true"
        :transition="{
          duration: 0.2,
          type: 'spring',
          bounce: 0,
        }"
      >
        <FiltersActiveSelect
          v-if="filter.type === 'select' || filter.type === 'autocomplete'"
          :filter="filter"
        />

        <FiltersActiveMultiSelect
          v-else-if="filter.type === 'multi-select' || filter.type === 'multi-autocomplete'"
          :filter="filter"
        />

        <FiltersActiveBoolean
          v-else-if="filter.type === 'boolean'"
          :filter="filter"
        />

        <FiltersActiveDate
          v-else-if="filter.type === 'date'"
          :filter="filter"
        />

        <FiltersActiveDateRange
          v-else-if="filter.type === 'date-range'"
          :filter="filter"
        />
      </Motion>

      <Motion
        v-if="activeFilters.length === 0"
        :initial="{
          opacity: 0,
          x: 10,
          filter: 'blur(2px)',
        }"
        :animate="{
          opacity: 1,
          x: 0,
          filter: 'blur(0px)',
        }"
        :exit="{
          opacity: 0,
          x: 10,
          filter: 'blur(2px)',
        }"
        :transition="{
          duration: 0.2,
          type: 'spring',
          bounce: 0,
        }"
        class="text-tertiary text-sm font-medium"
      >
        {{ i18n.t('component.table.no_active_filters') }}
      </Motion>
    </AnimatePresence>
  </AppGroup>
</template>
