<script setup lang="ts">
import { VcBadge } from '@wisemen/vue-core-components'
import {
  AnimatePresence,
  Motion,
} from 'motion-v'

import AnimateWidth from '@/components/app/transition/AnimateWidth.vue'

const props = defineProps<{
  isEmpty: boolean
  label: string | null
}>()
</script>

<template>
  <div class="overflow-hidden">
    <AnimateWidth :duration-in-ms="200">
      <AnimatePresence
        :initial="false"
        mode="popLayout"
      >
        <Motion
          :key="props.label ?? 'empty'"
          :initial="{
            opacity: 0,
            filter: 'blur(2px)',
          }"
          :animate="{
            opacity: 1,
            filter: 'blur(0)',
          }"
          :exit="{
            opacity: 0,
            filter: 'blur(2px)',
          }"
          :transition="{
            duration: 0.2,
            type: 'spring',
            bounce: 0,
          }"
          :class="{
            'text-disabled': isEmpty,
          }"
          as="span"
          class="inline-block"
        >
          <template v-if="isEmpty">
            ...
          </template>

          <VcBadge
            v-else
            :class-config="{
              root: 'rounded-sm !px-sm !py-0 !bg-gray-25 !border-gray-25 !text-primary',
            }"
            color="gray"
            variant="solid"
            size="sm"
          >
            <slot v-if="props.label === null" />

            <template v-else>
              {{ props.label }}
            </template>
          </VcBadge>
        </Motion>
      </AnimatePresence>
    </AnimateWidth>
  </div>
</template>
