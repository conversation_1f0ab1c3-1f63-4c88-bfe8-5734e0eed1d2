<script setup lang="ts">
import {
  VcBadgeRemoveButton,
  VcBadgeRoot,
} from '@wisemen/vue-core-components'

import type { Filter } from '@/components/filters/filters.type'

const props = defineProps<{
  filter: Filter
}>()

const emit = defineEmits<{
  clear: [key: string]
}>()
</script>

<template>
  <VcBadgeRoot
    :class-config="{
      root: `rounded-md !p-0 ${props.filter.isStatic ? '!pr-md' : '!pr-sm'} !gap-xs h-9 rounded-full !border-gray-100`,
      removeButton: {
        root: 'size-5',
      },
    }"
    :is-removable="!props.filter.isStatic"
    color="gray"
    size="lg"
    variant="outline"
    @remove="emit('clear', props.filter.key)"
  >
    <slot />
    <VcBadgeRemoveButton />
  </VcBadgeRoot>
</template>
