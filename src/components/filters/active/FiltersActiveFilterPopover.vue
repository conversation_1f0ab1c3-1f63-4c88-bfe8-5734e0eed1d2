<script setup lang="ts">
import {
  useKeyboardShortcut,
  VcButton,
  VcPopover,
} from '@wisemen/vue-core-components'
import { ref } from 'vue'

import AppGroup from '@/components/app/AppGroup.vue'
import { useInjectFiltersContext } from '@/components/filters/filters.context'
import type { Filter } from '@/components/filters/filters.type'

const props = defineProps<{
  filter: Filter
}>()

const {
  clearFilter, openFilterKey,
} = useInjectFiltersContext()

const isPopoverOpen = ref<boolean>(openFilterKey.value === props.filter.key)

function onUpdateIsOpen(isOpen: boolean): void {
  if (isOpen) {
    openFilterKey.value = props.filter.key
  }
  else {
    openFilterKey.value = null

    if (!props.filter.isStatic) {
      clearFilter(props.filter.key, true)
    }
  }
}

if (props.filter.keyboardShortcutKeys !== undefined) {
  useKeyboardShortcut({
    keys: props.filter.keyboardShortcutKeys,
    onTrigger: (event) => {
      event.preventDefault()
      isPopoverOpen.value = true
    },
  })
}
</script>

<template>
  <VcPopover
    v-model:is-open="isPopoverOpen"
    :is-popover-arrow-hidden="true"
    :popover-side-offset="5"
    popover-align="start"
    @update:is-open="onUpdateIsOpen"
  >
    <template #trigger>
      <VcButton
        :class-config="{
          root: 'rounded-xs justify-start pl-md text-secondary h-full !scale-100',
        }"
        variant="unstyled"
      >
        <AppGroup gap="sm">
          <slot name="label" />
        </AppGroup>
      </VcButton>
    </template>

    <template #content>
      <slot name="content" />
    </template>
  </VcPopover>
</template>
