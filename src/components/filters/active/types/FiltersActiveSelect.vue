<script setup lang="ts">
import { computed } from 'vue'

import FiltersActiveBadge from '@/components/filters/active/FiltersActiveBadge.vue'
import FiltersActiveFilterPopover from '@/components/filters/active/FiltersActiveFilterPopover.vue'
import FiltersActiveLabel from '@/components/filters/active/FiltersActiveLabel.vue'
import FiltersActiveValue from '@/components/filters/active/FiltersActiveValue.vue'
import { useInjectFiltersContext } from '@/components/filters/filters.context'
import type {
  AutocompleteFilter,
  SelectFilter,
  SelectFilterValue,
} from '@/components/filters/filters.type'
import FiltersListbox from '@/components/filters/FiltersListbox.vue'
import { useFilterValue } from '@/components/filters/filterValue.composable'

const props = defineProps<{
  filter: AutocompleteFilter | SelectFilter
}>()

const { clearFilter } = useInjectFiltersContext()

const value = useFilterValue<SelectFilterValue>(props.filter.key)

const valueLabel = computed<string | null>(() => {
  if (value.value === null) {
    return null
  }

  return props.filter.displayFn(value.value)
})
</script>

<template>
  <FiltersActiveBadge
    :filter="props.filter"
    @clear="clearFilter"
  >
    <FiltersActiveFilterPopover :filter="props.filter">
      <template #label>
        <FiltersActiveLabel>
          {{ filter.label }}:
        </FiltersActiveLabel>

        <FiltersActiveValue
          :is-empty="value === null"
          :label="valueLabel"
        />
      </template>

      <template #content>
        <FiltersListbox :filter="props.filter" />
      </template>
    </FiltersActiveFilterPopover>
  </FiltersActiveBadge>
</template>
