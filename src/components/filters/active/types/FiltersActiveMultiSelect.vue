<script setup lang="ts">
import NumberFlow from '@number-flow/vue'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import FiltersActiveBadge from '@/components/filters/active/FiltersActiveBadge.vue'
import FiltersActiveFilterPopover from '@/components/filters/active/FiltersActiveFilterPopover.vue'
import FiltersActiveLabel from '@/components/filters/active/FiltersActiveLabel.vue'
import FiltersActiveValue from '@/components/filters/active/FiltersActiveValue.vue'
import { useInjectFiltersContext } from '@/components/filters/filters.context'
import type {
  MultiAutocompleteFilter,
  MultiSelectFilter,
  SelectFilterValue,
} from '@/components/filters/filters.type'
import FiltersListbox from '@/components/filters/FiltersListbox.vue'
import { useFilterValue } from '@/components/filters/filterValue.composable'

const props = defineProps<{
  filter: MultiAutocompleteFilter | MultiSelectFilter
}>()

const { clearFilter } = useInjectFiltersContext()

const value = useFilterValue<SelectFilterValue[]>(props.filter.key)
const i18n = useI18n()

// This label isn't actually used as a label, just to trigger the width animation
const label = computed<string | null>(() => {
  if (value.value.length === 0) {
    return null
  }

  if (value.value.length === 1) {
    return props.filter.displayFn(value.value[0])
  }

  return null
})
</script>

<template>
  <FiltersActiveBadge
    :filter="props.filter"
    @clear="clearFilter"
  >
    <FiltersActiveFilterPopover :filter="props.filter">
      <template #label>
        <FiltersActiveLabel>
          {{ filter.label }}:
        </FiltersActiveLabel>

        <FiltersActiveValue
          :is-empty="value.length === 0"
          :label="label"
        >
          <NumberFlow
            :value="value.length"
            :spin-timing="{
              duration: 200,
              easing: 'ease-in-out',
            }"
            :transform-timing="{
              duration: 200,
              easing: 'ease-in-out',
            }"
            :opacity-timing="{
              duration: 200,
              easing: 'ease-in-out',
            }"
            class="tabular-nums"
          />

          {{ i18n.t('component.filters.selected') }}
        </FiltersActiveValue>
      </template>

      <template #content>
        <FiltersListbox :filter="props.filter" />
      </template>
    </FiltersActiveFilterPopover>
  </FiltersActiveBadge>
</template>
