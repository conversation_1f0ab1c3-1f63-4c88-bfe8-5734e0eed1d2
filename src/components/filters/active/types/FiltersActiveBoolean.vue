<script setup lang="ts">
import FiltersActiveBadge from '@/components/filters/active/FiltersActiveBadge.vue'
import FiltersActiveLabel from '@/components/filters/active/FiltersActiveLabel.vue'
import { useInjectFiltersContext } from '@/components/filters/filters.context'
import type { BooleanFilter } from '@/components/filters/filters.type'

const props = defineProps<{
  filter: BooleanFilter
}>()

const { clearFilter } = useInjectFiltersContext()
</script>

<template>
  <FiltersActiveBadge
    :filter="props.filter"
    @clear="clearFilter"
  >
    <FiltersActiveLabel class="pl-md whitespace-nowrap">
      {{ filter.label }}
    </FiltersActiveLabel>
  </FiltersActiveBadge>
</template>
