<script setup lang="ts">
import { VcButton } from '@wisemen/vue-core-components'
import { computed } from 'vue'

import AppGroup from '@/components/app/AppGroup.vue'
import FiltersActiveBadge from '@/components/filters/active/FiltersActiveBadge.vue'
import FiltersActiveLabel from '@/components/filters/active/FiltersActiveLabel.vue'
import FiltersActiveValue from '@/components/filters/active/FiltersActiveValue.vue'
import { useFiltersDateRangeDialog } from '@/components/filters/dialog/filtersDateRangeDialog.composable'
import { useInjectFiltersContext } from '@/components/filters/filters.context'
import type { DateRangeFilter } from '@/components/filters/filters.type'
import { useFilterValue } from '@/components/filters/filterValue.composable'
import { useLocalizedDateFormat } from '@/composables/localized-date-format/localizedDateFormat.composable'

const props = defineProps<{
  filter: DateRangeFilter
}>()

const { clearFilter } = useInjectFiltersContext()

const localizedDateFormat = useLocalizedDateFormat()
const dateRangeFilterDialog = useFiltersDateRangeDialog()
const value = useFilterValue<{
  from: Date | null
  until: Date | null
}>(props.filter.key)

const valueLabel = computed<string | null>(() => {
  const {
    from, until,
  } = value.value

  if (from === null || until === null) {
    return null
  }

  return `${localizedDateFormat.toShortDate(from)} - ${localizedDateFormat.toShortDate(until)}`
})
</script>

<template>
  <FiltersActiveBadge
    :filter="props.filter"
    @clear="clearFilter"
  >
    <VcButton
      :class-config="{
        root: 'rounded-xs justify-start px-md py-xs pr-0 !scale-100',
      }"
      variant="unstyled"
      @click="dateRangeFilterDialog.open(props.filter)"
    >
      <AppGroup
        gap="sm"
        class="tabular-nums"
      >
        <FiltersActiveLabel>
          {{ props.filter.label }}:
        </FiltersActiveLabel>

        <FiltersActiveValue
          :label="valueLabel"
          :is-empty="value === null"
        />
      </AppGroup>
    </VcButton>
  </FiltersActiveBadge>
</template>
