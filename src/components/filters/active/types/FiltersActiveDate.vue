<script setup lang="ts">
import { VcButton } from '@wisemen/vue-core-components'
import { computed } from 'vue'

import AppGroup from '@/components/app/AppGroup.vue'
import FiltersActiveBadge from '@/components/filters/active/FiltersActiveBadge.vue'
import FiltersActiveLabel from '@/components/filters/active/FiltersActiveLabel.vue'
import FiltersActiveValue from '@/components/filters/active/FiltersActiveValue.vue'
import { useFiltersDateDialog } from '@/components/filters/dialog/filtersDateDialog.composable'
import { useInjectFiltersContext } from '@/components/filters/filters.context'
import type { DateFilter } from '@/components/filters/filters.type'
import { useFilterValue } from '@/components/filters/filterValue.composable'
import { useLocalizedDateFormat } from '@/composables/localized-date-format/localizedDateFormat.composable'

const props = defineProps<{
  filter: DateFilter
}>()

const { clearFilter } = useInjectFiltersContext()

const localizedDateFormat = useLocalizedDateFormat()
const dateFilterDialog = useFiltersDateDialog()
const value = useFilterValue<Date | null>(props.filter.key)

const valueLabel = computed<string | null>(() => {
  if (value.value === null) {
    return null
  }

  return localizedDateFormat.toShortDate(value.value)
})
</script>

<template>
  <FiltersActiveBadge
    :filter="props.filter"
    @clear="clearFilter"
  >
    <VcButton
      :class-config="{
        root: 'rounded-xs justify-start px-md py-xs pr-0 !scale-100',
      }"
      variant="unstyled"
      @click="dateFilterDialog.open(props.filter)"
    >
      <AppGroup
        gap="sm"
        class="tabular-nums"
      >
        <FiltersActiveLabel>
          {{ props.filter.label }}:
        </FiltersActiveLabel>

        <FiltersActiveValue
          :label="valueLabel"
          :is-empty="value === null"
        />
      </AppGroup>
    </VcButton>
  </FiltersActiveBadge>
</template>
