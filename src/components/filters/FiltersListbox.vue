<script setup lang="ts">
import {
  VcCheckboxControl,
  VcCheckboxRoot,
  VcIcon,
  VcKeyboardShortcut,
  VcSpinner,
} from '@wisemen/vue-core-components'
import {
  AnimatePresence,
  Motion,
} from 'motion-v'
import {
  ListboxContent,
  ListboxFilter,
  ListboxItem,
  ListboxRoot,
  useFilter,
} from 'reka-ui'
import {
  computed,
  onBeforeUnmount,
  ref,
  toValue,
  watch,
} from 'vue'
import { useI18n } from 'vue-i18n'

import AnimateHeight from '@/components/app/transition/AnimateHeight.vue'
import { useInjectFiltersContext } from '@/components/filters/filters.context'
import type {
  AutocompleteFilter,
  MultiAutocompleteFilter,
  MultiSelectFilter,
  SelectFilter,
  SelectFilterValue,
} from '@/components/filters/filters.type'
import { useFilterValue } from '@/components/filters/filterValue.composable'

const props = defineProps<{
  filter: AutocompleteFilter | MultiAutocompleteFilter | MultiSelectFilter | SelectFilter
}>()

const searchTerm = ref<string>('')

const value = useFilterValue<SelectFilterValue | SelectFilterValue[]>(props.filter.key)

const { hasInteractedViaKeyboard } = useInjectFiltersContext()

const { contains } = useFilter({ sensitivity: 'base' })

function isMultiple(value: unknown): value is SelectFilterValue[] {
  return Array.isArray(value)
}

function isOptionSelected(option: SelectFilterValue): boolean {
  if (isMultiple(value.value)) {
    return value.value.some(
      (value) => JSON.stringify(value) === JSON.stringify(option),
    )
  }

  return JSON.stringify(value.value) === JSON.stringify(option)
}

const isAutocomplete = computed<boolean>(() => {
  return props.filter.type === 'autocomplete' || props.filter.type === 'multi-autocomplete'
})

const filteredOptions = computed<SelectFilterValue[]>(() => {
  const isSearchTermEmpty = searchTerm.value.trim() === ''

  let options: SelectFilterValue[] = []

  if (isSearchTermEmpty || isAutocomplete.value) {
    options = toValue(props.filter.options)
  }
  else {
    options = toValue(props.filter.options).filter((option) =>
      contains(props.filter.displayFn(option), searchTerm.value))
  }

  return options
})

watch(searchTerm, (searchTerm) => {
  if (isAutocomplete.value) {
    (props.filter as AutocompleteFilter).onSearch(searchTerm)
  }
})

onBeforeUnmount(() => {
  if (isAutocomplete.value) {
    (props.filter as AutocompleteFilter).onSearch('')
  }
})

const i18n = useI18n()
</script>

<template>
  <ListboxRoot
    v-model="value"
    :selection-behavior="props.filter.isStatic && !isMultiple(value) ? 'replace' : 'toggle'"
    :multiple="isMultiple(value)"
    class="
      flex max-h-(--reka-popper-available-height) w-54 flex-col overflow-hidden
    "
  >
    <div
      v-if="isAutocomplete || toValue(props.filter.options).length > 7"
      class="border-secondary relative flex items-center border-b"
    >
      <ListboxFilter
        v-model="searchTerm"
        :auto-focus="hasInteractedViaKeyboard"
        :placeholder="i18n.t('component.filters.listbox.search_placeholder')"
        class="px-lg py-md text-primary w-full text-sm outline-none"
      />

      <VcSpinner
        v-if="(props.filter.type === 'autocomplete' || props.filter.type === 'multi-autocomplete') && props.filter.isLoading"
        class="mr-md text-disabled size-4"
      />

      <VcKeyboardShortcut
        v-if="props.filter.keyboardShortcutKeys !== undefined"
        :keyboard-keys="props.filter.keyboardShortcutKeys"
        class="mr-md"
      />
    </div>

    <div class="p-xs flex h-full flex-col overflow-hidden">
      <ListboxContent
        class="h-full overflow-y-auto"
      >
        <AnimateHeight>
          <ListboxItem
            v-for="option in filteredOptions"
            :key="JSON.stringify(option)"
            :value="option"
            class="
              group/listbox-item gap-x-md p-md flex cursor-default items-center
              rounded-sm text-sm
              data-highlighted:bg-primary-hover
            "
          >
            <div class="flex w-5 shrink-0 items-center justify-center">
              <VcCheckboxRoot
                v-if="isMultiple(value)"
                :model-value="isOptionSelected(option)"
                :aria-hidden="true"
                tabindex="-1"
                class="pointer-events-none"
              >
                <VcCheckboxControl />
              </VcCheckboxRoot>

              <AnimatePresence
                v-else
                :initial="false"
              >
                <Motion
                  v-if="isOptionSelected(option)"
                  :initial="{
                    opacity: 0,
                    scale: 0.9,
                  }"
                  :animate="{
                    opacity: 1,
                    scale: 1,
                  }"
                  :exit="{
                    opacity: 0,
                    scale: 0.9,
                  }"
                  :transition="{
                    duration: 0.2,
                    type: 'spring',
                    bounce: 0,
                  }"
                >
                  <VcIcon
                    icon="check"
                    class="text-tertiary size-4"
                  />
                </Motion>
              </AnimatePresence>
            </div>

            <span class="text-secondary truncate">
              {{ props.filter.displayFn(option) }}
            </span>
          </ListboxItem>

          <span
            v-if="filteredOptions.length === 0"
            class="px-md py-sm text-tertiary block text-sm"
          >
            {{ i18n.t('component.filters.listbox.no_results') }}
          </span>
        </AnimateHeight>
      </ListboxContent>
    </div>
  </ListboxRoot>
</template>
