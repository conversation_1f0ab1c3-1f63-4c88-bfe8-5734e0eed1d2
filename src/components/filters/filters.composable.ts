import { useKeyboardShortcut } from '@wisemen/vue-core-components'
import superjson from 'superjson'
import type {
  ComputedRef,
  Ref,
} from 'vue'
import {
  computed,
  reactive,
  ref,
  watch,
} from 'vue'

import type {
  Filter,
  FilterGroup,
  FilterKeys,
  FilterValues,
  GetFiltersFromGroups,
} from '@/components/filters/filters.type'
import { useRouteQueryState } from '@/composables/route-query-state/routeQueryState.composable'

interface Options<TGroups extends readonly FilterGroup<readonly Filter[]>[]> {
  filterGroups: () => TGroups
  persistInUrl?: boolean | string
}

export interface Filters<
  TGroups extends readonly FilterGroup<readonly Filter[]>[],
  T<PERSON>ilt<PERSON> extends readonly Filter[],
> {
  /**
   * List of currently active filters (i.e., those whose value differs from their default or are open).
   */
  activeFilters: ComputedRef<TFilters>
  /**
   * Clears all filters by resetting their values to their default values.
   */
  clearAllFilters: () => void
  /**
   * Clears the value of a filter and removes it from the active list.
   * @param key The filter key to clear.
   * @param onlyIfEmpty If true, the filter is only cleared if it is currently empty.
   */
  clearFilter: (key: FilterKeys<TFilters>, onlyIfEmpty?: boolean) => void
  /**
   * List of filter groups, each containing a list of filters.
   */
  filterGroups: ComputedRef<TGroups>
  /**
   * List of all filters, including those that are not currently active.
   */
  filters: ComputedRef<TFilters>
  /**
   * The key of the currently opened filter (e.g., for showing dropdowns or UI focus).
   */
  openFilterKey: Ref<FilterKeys<TFilters> | null>
  /**
   * The current filter values.
   */
  values: Ref<FilterValues<TFilters>>
}

const DEFAULT_ROUTE_QUERY_KEY = 'filters'

// TODO: should the order (activeFiltersKeyset) be stored in the URL as well?
// TODO: should a version be stored in the URL to prevent issues with changing filter definitions?
//       This key should be generated from the options.filters function.

export function useFilters<
  TGroups extends readonly FilterGroup<readonly Filter[]>[],
  TFilters extends readonly Filter[] = GetFiltersFromGroups<TGroups>,
>(options: Options<TGroups>): Filters<TGroups, TFilters> {
  const persistInUrl = options.persistInUrl === false ? false : options.persistInUrl ?? DEFAULT_ROUTE_QUERY_KEY

  const filterGroups = computed<TGroups>(options.filterGroups)
  const filters = computed<TFilters>(
    () => filterGroups.value.flatMap((group) => group.filters) as unknown as TFilters,
  )

  // Tracks the keys of currently active filters. Used to determine the order of the active filters.
  const activeFiltersKeySet = reactive<Set<FilterKeys<TFilters>>>(
    new Set(),
  )

  // The values of the filters
  const values = (persistInUrl === true || typeof persistInUrl === 'string'
    ? useRouteQueryState<FilterValues<TFilters>>({
        initialState: getDefaultValues(),
        key: persistInUrl === true
          ? DEFAULT_ROUTE_QUERY_KEY
          : persistInUrl,
        serializer: {
          parse: (value) => {
            return superjson.parse(atob(value))
          },
          serialize: (value) => {
            return btoa(superjson.stringify(value))
          },
        },
      })
    : ref<FilterValues<TFilters>>(getDefaultValues())) as Ref<FilterValues<TFilters>>

  // Tracks which filter key is currently open
  const openFilterKey = ref<FilterKeys<TFilters> | null>(null)

  // Filters that are currently active (either have non-default values or are open)
  const activeFilters = computed<TFilters>(
    () =>
      Array.from(activeFiltersKeySet).map(getFilterByKey).sort(
        (a, b) => Number(b.isStatic ?? false) - Number(a.isStatic ?? false),
      ) as unknown as TFilters,
  )

  function getDefaultValues(): FilterValues<TFilters> {
    return filters.value.reduce((acc, filter) => {
      acc[filter.key as keyof typeof acc] = filter.defaultValue ?? null

      return acc
    }, {} as FilterValues<TFilters>)
  }

  /**
   * Returns the fallback value for a filter key.
   * @param key The key of the filter
   * @returns The fallback value for the filter key.
   */
  function getFallbackValue(key: FilterKeys<TFilters>): unknown {
    const filter = getFilterByKey(key)

    switch (filter.type) {
      case 'boolean':
        return false
      case 'select':
      case 'autocomplete':
      case 'date':
        return null
      case 'date-range':
        return {
          from: null,
          until: null,
        }
      case 'multi-select':
      case 'multi-autocomplete':
        return []
    }
  }

  /**
   * Determines whether a filter is considered "active."
   * A filter is active if its current value differs from its default or if it is currently open.
   * @param key The key of the filter to check.
   */
  function isFilterActive(key: FilterKeys<TFilters>): boolean {
    const isFilterOpen = openFilterKey.value === key

    return isFilterOpen || !isFilterEmpty(key)
  }

  /**
   * Clears the value of the specified filter.
   * Optionally, it only clears the filter if the value is already empty.
   * @param key The key of the filter to clear.
   * @param onlyIfEmpty If true, the filter is only cleared if it is currently empty. False by default.
   */
  function clearFilter(key: FilterKeys<TFilters>, onlyIfEmpty = false, onlyIfNotStatic = false): void {
    const isEmpty = isFilterEmpty(key)
    const filter = getFilterByKey(key)

    if ((onlyIfEmpty && !isEmpty) || (onlyIfNotStatic && filter.isStatic)) {
      return
    }

    values.value[key] = getFallbackValue(key) as FilterValues<TFilters>[FilterKeys<TFilters>]
    activeFiltersKeySet.delete(key as any)
  }

  /**
   * Clears all non-static filters by resetting their values to their fallback values.
   */
  function clearAllFilters(): void {
    for (const filter of filters.value) {
      const typedKey = filter.key

      if (isFilterActive(typedKey)) {
        clearFilter(typedKey, false, true)
      }
    }
  }

  /**
   * Checks if a filter is empty (meaning its value is equal to its fallback value).
   * @param key The key of the filter to check.
   * @returns True if the filter is empty, false otherwise.
   */
  function isFilterEmpty(key: FilterKeys<TFilters>): boolean {
    const value = values.value[key]

    return superjson.stringify(value) === superjson.stringify(getFallbackValue(key))
  }

  /**
   * Returns the filter definition by its key.
   * @param key The key of the filter to retrieve.
   * @returns The filter definition.
   */
  function getFilterByKey(key: FilterKeys<TFilters>): Filter {
    return filters.value.find((filter) => filter.key === key)!
  }

  // Watches filter values and updates the set of active filter keys accordingly.
  watch(values, () => {
    for (const filter of filters.value) {
      const typedKey = filter.key

      if (isFilterActive(typedKey)) {
        activeFiltersKeySet.add(typedKey as any)
      }
      else {
        activeFiltersKeySet.delete(typedKey as any)
      }
    }
  }, {
    deep: true,
    immediate: true,
  })

  // Init keyboard shortcuts
  const filtersWithShortcuts = filters.value.filter((filter) => filter.keyboardShortcutKeys !== undefined)

  for (const filter of filtersWithShortcuts) {
    useKeyboardShortcut({
      keys: filter.keyboardShortcutKeys!,
      onTrigger(e) {
        e.preventDefault()

        openFilterKey.value = filter.key
        activeFiltersKeySet.add(filter.key as any)
      },
    })
  }

  return {
    activeFilters,
    clearAllFilters,
    clearFilter,
    filterGroups,
    filters,
    openFilterKey,
    values,
  }
}
