<script setup lang="ts">
import type { Icon } from '@wisemen/vue-core-components'
import { VcRadioGroup } from '@wisemen/vue-core-components'
import type { Field } from 'formango'

import AppCardWithIcon from '@/components/app/card/AppCardWithIcon.vue'
import FormRadioGroupItem from '@/components/form/FormRadioGroupItem.vue'
import FormRadioGroupLayout from '@/components/form/FormRadioGroupLayout.vue'
import { toFormField } from '@/utils/formango.util'

export interface FormRadioGroupWithCardsOption {
  title: string
  icon: Icon | null
  value: any
}

const props = withDefaults(defineProps<{
  testId?: string
  isDisabled?: boolean
  isReadonly?: boolean
  isRequired?: boolean
  cols?: number
  field: Field<any>
  label: string
  options: FormRadioGroupWithCardsOption[]
}>(), {
  isDisabled: false,
  isReadonly: false,
  isRequired: false,
  cols: 3,
})

const emit = defineEmits<{
  clear: []
}>()

function onClear(): void {
  emit('clear')
}
</script>

<template>
  <VcRadioGroup
    v-bind="toFormField(props.field)"
    :is-disabled="props.isDisabled"
    :is-required="props.isRequired"
    :label="props.label"
  >
    <FormRadioGroupLayout :cols="props.cols">
      <FormRadioGroupItem
        v-for="option in props.options"
        :key="option.value"
        :test-id="props.testId"
        :value="option.value"
      >
        <AppCardWithIcon
          :is-disabled="props.isDisabled"
          :is-selected="props.field.value.value === option.value"
          :title="option.title"
          :has-clear-button="!props.isRequired && !props.isReadonly"
          :icon="option.icon"
          variant="gray-light"
          class="h-full min-h-[74px]"
          @clear="onClear"
        />
      </FormRadioGroupItem>
    </FormRadioGroupLayout>
  </VcRadioGroup>
</template>
