<script setup lang="ts" generic="TModel extends { id: string } | null">
import type { Icon } from '@wisemen/vue-core-components'
import {
  VcCheckbox,
  VcRadioGroup,
  VcRadioGroupItemRoot,
  Vc<PERSON><PERSON>ner,
} from '@wisemen/vue-core-components'
import type { Field } from 'formango'
import {
  computed,
  ref,
  watch,
} from 'vue'
import { useI18n } from 'vue-i18n'

import AppHeightTransition from '@/components/app/AppHeightTransition.vue'
import WizardFormOptionCard from '@/components/form/wizard/components/WizardFormOptionCard.vue'
import WizardFormSectionFieldset from '@/components/form/wizard/components/WizardFormSectionFieldset.vue'
import { toFormField } from '@/utils/formango.util'

const props = defineProps<{
  id: string
  testId?: string
  title: string
  isLoadingSuggestedResources: boolean
  isReadonly?: boolean
  isRequired?: boolean
  isUnknown?: {
    field: Field<boolean, boolean>
    label: string
  }
  isVisible: boolean
  autocompleteValue: TModel
  cardDescription: (value: NonNullable<TModel>) => string
  cardIcon: Icon
  cardTitle: (value: NonNullable<TModel>) => string
  field: Field<TModel>
  suggestedResources: NonNullable<TModel>[]
}>()

const i18n = useI18n()
const selectedResource = computed<TModel | null>(() => {
  const selected = props.field.value.value

  if (selected === null) {
    return null
  }

  return props.suggestedResources.find((r) => r.id === selected.id) ?? selected
})
const hasOnlyOneSuggestedResource = computed<boolean>(() => props.suggestedResources.length === 1)

const transitionDelayInMs = ref<number>(100)

function selectFirstResourceWhenOnlyOne(suggestedResources: TModel[]): void {
  if (hasOnlyOneSuggestedResource.value && props.field.value.value === null) {
    props.field.setValue(suggestedResources[0])
  }
}

function onBeforeLeave(el: Element): void {
  const element = el as HTMLElement

  const {
    height,
    marginLeft,
    marginTop,
    width,
  } = window.getComputedStyle(element)

  element.style.left = `${element.offsetLeft - Number.parseFloat(marginLeft)}px`
  element.style.top = `${element.offsetTop - Number.parseFloat(marginTop)}px`
  element.style.width = width
  element.style.height = height
}

function onAfterEnter(): void {
  transitionDelayInMs.value = 0
}

function onClearSelectedResource(): void {
  props.field.setValue(null)
}

const isAutocompleteVisible = computed<boolean>(
  () => !props.isLoadingSuggestedResources
    && !hasOnlyOneSuggestedResource.value
    && selectedResource.value === null
    && props.isUnknown?.field.modelValue.value !== true,
)

watch(() => props.suggestedResources, (suggestedResources) => {
  if (props.isUnknown?.field.value.value === false) {
    selectFirstResourceWhenOnlyOne(suggestedResources)
  }
}, { immediate: true })

watch(() => props.autocompleteValue, (resource) => {
  if (resource === null) {
    return
  }

  props.field.setValue(resource)
})
</script>

<template>
  <Transition
    enter-from-class="opacity-0"
    leave-to-class="opacity-0"
    enter-active-class="duration-300 delay-300"
    leave-active-class="duration-300"
  >
    <WizardFormSectionFieldset
      v-if="props.isVisible"
      :is-valid="props.field.isValid.value || props.isReadonly"
      :title="props.title"
      :is-required="props.isRequired"
    >
      <template #right>
        <Transition
          enter-active-class="duration-300"
          leave-active-class="duration-300"
          leave-to-class="opacity-0"
          enter-from-class="opacity-0"
        >
          <div
            v-if="isAutocompleteVisible && !props.isReadonly"
            class="w-100 shrink-0"
          >
            <slot name="autocomplete" />
          </div>
        </Transition>
      </template>

      <template #default>
        <AppHeightTransition :duration="300">
          <Transition
            enter-active-class="duration-300"
            leave-active-class="duration-300"
            mode="out-in"
            enter-from-class="opacity-0"
            leave-to-class="opacity-0"
          >
            <div v-if="selectedResource === null">
              <VcRadioGroup
                v-bind="toFormField(props.field)"
                :id="props.id"
                :is-disabled="props.isUnknown?.field.modelValue.value === true"
                :aria-busy="props.isLoadingSuggestedResources"
                :label="i18n.t('module.waste_inquiry.update.customer_and_location.suggestions')"
              >
                <TransitionGroup
                  :appear="true"
                  :class="{
                    'grid-cols-3': props.suggestedResources.length === 3,
                    'grid-cols-2': props.suggestedResources.length === 2,
                  }"
                  enter-active-class="duration-500 ease-move-grid-item"
                  leave-active-class="duration-500 !absolute ease-move-grid-item"
                  move-class="duration-500 ease-move-grid-item"
                  enter-from-class="opacity-0 scale-95"
                  leave-to-class="opacity-0 scale-95"
                  tag="div"
                  class="gap-md grid"
                  @before-leave="onBeforeLeave"
                  @after-enter="onAfterEnter"
                >
                  <template v-if="props.suggestedResources.length > 0">
                    <VcRadioGroupItemRoot
                      v-for="(suggestedResource, suggestedResourceIndex) of props.suggestedResources"
                      :id="suggestedResource.id"
                      :key="suggestedResource.id"
                      :test-id="props.testId"
                      :value="suggestedResource"
                      :style="{
                        transitionDelay: `${suggestedResourceIndex * transitionDelayInMs}ms`,
                      }"
                    >
                      <WizardFormOptionCard
                        :id="suggestedResource.id"
                        :is-remove-button-visible="false"
                        :is-selected="false"
                        :is-disabled="props.isUnknown?.field.modelValue.value === true"
                        :icon="props.cardIcon"
                        :title="props.cardTitle(suggestedResource)"
                        :description="props.cardDescription(suggestedResource)"
                        class="
                          inset-ring-brand-500 h-full
                          group-focus-visible/radio-group-item:inset-ring
                        "
                      >
                        <template #title>
                          <slot
                            :resource="suggestedResource"
                            name="card-title"
                          />
                        </template>

                        <template #description>
                          <slot
                            :resource="suggestedResource"
                            name="card-description"
                          />
                        </template>
                      </WizardFormOptionCard>
                    </VcRadioGroupItemRoot>
                  </template>
                  <p
                    v-else-if="!props.isLoadingSuggestedResources"
                    class="text-secondary pb-lg text-sm italic"
                  >
                    {{ i18n.t('component.form.option.no_suggestions') }}
                  </p>
                  <VcSpinner
                    v-else
                    class="text-secondary m-auto size-6"
                  />
                </TransitionGroup>
              </VcRadioGroup>

              <VcCheckbox
                v-if="props.isUnknown !== undefined && !props.isReadonly"
                v-bind="toFormField(props.isUnknown.field)"
                :label="props.isUnknown.label"
                class="pt-md"
              />
            </div>

            <WizardFormOptionCard
              v-else-if="!props.isReadonly"
              :id="selectedResource.id"
              :is-remove-button-visible="!hasOnlyOneSuggestedResource"
              :is-selected="true"
              :icon="props.cardIcon"
              :title="props.cardTitle(selectedResource)"
              :description="props.cardDescription(selectedResource)"
              @remove="onClearSelectedResource"
            />
            <WizardFormOptionCard
              v-else-if="props.isReadonly"
              :id="selectedResource.id"
              :is-remove-button-visible="false"
              :is-selected="false"
              :icon="props.cardIcon"
              :title="props.cardTitle(selectedResource)"
              :description="props.cardDescription(selectedResource)"
            />
          </Transition>
        </AppHeightTransition>
      </template>
    </WizardFormSectionFieldset>
  </Transition>
</template>
