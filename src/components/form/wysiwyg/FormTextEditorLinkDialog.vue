<script setup lang="ts">
import {
  VcDialog,
  VcTextField,
} from '@wisemen/vue-core-components'
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'

import AppGroup from '@/components/app/AppGroup.vue'
import AppDialogActionCancel from '@/components/app/dialog/AppDialogActionCancel.vue'
import AppDialogActionPrimary from '@/components/app/dialog/AppDialogActionPrimary.vue'
import AppDialogActions from '@/components/app/dialog/AppDialogActions.vue'
import AppDialogHeader from '@/components/app/dialog/AppDialogHeader.vue'

const props = defineProps<{
  previousLink: string | null
}>()

const emit = defineEmits<{
  confirm: [value: string]
  delete: []
}>()

const inputLink = ref<string>(props.previousLink ?? '')

const i18n = useI18n()

function onConfirm(): void {
  emit('confirm', inputLink.value)
}
</script>

<template>
  <VcDialog class="w-dialog-sm">
    <div class="p-xl">
      <AppDialogHeader
        :title="i18n.t('component.editor.add_link')"
        :description="i18n.t('component.editor.link_begin_with_https')"
        :is-description-hidden="true"
        icon="alertCircle"
      />

      <VcTextField
        v-model="inputLink"
        :label="i18n.t('component.editor.link_label')"
        :placeholder="i18n.t('component.editor.link_placeholder')"
        class="mt-xl"
      />

      <AppDialogActions>
        <AppDialogActionCancel
          :label="i18n.t('shared.cancel')"
        />

        <AppGroup>
          <AppDialogActionPrimary
            :label="i18n.t('shared.save')"
            @confirm="onConfirm"
          />
        </AppGroup>
      </AppDialogActions>
    </div>
  </VcDialog>
</template>
