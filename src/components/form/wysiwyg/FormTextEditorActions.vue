<script setup lang="ts">
import { RovingFocusGroup } from 'reka-ui'
import { useI18n } from 'vue-i18n'

import FormTextEditorAction from './FormTextEditorAction.vue'

const props = defineProps<{
  isBoldActive: boolean
  isH2Active: boolean
  isH3Active: boolean
  isItalicActive: boolean
  isLinkActive: boolean
  isUnderlineActive: boolean
  areTextLevelsHidden?: boolean
}>()

const emit = defineEmits<{
  toggleBold: []
  toggleH2: []
  toggleH3: []
  toggleItalic: []
  toggleLink: []
  toggleUnderline: []
}>()

function onToggleBold(): void {
  emit('toggleBold')
}

function onToggleItalic(): void {
  emit('toggleItalic')
}

function onToggleH3(): void {
  emit('toggleH3')
}

function onToggleH2(): void {
  emit('toggleH2')
}

function onToggleLink(): void {
  emit('toggleLink')
}

function onToggleUnderline(): void {
  emit('toggleUnderline')
}

const i18n = useI18n()
</script>

<template>
  <RovingFocusGroup
    class="
      gap-x-xs border-secondary flex items-center border-b border-solid p-2
    "
  >
    <FormTextEditorAction
      :is-active="props.isBoldActive"
      :label="i18n.t('component.editor.action.bold')"
      icon="bold"
      @action="onToggleBold"
    />

    <FormTextEditorAction
      :is-active="props.isItalicActive"
      :label="i18n.t('component.editor.action.italic')"
      icon="italic"
      @action="onToggleItalic"
    />

    <FormTextEditorAction
      :is-active="props.isUnderlineActive"
      :label="i18n.t('component.editor.action.underline')"
      icon="underline"
      @action="onToggleUnderline"
    />

    <FormTextEditorAction
      :is-active="props.isLinkActive"
      :label="i18n.t('component.editor.action.link')"
      icon="link"
      @action="onToggleLink"
    />

    <FormTextEditorAction
      :is-active="props.isH2Active"
      :is-hidden="props.areTextLevelsHidden"
      :label="i18n.t('component.editor.action.h2')"
      @action="onToggleH2"
    />

    <FormTextEditorAction
      :is-active="props.isH3Active"
      :is-hidden="props.areTextLevelsHidden"
      :label="i18n.t('component.editor.action.h3')"
      @action="onToggleH3"
    />
  </RovingFocusGroup>
</template>
