<script setup lang="ts">
import type { FileUploadInfo } from '@wisemen/vue-core-components'
import {
  useVcToast,
  VcFileUploadRoot,
  VcFileUploadTrigger,
  VcFormField,
  VcIcon,
} from '@wisemen/vue-core-components'
import { AnimatePresence } from 'motion-v'
import {
  computed,
  useId,
} from 'vue'
import { useI18n } from 'vue-i18n'

import { FileService } from '@/api/services/file.service'
import type { MimeType } from '@/client/types.gen'
import AppGroup from '@/components/app/AppGroup.vue'
import AppHeightTransition from '@/components/app/AppHeightTransition.vue'
import AppCard from '@/components/app/card/AppCard.vue'
import FormFileUploadItem from '@/components/form/file-upload/FormFileUploadItem.vue'
import FormFileUploadThumbnailItem from '@/components/form/file-upload/FormFileUploadThumbnailItem.vue'
import type { S3File } from '@/models/s3-file/s3File.model'
import { FileUtil } from '@/utils/file.util'

const props = withDefaults(defineProps<{
  id?: string | null
  isDisabled?: boolean
  isRequired?: boolean
  isTouched?: boolean
  display?: 'block' | 'inline'
  errors?: string[]
  hint?: string | null
  label?: string | null
  maxFileSizeMb?: number
  mimeTypes: MimeType[]
}>(), {
  id: null,
  isDisabled: false,
  isRequired: false,
  isTouched: false,
  display: 'inline',
  errors: () => [],
  hint: null,
  label: null,
})

const modelValue = defineModel<S3File | null>({ required: true })

const i18n = useI18n()

const delegatedModelValue = computed<S3File[]>({
  get: () => {
    if (modelValue.value === null) {
      return []
    }

    return [
      modelValue.value,
    ]
  },
  set: (value) => {
    const [
      file,
    ] = value

    modelValue.value = file ?? null
  },
})

const inputId = props.id ?? useId()
const toast = useVcToast()

async function getFileInfo(name: string, mimeType: string): Promise<FileUploadInfo> {
  const response = await FileService.upload(name, mimeType as MimeType)

  return {
    uuid: response.uuid,
    uploadUrl: response.url,
  }
}

async function confirmUpload(): Promise<void> {}

function onFilesRejected(rejectedFiles: File[]): void {
  toast.error({
    title: i18n.t('component.file_upload.files_rejected_toast.title'),
    description: rejectedFiles.map((file) => file.name).join(', '),
  })
}
</script>

<template>
  <VcFormField
    :for="inputId"
    :is-required="props.isRequired"
    :is-touched="props.isTouched"
    :error-message="props.errors[0]"
    :hint="props.hint"
    :label="props.label"
    class="w-full"
  >
    <VcFileUploadRoot
      v-slot="{ items }"
      v-model="delegatedModelValue"
      :is-disabled="props.isDisabled"
      :accept="props.mimeTypes"
      :get-file-info="getFileInfo"
      :confirm-upload="confirmUpload"
      :is-valid-file="(file) => file.size <= FileUtil.mbToBytes(props.maxFileSizeMb ?? 100)"
      @files-rejected="onFilesRejected"
    >
      <AppHeightTransition class="w-full">
        <AppGroup
          direction="col"
          align="start"
          class="w-full"
        >
          <VcFileUploadTrigger
            v-if="items.length === 0"
            :class="{
              'h-52 !w-72': props.display === 'block',
            }"
            class="group"
          >
            <AppCard
              :variant="props.isDisabled ? 'disabled' : 'transparent'"
              :is-disabled="props.isDisabled"
              :class="{
                'flex size-full flex-col items-center justify-center': props.display === 'block',
              }"
              class="!border-dashed"
            >
              <VcIcon
                icon="imagePlus"
                class="text-tertiary size-10"
              />
            </AppCard>
          </VcFileUploadTrigger>

          <AnimatePresence
            :initial="false"
            mode="popLayout"
          >
            <template v-if="props.display === 'inline'">
              <FormFileUploadItem
                v-for="item of items"
                :key="item.key"
                :item="item"
              />
            </template>
            <template v-else>
              <FormFileUploadThumbnailItem
                v-for="item of items"
                :key="item.key"
                :item="item"
              />
            </template>
          </AnimatePresence>
        </AppGroup>
      </AppHeightTransition>
    </VcFileUploadRoot>
  </VcFormField>
</template>
