<script setup lang="ts">
import type {
  FileUploadInfo,
  FileUploadItem,
} from '@wisemen/vue-core-components'
import {
  VcFileUploadRoot,
  VcFormField,
} from '@wisemen/vue-core-components'
import { AnimatePresence } from 'motion-v'
import { useId } from 'vue'

import { FileService } from '@/api/services/file.service'
import type { MimeType } from '@/client/types.gen'
import AppGroup from '@/components/app/AppGroup.vue'
import AppHeightTransition from '@/components/app/AppHeightTransition.vue'
import FormFileUploadItem from '@/components/form/file-upload/FormFileUploadItem.vue'
import FormFileUploadTriggerCard from '@/components/form/file-upload/FormFileUploadTriggerCard.vue'
import type { S3File } from '@/models/s3-file/s3File.model'
import { FileUtil } from '@/utils/file.util'

const props = withDefaults(defineProps<{
  id?: string | null
  isDisabled?: boolean
  isReadonly?: boolean
  isRequired?: boolean
  isTouched?: boolean
  downloadFn?: (item: FileUploadItem) => Promise<void>
  errors?: string[]
  hideDeleteButton?: boolean
  hint?: string | null
  label?: string | null
  maxFileSizeMb?: number
  mimeTypes: MimeType[]
}>(), {
  id: null,
  isDisabled: false,
  isRequired: false,
  isTouched: false,
  errors: () => [],
  hint: null,
  label: null,
})

const value = defineModel<S3File[]>({ required: true })

const inputId = props.id ?? useId()

async function getFileInfo(name: string, mimeType: string): Promise<FileUploadInfo> {
  const response = await FileService.upload(name, mimeType as MimeType)

  return {
    uuid: response.uuid,
    uploadUrl: response.url,
  }
}

async function confirmUpload(): Promise<void> {}
</script>

<template>
  <VcFormField
    :for="inputId"
    :is-required="props.isRequired"
    :is-touched="props.isTouched"
    :error-message="props.errors?.[0]"
    :hint="props.hint"
    :label="props.label"
    class="w-full"
  >
    <VcFileUploadRoot
      v-slot="{ items }"
      v-model="value"
      :is-disabled="props.isDisabled"
      :accept="props.mimeTypes"
      :confirm-upload="confirmUpload"
      :get-file-info="getFileInfo"
      :is-valid-file="(file) => file.size <= FileUtil.mbToBytes(props.maxFileSizeMb ?? 100)"
    >
      <AppHeightTransition class="w-full">
        <AppGroup
          direction="col"
          align="start"
          class="w-full"
        >
          <FormFileUploadTriggerCard
            v-if="!props.isReadonly"
            :id="inputId"
            :is-disabled="props.isDisabled"
            :mime-types="props.mimeTypes"
          />

          <AnimatePresence
            :initial="false"
            mode="popLayout"
          >
            <FormFileUploadItem
              v-for="item of items"
              :key="item.key"
              :item="item"
              :hide-delete-button="props.hideDeleteButton || props.isReadonly"
              :download-fn="props.downloadFn"
            />
          </AnimatePresence>
        </AppGroup>
      </AppHeightTransition>
    </VcFileUploadRoot>
  </VcFormField>
</template>
