<script setup lang="ts">
import { useInfiniteScroll } from '@vueuse/core'
import { useTemplateRef } from 'vue'

const props = withDefaults(defineProps<{
  distance?: number
  onNext: () => Promise<void> | void
}>(), { distance: 250 })

const infiniteScrollElementRef = useTemplateRef('infiniteScrollElementRef')

useInfiniteScroll(
  infiniteScrollElementRef,
  async () => {
    await props.onNext()
  },
  { distance: props.distance },
)
</script>

<template>
  <div
    ref="infiniteScrollElementRef"
    class="overflow-auto"
  >
    <slot />
  </div>
</template>
