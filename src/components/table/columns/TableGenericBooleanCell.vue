<script setup lang="ts">
import { VcIcon } from '@wisemen/vue-core-components'

import DataTableCell from '@/components/table/data-table/DataTableCell.vue'

const props = defineProps<{
  value: boolean | null
}>()
</script>

<template>
  <DataTableCell>
    <VcIcon
      v-if="props.value === true"
      icon="check"
      class="text-brand-tertiary size-5"
    />

    <VcIcon
      v-else-if="props.value === false"
      icon="close"
      class="text-error-primary size-5"
    />
    <span v-else>-</span>
  </DataTableCell>
</template>
