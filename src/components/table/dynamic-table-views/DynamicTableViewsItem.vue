<script setup lang="ts">
import { VcButton } from '@wisemen/vue-core-components'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import { Permission } from '@/client/types.gen'
import AppGroup from '@/components/app/AppGroup.vue'
import type { useDynamicTableV2 } from '@/composables/dynamic-table/dynamicTableV2.composable'
import type { DynamicTableViewIndex } from '@/models/dynamic-table/view/dynamicTableViewIndex.model'
import { useAuthStore } from '@/stores/auth.store'

const props = defineProps<{
  dynamicTable: ReturnType<typeof useDynamicTableV2>
  item: DynamicTableViewIndex
}>()

const emit = defineEmits<{
  select: []
  updateActiveView: []
}>()

const i18n = useI18n()
const authStore = useAuthStore()

const isSelected = computed<boolean>(() => props.dynamicTable.activeView.value?.uuid === props.item.uuid)
const hasDynamicTableManagePermission = computed<boolean>(() => {
  return authStore.hasPermission(Permission.DYNAMIC_TABLE_VIEW_MANAGE)
})

const isDefaultViewIndicatorVisible = computed<boolean>(() => {
  const doesUserDefaultViewExist = props.dynamicTable.views.value.some(
    (view) => view.isUserDefault,
  )

  if (doesUserDefaultViewExist) {
    return props.item.isUserDefault
  }

  return props.item.isDefaultGlobal
})

const canUpdateView = computed<boolean>(() => {
  return !props.item.isGlobal || hasDynamicTableManagePermission.value
})
</script>

<template>
  <li
    :class="{
      'bg-brand-primary-alt hover:brightness-98': isSelected,
      'hover:bg-primary-hover': !isSelected,
    }"
    class="flex h-10 items-center rounded-md"
  >
    <button
      variant="unstyled"
      type="button"
      class="
        px-xl block size-full cursor-pointer truncate text-left duration-100
      "
      @click="emit('select')"
    >
      <AppGroup justify="between">
        <AppGroup>
          <div class="size-1.5">
            <div
              v-if="isDefaultViewIndicatorVisible"
              class="bg-brand-solid size-full rounded-full"
            />
          </div>

          <span
            :class="{
              'text-primary': isSelected,
              'text-secondary': !isSelected,
            }"
            class="truncate text-sm font-medium"
          >
            {{ props.item.name }}
          </span>
        </AppGroup>

        <AppGroup>
          <span
            v-if="props.item.isGlobal"
            class="text-quaternary text-xs"
          >
            {{ i18n.t('component.table.dynamic_view.shared') }}
          </span>
        </AppGroup>
      </AppGroup>
    </button>

    <VcButton
      v-if="props.dynamicTable.hasActiveViewBeenChanged.value && isSelected && canUpdateView"
      :is-loading="props.dynamicTable.isUpdatingActiveView.value"
      :class-config="{
        root: 'h-8 rounded-md bg-primary not-disabled:data-[loading=false]:hover:bg-primary px-sm shrink-0',
        content: 'text-secondary',
      }"
      variant="secondary"
      class="mr-md"
      @click="emit('updateActiveView')"
    >
      {{ i18n.t('component.table.dynamic_view.update') }}
    </VcButton>
  </li>
</template>
