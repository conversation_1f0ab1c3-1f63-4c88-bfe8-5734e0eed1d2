<script setup lang="ts">
import {
  VcButton,
  VcIcon,
  VcPopover,
  VcTooltip,
} from '@wisemen/vue-core-components'
import {
  AnimatePresence,
  Motion,
} from 'motion-v'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import AppGroup from '@/components/app/AppGroup.vue'
import AppSeparator from '@/components/app/AppSeparator.vue'
import AnimateWidth from '@/components/app/transition/AnimateWidth.vue'
import DynamicTableViewsItem from '@/components/table/dynamic-table-views/DynamicTableViewsItem.vue'
import type { useDynamicTableV2 } from '@/composables/dynamic-table/dynamicTableV2.composable'

const props = defineProps<{
  dynamicTable: ReturnType<typeof useDynamicTableV2>
}>()

const i18n = useI18n()

const hasUserViews = computed<boolean>(() => {
  return props.dynamicTable.views.value.some((view) => !view.isGlobal)
})

// This function assumes the views are sorted global first, then user-defined views.
// If the sorting changes, this function may need to be updated.
function isLastGlobalView(viewIndex: number): boolean {
  const views = props.dynamicTable.views.value
  const globalViewCount = views.filter((view) => view.isGlobal).length

  return viewIndex === globalViewCount - 1
}
</script>

<template>
  <VcPopover
    v-if="props.dynamicTable.activeView.value !== null"
    :is-popover-arrow-hidden="true"
    popover-align="end"
  >
    <template #trigger>
      <VcButton
        icon-right="chevronDown"
        size="sm"
        variant="secondary"
      >
        <div class="overflow-hidden">
          <AnimateWidth :duration-in-ms="200">
            <AnimatePresence mode="popLayout">
              <Motion
                :key="props.dynamicTable.activeView.value.name"
                :initial="{ opacity: 0 }"
                :animate="{ opacity: 1 }"
                :exit="{ opacity: 0 }"
                :transition="{
                  duration: 0.2,
                  type: 'spring',
                  bounce: 0,
                }"
              >
                {{ props.dynamicTable.activeView.value.name }}
              </Motion>
            </AnimatePresence>
          </AnimateWidth>
        </div>

        <AnimatePresence>
          <Motion
            v-if="props.dynamicTable.hasActiveViewBeenChanged.value"
            :aria-hidden="true"
            :initial="{ opacity: 0, width: 0 }"
            :animate="{ opacity: 1, width: 'auto' }"
            :exit="{ opacity: 0, width: 0 }"
            :transition="{
              duration: 0.2,
              type: 'spring',
              bounce: 0,
            }"
          >
            *
          </Motion>
        </AnimatePresence>
      </VcButton>
    </template>

    <template #content>
      <div class="w-64">
        <header class="px-xl py-lg border-secondary border-b">
          <h1 class="text-secondary text-xs font-semibold">
            Views
          </h1>
        </header>

        <div class="p-xs gap-xs flex flex-col">
          <template
            v-for="(view, viewIndex) of props.dynamicTable.views.value"
            :key="view.uuid"
          >
            <DynamicTableViewsItem
              :item="view"
              :dynamic-table="props.dynamicTable"
              @select="props.dynamicTable.selectView(view)"
              @update-active-view="props.dynamicTable.updateActiveView"
            />

            <AppSeparator v-if="isLastGlobalView(viewIndex)" />
          </template>

          <div class="bg-primary sticky bottom-0 z-1">
            <AppSeparator v-if="hasUserViews" />

            <VcTooltip :is-disabled="props.dynamicTable.hasActiveViewBeenChanged.value">
              <template #trigger>
                <button
                  :disabled="!props.dynamicTable.hasActiveViewBeenChanged.value"
                  type="button"
                  class="
                    disabled:text-disabled disabled:cursor-not-allowed
                    text-secondary px-lg mt-xs h-10 w-full cursor-pointer
                    rounded-md
                    not-disabled:hover:bg-primary-hover
                  "
                  @click="props.dynamicTable.createNewView"
                >
                  <AppGroup>
                    <VcIcon
                      icon="plus"
                      class="size-4"
                    />

                    <span class="text-sm font-medium">
                      {{ i18n.t('component.table.dynamic_view.save_as_new') }}
                    </span>
                  </AppGroup>
                </button>
              </template>

              <template #content>
                <p class="text-secondary px-lg py-md w-56 text-center text-xs">
                  {{ i18n.t('component.table.dynamic_view.save_as_new_disabled') }}
                </p>
              </template>
            </VcTooltip>
          </div>
        </div>
      </div>
    </template>
  </VcPopover>

  <div
    v-else
    class="bg-secondary h-9 w-24 rounded-full"
  />
</template>
