<script setup lang="ts">
import { VcCheckbox } from '@wisemen/vue-core-components'
import { Motion } from 'motion-v'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import TableSettingsLayout from '@/components/table/TableSettingsLayout.vue'
import type { useDynamicTableV2 } from '@/composables/dynamic-table/dynamicTableV2.composable'

const props = defineProps<{
  layoutId: string
  dynamicTable: ReturnType<typeof useDynamicTableV2>
}>()

const emit = defineEmits<{
  back: []
}>()

const i18n = useI18n()

const isOnlyOneColumnVisible = computed<boolean>(() => {
  return props.dynamicTable.columns.value.filter((column) => column.isVisible).length === 1
})
</script>

<template>
  <TableSettingsLayout
    :can-go-back="true"
    :layout-id="props.layoutId"
    :title="i18n.t('component.table.dynamic_view.toggle_columns')"
    @back="emit('back')"
  >
    <ul class="py-sm">
      <Motion
        v-for="column of props.dynamicTable.columns.value"
        :key="column.uuid"
        :layout-id="column.uuid"
        as="li"
        class="px-2xl py-sm"
      >
        <VcCheckbox
          :model-value="column.isVisible"
          :is-disabled="(isOnlyOneColumnVisible && column.isVisible) || !column.isHideable"
          :label="column.label"
          @update:model-value="(value) => props.dynamicTable.updateColumnVisibility(column, value)"
        />
      </Motion>
    </ul>
  </TableSettingsLayout>
</template>
