<script setup lang="ts">
import {
  VcIcon<PERSON>utton,
  VcTooltip,
} from '@wisemen/vue-core-components'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

import AppGroup from '@/components/app/AppGroup.vue'
import AppSeparator from '@/components/app/AppSeparator.vue'
import TableSettingsLayout from '@/components/table/TableSettingsLayout.vue'
import type { DynamicTableViewIndex } from '@/models/dynamic-table/view/dynamicTableViewIndex.model'
import { useAuthStore } from '@/stores/auth.store'

const props = defineProps<{
  layoutId: string
  tableViews: DynamicTableViewIndex[]

}>()

const emit = defineEmits<{
  back: []
  deleteView: [view: DynamicTableViewIndex]
  updateView: [view: DynamicTableViewIndex]
}>()

const i18n = useI18n()
const authStore = useAuthStore()

const isUserSystemAdmin = computed<boolean>(() => {
  return authStore.isSystemAdmin()
})

const hasUserViews = computed<boolean>(() => {
  return props.tableViews.some((view) => !view.isGlobal)
})

function isDeleteDisabled(view: DynamicTableViewIndex): boolean {
  return props.tableViews.length <= 1
    || view.isDefaultGlobal
    || (view.isGlobal && !isUserSystemAdmin.value)
}

function getTooltipMessage(view: DynamicTableViewIndex): string {
  if (props.tableViews.length === 1) {
    return i18n.t('component.table.dynamic_view.cannot_delete_last_view')
  }
  if (view.isDefaultGlobal) {
    return i18n.t('component.table.dynamic_view.cannot_delete_global_default_view')
  }

  return i18n.t('component.table.dynamic_view.cannot_delete_global_view_if_not_admin')
}

function isLastGlobalView(viewIndex: number): boolean {
  const lastGlobalViewIndex = ((): number => {
    for (let i = props.tableViews.length - 1; i >= 0; i--) {
      if (props.tableViews[i].isGlobal) {
        return i
      }
    }

    return -1
  })()

  return viewIndex === lastGlobalViewIndex
}

function onUpdateView(view: DynamicTableViewIndex): void {
  emit('updateView', view)
}

function onDeleteView(view: DynamicTableViewIndex): void {
  emit('deleteView', view)
}
</script>

<template>
  <TableSettingsLayout
    :layout-id="props.layoutId"
    :can-go-back="true"
    :title="i18n.t('component.table.dynamic_view.manage_views')"
    @back="emit('back')"
  >
    <div class="p-md">
      <template
        v-for="(view, viewIndex) of props.tableViews"
        :key="view.uuid"
      >
        <AppGroup
          justify="between"
          class="pl-xl py-xs text-secondary text-sm font-semibold"
        >
          <p class="max-w-40 truncate">
            {{ view.name }}
          </p>
          <AppGroup gap="xxs">
            <VcIconButton
              :label="i18n.t('component.table.dynamic_view.edit_view')"
              icon="pencil"
              size="sm"
              variant="tertiary"
              class="text-quaternary ml-auto w-4"
              @click="onUpdateView(view)"
            />

            <VcTooltip
              :is-disabled="!isDeleteDisabled(view)"
              :disable-close-on-trigger-click="true"
            >
              <template #trigger>
                <VcIconButton
                  :is-disabled="isDeleteDisabled(view)"
                  :label="i18n.t('component.table.dynamic_view.delete_view')"
                  icon="trash"
                  size="sm"
                  variant="destructive-tertiary"
                  class="ml-auto w-4"
                  @click="onDeleteView(view)"
                />
              </template>
              <template #content>
                <p class="p-md text-secondary text-xs">
                  {{ getTooltipMessage(view) }}
                </p>
              </template>
            </VcTooltip>
          </AppGroup>
        </AppGroup>

        <AppSeparator v-if="isLastGlobalView(viewIndex) && hasUserViews" />
      </template>
    </div>
  </TableSettingsLayout>
</template>
