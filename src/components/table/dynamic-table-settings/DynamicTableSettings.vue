<script setup lang="ts">
import {
  useVcDialog,
  useVcToast,
  VcIconButton,
  VcPopover,
} from '@wisemen/vue-core-components'
import {
  AnimatePresence,
  MotionConfig,
} from 'motion-v'
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'

import AppAnimateHeight from '@/components/animate-height/AppAnimateHeight.vue'
import AppSeparator from '@/components/app/AppSeparator.vue'
import DynamicTableSettingsChangeOrder from '@/components/table/dynamic-table-settings/DynamicTableSettingsChangeOrder.vue'
import DynamicTableSettingsManageViews from '@/components/table/dynamic-table-settings/DynamicTableSettingsManageViews.vue'
import DynamicTableSettingsToggleColumns from '@/components/table/dynamic-table-settings/DynamicTableSettingsToggleColumns.vue'
import TableSettingsItem from '@/components/table/TableSettingsItem.vue'
import TableSettingsLayout from '@/components/table/TableSettingsLayout.vue'
import { useApiErrorToast } from '@/composables/api-error-toast/apiErrorToast.composable'
import type { useDynamicTableV2 } from '@/composables/dynamic-table/dynamicTableV2.composable'
import type { DynamicTableViewIndex } from '@/models/dynamic-table/view/dynamicTableViewIndex.model'
import { UuidUtil } from '@/utils/uuid.util'

const props = defineProps<{
  dynamicTable: ReturnType<typeof useDynamicTableV2>
}>()

type Layout = 'change-order' | 'manage-views' | 'toggle-columns'

const i18n = useI18n()

const apiErrorToast = useApiErrorToast()
const toast = useVcToast()

const activeLayout = ref<Layout | null>(null)
const confirmDialog = useVcDialog({ component: () => import('@/components/dialog/AppConfirmDialog.vue') })
const updateDynamicViewDialog = useVcDialog({ component: () => import('@/components/app/table/AppTableEditDynamicViewDialog.vue') })

const layoutId = ref<string>(UuidUtil.getRandom())

function onLayoutChange(layout: Layout | null): void {
  activeLayout.value = layout
}

function onClose(): void {
  activeLayout.value = null
  layoutId.value = UuidUtil.getRandom()
}

function onUpdateView(view: DynamicTableViewIndex): void {
  if (props.dynamicTable.activeView.value === null) {
    return
  }

  updateDynamicViewDialog.open({
    disableUncheckGlobalDefault: view.isDefaultGlobal,
    tableName: props.dynamicTable.dynamicTableName,
    view,
    onSaveView: () => {
      updateDynamicViewDialog.close()
    },
  })
}

function onDeleteView(view: DynamicTableViewIndex): void {
  if (props.dynamicTable.activeView.value === null) {
    return
  }

  confirmDialog.open({
    title: i18n.t('component.table.dynamic_view.delete_view'),
    isDestructive: true,
    cancelText: i18n.t('shared.cancel'),
    confirmText: i18n.t('shared.delete'),
    description: i18n.t('component.table.dynamic_view.delete_view_description', { name: view.name }),
    onConfirm: async () => {
      confirmDialog.close()

      try {
        await props.dynamicTable.deleteView(view)
        toast.info({
          title: i18n.t('component.table.dynamic_view.view_deleted'),
          description: i18n.t('component.table.dynamic_view.view_deleted_description', { name: view.name }),
        })
      }
      catch (error) {
        apiErrorToast.show(error)
      }
    },
  })
}
</script>

<template>
  <VcPopover
    :is-popover-arrow-hidden="true"
    popover-align="end"
    @update:is-open="onClose"
  >
    <template #trigger>
      <VcIconButton
        :label="i18n.t('component.table.dynamic_view.settings')"
        icon="threeDots"
        size="sm"
        variant="tertiary"
      />
    </template>

    <template #content>
      <AppAnimateHeight>
        <div class="w-64">
          <MotionConfig
            :transition="{
              duration: 0.4,
              type: 'spring',
              bounce: 0,
            }"
          >
            <AnimatePresence mode="popLayout">
              <TableSettingsLayout
                v-if="activeLayout === null"
                :can-go-back="false"
                :layout-id="layoutId"
                :title="i18n.t('component.table.dynamic_view.settings')"
              >
                <TableSettingsItem
                  :label="i18n.t('component.table.dynamic_view.toggle_columns')"
                  icon="eyeOff"
                  @click="onLayoutChange('toggle-columns')"
                />

                <TableSettingsItem
                  :label="i18n.t('component.table.dynamic_view.change_order')"
                  icon="switchHorizontal"
                  @click="onLayoutChange('change-order')"
                />

                <TableSettingsItem
                  :label="i18n.t('component.table.dynamic_view.manage_views')"
                  icon="building"
                  @click="onLayoutChange('manage-views')"
                />

                <AppSeparator />

                <TableSettingsItem
                  :is-disabled="!props.dynamicTable.hasActiveViewBeenChanged.value"
                  :label="i18n.t('component.table.dynamic_view.reset_all_changes')"
                  icon="refresh"
                  @click="props.dynamicTable.resetChangesToActiveView"
                />
              </TableSettingsLayout>

              <DynamicTableSettingsToggleColumns
                v-if="activeLayout === 'toggle-columns'"
                :layout-id="layoutId"
                :dynamic-table="props.dynamicTable"
                @back="onLayoutChange(null)"
              />

              <DynamicTableSettingsChangeOrder
                v-if="activeLayout === 'change-order'"
                :dynamic-table="props.dynamicTable"
                :layout-id="layoutId"
                @back="onLayoutChange(null)"
              />

              <DynamicTableSettingsManageViews
                v-if="activeLayout === 'manage-views'"
                :layout-id="layoutId"
                :table-views="props.dynamicTable.views.value"
                @back="onLayoutChange(null)"
                @update-view="onUpdateView"
                @delete-view="onDeleteView"
              />
            </AnimatePresence>
          </MotionConfig>
        </div>
      </AppAnimateHeight>
    </template>
  </VcPopover>
</template>
