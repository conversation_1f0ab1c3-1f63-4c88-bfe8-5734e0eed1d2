<script setup lang="ts">
import { useDragAndDrop } from '@formkit/drag-and-drop/vue'
import { VcIcon } from '@wisemen/vue-core-components'
import { Motion } from 'motion-v'
import { watch } from 'vue'
import { useI18n } from 'vue-i18n'

import AppGroup from '@/components/app/AppGroup.vue'
import TableSettingsLayout from '@/components/table/TableSettingsLayout.vue'
import type { useDynamicTableV2 } from '@/composables/dynamic-table/dynamicTableV2.composable'

const props = defineProps<{
  layoutId: string
  dynamicTable: ReturnType<typeof useDynamicTableV2>
}>()

const emit = defineEmits<{
  back: []
}>()

const i18n = useI18n()

const visibleColumns = props.dynamicTable.columns.value.filter(
  (column) => column.isVisible,
)

const [
  parent,
  columns,
] = useDragAndDrop(visibleColumns, {
  dragHandle: '.handle',
  dragPlaceholderClass: 'opacity-25',
})

watch(columns, (columns) => {
  props.dynamicTable.updateColumnOrder(columns)
})
</script>

<template>
  <TableSettingsLayout
    :layout-id="props.layoutId"
    :can-go-back="true"
    :title="i18n.t('component.table.dynamic_view.change_order')"
    @back="emit('back')"
  >
    <div ref="parent">
      <Motion
        v-for="column of columns"
        :key="column.uuid"
        :layout-id="column.uuid"
        class="px-2xl py-md"
      >
        <AppGroup>
          <VcIcon
            icon="menu"
            class="handle text-tertiary py-xs box-content size-4 cursor-move"
          />

          <span class="text-secondary text-sm font-semibold">
            {{ column.label }}
          </span>
        </AppGroup>
      </Motion>
    </div>
  </TableSettingsLayout>
</template>
