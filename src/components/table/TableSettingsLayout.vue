<script setup lang="ts">
import { useInfiniteScroll } from '@vueuse/core'
import { VcIconButton } from '@wisemen/vue-core-components'
import { Motion } from 'motion-v'
import {
  computed,
  ref,
} from 'vue'
import { useI18n } from 'vue-i18n'

import AppGroup from '@/components/app/AppGroup.vue'
import AppSeparator from '@/components/app/AppSeparator.vue'

const props = defineProps<{
  layoutId: string
  title: string
  canGoBack: boolean
  onScrollToBottom?: () => Promise<void>
}>()

const emit = defineEmits<{
  back: []
}>()

const backButtonRef = ref<InstanceType<any> | null>(null)
const titleRef = ref<InstanceType<any> | null>(null)

const scrollContainerRef = ref<HTMLElement | null>(null)

const i18n = useI18n()

useInfiniteScroll(
  computed<HTMLElement | null>(() => scrollContainerRef.value),
  props.onScrollToBottom || ((): Promise<void> => Promise.resolve()),
  { offset: { bottom: 100 } },
)

function onLayoutAnimationComplete(): void {
  const backButtonEl = backButtonRef.value?.$el as HTMLElement
  const titleEl = titleRef.value?.$el as HTMLElement

  backButtonEl.style.opacity = '1'
  titleEl.style.opacity = '1'
}
</script>

<template>
  <Motion
    ref="scrollContainerRef"
    :layout="true"
    :transition="{ duration: 0 }"
    initial="initial"
    animate="animate"
    exit="exit"
  >
    <div class="px-xl py-lg">
      <AppGroup>
        <Motion
          v-if="props.canGoBack"
          ref="backButtonRef"
          :variants="{
            initial: { opacity: 0, x: -4 },
            animate: { opacity: 1, x: 0 },
            exit: { opacity: 0, x: -4 },
          }"
          class="relative size-4"
          @animation-complete="onLayoutAnimationComplete"
        >
          <VcIconButton
            :class-config="{
              root: 'size-6',
            }"
            :label="i18n.t('shared.back')"
            icon="chevronLeft"
            size="sm"
            variant="tertiary"
            class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
            @click="emit('back')"
          />
        </Motion>

        <Motion
          ref="titleRef"
          :as-child="true"
          :variants="{
            initial: { opacity: 0 },
            animate: { opacity: 1 },
            exit: { opacity: 0 },
          }"
          :layout-id="`${layoutId}-title`"
        >
          <span class="text-secondary text-xs font-semibold">
            {{ props.title }}
          </span>
        </Motion>
      </AppGroup>
    </div>

    <AppSeparator />

    <Motion
      :variants="{
        initial: { opacity: 0 },
        animate: { opacity: 1 },
        exit: { opacity: 0 },
      }"
      class="max-h-100 overflow-y-auto rounded-b-2xl"
    >
      <slot />
    </Motion>
  </Motion>
</template>
