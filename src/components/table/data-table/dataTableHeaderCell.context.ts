import type { ComputedRef } from 'vue'

import { useContext } from '@/composables/context/context.composable'
import type { useSort } from '@/composables/sort/sort.composable'
import type { DataTableColumn } from '@/types/table.type'

interface TableHeaderCellContext {
  column: ComputedRef<DataTableColumn<any>>
  sort: ReturnType<typeof useSort> | null
}

export const [
  useProvideTableHeaderCellContext,
  useInjectTableHeaderCellContext,
] = useContext<TableHeaderCellContext>('TableHeaderCell')
