<script setup lang="ts">
import { VcTableHeaderCellNext } from '@wisemen/vue-core-components'

import { useInjectTableHeaderCellContext } from '@/components/table/data-table/dataTableHeaderCell.context'

const {
  column, sort,
} = useInjectTableHeaderCellContext()
</script>

<template>
  <VcTableHeaderCellNext
    :is-sortable="sort?.existsSort(column.key)"
    :sort-direction="sort?.getSort(column.key)?.direction"
    class="whitespace-nowrap"
    @sort="sort?.setSort(column.key)"
  >
    <slot>
      {{ column.headerLabel }}
    </slot>
  </VcTableHeaderCellNext>
</template>
