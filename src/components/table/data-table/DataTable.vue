<script
  setup lang="ts"
  generic="TData,
  TSearchableColumn<PERSON>eys extends string,
  TSort extends string,
  TFilterGroups extends FilterGroup[],
  <PERSON><PERSON><PERSON><PERSON> extends Filter[]"
>
import { useInfiniteScroll } from '@vueuse/core'
import type {
  TableColumn,
  TableRowAction,
} from '@wisemen/vue-core-components'
import {
  VcTableBodyNext,
  VcTableContentNext,
  VcTableRootNext,
  VcTableRowNext,
  VcTableScrollContainerNext,
} from '@wisemen/vue-core-components'
import {
  AnimatePresence,
  Motion,
} from 'motion-v'
import {
  computed,
  useSlots,
  useTemplateRef,
} from 'vue'

import type { Filters } from '@/components/filters/filters.composable'
import type {
  Filter,
  FilterGroup,
} from '@/components/filters/filters.type'
import { useProvideDataTableContext } from '@/components/table/data-table/dataTable.context'
import DataTableCellHost from '@/components/table/data-table/DataTableCellHost.vue'
import DataTableEmptyOrLoadingStateOverlay from '@/components/table/data-table/DataTableEmptyOrLoadingStateOverlay.vue'
import DataTableEmptyState from '@/components/table/data-table/DataTableEmptyState.vue'
import DataTableHeader from '@/components/table/data-table/DataTableHeader.vue'
import type { useSearch } from '@/composables/search/search.composable'
import type { useSearchableTableColumns } from '@/composables/searchable-table-columns/searchableTableColumns.composable'
import type { useSort } from '@/composables/sort/sort.composable'
import type {
  DataTableButtonAction,
  DataTableRowAction,
} from '@/types/table.type'
import { LoggerUtil } from '@/utils/logger.util'

const props = withDefaults(defineProps<{
  hasShadow?: boolean
  isFirstColumnSticky?: boolean
  isLastColumnSticky?: boolean
  isLoading: boolean
  columns: TableColumn<TData>[]
  data: TData[]
  disableTopLeftBorderRadius?: boolean
  error: unknown
  filters?: Filters<TFilterGroups, TFilters> | null
  getKey: (item: TData, index: number) => string
  rowAction?: DataTableRowAction<TData> | null
  search?: ReturnType<typeof useSearch> | null
  searchableColumns?: ReturnType<typeof useSearchableTableColumns<TSearchableColumnKeys>> | null
  sort?: ReturnType<typeof useSort<TSort>> | null
  onNextPage: () => void
}>(), {
  hasShadow: false,
  isFirstColumnSticky: false,
  isLastColumnSticky: false,
  disableTopLeftBorderRadius: false,
  filters: null,
  rowAction: null,
  search: null,
  searchableColumns: null,
  sort: null,
})

// Without `as any` I get the error: `'slots' implicitly has type 'any' because it does not have a type annotation and
// is referenced directly or indirectly in its own initializer.`
const slots = useSlots() as any
const scrollContainerRef = useTemplateRef('scrollContainerRef')

const isMaxWidthDefinedForAllColumns = props.columns.every((column) => (
  column.maxWidth !== undefined
))

const gridTemplateColumns = computed<string>(() => (
  `${props.columns.map((col) => `minmax(${col.width ?? 'min-content'},${col.maxWidth ?? 'auto'})`).join(' ')}`
))

const isSearchActive = computed<boolean>(() => {
  if (props.search === null) {
    return false
  }

  return props.search.debouncedSearch.value.trim().length > 0
})

const activeFilterCount = computed<number>(() => {
  if (props.filters === null) {
    return isSearchActive.value ? 1 : 0
  }

  const activeFilters = props.filters.activeFilters.value.filter((f) => !f.isStatic)

  return activeFilters.length + (isSearchActive.value ? 1 : 0)
})

const isEmpty = computed<boolean>(() => (
  props.data.length === 0 && !props.isLoading
))

function onClearFiltersAndSearch(): void {
  props.filters?.clearAllFilters()
  props.search?.clearSearch()
}

function getRowAction(item: TData): TableRowAction | null {
  if (props.rowAction?.type === 'link') {
    return {
      label: props.rowAction.label(item),
      to: props.rowAction.to(item),
      type: 'link',
    }
  }

  if (props.rowAction?.type === 'button') {
    return {
      label: props.rowAction.label(item),
      type: 'button',
      onClick: () => (props.rowAction as DataTableButtonAction<TData>).onClick(item),
    }
  }

  return null
}

if (isMaxWidthDefinedForAllColumns) {
  LoggerUtil.info(
    'All columns have a maxWidth defined. This can restrict the table from expanding to fill available space, potentially causing layout issues. Consider leaving at least one column without a maxWidth to allow flexible sizing.',
  )
}

useInfiniteScroll(
  computed<HTMLElement | null>(() => scrollContainerRef.value?.$el ?? null),
  () => {
    props.onNextPage()
  },
  { distance: 400 },
)

useProvideDataTableContext({
  isEmpty,
  isLoading: computed<boolean>(() => props.isLoading),
  activeFilterCount,
  columns: computed<TableColumn<any>[]>(() => props.columns),
  gridTemplateColumns,
  searchableColumns: props.searchableColumns,
  sort: props.sort as any,
  onClear: onClearFiltersAndSearch,
})
</script>

<template>
  <div
    :class="{
      'shadow-lg': props.hasShadow,
      'rounded-tl-none': props.disableTopLeftBorderRadius,
    }"
    class="flex flex-col overflow-hidden rounded-xl"
  >
    <AnimatePresence :initial="false">
      <Motion
        v-if="slots.top || props.search !== null"
        :initial="{ height: 0, opacity: 0 }"
        :animate="{ height: 'auto', opacity: 1 }"
        :exit="{ height: 0, opacity: 0 }"
        :transition="{ duration: 0.15 }"
        :class="{
          'rounded-tl-xl': !props.disableTopLeftBorderRadius,
        }"
        class="border-secondary rounded-tr-xl border border-b-0"
      >
        <slot name="top" />
      </Motion>
    </AnimatePresence>

    <VcTableRootNext
      :is-first-column-sticky="props.isFirstColumnSticky"
      :is-last-column-sticky="props.isLastColumnSticky"
      :grid-template-columns="gridTemplateColumns"
      :class-config="{
        row: 'last:border-0',
        cell: 'group-hover/row:bg-secondary !py-md min-h-12',
        root: slots.top ? 'rounded-t-none' : 'rounded-tl-none rounded-tr-xl',
      }"
      class="relative"
    >
      <VcTableScrollContainerNext ref="scrollContainerRef">
        <VcTableContentNext>
          <DataTableHeader />

          <VcTableBodyNext>
            <VcTableRowNext
              v-for="(item, itemIndex) of props.data"
              :key="props.getKey(item, itemIndex)"
              :action="getRowAction(item)"
            >
              <DataTableCellHost
                v-for="column of columns"
                :key="column.key"
                :node="column.cell(item)"
                :test-id="column.testId"
              />
            </VcTableRowNext>
          </VcTableBodyNext>

          <DataTableEmptyOrLoadingStateOverlay />
        </VcTableContentNext>
      </VcTableScrollContainerNext>

      <DataTableEmptyState />
    </VcTableRootNext>
  </div>
</template>
