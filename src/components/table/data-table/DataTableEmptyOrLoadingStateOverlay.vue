<script setup lang="ts">
import {
  VcTableCellNext,
  VcTableRowNext,
} from '@wisemen/vue-core-components'

import { useInjectDataTableContext } from '@/components/table/data-table/dataTable.context'

const {
  isEmpty,
  isLoading,
  columns,
} = useInjectDataTableContext()

const ROW_COUNT = 10
</script>

<template>
  <div
    v-if="isEmpty || isLoading"
    class="
      relative col-span-full grid grid-cols-subgrid mask-b-from-0% mask-b-to-75%
      backdrop-blur-md
    "
  >
    <VcTableRowNext
      v-for="n in ROW_COUNT"
      :key="n"
    >
      <VcTableCellNext
        v-for="column of columns"
        :key="column.key"
        class="!bg-primary"
      >
        <div class="bg-tertiary h-4 w-full rounded-md" />
      </VcTableCellNext>
    </VcTableRowNext>
  </div>
</template>
