<script setup lang="ts">
import { computed } from 'vue'

import { useProvideTableHeaderCellContext } from '@/components/table/data-table/dataTableHeaderCell.context'
import type { useSort } from '@/composables/sort/sort.composable'
import type { DataTableColumn } from '@/types/table.type'

const props = defineProps<{
  column: DataTableColumn<any>
  sort: ReturnType<typeof useSort> | null
}>()

useProvideTableHeaderCellContext({
  column: computed<DataTableColumn<any>>(() => props.column),
  sort: props.sort,
})
</script>

<template>
  <slot />
</template>
