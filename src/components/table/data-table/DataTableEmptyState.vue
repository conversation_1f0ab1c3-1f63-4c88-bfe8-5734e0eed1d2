<script setup lang="ts">
import {
  VcButton,
  VcIcon,
} from '@wisemen/vue-core-components'
import { useI18n } from 'vue-i18n'

import { useInjectDataTableContext } from '@/components/table/data-table/dataTable.context'

const {
  isEmpty,
  activeFilterCount,
  onClear,
} = useInjectDataTableContext()

const i18n = useI18n()
</script>

<template>
  <div
    v-if="isEmpty"
    class="
      absolute top-1/2 left-1/2 z-12 -translate-x-1/2 -translate-y-1/2
      text-center
    "
  >
    <VcIcon
      icon="search"
      class="text-secondary mx-auto size-6"
    />

    <p class="mt-xl text-primary text-lg font-semibold">
      <template v-if="activeFilterCount > 0">
        {{ i18n.t('component.table.no_results.title') }}
      </template>

      <template v-else>
        {{ i18n.t('component.table.no_data.title') }}
      </template>
    </p>

    <p class="pt-md pb-xl text-secondary mx-auto max-w-92 text-sm">
      <template v-if="activeFilterCount > 0">
        {{ i18n.t('component.table.no_results.description') }}
      </template>

      <template v-else>
        {{ i18n.t('component.table.no_data.description') }}
      </template>
    </p>

    <VcButton
      v-if="activeFilterCount > 0"
      variant="secondary"
      class="mx-auto"
      size="sm"
      @click="onClear()"
    >
      {{ i18n.t('component.table.clear_filter', { count: activeFilterCount }) }}
    </VcButton>
  </div>
</template>
