<script setup lang="ts">
import { VcTableCellNext } from '@wisemen/vue-core-components'

const props = withDefaults(defineProps<{
  testId?: string
  hasInteractiveContent?: boolean
  isPrimaryCell?: boolean
}>(), {
  hasInteractiveContent: false,
  isPrimaryCell: false,
})
</script>

<template>
  <VcTableCellNext
    :has-interactive-content="props.hasInteractiveContent"
    :is-primary-cell="props.isPrimaryCell"
    :test-id="props.testId"
  >
    <span :data-test-id="props.testId">
      <slot />
    </span>
  </VcTableCellNext>
</template>
