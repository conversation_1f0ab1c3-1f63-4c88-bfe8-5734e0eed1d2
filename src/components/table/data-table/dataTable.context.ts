import type { ComputedRef } from 'vue'

import { useContext } from '@/composables/context/context.composable'
import type { useSearchableTableColumns } from '@/composables/searchable-table-columns/searchableTableColumns.composable'
import type { useSort } from '@/composables/sort/sort.composable'
import type { DataTableColumn } from '@/types/table.type'

interface DataTableContext {
  isEmpty: ComputedRef<boolean>
  isLoading: ComputedRef<boolean>
  activeFilterCount: ComputedRef<number>
  columns: ComputedRef<DataTableColumn<any>[]>
  gridTemplateColumns: ComputedRef<string>
  searchableColumns: ReturnType<typeof useSearchableTableColumns<string>> | null
  sort: ReturnType<typeof useSort> | null
  onClear: () => void

}

export const [
  useProvideDataTableContext,
  useInjectDataTableContext,
] = useContext<DataTableContext>('DataTable')
