<script setup lang="ts">
import { VcTableHeaderNext } from '@wisemen/vue-core-components'

import SearchField from '@/components/search-field/SearchField.vue'
import { useInjectDataTableContext } from '@/components/table/data-table/dataTable.context'
import DataTableCell from '@/components/table/data-table/DataTableCell.vue'
import DataTableHeaderCell from '@/components/table/data-table/DataTableHeaderCell.vue'
import DataTableHeaderCellProvider from '@/components/table/data-table/DataTableHeaderCellProvider.vue'

const {
  columns,
  searchableColumns,
  sort,
} = useInjectDataTableContext()
</script>

<template>
  <VcTableHeaderNext>
    <DataTableHeaderCellProvider
      v-for="column of columns"
      :key="column.key"
      :column="column"
      :sort="sort"
    >
      <DataTableHeaderCell
        v-if="column.header === undefined"
        :column="column"
      />

      <Component
        :is="column.header(column)"
        v-else
        :column="column"
      />
    </DataTableHeaderCellProvider>
  </VcTableHeaderNext>

  <!-- Very specific offset top for sticky, but it's the height of the main header -->
  <VcTableHeaderNext
    v-if="searchableColumns?.searchableColumns !== undefined
      && Object.keys(searchableColumns.searchableColumns).length > 0"
    class="top-[2.9375rem]"
  >
    <DataTableCell
      v-for="column of columns"
      :key="column.key"
    >
      <div v-if="searchableColumns?.searchableColumns[column.key] !== undefined">
        <SearchField
          :search="searchableColumns.searchableColumns[column.key]"
          :is-keyboard-shortcut-disabled="true"
        />
      </div>
    </DataTableCell>
  </VcTableHeaderNext>
</template>
