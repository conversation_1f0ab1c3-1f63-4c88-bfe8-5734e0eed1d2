import { useVcToast } from '@wisemen/vue-core-components'
import {
  onUnmounted,
  ref,
  watch,
} from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'

import { useImpersonationStore } from '@/stores/impersonation.store'

const INACTIVITY_TIMEOUT = 5 * 60 * 1000

export function useImpersonationWarning(): {
  startTracking: () => void
  stopImpersonation: () => Promise<void>
} {
  const toast = useVcToast()
  const i18n = useI18n()
  const router = useRouter()
  const impersonationStore = useImpersonationStore()

  const inactivityTimer = ref<ReturnType<typeof setTimeout> | null>(null)
  const isWarningShown = ref<boolean>(false)

  function resetInactivityTimer(): void {
    if (inactivityTimer.value) {
      clearTimeout(inactivityTimer.value)
    }

    if (impersonationStore.isImpersonating && !isWarningShown.value) {
      inactivityTimer.value = setTimeout(() => {
        showInactivityWarning()
      }, INACTIVITY_TIMEOUT)
    }
  }

  function showInactivityWarning(): void {
    if (!impersonationStore.isImpersonating || isWarningShown.value) {
      return
    }

    isWarningShown.value = true

    const impersonatedUser = impersonationStore.impersonatedUser
    const userName = impersonatedUser
      ? `${impersonatedUser.firstName ?? ''} ${impersonatedUser.lastName ?? ''}`.trim()
      : 'user'

    toast.info({
      title: i18n.t('impersonation.warning.title'),
      actions: [
        {
          label: i18n.t('impersonation.warning.continue'),
          variant: 'secondary',
          onClick: (close: () => void): void => {
            close()
            isWarningShown.value = false
            resetInactivityTimer()
          },
        },
        {
          label: i18n.t('impersonation.warning.stop'),
          variant: 'primary',
          onClick: async (close): Promise<void> => {
            close()
            await stopImpersonation()
          },
        },
      ],
      description: i18n.t('impersonation.warning.description', { user: userName }),
    })
  }

  async function stopImpersonation(): Promise<void> {
    impersonationStore.stopImpersonation()
    isWarningShown.value = false

    if (inactivityTimer.value) {
      clearTimeout(inactivityTimer.value)
      inactivityTimer.value = null
    }

    await router.push({ name: 'users-overview' })
    window.location.reload()
  }

  function startTracking(): void {
    if (!impersonationStore.isImpersonating) {
      return
    }

    resetInactivityTimer()

    const events = [
      'mousedown',
      'mousemove',
      'keypress',
      'scroll',
      'touchstart',
      'click',
    ]

    function handleActivity(): void {
      if (impersonationStore.isImpersonating && !isWarningShown.value) {
        resetInactivityTimer()
      }
    }

    for (const event of events) {
      document.addEventListener(event, handleActivity, true)
    }

    function cleanup(): void {
      for (const event of events) {
        document.removeEventListener(event, handleActivity, true)
      }

      if (inactivityTimer.value) {
        clearTimeout(inactivityTimer.value)
        inactivityTimer.value = null
      }
    }

    const stopWatching = watch(
      () => impersonationStore.isImpersonating,
      (isImpersonating) => {
        if (isImpersonating) {
          isWarningShown.value = false
          resetInactivityTimer()
        }
        else {
          cleanup()
        }
      },
    )

    onUnmounted(() => {
      cleanup()
      stopWatching()
    })
  }

  return {
    startTracking,
    stopImpersonation,
  }
}
