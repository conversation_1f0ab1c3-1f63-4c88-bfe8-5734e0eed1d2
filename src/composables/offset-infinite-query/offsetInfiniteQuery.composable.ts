import { useInfiniteQuery } from '@tanstack/vue-query'
import type { QueryKeys } from '@wisemen/vue-core-query'
import type { MaybeRef } from 'vue'
import { computed } from 'vue'

import type {
  OffsetPaginationParams,
  OffsetPaginationResponse,
} from '@/types/pagination.type'

type NonOptionalKeys<T> = {
  [K in keyof T]-?: T[K]
}

interface Options<TData> {
  limit?: number
  queryFn: (paginationParams: OffsetPaginationParams) => Promise<OffsetPaginationResponse<TData>>
  queryKey: {
    [TQueryKey in keyof QueryKeys]?: {
      [TQueryKeyParam in keyof NonOptionalKeys<QueryKeys[TQueryKey]>]: MaybeRef<QueryKeys[TQueryKey][TQueryKeyParam]>
    }
  }
}

const DEFAULT_LIMIT = 20

// eslint-disable-next-line ts/explicit-function-return-type
export function useOffsetInfiniteQuery<TData>(options: Options<TData>) {
  function getQueryKey(): unknown[] {
    const [
      queryKey,
      params,
    ] = Object.entries(options.queryKey)[0]

    return [
      queryKey,
      params,
    ]
  }

  const infiniteQuery = useInfiniteQuery({
    getNextPageParam: (lastPage: OffsetPaginationResponse<TData>) => {
      const total = lastPage.meta.offset + lastPage.meta.limit

      if (total >= lastPage.meta.total) {
        return null
      }

      return total
    },
    initialPageParam: 0,
    placeholderData: (data) => data,
    queryFn: ({ pageParam }) => options.queryFn({
      limit: options.limit ?? DEFAULT_LIMIT,
      offset: pageParam ?? 0,
    }),
    queryKey: getQueryKey(),
  })

  const data = computed<OffsetPaginationResponse<TData>>(() => {
    const data = infiniteQuery.data.value?.pages.flatMap((page) => page.data) ?? []
    const meta = infiniteQuery.data.value?.pages[0].meta ?? null

    return {
      data,
      meta: {
        limit: meta?.limit ?? 0,
        offset: meta?.offset ?? 0,
        total: meta?.total ?? data.length,
      },
    }
  })

  // eslint-disable-next-line ts/explicit-function-return-type
  function fetchNextPage() {
    if (!infiniteQuery.hasNextPage.value || infiniteQuery.isFetchingNextPage.value) {
      return
    }

    return infiniteQuery.fetchNextPage()
  }

  return {
    ...infiniteQuery,
    data,
    fetchNextPage: fetchNextPage as unknown as (() => Promise<void>),
  }
}
