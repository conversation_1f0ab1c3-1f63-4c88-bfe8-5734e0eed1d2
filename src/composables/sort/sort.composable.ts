import {
  computed,
  shallowRef,
} from 'vue'

import { useRouteQueryState } from '@/composables/route-query-state/routeQueryState.composable'

export type SortDirection = 'asc' | 'desc'

interface Options<TKey extends string = string> {
  enableMultiSort?: boolean
  initialValues?: Sort<TKey>[]
  keys: TKey[]
  persistInUrl?: boolean | string
}

export interface Sort<TKey extends string = string> {
  direction: SortDirection
  key: TKey
}

const DEFAULT_ROUTE_QUERY_KEY = 'sort'

function serializeSorts<TKey extends string>(sorts: Sort<TKey>[]): string {
  return sorts.map((s) => `${s.key}:${s.direction}`).join(',')
}

function deserializeSorts<TKey extends string>(value: string | null): Sort<TKey>[] {
  if (!value) {
    return []
  }

  return value.split(',').map((part) => {
    const [
      key,
      direction,
    ] = part.split(':') as [TKey, SortDirection]

    if (!key || (direction !== 'asc' && direction !== 'desc')) {
      return null
    }

    return {
      direction,
      key,
    }
  }).filter(Boolean) as Sort<TKey>[]
}

// eslint-disable-next-line ts/explicit-function-return-type
export function useSort<TKey extends string>(options: Options<TKey>) {
  const persistInUrl = options.persistInUrl === false ? false : options.persistInUrl ?? DEFAULT_ROUTE_QUERY_KEY

  const sorts = persistInUrl === true || typeof persistInUrl === 'string'
    ? useRouteQueryState({
        initialState: options?.initialValues ?? [],
        key: typeof persistInUrl === 'string' ? persistInUrl : DEFAULT_ROUTE_QUERY_KEY,
        serializer: {
          parse: deserializeSorts,
          serialize: serializeSorts,
        },
      })
    : shallowRef<Sort<TKey>[]>(options?.initialValues ?? [])

  function setSort(key: TKey): void {
    const existingIndex = sorts.value.findIndex((s) => s.key === key)

    if (existingIndex !== -1) {
      const existing = sorts.value[existingIndex]

      if (existing.direction === 'asc') {
        sorts.value = options.enableMultiSort
          ? sorts.value.map(
              (s, index) =>
                index === existingIndex
                  ? {
                      ...s,
                      direction: 'desc',
                    }
                  : s,
            )
          : [
              {
                direction: 'desc',
                key,
              },
            ]
      }
      else {
        sorts.value = sorts.value.filter((_, index) => index !== existingIndex)
      }
    }
    else {
      sorts.value = options.enableMultiSort
        ? [
            {
              direction: 'asc',
              key,
            },
            ...sorts.value as Sort<TKey>[],
          ]
        : [
            {
              direction: 'asc',
              key,
            },
          ]
    }
  }

  function existsSort(key: TKey): boolean {
    return options.keys.includes(key)
  }

  function getSort(key: TKey): Sort<TKey> | null {
    return sorts.value.find((s) => s.key === key) ?? null
  }

  function resetSort(): void {
    sorts.value = []
  }

  return {
    isMultiSortEnabled: computed<boolean>(() => options.enableMultiSort ?? false),
    existsSort,
    getSort,
    resetSort,
    setSort,
    values: sorts,
  }
}
