import type { ComputedRef } from 'vue'
import {
  ref,
  watch,
} from 'vue'

import { useRouteQueryState } from '@/composables/route-query-state/routeQueryState.composable'

interface Options {
  debounceMs?: number
  persistInUrl?: boolean | string
}

const DEFAULT_DEBOUNCE_MS = 50
const DEFAULT_ROUTE_QUERY_KEY = 'search'

// eslint-disable-next-line ts/explicit-function-return-type
export function useSearch(options?: Options) {
  const persistInUrl = options?.persistInUrl === false ? false : options?.persistInUrl ?? DEFAULT_ROUTE_QUERY_KEY

  const search = persistInUrl === true || typeof persistInUrl === 'string'
    ? useRouteQueryState({
        initialState: '',
        key: typeof persistInUrl === 'string' ? persistInUrl : DEFAULT_ROUTE_QUERY_KEY,
        serializer: {
          parse: (value) => value ?? '',
          serialize: (value) => value,
        },
      })
    : ref<string>('')

  const debouncedSearch = ref<string>(search.value)
  const isDebouncing = ref<boolean>(false)

  let debounceTimeout: ReturnType<typeof setTimeout> | null = null

  function clearSearch(): void {
    search.value = ''
    debouncedSearch.value = ''
    isDebouncing.value = false

    if (debounceTimeout) {
      clearTimeout(debounceTimeout)
    }
  }

  watch(search, (value) => {
    if (debounceTimeout) {
      clearTimeout(debounceTimeout)
    }

    isDebouncing.value = true

    debounceTimeout = setTimeout(() => {
      debouncedSearch.value = value
      isDebouncing.value = false
    }, options?.debounceMs ?? DEFAULT_DEBOUNCE_MS)
  }, { immediate: true })

  function updateSearch(value: string): void {
    search.value = value
  }

  return {
    isDebouncing,
    clearSearch,
    debouncedSearch: debouncedSearch as ComputedRef<string>,
    search,
    updateSearch,
  }
}
