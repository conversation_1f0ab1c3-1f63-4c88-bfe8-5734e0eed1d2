import { useInfiniteQuery } from '@tanstack/vue-query'
import type { QueryKeys } from '@wisemen/vue-core-query'
import type {
  ComputedRef,
  MaybeRef,
} from 'vue'
import { computed } from 'vue'

import type {
  KeysetPaginationParams,
  KeysetPaginationResponse,
} from '@/types/pagination.type'

type NonOptionalKeys<T> = {
  [K in keyof T]-?: T[K]
}

interface Options<TData> {
  isEnabled?: ComputedRef<boolean>
  limit?: number
  queryFn: (paginationParams: KeysetPaginationParams) => Promise<KeysetPaginationResponse<TData>>
  queryKey: {
    [TQueryKey in keyof QueryKeys]?: {
      [TQueryKeyParam in keyof NonOptionalKeys<QueryKeys[TQueryKey]>]: MaybeRef<QueryKeys[TQueryKey][TQueryKeyParam]>
    }
  }
}

const DEFAULT_LIMIT = 20

// eslint-disable-next-line ts/explicit-function-return-type
export function useKeysetInfiniteQuery<TData>(options: Options<TData>) {
  function getQueryKey(): unknown[] {
    const [
      queryKey,
      params,
    ] = Object.entries(options.queryKey)[0]

    return [
      queryKey,
      params,
    ]
  }

  const infiniteQuery = useInfiniteQuery({
    enabled: options.isEnabled,
    getNextPageParam: (lastPage: KeysetPaginationResponse<TData>) => {
      return lastPage.meta.next ?? null
    },
    initialPageParam: undefined,
    placeholderData: (data) => data,
    queryFn: ({ pageParam }) =>
      options.queryFn({
        key: pageParam as string,
        limit: options.limit ?? DEFAULT_LIMIT,
      }),
    queryKey: getQueryKey(),
  })

  const data = computed<KeysetPaginationResponse<TData>>(() => {
    const pages = infiniteQuery.data.value?.pages ?? []
    const flattenedData = pages.flatMap((page) => page.data)

    const firstMeta = pages[0]?.meta ?? {
      next: null,
      total: flattenedData.length,
    }

    return {
      data: flattenedData,
      meta: { next: infiniteQuery.hasNextPage.value ? firstMeta.next : null },
    }
  })

  // eslint-disable-next-line ts/explicit-function-return-type
  function fetchNextPage() {
    if (!infiniteQuery.hasNextPage.value || infiniteQuery.isFetchingNextPage.value) {
      return
    }

    return infiniteQuery.fetchNextPage()
  }

  return {
    ...infiniteQuery,
    data,
    fetchNextPage: fetchNextPage as unknown as () => Promise<void>,
  }
}
