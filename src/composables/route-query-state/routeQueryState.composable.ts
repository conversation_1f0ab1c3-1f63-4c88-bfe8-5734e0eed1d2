import type { Ref } from 'vue'
import {
  ref,
  watch,
} from 'vue'
import {
  useRoute,
  useRouter,
} from 'vue-router'

interface Serializer<T> {
  parse: (value: string) => T
  serialize: (value: T) => string
}

interface UseRouteQueryStateOptions<T> {
  initialState: T
  key: string
  serializer: Serializer<T>
}

// eslint-disable-next-line ts/explicit-function-return-type
export function useRouteQueryState<T>(options: UseRouteQueryStateOptions<T>) {
  const route = useRoute<string>()
  const router = useRouter()

  function parse(value: string | null): T {
    if (value === null) {
      return options.initialState
    }

    try {
      return options.serializer.parse(value)
    }
    catch {
      return options.initialState
    }
  }

  const internalState = ref<T>(
    parse((route).query[options.key] as string | null),
  )

  watch(
    () => route.query[options.key],
    (value) => {
      internalState.value = parse(value as string | null)
    },
  )

  watch(
    internalState,
    (value) => {
      const q = {
        ...route.query,
        [options.key]: options.serializer.serialize(value) || undefined,
      }

      router.replace({ query: q })
    },
    { deep: true },
  )

  return internalState as Ref<T>
}
