import { useI18n } from 'vue-i18n'

import { createSelectFilter } from '@/components/filters'
import { useEwcCodeIndexQuery } from '@/modules/waste-inquiry/api/queries/ewcCodeIndex.query'

// eslint-disable-next-line ts/explicit-function-return-type
export function useEwcFilter() {
  const i18n = useI18n()
  const ewcCodeIndexQuery = useEwcCodeIndexQuery()

  const level3Codes = ewcCodeIndexQuery.data.value?.filter((ewcCode) => {
    return ewcCode.code.split(' ').length === 3
  }) ?? []

  return createSelectFilter({
    defaultValue: null,
    displayFn: (value) => value.label,
    key: 'ewcCode',
    label: i18n.t('enum.dynamic_table_column_name.ewc_code'),
    options: level3Codes.map((ewcCode) => ({
      label: ewcCode.code,
      value: ewcCode.code,
    })),
  })
}
