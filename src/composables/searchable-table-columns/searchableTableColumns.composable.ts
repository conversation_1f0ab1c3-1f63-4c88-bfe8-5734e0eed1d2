import { computed } from 'vue'

import { useSearch } from '@/composables/search/search.composable'
import { SearchUtil } from '@/utils/search.util'

interface Options<TKey extends string> {
  keys: TKey[]
}

// eslint-disable-next-line ts/explicit-function-return-type
export function useSearchableTableColumns<TKey extends string>(options: Options<TKey>) {
  const searchableColumns = options.keys.reduce((acc, key) => {
    acc[key] = useSearch({ persistInUrl: false })

    return acc
  }, {} as Record<TKey, ReturnType<typeof useSearch>>)

  const values = computed<Record<TKey, string | undefined>>(() => {
    return Object.entries(searchableColumns).reduce((acc, [
      key,
      search,
    ]) => {
      const value = (search as ReturnType<typeof useSearch>).debouncedSearch.value

      acc[key as TKey] = SearchUtil.isEmpty(value) ? undefined : value

      return acc
    }, {} as Record<TKey, string | undefined>)
  })

  return {
    searchableColumns,
    values,
  }
}
