import { useI18n } from 'vue-i18n'

import { createSelectFilter } from '@/components/filters'
import type { SelectFilter } from '@/components/filters/filters.type'

export type CustomBooleanFilterValue = 'falsy' | 'truthy' | null

export function useCustomBooleanFilter(key: string, label: string): SelectFilter<string, 'falsy' | 'truthy'> {
  const i18n = useI18n()

  return createSelectFilter({
    defaultValue: null,
    displayFn: (value): string => {
      if (value === 'truthy') {
        return i18n.t('component.filter.custom_boolean_filter.truthy')
      }

      return i18n.t('component.filter.custom_boolean_filter.falsy')
    },
    key,
    label,
    options: [
      'truthy',
      'falsy',
    ],
  })
}
