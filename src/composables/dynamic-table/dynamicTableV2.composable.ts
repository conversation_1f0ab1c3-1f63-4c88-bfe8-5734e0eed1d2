// TODO: refactor this file

import {
  usePagination,
  useVcDialog,
} from '@wisemen/vue-core-components'
import {
  computed,
  ref,
  watch,
} from 'vue'
import { useI18n } from 'vue-i18n'

import { useDynamicTableDeleteViewMutation } from '@/api/mutations/dynamicTableDeleteView.mutation'
import { useDynamicTableUpdateViewMutation } from '@/api/mutations/dynamicTableUpdateView.mutation'
import { useDynamicTableColumnIndexQuery } from '@/api/queries/dynamicTableColumnIndex.query'
import { useDynamicTableDefaultViewQuery } from '@/api/queries/dynamicTableDefaultView.query'
import { useDynamicTableViewIndexQuery } from '@/api/queries/dynamicTableViewIndex.query'
import type { DynamicColumnNames } from '@/client'
import { SortDirection } from '@/client'
import type { Filters } from '@/components/filters/filters.composable'
import type {
  Sort,
  useSort,
} from '@/composables/sort/sort.composable'
import type { DynamicTableColumnIndex } from '@/models/dynamic-table/column/dynamicTableColumnIndex.model'
import type { DynamicTableViewIndex } from '@/models/dynamic-table/view/dynamicTableViewIndex.model'
import type { DynamicTableViewIndexPagination } from '@/models/dynamic-table/view/dynamicTableViewIndexPagination.model'
import { DynamicTableEnumUtil } from '@/models/enums/dynamicTable.enum'
import type { DynamicTableName } from '@/models/enums/dynamicTableName.enum'
import { assert } from '@/utils/assert.util'
import { ObjectUtil } from '@/utils/object.util'

export interface Column {
  uuid: string
  isHideable: boolean
  isSortable: boolean
  isVisible: boolean
  name: DynamicColumnNames
  label: string
}

interface Options<TSort extends string> {
  dynamicTableName: DynamicTableName
  filters: Filters<any[], any[]>
  sort: ReturnType<typeof useSort<TSort>>
}

function columnUuidToColumn(
  columnUuid: string,
  columns: DynamicTableColumnIndex[],
): DynamicTableColumnIndex {
  const col = columns.find((column) => column.uuid === columnUuid) ?? null

  assert(col !== null, `Column with uuid ${columnUuid} not found in columns.`)

  return col
}

function isFalsy(value: unknown): boolean {
  if (isDateRangeFilter(value)) {
    return value.from === null && value.until === null
  }

  return value === null || value === false || value === undefined || value === '' || (Array.isArray(value) && value.length === 0)
}

function isDateRangeFilter(value: unknown): value is {
  from: Date | null
  until: Date | null
} {
  return value !== null && typeof value === 'object' && 'from' in value && 'until' in value
}

// eslint-disable-next-line ts/explicit-function-return-type
export function useDynamicTableV2<TSort extends string>(options: Options<TSort>) {
  const addDynamicViewDialog = useVcDialog({ component: () => import('@/components/app/table/AppTableAddDynamicViewDialog.vue') })

  const i18n = useI18n()
  // TODO: refactor to new pagination
  const dynamicTableIndexPagination = usePagination<DynamicTableViewIndexPagination>({ isRouteQueryEnabled: false })

  // Dynamic table columns - Dit zijn alle mogelijke kolommen
  const dynamicTableColumnIndexQuery = useDynamicTableColumnIndexQuery(options.dynamicTableName)

  // Views
  const dynamicTableViewIndex = useDynamicTableViewIndexQuery(
    options.dynamicTableName,
    dynamicTableIndexPagination.paginationOptions,
  )

  const dynamicTableColumns = computed<DynamicTableColumnIndex[]>(() => {
    return dynamicTableColumnIndexQuery.data.value ?? []
  })

  // Default view
  const { data: dynamicTableDefaultView } = useDynamicTableDefaultViewQuery(options.dynamicTableName)
  const dynamicTableUpdateViewMutation = useDynamicTableUpdateViewMutation()
  const dynamicTableDeleteViewMutation = useDynamicTableDeleteViewMutation()

  const activeView = ref<DynamicTableViewIndex | null>(null)

  const views = computed<DynamicTableViewIndex[]>(() => {
    return dynamicTableViewIndex.data.value.data ?? []
  })

  function getColumnUuidByFilterableField(filterableField: string): string | null {
    return dynamicTableColumns.value.find((column) => column.filterableField === filterableField)?.uuid ?? null
  }

  function getColumnUuidBySortableField(sortableField: string): string | null {
    return dynamicTableColumns.value.find((column) => column.sortableFields.includes(sortableField))?.uuid ?? null
  }

  function getFilterableFieldByColumnUuid(columnUuid: string): string | null {
    return dynamicTableColumns.value.find((column) => column.uuid === columnUuid)?.filterableField ?? null
  }

  function getSortableFieldByColumnUuid(columnUuid: string): string | null {
    return dynamicTableColumns.value.find((column) => column.uuid === columnUuid)?.sortableFields[0] ?? null
  }

  function getViewByUuid(viewUuid: string): DynamicTableViewIndex {
    const view = views.value.find((view) => view.uuid === viewUuid) ?? null

    assert(view !== null, `View with uuid ${viewUuid} not found in views.`)

    return view
  }

  const haveFilterValuesChanged = computed<boolean>(() => {
    if (!activeView.value) {
      return false
    }

    const mappedFilters = Object.entries(options.filters.values.value).reduce((acc, [
      key,
      value,
    ]) => {
      const columnKey = getColumnUuidByFilterableField(key)

      if (columnKey) {
        acc.push({
          columnUuid: columnKey,
          value: value as string | string[],
        })
      }

      return acc
    }, [] as {
      columnUuid: string
      value: boolean | string | string[]
    }[])

    const flatFilters = mappedFilters.flatMap((f) => f.value)
    const truthyFilters = flatFilters.filter((f) => !isFalsy(f))

    return !ObjectUtil.isEqual(truthyFilters, activeView.value.filters.flatMap((f) => f.value))
  })

  const haveSortValuesChanged = computed<boolean>(() => {
    if (!activeView.value) {
      return false
    }

    const mappedSorts = options.sort.values.value.map((sort, index) => ({
      columnUuid: getColumnUuidBySortableField(sort.key),
      direction: sort.direction,
      order: index,
    })).filter((sort) => sort.columnUuid !== null)

    return !ObjectUtil.isEqual(mappedSorts, activeView.value.sorts)
  })

  const columns = computed<Column[]>(() => {
    if (dynamicTableColumns.value.length === 0) {
      return []
    }

    const viewVisibleColumns = activeView.value?.visibleColumns ?? []

    const viewVisibleColumnsSortedByOrder = [
      ...viewVisibleColumns,
    ].sort((a, b) => a.order - b.order)

    const visibleColumns = viewVisibleColumnsSortedByOrder.map((col) => {
      const column = columnUuidToColumn(col.columnUuid, dynamicTableColumns.value)

      return {
        uuid: column.uuid,
        isHideable: column.isHideable,
        isSortable: column.sortableFields.length > 0,
        isVisible: true,
        name: column.name,
        label: i18n.t(DynamicTableEnumUtil.getLabelI18nKey(column.name)),
      }
    })

    const invisibleColumns = dynamicTableColumns.value.filter((col) => {
      return !viewVisibleColumns.some((visibleCol) => visibleCol.columnUuid === col.uuid)
    }).map((col) => {
      return {
        uuid: col.uuid,
        isHideable: col.isHideable,
        isSortable: col.sortableFields.length > 0,
        isVisible: false,
        name: col.name,
        label: i18n.t(DynamicTableEnumUtil.getLabelI18nKey(col.name)),
      }
    })

    return [
      ...visibleColumns,
      ...invisibleColumns,
    ]
  })

  const hasActiveViewBeenChanged = computed<boolean>(() => {
    if (activeView.value === null || views.value.length === 0) {
      return false
    }

    const view = getViewByUuid(activeView.value.uuid)

    assert(view !== null, 'View should not be null when checking if it has been changed.')

    const haveVisibleColumnsChanged = !ObjectUtil.isEqual(
      view.visibleColumns,
      activeView.value.visibleColumns,
    )

    return haveFilterValuesChanged.value || haveVisibleColumnsChanged || haveSortValuesChanged.value
  })

  async function updateActiveView(): Promise<void> {
    const activeView = getActiveViewOrThrow()

    const mappedFilters = Object.entries(options.filters.values.value).reduce((acc, [
      key,
      value,
    ]) => {
      const columnKey = getColumnUuidByFilterableField(key)

      if (columnKey && !isFalsy(value)) {
        acc.push({
          columnUuid: columnKey,
          value: value as string | string[],
        })
      }

      return acc
    }, [] as {
      columnUuid: string
      value: string | string[]
    }[])

    const mappedSorts = options.sort.values.value.map((sort) => ({
      columnUuid: getColumnUuidBySortableField(sort.key)!,
      direction: sort.direction,
    })).filter((sort) => sort.columnUuid !== null)

    await dynamicTableUpdateViewMutation.execute({
      body: {
        filters: mappedFilters,
        sorts: mappedSorts,
        viewName: activeView.name,
        visibleColumns: activeView.visibleColumns.map((column) => ({ columnUuid: column.columnUuid })),
      },
      params: {
        viewUuid: activeView.uuid,
        tableName: options.dynamicTableName,
      },
    })
  }

  function createNewView(): void {
    assert(
      activeView.value !== null,
      'Active view should not be null when creating new view.',
    )

    const mappedFilters = Object.entries(options.filters.values.value).reduce((acc, [
      key,
      value,
    ]) => {
      const columnKey = getColumnUuidByFilterableField(key)

      if (columnKey && !isFalsy(value)) {
        acc.push({
          columnUuid: columnKey,
          value: value as string | string[],
        })
      }

      return acc
    }, [] as { columnUuid: string
      value: string | string[] }[])

    const mappedSorts = options.sort.values.value.map((sort, index) => ({
      columnUuid: getColumnUuidBySortableField(sort.key)!,
      direction: sort.direction === 'asc' ? SortDirection.ASC : SortDirection.DESC,
      order: index,
    })).filter((sort) => sort.columnUuid !== null)

    addDynamicViewDialog.open({
      tableName: options.dynamicTableName,
      view: {
        ...activeView.value,
        filters: mappedFilters,
        sorts: mappedSorts,
      },
      onSaveView: (viewName: DynamicTableName) => {
        addDynamicViewDialog.close()

        const createdView = views.value.find((view) => view.name === viewName)

        if (createdView === undefined) {
          return
        }

        activeView.value = createdView
      },
    })
  }

  async function deleteView(view: DynamicTableViewIndex): Promise<void> {
    await dynamicTableDeleteViewMutation.execute({
      params: {
        viewUuid: view.uuid,
        tableName: options.dynamicTableName,
      },
    })
  }

  function selectView(view: DynamicTableViewIndex): void {
    activeView.value = ObjectUtil.deepClone(view)
  }

  function resetChangesToActiveView(): void {
    const view = getViewByUuid(getActiveViewOrThrow().uuid)

    activeView.value = ObjectUtil.deepClone(view)
  }

  function updateColumnOrder(updatedColumns: Column[]): void {
    assert(
      activeView.value !== null,
      'Active view should not be null when updating column order.',
    )

    const updatedVisibleColumns = updatedColumns.map((column, columnIndex) => ({
      columnUuid: column.uuid,
      order: columnIndex,
    }))

    activeView.value = {
      ...activeView.value,
      visibleColumns: updatedVisibleColumns,
    }
  }

  function updateColumnVisibility(column: Column, isVisible: boolean): void {
    assert(
      activeView.value !== null,
      'Active view should not be null when updating column visibility.',
    )

    const updatedVisibleColumns = isVisible
      ? [
          ...activeView.value.visibleColumns.map((visibleColumn, visibleColumnIndex) => ({
            columnUuid: visibleColumn.columnUuid,
            order: visibleColumnIndex,
          })),
          {
            columnUuid: column.uuid,
            order: activeView.value.visibleColumns.length,
          },
        ]
      : activeView.value!.visibleColumns.filter(
          (visibleColumn) => visibleColumn.columnUuid !== column.uuid,
        )

    activeView.value = {
      ...activeView.value,
      visibleColumns: updatedVisibleColumns,
    }
  }

  function getActiveViewOrThrow(): DynamicTableViewIndex {
    assert(
      activeView.value !== null,
      'Active view should not be null when getting active view.',
    )

    return activeView.value
  }

  watch(dynamicTableDefaultView, (view) => {
    activeView.value = ObjectUtil.deepClone(view)
  }, { immediate: true })

  watch(views, () => {
    if (activeView.value === null) {
      return
    }

    activeView.value = getViewByUuid(getActiveViewOrThrow().uuid)
  })

  watch([
    activeView,
    dynamicTableColumns,
  ], ([
    view,
  ]) => {
    if (view === null) {
      return
    }

    options.filters.clearAllFilters()

    // Reset filters to view defaults
    for (const filter of view.filters) {
      const filterableField = getFilterableFieldByColumnUuid(filter.columnUuid)

      if (filterableField !== null) {
        // TODO: clean up
        // TODO: should validate of we support this filterable field
        options.filters.values.value[filterableField] = isDateRangeFilter(filter.value)
          ? {
              from: filter.value.from ? new Date(filter.value.from) : null,
              until: filter.value.until ? new Date(filter.value.until) : null,
            }
          : filter.value
      }
    }

    // Reset sorts to view defaults
    options.sort.values.value = view.sorts
      .map((sort) => ({
        direction: sort.direction === SortDirection.ASC ? 'asc' : 'desc',
        key: getSortableFieldByColumnUuid(sort.columnUuid) ?? '',
      }))
      .filter((sort) => sort.key !== null) as Sort<TSort>[]
  }, { immediate: true })

  return {
    hasActiveViewBeenChanged,
    isUpdatingActiveView: dynamicTableUpdateViewMutation.isLoading,
    activeView,
    columns,
    createNewView,
    deleteView,
    dynamicTableName: options.dynamicTableName,
    haveFilterValuesChanged,
    haveSortValuesChanged,
    resetChangesToActiveView,
    selectView,
    updateActiveView,
    updateColumnOrder,
    updateColumnVisibility,
    views,
  }
}
