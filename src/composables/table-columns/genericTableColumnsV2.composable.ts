import type { TableColumn } from '@wisemen/vue-core-components'
import type { VNode } from 'vue'
import { h } from 'vue'

import TableGenericBooleanCell from '@/components/table/columns/TableGenericBooleanCell.vue'
import DataTableCell from '@/components/table/data-table/DataTableCell.vue'
import { useLocalizedDateFormat } from '@/composables/localized-date-format/localizedDateFormat.composable'

function isStringArray(value: unknown): value is string[] {
  return Array.isArray(value) && value.every((item) => typeof item === 'string')
}

export function useGenericColumn<T>(key: string, label: string, value?: (data: T) => unknown): TableColumn<T> {
  return {
    cell: (row): VNode => {
      const data = value?.(row) ?? row[key as keyof typeof row]

      if (typeof data === 'string' || typeof data === 'number') {
        return h(DataTableCell, () => data)
      }

      if (data instanceof Date) {
        const localizedDateFormat = useLocalizedDateFormat()

        return h(DataTableCell, () => localizedDateFormat.toNumericDate(data))
      }

      if (typeof data === 'boolean') {
        return h(TableGenericBooleanCell, { value: data })
      }

      if (isStringArray(data)) {
        return h(DataTableCell, () => data.length > 0 ? data.join(', ') : '-')
      }

      return h(DataTableCell, () => '-')
    },
    headerLabel: label,
    key,
  }
}
