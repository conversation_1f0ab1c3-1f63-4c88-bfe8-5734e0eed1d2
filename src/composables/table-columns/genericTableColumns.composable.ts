import type { TableColumn } from '@wisemen/vue-core-components'
import { VcTableCell } from '@wisemen/vue-core-components'
import type { VNode } from 'vue'
import { h } from 'vue'

import type {
  MailStatus,
  TransportMode,
} from '@/client'
import AppTableBooleanCell from '@/components/app/table/AppTableBooleanCell.vue'
import { useLocalizedDateFormat } from '@/composables/localized-date-format/localizedDateFormat.composable'
import { TEST_ID } from '@/constants/testId.constant'
import { MailStatusEnumUtil } from '@/models/enums/mailStatus.enum'
import { TransportModeEnumUtil } from '@/models/enums/transportMode.enum'
import { i18nPlugin } from '@/plugins/i18n.plugin'

export function useGenericAccountManagerColumn<T extends { accountManager: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.accountManager ?? '-'),
    headerLabel: label,
    key: 'accountManager',
  }
}

export function useGenericAsnColumn<T extends { asn: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.asn ?? '-'),
    headerLabel: label,
    key: 'asn',
  }
}

export function useGenericPayerIdColumn<T extends { payerId: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.payerId ?? '-'),
    headerLabel: label,
    key: 'payerId',
  }
}

export function useGenericPayerNameColumn<T extends { payerName: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.payerName ?? '-'),
    headerLabel: label,
    key: 'payerName',
  }
}

export function useGenericNetAmountColumn<T extends { netAmount: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.netAmount ?? '-'),
    headerLabel: label,
    key: 'netAmount',
  }
}

export function useGenericVatAmountColumn<T extends { vatAmount: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.vatAmount ?? '-'),
    headerLabel: label,
    key: 'vatAmount',
  }
}

export function useGenericCurrencyColumn<T extends { currency: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.currency ?? '-'),
    headerLabel: label,
    key: 'currency',
  }
}

export function useGenericAccountDocumentNumberColumn<T extends { accountDocumentNumber: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.accountDocumentNumber ?? '-'),
    headerLabel: label,
    key: 'accountDocumentNumber',
  }
}

export function useGenericAccountManagerNameColumn<T extends { accountManagerName: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.accountManagerName ?? '-'),
    headerLabel: label,
    key: 'accountManagerName',
  }
}

export function useGenericInvoiceNumberColumn<T extends { invoiceNumber: string }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.invoiceNumber),
    headerLabel: label,
    key: 'invoiceNumber',
  }
}

export function useGenericDueOnColumn<T extends { dueOn: Date | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  const localizedDateFormat = useLocalizedDateFormat()

  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.dueOn ? localizedDateFormat.toNumericDate(new Date(row.dueOn)) : '-'),
    headerLabel: label,
    key: 'dueOn',
  }
}

export function useGenericIssuedOnColumn<T extends { issuedOn: Date | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  const localizedDateFormat = useLocalizedDateFormat()

  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.issuedOn ? localizedDateFormat.toNumericDate(new Date(row.issuedOn)) : '-'),
    headerLabel: label,
    key: 'issuedOn',
  }
}

export function useGenericCompanyNameColumn<T extends { companyName: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.companyName ?? '-'),
    headerLabel: label,
    key: 'companyName',
  }
}

export function useGenericFirstReminderStatusColumn<T extends { firstReminderMailStatus: MailStatus }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () =>
      i18nPlugin.global.t(MailStatusEnumUtil.getI18nKey(row.firstReminderMailStatus))),
    headerLabel: label,
    key: 'firstReminderStatus',
  }
}

export function useGenericSecondReminderStatusColumn<T extends { secondReminderMailStatus: MailStatus }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () =>
      i18nPlugin.global.t(MailStatusEnumUtil.getI18nKey(row.secondReminderMailStatus))),
    headerLabel: label,
    key: 'secondReminderStatus',
  }
}

export function useGenericThirdReminderStatusColumn<T extends { thirdReminderStatus: MailStatus }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () =>
      i18nPlugin.global.t(MailStatusEnumUtil.getI18nKey(row.thirdReminderStatus))),
    headerLabel: label,
    key: 'thirdReminderStatus',
  }
}

export function useGenericFirstReminderOnColumn<T extends { firstReminderOn: Date | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  const localizedDateFormat = useLocalizedDateFormat()

  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.firstReminderOn ? localizedDateFormat.toNumericDate(new Date(row.firstReminderOn)) : '-'),
    headerLabel: label,
    key: 'firstReminderOn',
  }
}

export function useGenericSecondReminderOnColumn<T extends { secondReminderOn: Date | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  const localizedDateFormat = useLocalizedDateFormat()

  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.secondReminderOn ? localizedDateFormat.toNumericDate(new Date(row.secondReminderOn)) : '-'),
    headerLabel: label,
    key: 'secondReminderOn',
  }
}

export function useGenericAutoApprovedOnColumn<T extends { autoApprovedOn: Date | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  const localizedDateFormat = useLocalizedDateFormat()

  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.autoApprovedOn ? localizedDateFormat.toNumericDate(new Date(row.autoApprovedOn)) : '-'),
    headerLabel: label,
    key: 'autoApprovedOn',
  }
}

export function useGenericConfirmedTransportDateColumn<T extends { confirmedTransportDate: Date | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  const localizedDateFormat = useLocalizedDateFormat()

  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.confirmedTransportDate ? localizedDateFormat.toNumericDate(new Date(row.confirmedTransportDate)) : '-'),
    headerLabel: label,
    key: 'confirmedTransportDate',
  }
}

export function useGenericContainerNumberColumn<T extends { containerNumber: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.containerNumber ?? '-'),
    headerLabel: label,
    key: 'containerNumber',
  }
}

export function useGenericContractIdColumn<T extends { contractId: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.contractId ?? '-'),
    headerLabel: label,
    key: 'contractId',
  }
}

export function useGenericContractItemColumn<T extends { contractItem: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.contractItem ?? '-'),
    headerLabel: label,
    key: 'contractItem',
  }
}

export function useGenericContractNumberColumn<T extends { contractNumber: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.contractNumber ?? '-'),
    headerLabel: label,
    key: 'contractNumber',
  }
}

export function useGenericCostCenterColumn<T extends { costCenter: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.costCenter ?? '-'),
    headerLabel: label,
    key: 'costCenter',
  }
}

export function useGenericCustomerIdColumn<T extends { customerId: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.customerId ?? '-'),
    headerLabel: label,
    key: 'customerId',
  }
}

export function useGenericCustomerNameColumn<T extends { customerName: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.customerName ?? '-'),
    headerLabel: label,
    key: 'customerName',
  }
}

export function useGenericCustomerReferenceColumn<T extends { customerReference: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.customerReference ?? '-'),
    headerLabel: label,
    key: 'customerReference',
  }
}

export function useGenericDateColumn<T extends { date: Date | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  const localizedDateFormat = useLocalizedDateFormat()

  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.date ? localizedDateFormat.toNumericDate(new Date(row.date)) : '-'),
    headerLabel: label,
    key: 'date',
  }
}

export function useGenericDateOfRequestColumn<T extends { dateOfRequest: Date | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  const localizedDateFormat = useLocalizedDateFormat()

  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.dateOfRequest ? localizedDateFormat.toNumericDate(new Date(row.dateOfRequest)) : '-'),
    headerLabel: label,
    key: 'dateOfRequest',
  }
}

export function useGenericDeliveryInfoColumn<T extends { deliveryInfo: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.deliveryInfo ?? '-'),
    headerLabel: label,
    key: 'deliveryInfo',
  }
}

export function useGenericDisposalCertificateNumberColumn<T extends { disposalCertificateNumber: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.disposalCertificateNumber ?? '-'),
    headerLabel: label,
    key: 'disposalCertificateNumber',
  }
}

export function useGenericEndTreatmentCenterIdColumn<T extends { endTreatmentCenterId: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.endTreatmentCenterId ?? '-'),
    headerLabel: label,
    key: 'endTreatmentCenterId',
  }
}

export function useGenericEndTreatmentCenterNameColumn<T extends { endTreatmentCenterName: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.endTreatmentCenterName ?? '-'),
    headerLabel: label,
    key: 'endTreatmentCenterName',
  }
}

export function useGenericEsnNumberColumn<T extends { esnNumber: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.esnNumber ?? '-'),
    headerLabel: label,
    key: 'esnNumber',
  }
}

export function useGenericEwcCodeColumn<T extends { ewcCode: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.ewcCode ?? '-'),
    headerLabel: label,
    key: 'ewcCode',
  }
}

export function useGenericEwcColumn<T extends { ewc: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.ewc ?? '-'),
    headerLabel: label,
    key: 'ewc',
  }
}

export function useGenericInquiryNumberColumn<T extends { inquiryNumber: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.inquiryNumber ?? '-'),
    headerLabel: label,
    key: 'inquiryNumber',
  }
}

export function useGenericInstallationNameColumn<T extends { installationName: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.installationName ?? '-'),
    headerLabel: label,
    key: 'installationName',
  }
}

export function useGenericIsHazardousColumn<T extends { isHazardous: boolean | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(AppTableBooleanCell, { value: row.isHazardous }),
    headerLabel: label,
    key: 'isHazardous',
  }
}

export function useGenericIsTransportByIndaverColumn<T extends { isTransportByIndaver: boolean | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(AppTableBooleanCell, { value: row.isTransportByIndaver }),
    headerLabel: label,
    key: 'isTransportByIndaver',
  }
}

export function useGenericMaterialAnalysisColumn<T extends { materialAnalysis: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.materialAnalysis ?? '-'),
    headerLabel: label,
    key: 'materialAnalysis',
  }
}

export function useGenericMaterialNumberColumn<T extends { materialNumber: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.materialNumber ?? '-'),
    headerLabel: label,
    key: 'materialNumber',
  }
}

export function useGenericMaterialTypeColumn<T extends { materialType: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.materialType ?? '-'),
    headerLabel: label,
    key: 'materialType',
  }
}

export function useGenericNameInstallationColumn<T extends { nameInstallation: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.nameInstallation ?? '-'),
    headerLabel: label,
    key: 'nameInstallation',
  }
}

export function useGenericNameOfApplicantColumn<T extends { nameOfApplicant: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.nameOfApplicant ?? '-'),
    headerLabel: label,
    key: 'nameOfApplicant',
  }
}

export function useGenericOrderNumberColumn<T extends { orderNumber: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.orderNumber ?? '-'),
    headerLabel: label,
    key: 'orderNumber',
  }
}

export function useGenericPackagedColumn<T extends { packaged: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.packaged ?? '-'),
    headerLabel: label,
    key: 'packaged',
  }
}

export function useGenericPackagingIndicatorColumn<T extends { packagingIndicator: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.packagingIndicator ?? '-'),
    headerLabel: label,
    key: 'packagingIndicator',
  }
}

export function useGenericPickupAddressIdColumn<T extends { pickupAddressId: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.pickupAddressId ?? '-'),
    headerLabel: label,
    key: 'pickupAddressId',
  }
}

export function useGenericPickupAddressNameColumn<T extends { pickupAddressName: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.pickupAddressName ?? '-'),
    headerLabel: label,
    key: 'pickupAddressName',
  }
}

export function useGenericProcessCodeColumn<T extends { processCode: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.processCode ?? '-'),
    headerLabel: label,
    key: 'processCode',
  }
}

export function useGenericRemarksColumn<T extends { remarks: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.remarks ?? '-'),
    headerLabel: label,
    key: 'remarks',
  }
}

export function useGenericRequestNumberColumn<T extends { requestNumber: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.requestNumber ?? '-'),
    headerLabel: label,
    key: 'requestNumber',
  }
}

export function useGenericRequestedEndDateColumn<T extends { requestedEndDate: Date | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  const localizedDateFormat = useLocalizedDateFormat()

  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.requestedEndDate ? localizedDateFormat.toNumericDate(new Date(row.requestedEndDate)) : '-'),
    headerLabel: label,
    key: 'requestedEndDate',
  }
}

export function useGenericRequestedStartDateColumn<T extends { requestedStartDate: Date | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  const localizedDateFormat = useLocalizedDateFormat()

  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.requestedStartDate ? localizedDateFormat.toNumericDate(new Date(row.requestedStartDate)) : '-'),
    headerLabel: label,
    key: 'requestedStartDate',
  }
}

export function useGenericRequestorNameColumn<T extends { requestorName: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.requestorName ?? '-'),
    headerLabel: label,
    key: 'requestorName',
  }
}

export function useGenericSalesOrderColumn<T extends { salesOrder: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.salesOrder ?? '-'),
    headerLabel: label,
    key: 'salesOrder',
  }
}

export function useGenericSalesOrganisationIdColumn<T extends { salesOrganisationId: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.salesOrganisationId ?? '-'),
    headerLabel: label,
    key: 'salesOrganisationId',
  }
}

export function useGenericSalesOrganisationNameColumn<T extends { salesOrganisationName: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.salesOrganisationName ?? '-'),
    headerLabel: label,
    key: 'salesOrganisationName',
  }
}

export function useGenericTcNumberColumn<T extends { tcNumber: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.tcNumber ?? '-'),
    headerLabel: label,
    key: 'tcNumber',
  }
}

export function useGenericTfsNumberColumn<T extends { tfsNumber: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.tfsNumber ?? '-'),
    headerLabel: label,
    key: 'tfsNumber',
  }
}

export function useGenericTfsColumn<T extends { tfs: boolean | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(AppTableBooleanCell, { value: row.tfs }),
    headerLabel: label,
    key: 'tfs',
  }
}

export function useGenericTransportModeColumn<T extends { transportMode: TransportMode | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.transportMode ? i18nPlugin.global.t(TransportModeEnumUtil.getLabelI18nKey(row.transportMode)) : '-'),
    headerLabel: label,
    key: 'transportMode',
  }
}

export function useGenericTreatmentCenterNameColumn<T extends { treatmentCenterName: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.treatmentCenterName ?? '-'),
    headerLabel: label,
    key: 'treatmentCenterName',
  }
}

export function useGenericWasteMaterialColumn<T extends { wasteMaterial: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.wasteMaterial ?? '-'),
    headerLabel: label,
    key: 'wasteMaterial',
  }
}

export function useGenericWasteProducerIdColumn<T extends { wasteProducerId: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.wasteProducerId ?? '-'),
    headerLabel: label,
    key: 'wasteProducerId',
  }
}

export function useGenericWasteProducerNameColumn<T extends { wasteProducerName: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    testId: TEST_ID.WASTE_INQUIRY.OVERVIEW.TABLE.WASTE_PRODUCER_COLUMN,
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.wasteProducerName ?? '-'),
    headerLabel: label,
    key: 'wasteProducerName',
  }
}

export function useGenericWasteStreamNameColumn<T extends { wasteStreamName: string | null }>(
  label: string,
  isSortable: boolean,
): TableColumn<T> {
  return {
    isSortable,
    cell: (row): VNode => h(VcTableCell, () => row.wasteStreamName ?? '-'),
    headerLabel: label,
    key: 'wasteStreamName',
  }
}
