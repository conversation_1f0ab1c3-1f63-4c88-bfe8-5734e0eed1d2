import { useQueryClient } from '@tanstack/vue-query'
import { useLocalStorage } from '@vueuse/core'
import { useVcToast } from '@wisemen/vue-core-components'
import type {
  ComputedRef,
  Ref,
} from 'vue'
import {
  computed,
  watch,
} from 'vue'
import { useI18n } from 'vue-i18n'

import { client } from '@/client/client.gen'
import { useSearch } from '@/composables/search/search.composable'
import type { CustomerIndex } from '@/models/customer/index/customerIndex.model'
import { useCustomerIndexQuery } from '@/modules/waste-inquiry/api/queries/customerIndex.query'
import { useAuthStore } from '@/stores/auth.store'

interface UseGlobalCustomerReturnType {
  isDebouncing: ComputedRef<boolean>
  isFetching: ComputedRef<boolean>
  isLoading: ComputedRef<boolean>
  customers: ComputedRef<CustomerIndex[]>
  debouncedSearch: Ref<string>
  fetchNextPage: () => Promise<void>
  globalCustomer: Ref<CustomerIndex | null>
  search: Ref<string>
}

let isInitialized = false

export function useGlobalCustomer(): UseGlobalCustomerReturnType {
  const queryClient = useQueryClient()

  const globalCustomer = useLocalStorage<CustomerIndex | null>('global-customer', null, {
    serializer: {
      read: (value) => value !== null ? JSON.parse(value) : null,
      write: (value) => JSON.stringify(value),
    },
  })

  const i18n = useI18n()
  const search = useSearch()
  const toast = useVcToast()

  const customerIndexQuery = useCustomerIndexQuery({ params: { search: search.debouncedSearch } })
  const customers = computed<CustomerIndex[]>(() => customerIndexQuery.data.value?.data ?? [])

  if (!isInitialized) {
    const authStore = useAuthStore()

    isInitialized = true

    // Set the global customer to the first customer in the list if it is not yet set
    watch(customers, (customers) => {
      if (globalCustomer.value === null && customers.length > 0 && !authStore.authUser?.isInternalUser) {
        globalCustomer.value = customers[0]
      }
    }, { immediate: true })

    watch(customerIndexQuery.isLoading, (isLoading) => {
      if (isLoading) {
        return
      }

      if (customers.value.length === 0 && !authStore.authUser?.isInternalUser) {
        toast.error({
          title: i18n.t('component.global_customer.no_customers_toast.title'),
          description: i18n.t('component.global_customer.no_customers_toast.description'),
        })
      }
    })

    // Set header for the global customer
    watch(globalCustomer, (customer) => {
      client.setConfig({ headers: { 'x-selected-customer': customer?.id ?? null } })

      // Refetch queries since the `x-selected-customer` header causes the backend to filter results
      // based on the selected customer.
      queryClient.refetchQueries({ type: 'active' })
    }, { immediate: true })
  }

  return {
    isDebouncing: computed<boolean>(() => search.isDebouncing.value),
    isFetching: computed<boolean>(() => customerIndexQuery.isFetching.value),
    isLoading: computed<boolean>(() => customerIndexQuery.isLoading.value),
    customers,
    debouncedSearch: search.debouncedSearch,
    fetchNextPage: customerIndexQuery.fetchNextPage,
    globalCustomer,
    search: search.search,
  }
}
