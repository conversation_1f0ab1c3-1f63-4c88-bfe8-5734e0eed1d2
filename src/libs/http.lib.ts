import { client } from '@/client/client.gen.ts'
import { API_BASE_URL } from '@/constants/environment.constant.ts'
import { oAuthClient } from '@/libs/oAuth.lib.ts'
import { i18nPlugin } from '@/plugins/i18n.plugin.ts'
import { useAuthStore } from '@/stores/auth.store.ts'
import { useImpersonationStore } from '@/stores/impersonation.store.ts'
import { QueryParamUtil } from '@/utils/queryParam.util.ts'

export function setupHttpClient(): void {
  client.setConfig({
    baseUrl: API_BASE_URL,
    headers: { 'Accept-Language': i18nPlugin.global.locale.value.split('-')[0] },
    querySerializer: QueryParamUtil.serialize,
  })

  client.interceptors.request.use(async (request: Request): Promise<Request> => {
    const isLoggedIn = await oAuthClient.isLoggedIn()

    if (!isLoggedIn) {
      return request
    }

    if (request.headers.has('Authorization')) {
      return request
    }

    const token = await oAuthClient.getAccessToken()

    request.headers.set('Authorization', `Bearer ${token}`)

    const impersonationStore = useImpersonationStore()
    const impersonationToken = impersonationStore.getImpersonationToken()

    if (impersonationToken) {
      request.headers.set('x-impersonate-user', impersonationToken)
    }

    return request
  })

  client.interceptors.response.use((response: Response): Response => {
    if (response?.status !== 401) {
      return response
    }

    const authStore = useAuthStore()

    authStore.logout()

    return response
  })
}
