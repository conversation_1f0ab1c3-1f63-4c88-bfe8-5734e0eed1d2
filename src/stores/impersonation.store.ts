import { defineStore } from 'pinia'
import {
  ref,
  watch,
} from 'vue'

import { ImpersonationService } from '@/api/services/impersonation.service'
import type { UserIndex } from '@/models/user/index/userIndex.model'

const STORAGE_KEY = 'impersonation_state'

interface ImpersonationState {
  isImpersonating: boolean
  token: string | null
  user: UserIndex | null
}

export const useImpersonationStore = defineStore('impersonation', () => {
  const savedState = getSavedState()

  const impersonationToken = ref<string | null>(savedState.token)
  const impersonatedUser = ref<UserIndex | null>(savedState.user)
  const isImpersonating = ref<boolean>(savedState.isImpersonating)

  watch(
    [
      impersonationToken,
      impersonatedUser,
      isImpersonating,
    ],
    () => {
      saveState({
        isImpersonating: isImpersonating.value,
        token: impersonationToken.value,
        user: impersonatedUser.value,
      })
    },
    { deep: true },
  )

  async function startImpersonation(user: UserIndex): Promise<void> {
    try {
      const token = await ImpersonationService.startImpersonation(user.uuid)

      impersonationToken.value = token
      impersonatedUser.value = user
      isImpersonating.value = true
    }
    catch (error) {
      console.error('Failed to start impersonation:', error)

      throw error
    }
  }

  function stopImpersonation(): void {
    impersonationToken.value = null
    impersonatedUser.value = null
    isImpersonating.value = false
    // Clear localStorage
    localStorage.removeItem(STORAGE_KEY)
  }

  function getImpersonationToken(): string | null {
    return impersonationToken.value
  }

  return {
    isImpersonating,
    getImpersonationToken,
    impersonatedUser,
    impersonationToken,
    startImpersonation,
    stopImpersonation,
  }
})

function getSavedState(): ImpersonationState {
  try {
    const saved = localStorage.getItem(STORAGE_KEY)

    if (saved) {
      return JSON.parse(saved)
    }
  }
  catch (error) {
    console.error('Failed to load impersonation state:', error)
  }

  return {
    isImpersonating: false,
    token: null,
    user: null,
  }
}

function saveState(state: ImpersonationState): void {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(state))
  }
  catch (error) {
    console.error('Failed to save impersonation state:', error)
  }
}
