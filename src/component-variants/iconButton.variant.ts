import { defineComponentVariant } from '@wisemen/vue-core-components'

export const defaultIconButtonVariant = defineComponentVariant({
  config: { root: 'rounded-full' },
  component: 'iconButton',
})

export const unstyledIconButtonVariant = defineComponentVariant({
  config: { root: 'p-0 shadow-none border-none' },
  target: {
    prop: 'variant',
    value: 'unstyled',
  },
  component: 'iconButton',
})

export const primaryIconButtonVariant = defineComponentVariant({
  config: { root: 'bg-brand-500 border-brand-500 shadow-md disabled:shadow-none' },
  target: {
    prop: 'variant',
    value: 'primary',
  },
  component: 'iconButton',
})
