import { defineComponentVariant } from '@wisemen/vue-core-components'

export const defaultRouterLinkButtonVariant = defineComponentVariant({
  config: { root: 'rounded-full' },
  component: 'routerLinkButton',
})

export const primaryRouterLinkButtonVariant = defineComponentVariant({
  config: { root: 'bg-brand-500 border-brand-500 shadow-md disabled:shadow-none' },
  target: {
    prop: 'variant',
    value: 'primary',
  },
  component: 'routerLinkButton',
})

export const secondaryRouterLinkButtonVariant = defineComponentVariant({
  config: { root: 'text-brand-700 data-[loading=false]:not-disabled:hover:bg-brand-25/50 shadow-none' },
  target: {
    prop: 'variant',
    value: 'secondary',
  },
  component: 'routerLinkButton',
})
