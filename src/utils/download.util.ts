export class DownloadUtil {
  static downloadBlob(blob: <PERSON>lo<PERSON>, contentDisposition: string | null): void {
    let filename = 'document.pdf'

    if (contentDisposition && contentDisposition.includes('filename=')) {
      const match = contentDisposition.match(/filename="?([^"]+)"?/)

      if (match?.[1]) {
        filename = match[1]
      }
    }

    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')

    link.href = url
    link.download = filename
    link.click()

    URL.revokeObjectURL(url)
  }

  static openBlobInNewTab(blob: Blob, filename: string, mimeType: string): void {
    const reader = new FileReader()

    reader.onload = (): void => {
      const dataUrl = reader.result as string
      const newTab = window.open()

      if (!newTab) {
        return
      }

      newTab.document.write(`
        <html>
          <head><title>${filename}</title></head>
          <body style="margin:0">
            <iframe 
              src="${dataUrl}" 
              type="${mimeType}" 
              width="100%" 
              height="100%" 
              style="border:none"
            ></iframe>
            <a href="${dataUrl}" download="${filename}" style="display:none" id="dl"></a>
          </body>
        </html>
      `)
      newTab.document.close()
    }

    reader.readAsDataURL(blob)
  }

  static transformBase64ToBlob(base64: string, mimeType: string): Blob {
    const byteCharacters = atob(base64)
    const byteNumbers = Array.from({ length: byteCharacters.length }).fill(0).map((_, i) =>
      byteCharacters.charCodeAt(i))
    const byteArray = new Uint8Array(byteNumbers)

    return new Blob([
      byteArray,
    ], { type: mimeType })
  }
}
