import type { ViewUserIndexV1Data } from '@/client'
import type { OffsetPagination } from '@/types/pagination.type.ts'
import { SearchUtil } from '@/utils/search.util.ts'

import type { UserIndexQueryParams } from './userIndexQueryParams.model.ts'

export class UserIndexQueryParamsTransformer {
  static toDto(params: OffsetPagination<UserIndexQueryParams>): ViewUserIndexV1Data['query'] {
    return {
      pagination: {
        limit: params.pagination.limit,
        offset: params.pagination.offset,
      },
      search: SearchUtil.toDto(params.search),
    }
  }
}
