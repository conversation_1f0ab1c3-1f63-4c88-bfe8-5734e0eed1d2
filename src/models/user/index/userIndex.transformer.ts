import type { UserIndexView } from '@/client'

import type { UserIndex } from './userIndex.model.ts'

export class UserIndexTransformer {
  static fromDto(dto: UserIndexView): UserIndex {
    return {
      uuid: dto.uuid,
      email: dto.email,
      firstName: dto.firstName,
      lastName: dto.lastName,
      roles: dto.roles.map((role) => ({
        uuid: role.uuid,
        name: role.name,
      })),
      upn: dto.upn,
    }
  }
}
