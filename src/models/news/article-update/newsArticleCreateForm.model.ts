import { z } from 'zod'

import { Locale } from '@/client'
import { calendarDateSchema } from '@/models/date/calendarDate.model'
import { tiptapJSONContentSchema } from '@/models/editor/tiptapJsonContent.model'
import { s3FileSchema } from '@/models/s3-file/s3File.model'
import { i18nPlugin } from '@/plugins/i18n.plugin'
import { DateUtil } from '@/utils/date.util'
import { ZodUtil } from '@/utils/zod'

const iframeRegex = /^<iframe\b[^>]*>(.*?)<\/iframe>$/i

export const newsTranslationFormSchema = z.object({
  title: z.string().nullable(),
  content: tiptapJSONContentSchema.nullable(),
  language: z.nativeEnum(Locale),
})

export const newsArticleFormSchema = z.object({
  endDate: calendarDateSchema.nullable(),
  startDate: calendarDateSchema.nullable(),
  image: s3FileSchema.nullable(),
  newsItemTranslations: newsTranslationFormSchema.array(),
  videoIFrame: z
    .string()
    .trim()
    .regex(iframeRegex, { message: 'Only a valid <iframe> tag is allowed.' })
    .or(z.literal('').transform(() => null))
    .nullable(),
}).superRefine((data, ctx) => {
  ZodUtil.validateObject('image', data.image, ctx)
  ZodUtil.validateField('startDate', z.date(), data.startDate, ctx)

  if (data.startDate && data.endDate && DateUtil.isAfter(data.startDate, data.endDate)) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: i18nPlugin.global.t('module.news.update.validation.end_date_after_start_date'),
      path: [
        'endDate',
      ],
    })
  }

  for (const [
    index,
    translation,
  ] of data.newsItemTranslations.entries()) {
    if ((translation.title && !translation.content) || (!translation.title && translation.content)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: i18nPlugin.global.t('module.news.update.validation.title_and_content_required'),
        path: [
          'newsItemTranslations',
          index,
        ],
      })
    }
  }

  const validTranslations = data.newsItemTranslations.some((translation) => {
    return translation.title && translation.content
  })

  if (!validTranslations) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: i18nPlugin.global.t('module.news.update.validation.at_least_one_translation'),
      path: [
        'newsItemTranslations',
      ],
    })
  }
})

export type NewsArticleForm = z.infer<typeof newsArticleFormSchema>
export type NewsArticleTranslationForm = z.infer<typeof newsTranslationFormSchema>
