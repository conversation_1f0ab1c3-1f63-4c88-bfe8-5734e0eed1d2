import {
  Locale,
  MimeType,
  PublishStatus,
} from '@/client'
import { CalendarDateTimeTransformer } from '@/models/date/calendarDateTime.transformer.ts'
import type { NewsArticleDetailDto } from '@/models/news/detail/newsArticleDetailDto.model.ts'
import { UuidUtil } from '@/utils/uuid.util.ts'

export class NewsArticleDetailDtoBuilder {
  private value: NewsArticleDetailDto = {
    uuid: UuidUtil.getRandom(),
    createdAt: CalendarDateTimeTransformer.toDto(new Date()),
    endDate: null,
    startDate: CalendarDateTimeTransformer.toDto(new Date()),
    updatedAt: CalendarDateTimeTransformer.toDto(new Date()),
    author: {
      uuid: UuidUtil.getRandom(),
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
    },
    image: {
      uuid: UuidUtil.getRandom(),
      name: 'image',
      mimeType: MimeType.IMAGE_JPEG,
      url: '',
    },
    publishStatus: PublishStatus.PUBLISHED,
    salesOrganisations: [],
    translations: [
      {
        uuid: UuidUtil.getRandom(),
        title: 'Article Title',
        createdAt: CalendarDateTimeTransformer.toDto(new Date()),
        updatedAt: CalendarDateTimeTransformer.toDto(new Date()),
        content: {
          en_GB: 'This is the content of the article in English.',
          nl_BE: 'Dit is de inhoud van het artikel in het Nederlands.',
        },
        language: Locale.EN_GB,
      },
    ],
    videoIFrame: null,
  }

  constructor() {
  }

  build(): NewsArticleDetailDto {
    return this.value
  }

  withUuid(uuid: string): NewsArticleDetailDtoBuilder {
    this.value.uuid = uuid

    return this
  }
}
