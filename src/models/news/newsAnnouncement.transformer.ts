import type { Locale } from '@/client'
import { AnnouncementType } from '@/client'
import type { AnnouncementTranslationUuid } from '@/models/announcement/announcementTranslationUuid.model'
import type { DashboardAnnouncementIndex } from '@/models/announcement/dashboard-index/dashboardAnnouncementIndex.model'
import type { DashboardAnnoucementIndexDto } from '@/models/announcement/dashboard-index/dashboardAnnouncementIndexDto.model'
import type { DashboardAnnouncementIndexPagination } from '@/models/announcement/dashboard-index/dashboardAnnouncementIndexPagination.model'
import type { DashboardAnnouncementIndexPaginationDto } from '@/models/announcement/dashboard-index/dashboardAnnouncementIndexPaginationDto.model'
import type {
  NewsAnnouncementDetail,
  NewsAnnouncementDetailAuthor,
  NewsAnnouncementDetailTranslation,
} from '@/models/announcement/detail/announcementDetail.model'
import type { NewsAnnouncementDetailDto } from '@/models/announcement/detail/announcementDetailDto.model'
import type { NewsAnnouncementIndex } from '@/models/announcement/index/announcementIndex.model'
import type { NewsAnnouncementIndexDto } from '@/models/announcement/index/announcementIndexDto.model'
import type { NewsAnnouncementIndexPagination } from '@/models/announcement/index/announcementIndexPagination.model'
import type { NewsAnnouncementIndexPaginationDto } from '@/models/announcement/index/announcementIndexPaginationDto.model'
import type { CalendarDate } from '@/models/date/calendarDate.model'
import { CalendarDateTransformer } from '@/models/date/calendarDate.transformer'
import type { NewsAnnouncementCreateDto } from '@/models/news/announcement-create/newsAnnouncementCreateDto.model'
import type { NewsAnnouncementCreateForm } from '@/models/news/announcement-create/newsAnnouncementCreateForm.model'
import type { NewsAnnouncementUpdateDto } from '@/models/news/announcement-update/newsAnnouncementUpdateDto.model'
import type { NewsAnnouncementUpdateForm } from '@/models/news/announcement-update/newsAnnouncementUpdateForm.model'
import type { NewsAnnouncementUuid } from '@/models/news/newsAnnouncementUuid.model'

export class NewsAnnouncementCreateTransformer {
  static toDto(form: NewsAnnouncementCreateForm): NewsAnnouncementCreateDto {
    return {
      endDate: CalendarDateTransformer.toNullableDto(form.endDate),
      startDate: CalendarDateTransformer.toDto(form.startDate!),
      translations: form.translations
        .filter((item) => item.title !== null && item.content !== null)
        .map((item) => ({
          title: item.title!,
          content: item.content!,
          language: item.language as unknown as Locale,
        })),
      type: form.type,
    }
  }
}

export class NewsAnnouncementUpdateFormTransformer {
  static toDto(form: NewsAnnouncementUpdateForm): NewsAnnouncementUpdateDto {
    return {
      endDate: CalendarDateTransformer.toNullableDto(form.endDate),
      startDate: CalendarDateTransformer.toDto(form.startDate!),
      translations: form.translations
        .filter((item) => item.title !== null && item.content !== null)
        .map((item) => ({
          title: item.title!,
          content: item.content!,
          language: item.language as unknown as Locale,
        })),
      type: form.type,
    }
  }
}

export class NewsAnnouncementIndexPaginationTransformer {
  static toDto(pagination: NewsAnnouncementIndexPagination): NewsAnnouncementIndexPaginationDto {
    return {
      filter: pagination.filter,
      sort: pagination.sort,
    }
  }
}

export class NewsAnnouncementDetailTransformer {
  static fromDto(dto: NewsAnnouncementDetailDto): NewsAnnouncementDetail {
    return {
      uuid: dto.uuid as NewsAnnouncementUuid,
      createdAt: new Date(dto.createdAt) as CalendarDate,
      endDate: CalendarDateTransformer.fromNullableDto(dto.endDate),
      startDate: CalendarDateTransformer.fromNullableDto(dto.startDate),
      updatedAt: new Date(dto.updatedAt) as CalendarDate,
      author: this.mapAuthor(dto),
      publishStatus: dto.publishStatus,
      translations: this.mapTranslations(dto),
      type: dto.type,
    }
  }

  static mapAuthor(dto: NewsAnnouncementDetailDto | NewsAnnouncementIndexDto): NewsAnnouncementDetailAuthor {
    return {
      uuid: dto.author.uuid,
      email: dto.author.email,
      firstName: dto.author.firstName,
      fullName: `${dto.author.firstName ?? ''} ${dto.author.lastName ?? ''}`,
      lastName: dto.author.lastName,
    }
  }

  static mapTranslations(dto: NewsAnnouncementDetailDto): NewsAnnouncementDetailTranslation[] {
    return dto.translations.map((item) => ({
      uuid: item.uuid as AnnouncementTranslationUuid,
      title: item.title,
      createdAt: new Date(dto.createdAt) as CalendarDate,
      updatedAt: new Date(dto.updatedAt) as CalendarDate,
      content: item.content,
      language: item.language,
    }))
  }
}

export class NewsAnnouncementIndexTransformer {
  static fromDto(dto: NewsAnnouncementIndexDto): NewsAnnouncementIndex {
    return {
      uuid: dto.uuid as NewsAnnouncementUuid,
      createdAt: new Date(dto.createdAt) as CalendarDate,
      endDate: dto.endDate ? new Date(dto.endDate) as CalendarDate : null,
      startDate: dto.startDate ? new Date(dto.startDate) as CalendarDate : null,
      updatedAt: new Date(dto.updatedAt) as CalendarDate,
      author: NewsAnnouncementDetailTransformer.mapAuthor(dto),
      publishStatus: dto.publishStatus,
      translations: dto.translations.map((item) => ({
        uuid: item.uuid as AnnouncementTranslationUuid,
        title: item.title,
        language: item.language,
      })),
      type: dto.type,
    }
  }
}

export class DashboardAnnoucementIndexTransformer {
  static fromDto(dto: DashboardAnnoucementIndexDto): DashboardAnnouncementIndex {
    return {
      uuid: dto.uuid as NewsAnnouncementUuid,
      translation: {
        title: dto.translation.title ?? '',
        content: dto.translation.content ?? {},
      },
      type: dto.type ?? AnnouncementType.INFORMATIONAL,
    }
  }
}

export class DashboardAnnouncementIndexPaginationTransformer {
  static toDto(pagination: DashboardAnnouncementIndexPagination): DashboardAnnouncementIndexPaginationDto {
    return {
      filter: pagination.filter,
      sort: pagination.sort,
    }
  }
}
