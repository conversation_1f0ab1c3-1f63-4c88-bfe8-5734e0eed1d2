import type { ViewGuidanceLetterIndexV1Data } from '@/client'
import { ViewGuidanceLetterIndexSortQueryKey } from '@/client'
import type { GuidanceLetterIndexQueryParams } from '@/models/guidance-letter/index/guidanceLetterIndexQueryParams.model'
import type { OffsetPagination } from '@/types/pagination.type'
import { SortUtil } from '@/utils/sort.util'

export class GuidanceLetterIndexQueryParamsTransformer {
  static toDto(params: OffsetPagination<GuidanceLetterIndexQueryParams>): ViewGuidanceLetterIndexV1Data['query'] {
    return {
      pagination: params.pagination,
      sort: SortUtil.toDto(params.sort, {
        customerId: ViewGuidanceLetterIndexSortQueryKey.CUSTOMER_ID,
        pickUpAddressId: ViewGuidanceLetterIndexSortQueryKey.PICK_UP_ADDRESS_ID,
        shipmentId: ViewGuidanceLetterIndexSortQueryKey.SHIPMENT_ID,
        wasteProducerId: ViewGuidanceLetterIndexSortQueryKey.WASTE_PRODUCER_ID,
        transportDate: ViewGuidanceLetterIndexSortQueryKey.TRANSPORT_DATE,
        customerName: ViewGuidanceLetterIndexSortQueryKey.CUSTOMER_NAME,
        pickUpAddressName: ViewGuidanceLetterIndexSortQueryKey.PICK_UP_ADDRESS_NAME,
        requestNumber: ViewGuidanceLetterIndexSortQueryKey.REQUEST_NUMBER,
        salesDoc: ViewGuidanceLetterIndexSortQueryKey.SALES_DOC,
        wasteMaterial: ViewGuidanceLetterIndexSortQueryKey.WASTE_MATERIAL,
        wasteProducerName: ViewGuidanceLetterIndexSortQueryKey.WASTE_PRODUCER_NAME,
      }),
    }
  }
}
