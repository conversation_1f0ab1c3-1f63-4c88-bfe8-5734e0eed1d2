import type { GuidanceLetterResponse } from '@/client'
import { CalendarDateTransformer } from '@/models/date/calendarDate.transformer'
import type { GuidanceLetterIndex } from '@/models/guidance-letter/index/guidanceLetterIndex.model'

export class GuidanceLetterIndexTransformer {
  static fromDto(dto: GuidanceLetterResponse): GuidanceLetterIndex {
    return {
      customerId: dto.customerId,
      pickUpAddressId: dto.pickUpAddressId,
      shipmentId: dto.shipmentId,
      wasteProducerId: dto.wasteProducerId,
      transportDate: CalendarDateTransformer.fromNullableDto(dto.transportDate),
      attachment: dto.attachment,
      contractNumber: dto.salesDoc,
      customerName: dto.customerName,
      guidanceLetter: dto.guidanceLetter,
      pickUpAddressName: dto.pickUpAddressName,
      requestNumber: dto.requestNumber,
      unit: dto.unit,
      wasteMaterial: dto.wasteMaterial,
      wasteProducerName: dto.wasteProducerName,
      weightOrVolume: dto.weightOrVolume,
    }
  }
}
