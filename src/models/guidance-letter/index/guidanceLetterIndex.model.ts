import type { CalendarDate } from '@/models/date/calendarDate.model'

export interface GuidanceLetterIndex {
  customerId: string | null
  pickUpAddressId: string | null
  shipmentId: string
  wasteProducerId: string | null
  transportDate: CalendarDate | null
  attachment: boolean
  contractNumber: string
  customerName: string | null
  guidanceLetter: boolean
  pickUpAddressName: string | null
  requestNumber: string | null
  unit: string | null
  wasteMaterial: string | null
  wasteProducerName: string | null
  weightOrVolume: number | null
}
