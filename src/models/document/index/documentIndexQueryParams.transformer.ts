import type { ViewDocumentIndexV1Data } from '@/client'
import type { DocumentIndexQueryParams } from '@/models/document/index/documentIndexQueryParams.model'
import type { KeysetPagination } from '@/types/pagination.type'
import { SearchUtil } from '@/utils/search.util'

export class DocumentIndexQueryParamsTransformer {
  static toDto(params: KeysetPagination<DocumentIndexQueryParams>): ViewDocumentIndexV1Data['query'] {
    return {
      filter: {
        customerUuid: params.filters.customerUuid,
        refExt: params.filters.refExt ?? undefined,
        transportType: params.filters.transportType ?? undefined,
        viewName: params.filters.viewName,
        wasteProducerIds: params.filters.wasteProducerIds,
        year: params.filters.year ?? undefined,
      },
      pagination: params.pagination,
      search: SearchUtil.toDto(params.search),
    }
  }
}
