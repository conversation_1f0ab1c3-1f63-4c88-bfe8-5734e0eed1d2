import type { SharepointDocumentViewName } from '@/client'
import type {
  <PERSON><PERSON><PERSON>er<PERSON>uery,
  WithSearchQuery,
} from '@/types/query.type'

export interface DocumentIndexQueryParams extends WithSearchQuery, WithFilterQuery<{
  customerUuid: string
  refExt: string | null
  transportType: string | null
  viewName: SharepointDocumentViewName
  wasteProducerIds: Array<string>
  year: string | null
}> {}
