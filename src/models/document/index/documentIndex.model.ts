import type {
  SharepointDocumentStatus,
  SharepointDocumentType,
} from '@/client'
import type { CalendarDate } from '@/models/date/calendarDate.model'

export interface DocumentIndex {
  id: string
  actionAt: CalendarDate | null
  name: string
  applicableFrom: CalendarDate | null
  applicableTill: CalendarDate | null
  status: SharepointDocumentStatus | null
  tfsType: SharepointDocumentType | null
  wasteProducer: string
}
