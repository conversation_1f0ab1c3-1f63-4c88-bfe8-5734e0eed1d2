import type { DocumentSiteIndex } from '@/models/document/site/documentSiteIndex.model.ts'
import type { DocumentSiteIndexDto } from '@/models/document/site/documentSiteIndexDto.model.ts'
import type { DocumentSiteUuid } from '@/models/document/site/documentSiteUuid.model.ts'

export class DocumentSiteIndexTransformer {
  static fromDto(dto: DocumentSiteIndexDto): DocumentSiteIndex {
    return {
      uuid: dto.uuid as DocumentSiteUuid,
      name: dto.name,
      wasteProducers: dto.wasteProducers.map((wasteProducer) => ({
        id: wasteProducer.id,
        name: wasteProducer.name,
      })),
    }
  }
}
