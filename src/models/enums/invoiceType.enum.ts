import { InvoiceType } from '@/client'
import type { I18n<PERSON><PERSON> } from '@/plugins/i18n.plugin.ts'
import { createI18nKeyMap } from '@/types/enum.type'

export class InvoiceTypeEnumUtil {
  private static i18nKeys = createI18nKeyMap<InvoiceType>({
    [InvoiceType.CREDIT_MEMO]: 'enum.invoice_type.credit_memo',
    [InvoiceType.DEBIT_MEMO]: 'enum.invoice_type.debit_memo',
    [InvoiceType.INVOICE]: 'enum.invoice_type.invoice',
    [InvoiceType.UNKNOWN]: 'enum.invoice_type.unknown',
  })

  static getI18nKey(value: InvoiceType): I18nKey {
    return this.i18nKeys.get(value)!
  }
}
