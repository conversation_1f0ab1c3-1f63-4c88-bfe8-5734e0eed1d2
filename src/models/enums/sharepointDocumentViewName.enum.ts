import { SharepointDocumentViewName } from '@/client'
import type { I18nKey } from '@/plugins/i18n.plugin.ts'
import { createI18nKeyMap } from '@/types/enum.type'

export class SharepointDocumentViewNameEnumUtil {
  private static i18nKeys = createI18nKeyMap<SharepointDocumentViewName>({
    [SharepointDocumentViewName.BSC]: 'enum.sharepoint_document_view_name.bsc',
    [SharepointDocumentViewName.CONTRACT]: 'enum.sharepoint_document_view_name.contract',
    [SharepointDocumentViewName.MANUAL]: 'enum.sharepoint_document_view_name.manual',
    [SharepointDocumentViewName.MASTERTABLE]: 'enum.sharepoint_document_view_name.mastertable',
    [SharepointDocumentViewName.MEETINGS]: 'enum.sharepoint_document_view_name.meetings',
    [SharepointDocumentViewName.QUOTATION]: 'enum.sharepoint_document_view_name.quotation',
    [SharepointDocumentViewName.TFS]: 'enum.sharepoint_document_view_name.tfs',
    [SharepointDocumentViewName.TRANSPORT]: 'enum.sharepoint_document_view_name.transport',
  })

  static getLabelI18nKey(value: SharepointDocumentViewName): I18nKey {
    return this.i18nKeys.get(value)!
  }

  static getOptions(): SharepointDocumentViewName[] {
    return Object.values(SharepointDocumentViewName)
  }
}
