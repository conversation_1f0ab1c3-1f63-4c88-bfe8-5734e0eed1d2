import type { SharepointDocumentType } from '@/client'

export class SharepointDocumentTypeEnumUtil {
  // TODO: BACKEND NEEDED Come back ot this when the enum isnt empty
  // private static i18nKeys = createI18nKeyMap<SharepointDocumentType>({})

  // static getLabelI18nKey(value: SharepointDocumentType): I18nKey {
  //   return this.i18nKeys.get(value)!
  // }

  static getOptions(): SharepointDocumentType[] {
    return []
  }
}
