import { ContractLinePackagingType } from '@/client'
import type { I18n<PERSON><PERSON> } from '@/plugins/i18n.plugin.ts'
import { createI18nKeyMap } from '@/types/enum.type'

export class ContractLinePackagingTypeEnumUtil {
  private static i18nKeys = createI18nKeyMap<ContractLinePackagingType>({
    [ContractLinePackagingType.BULK]: 'enum.contract_line_packaging_type.bulk',
    [ContractLinePackagingType.PACKAGED]: 'enum.contract_line_packaging_type.packaged',
  })

  static getI18nKey(value: ContractLinePackagingType): I18nKey {
    return this.i18nKeys.get(value)!
  }
}
