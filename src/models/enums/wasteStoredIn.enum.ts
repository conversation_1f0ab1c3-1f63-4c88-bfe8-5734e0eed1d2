import type { Icon } from '@wisemen/vue-core-components'

import { WasteStoredInOption } from '@/client'
import type { I18nKey } from '@/plugins/i18n.plugin.ts'
import { createI18nKeyMap } from '@/types/enum.type'

export class WasteStoredInEnumUtil {
  private static descriptionI18nKeys = createI18nKeyMap<WasteStoredInOption>({
    [WasteStoredInOption.DRUMS]: 'enum.waste_stored_in.drums.description',
    [WasteStoredInOption.IBCS]: 'enum.waste_stored_in.ibcs.description',
    [WasteStoredInOption.OTHER]: 'enum.waste_stored_in.other.description',
    [WasteStoredInOption.STORAGE_TANK]: 'enum.waste_stored_in.storage_tank.description',
    [WasteStoredInOption.TANK_CONTAINER]: 'enum.waste_stored_in.tank_container.description',
  })

  private static labelI18nKeys = createI18nKeyMap<WasteStoredInOption>({
    [WasteStoredInOption.DRUMS]: 'enum.waste_stored_in.drums.label',
    [WasteStoredInOption.IBCS]: 'enum.waste_stored_in.ibcs.label',
    [WasteStoredInOption.OTHER]: 'enum.waste_stored_in.other.label',
    [WasteStoredInOption.STORAGE_TANK]: 'enum.waste_stored_in.storage_tank.label',
    [WasteStoredInOption.TANK_CONTAINER]: 'enum.waste_stored_in.tank_container.label',
  })

  static getDescriptionI18nKey(value: WasteStoredInOption): I18nKey {
    return this.descriptionI18nKeys.get(value)!
  }

  static getIcon(value: WasteStoredInOption): Icon | null {
    switch (value) {
      case WasteStoredInOption.DRUMS:
        return 'drum'
      case WasteStoredInOption.IBCS:
        return 'ibc'
      case WasteStoredInOption.OTHER:
        return null
      case WasteStoredInOption.STORAGE_TANK:
        return 'storageTank'
      case WasteStoredInOption.TANK_CONTAINER:
        return 'container'
    }
  }

  static getLabelI18nKey(value: WasteStoredInOption): I18nKey {
    return this.labelI18nKeys.get(value)!
  }
}
