import { SharepointDocumentStatus } from '@/client'
import type { I18n<PERSON><PERSON> } from '@/plugins/i18n.plugin.ts'
import { createI18nKeyMap } from '@/types/enum.type'
import type { SelectOption } from '@/types/tableFilter.type'

export class SharepointDocumentStatusEnumUtil {
  private static i18nKeys = createI18nKeyMap<SharepointDocumentStatus>({
    [SharepointDocumentStatus.ACTIVE]: 'enum.sharepoint_document_status.active',
    [SharepointDocumentStatus.ARCHIVED]: 'enum.sharepoint_document_status.archived',
  })

  static getLabelI18nKey(value: SharepointDocumentStatus): I18nKey {
    return this.i18nKeys.get(value)!
  }

  static getSelectOptions(): SelectOption<SharepointDocumentStatus>[] {
    return Object.values(SharepointDocumentStatus).map((status) => ({
      type: 'option',
      value: status,
    }))
  }
}
