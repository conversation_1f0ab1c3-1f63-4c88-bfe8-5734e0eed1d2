import type { Icon } from '@wisemen/vue-core-components'

import { WasteDischargeFrequency } from '@/client'
import type { I18nKey } from '@/plugins/i18n.plugin.ts'
import { createI18nKeyMap } from '@/types/enum.type'

export class WasteDischargeFrequencyEnumUtil {
  private static descriptionI18nKeys = createI18nKeyMap<WasteDischargeFrequency>({
    [WasteDischargeFrequency.ONCE_OFF_CAMPAIGN]: 'enum.waste_discharge_frequency.once_off_campaign.description',
    [WasteDischargeFrequency.ONCE_OFF_STREAM]: 'enum.waste_discharge_frequency.once_off_stream.description',
    [WasteDischargeFrequency.REGULAR_CAMPAIGN]: 'enum.waste_discharge_frequency.regular_campaign.description',
    [WasteDischargeFrequency.REGULAR_STREAM]: 'enum.waste_discharge_frequency.regular_stream.description',
  })

  private static labelI18nKeys = createI18nKeyMap<WasteDischargeFrequency>({
    [WasteDischargeFrequency.ONCE_OFF_CAMPAIGN]: 'enum.waste_discharge_frequency.once_off_campaign.label',
    [WasteDischargeFrequency.ONCE_OFF_STREAM]: 'enum.waste_discharge_frequency.once_off_stream.label',
    [WasteDischargeFrequency.REGULAR_CAMPAIGN]: 'enum.waste_discharge_frequency.regular_campaign.label',
    [WasteDischargeFrequency.REGULAR_STREAM]: 'enum.waste_discharge_frequency.regular_stream.label',
  })

  static getDescriptionI18nKey(value: WasteDischargeFrequency): I18nKey {
    return this.descriptionI18nKeys.get(value)!
  }

  static getIcon(value: WasteDischargeFrequency): Icon {
    switch (value) {
      case WasteDischargeFrequency.ONCE_OFF_CAMPAIGN:
        return 'onceOffStream'
      case WasteDischargeFrequency.ONCE_OFF_STREAM:
        return 'regularStream'
      case WasteDischargeFrequency.REGULAR_CAMPAIGN:
        return 'onceOffPhase'
      case WasteDischargeFrequency.REGULAR_STREAM:
        return 'regularPhase'
    }
  }

  static getLabelI18nKey(value: WasteDischargeFrequency): I18nKey {
    return this.labelI18nKeys.get(value)!
  }
}
