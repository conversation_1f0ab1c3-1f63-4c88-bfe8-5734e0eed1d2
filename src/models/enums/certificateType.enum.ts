import { CertificateType } from '@/client'
import type { I18n<PERSON><PERSON> } from '@/plugins/i18n.plugin.ts'
import { createI18nKeyMap } from '@/types/enum.type'

export class CertificateTypeEnumUtil {
  private static i18nKeys = createI18nKeyMap<CertificateType>({
    [CertificateType.COR]: 'enum.certificate_type.cor',
    [CertificateType.COT_COB]: 'enum.certificate_type.cot_cob',
  })

  static getLabelI18nKey(value: CertificateType): I18nKey {
    return this.i18nKeys.get(value)!
  }

  static getOptions(): CertificateType[] {
    return Object.values(CertificateType)
  }
}
