import type { Icon } from '@wisemen/vue-core-components'

import { RegulatedTransportOption } from '@/client'
import type { I18n<PERSON><PERSON> } from '@/plugins/i18n.plugin.ts'
import { createI18nKeyMap } from '@/types/enum.type'

export class RegulatedTransportEnumUtil {
  private static i18nKeys = createI18nKeyMap<RegulatedTransportOption>({
    [RegulatedTransportOption.NO]: 'enum.regulated_transport.no',
    [RegulatedTransportOption.UNKNOWN]: 'enum.regulated_transport.unknown',
    [RegulatedTransportOption.YES]: 'enum.regulated_transport.yes',
  })

  static getI18nKey(value: RegulatedTransportOption): I18nKey {
    return this.i18nKeys.get(value)!
  }

  static getIcon(value: RegulatedTransportOption): Icon {
    switch (value) {
      case RegulatedTransportOption.NO:
        return 'nonRegulatedTransport'
      case RegulatedTransportOption.UNKNOWN:
        return 'unknownRegulatedTransport'
      case RegulatedTransportOption.YES:
        return 'regulatedTransport'
    }
  }
}
