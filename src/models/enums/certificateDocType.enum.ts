import { CertificateDocType } from '@/client'
import type { I18n<PERSON><PERSON> } from '@/plugins/i18n.plugin'
import { createI18nKeyMap } from '@/types/enum.type'

export class CertificateDocTypeEnumUtil {
  private static i18nKeys = createI18nKeyMap<CertificateDocType>({
    [CertificateDocType.BLENDING_CONFIRMATION]: 'enum.certificate_doc_type.blending_confirmation',
    [CertificateDocType.CERTIFICATE_OF_TREATMENT]: 'enum.certificate_doc_type.certificate_of_treatment',
    [CertificateDocType.RECEIPT_CONFIRMATION]: 'enum.certificate_doc_type.receipt_confirmation',
    [CertificateDocType.TF_CERTIFICATES]: 'enum.certificate_doc_type.tf_certificates',
    [CertificateDocType.TREATMENT_CERTIFICATES]: 'enum.certificate_doc_type.treatment_certificates',
  })

  static getI18nKey(value: CertificateDocType): I18n<PERSON><PERSON> {
    return this.i18nKeys.get(value)!
  }
}
