import { DraftInvoiceStatus } from '@/client'
import type { I18n<PERSON>ey } from '@/plugins/i18n.plugin.ts'
import { createI18nKeyMap } from '@/types/enum.type'
import type { SelectOption } from '@/types/tableFilter.type'

export class InvoiceDraftStatusEnumUtil {
  private static i18nKeys = createI18nKeyMap<DraftInvoiceStatus>({
    [DraftInvoiceStatus.APPROVED_BY_CUSTOMER]: 'enum.draft_invoice_status.approved_by_customer',
    [DraftInvoiceStatus.AUTO_APPROVED]: 'enum.draft_invoice_status.auto_approved',
    [DraftInvoiceStatus.INTERNAL_APPROVED]: 'enum.draft_invoice_status.internal_approved',
    [DraftInvoiceStatus.REJECTED_BY_CUSTOMER]: 'enum.draft_invoice_status.rejected_by_customer',
    [DraftInvoiceStatus.REJECTED_BY_INDAVER]: 'enum.draft_invoice_status.rejected_by_indaver',
    [DraftInvoiceStatus.TO_BE_APPROVED_BY_CUSTOMER]: 'enum.draft_invoice_status.to_be_approved_by_customer',
    [DraftInvoiceStatus.TO_BE_APPROVED_BY_INDAVER]: 'enum.draft_invoice_status.to_be_approved_by_indaver',
  })

  private static shortStatusI18nKeys = createI18nKeyMap<DraftInvoiceStatus>({
    [DraftInvoiceStatus.APPROVED_BY_CUSTOMER]: 'enum.draft_invoice_status_short.approved',
    [DraftInvoiceStatus.AUTO_APPROVED]: 'enum.draft_invoice_status.auto_approved',
    [DraftInvoiceStatus.INTERNAL_APPROVED]: 'enum.draft_invoice_status_short.approved',
    [DraftInvoiceStatus.REJECTED_BY_CUSTOMER]: 'enum.draft_invoice_status_short.rejected',
    [DraftInvoiceStatus.REJECTED_BY_INDAVER]: 'enum.draft_invoice_status_short.rejected',
    [DraftInvoiceStatus.TO_BE_APPROVED_BY_CUSTOMER]: 'enum.draft_invoice_status_short.to_be_approved',
    [DraftInvoiceStatus.TO_BE_APPROVED_BY_INDAVER]: 'enum.draft_invoice_status_short.to_be_approved',
  })

  static getI18nKey(value: DraftInvoiceStatus): I18nKey {
    return this.i18nKeys.get(value)!
  }

  static getSelectOptions(): SelectOption<DraftInvoiceStatus>[] {
    return Object.values(DraftInvoiceStatus).map((status) => ({
      type: 'option',
      value: status,
    }))
  }

  static getSelectOptionsBasedOnUser(isInternalUser: boolean): SelectOption<DraftInvoiceStatus>[] {
    if (isInternalUser) {
      return this.getSelectOptions()
    }

    const customerStatuses: DraftInvoiceStatus[] = [
      DraftInvoiceStatus.APPROVED_BY_CUSTOMER,
      DraftInvoiceStatus.REJECTED_BY_CUSTOMER,
      DraftInvoiceStatus.TO_BE_APPROVED_BY_CUSTOMER,
    ]

    return customerStatuses.map((status) => ({
      type: 'option',
      value: status,
    }))
  }

  static getShortStatusI18nKey(value: DraftInvoiceStatus): I18nKey {
    return this.shortStatusI18nKeys.get(value)!
  }
}
