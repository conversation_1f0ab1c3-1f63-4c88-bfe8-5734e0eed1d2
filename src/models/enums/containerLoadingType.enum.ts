import type { Icon } from '@wisemen/vue-core-components'

import { ContainerLoadingType } from '@/client'
import type { I18nKey } from '@/plugins/i18n.plugin.ts'
import { createI18nKeyMap } from '@/types/enum.type'

export class ContainerLoadingTypeEnumUtil {
  private static descriptionI18nKeys = createI18nKeyMap<ContainerLoadingType>({
    [ContainerLoadingType.CHAIN]: 'enum.container_loading_type.chain.description',
    [ContainerLoadingType.HOOK]: 'enum.container_loading_type.hook.description',
  })

  private static labelI18nKeys = createI18nKeyMap<ContainerLoadingType>({
    [ContainerLoadingType.CHAIN]: 'enum.container_loading_type.chain.label',
    [ContainerLoadingType.HOOK]: 'enum.container_loading_type.hook.label',
  })

  static getDescriptionI18nKey(value: ContainerLoadingType): I18n<PERSON>ey {
    return this.descriptionI18nKeys.get(value)!
  }

  static getIcon(value: ContainerLoadingType): Icon {
    switch (value) {
      case ContainerLoadingType.CHAIN:
        return 'chain'
      case ContainerLoadingType.HOOK:
        return 'hook'
    }
  }

  static getLabelI18nKey(value: ContainerLoadingType): I18nKey {
    return this.labelI18nKeys.get(value)!
  }
}
