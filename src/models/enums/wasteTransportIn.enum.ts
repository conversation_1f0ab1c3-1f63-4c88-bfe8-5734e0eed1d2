import type { Icon } from '@wisemen/vue-core-components'

import { WasteTransportInOption } from '@/client'
import type { I18nKey } from '@/plugins/i18n.plugin.ts'
import { createI18nKeyMap } from '@/types/enum.type'

export class WasteTransportInEnumUtil {
  private static descriptionI18nKeys = createI18nKeyMap<WasteTransportInOption>({
    [WasteTransportInOption.NO_PREFERENCE]: 'enum.waste_transport_in.no_preference.description',
    [WasteTransportInOption.OTHER]: 'enum.waste_transport_in.other.description',
    [WasteTransportInOption.TANK_CONTAINER]: 'enum.waste_transport_in.tank_container.description',
    [WasteTransportInOption.TANK_TRAILER]: 'enum.waste_transport_in.tank_trailer.description',
  })

  private static labelI18nKeys = createI18nKeyMap<WasteTransportInOption>({
    [WasteTransportInOption.NO_PREFERENCE]: 'enum.waste_transport_in.no_preference.label',
    [WasteTransportInOption.OTHER]: 'enum.waste_transport_in.other.label',
    [WasteTransportInOption.TANK_CONTAINER]: 'enum.waste_transport_in.tank_container.label',
    [WasteTransportInOption.TANK_TRAILER]: 'enum.waste_transport_in.tank_trailer.label',
  })

  static getDescriptionI18nKey(value: WasteTransportInOption): I18nKey {
    return this.descriptionI18nKeys.get(value)!
  }

  static getIcon(value: WasteTransportInOption): Icon | null {
    switch (value) {
      case WasteTransportInOption.TANK_TRAILER:
        return 'tankTrailer'
      case WasteTransportInOption.TANK_CONTAINER:
        return 'bulk'
      case WasteTransportInOption.OTHER:
        return null
      case WasteTransportInOption.NO_PREFERENCE:
        return null
    }
  }

  static getLabelI18nKey(value: WasteTransportInOption): I18nKey {
    return this.labelI18nKeys.get(value)!
  }
}
