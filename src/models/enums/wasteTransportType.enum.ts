import type { Icon } from '@wisemen/vue-core-components'

import { WasteTransportType } from '@/client'
import type { I18nKey } from '@/plugins/i18n.plugin.ts'
import { createI18nKeyMap } from '@/types/enum.type'

export class WasteTransportTypeEnumUtil {
  private static descriptionI18nKeys = createI18nKeyMap<WasteTransportType>({
    [WasteTransportType.CONTAINER]: 'enum.waste_transport_type.container.description',
    [WasteTransportType.OTHER]: 'enum.waste_transport_type.other.description',
    [WasteTransportType.REL_TRUCK]: 'enum.waste_transport_type.rel_truck.description',
    [WasteTransportType.SKIP]: 'enum.waste_transport_type.skip.description',
    [WasteTransportType.TIPPER_TRUCK]: 'enum.waste_transport_type.tripper_truck.description',
  })

  private static labelI18nKeys = createI18nKeyMap<WasteTransportType>({
    [WasteTransportType.CONTAINER]: 'enum.waste_transport_type.container.label',
    [WasteTransportType.OTHER]: 'enum.waste_transport_type.other.label',
    [WasteTransportType.REL_TRUCK]: 'enum.waste_transport_type.rel_truck.label',
    [WasteTransportType.SKIP]: 'enum.waste_transport_type.skip.label',
    [WasteTransportType.TIPPER_TRUCK]: 'enum.waste_transport_type.tripper_truck.label',
  })

  static getDescriptionI18nKey(value: WasteTransportType): I18nKey {
    return this.descriptionI18nKeys.get(value)!
  }

  static getIcon(value: WasteTransportType): Icon {
    switch (value) {
      case WasteTransportType.CONTAINER:
        return 'container'
      case WasteTransportType.OTHER:
        return 'truck'
      case WasteTransportType.REL_TRUCK:
        return 'relFelTruck'
      case WasteTransportType.SKIP:
        return 'skip'
      case WasteTransportType.TIPPER_TRUCK:
        return 'tipperTruck'
      default:
        return 'truck'
    }
  }

  static getLabelI18nKey(value: WasteTransportType): I18nKey {
    return this.labelI18nKeys.get(value)!
  }
}
