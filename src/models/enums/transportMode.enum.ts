import type { Icon } from '@wisemen/vue-core-components'

import {
  PickUpTransportMode,
  TransportMode,
} from '@/client'
import type { I18nKey } from '@/plugins/i18n.plugin.ts'
import { createI18nKeyMap } from '@/types/enum.type'

export class PickupTransportModeEnumUtil {
  private static descriptionI18nKeys = createI18nKeyMap<PickUpTransportMode>({
    [PickUpTransportMode.BULK_ISO_TANK]: 'enum.pickup_transport_mode.bulk_iso_tank.description',
    [PickUpTransportMode.BULK_SKIPS_CONTAINER]: 'enum.pickup_transport_mode.bulk_skips_container.description',
    [PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS]: 'enum.pickup_transport_mode.bulk_vacuum_tankers_road_tankers.description',
    [PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK]: 'enum.pickup_transport_mode.packaged_curtain_sider_truck.description',
  })

  private static labelI18nKeys = createI18nKeyMap<PickUpTransportMode>({
    [PickUpTransportMode.BULK_ISO_TANK]: 'enum.pickup_transport_mode.bulk_iso_tank.label',
    [PickUpTransportMode.BULK_SKIPS_CONTAINER]: 'enum.pickup_transport_mode.bulk_skips_container.label',
    [PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS]: 'enum.pickup_transport_mode.bulk_vacuum_tankers_road_tankers.label',
    [PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK]: 'enum.pickup_transport_mode.packaged_curtain_sider_truck.label',
  })

  static getDescriptionI18nKey(value: PickUpTransportMode): I18nKey {
    return this.descriptionI18nKeys.get(value)!
  }

  static getIcon(value: PickUpTransportMode): Icon {
    switch (value) {
      case PickUpTransportMode.BULK_ISO_TANK:
        return 'bulk'
      case PickUpTransportMode.BULK_SKIPS_CONTAINER:
        return 'skip'
      case PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS:
        return 'vacuumTanker'
      case PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK:
        return 'curtainSideTruck'
      default:
        return 'truck'
    }
  }

  static getLabelI18nKey(value: PickUpTransportMode): I18nKey {
    return this.labelI18nKeys.get(value)!
  }

  static getOptions(): PickUpTransportMode[] {
    return Object.values(PickUpTransportMode)
  }
}

export class TransportModeEnumUtil {
  private static labelI18nKeys = createI18nKeyMap<TransportMode>({
    [TransportMode.BULK_ISO_TANK]: 'enum.pickup_transport_mode.bulk_iso_tank.label',
    [TransportMode.BULK_SKIPS_CONTAINER]: 'enum.pickup_transport_mode.bulk_skips_container.label',
    [TransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS]: 'enum.pickup_transport_mode.bulk_vacuum_tankers_road_tankers.label',
    [TransportMode.PACKAGED_CURTAIN_SIDER_TRUCK]: 'enum.pickup_transport_mode.packaged_curtain_sider_truck.label',
    [TransportMode.PACKAGING_REQUEST_ORDER]: 'enum.pickup_transport_mode.packaging_request_order.label',
  })

  static getLabelI18nKey(value: TransportMode): I18nKey {
    return this.labelI18nKeys.get(value)!
  }

  static getOptions(): TransportMode[] {
    return Object.values(TransportMode)
  }
}
