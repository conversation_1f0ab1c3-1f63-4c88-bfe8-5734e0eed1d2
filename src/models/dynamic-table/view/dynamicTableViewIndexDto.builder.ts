import { CalendarDateTimeTransformer } from '@/models/date/calendarDateTime.transformer'
import type { DynamicTableViewIndexDto } from '@/models/dynamic-table/view/dynamicTableViewIndexDto.model'
import { UuidUtil } from '@/utils/uuid.util'

export class DynamicTableViewIndexDtoBuilder {
  private value: DynamicTableViewIndexDto = {
    uuid: UuidUtil.getRandom(),
    createdAt: CalendarDateTimeTransformer.toDto(new Date()),
    updatedAt: CalendarDateTimeTransformer.toDto(new Date()),
    isDefaultGlobal: true,
    isGlobal: true,
    isUserDefault: true,
    name: 'Dynamic Table View',
    filters: [],
    sorts: [],
    visibleColumns: [
      {
        uuid: UuidUtil.getRandom(),
        order: 0,
      },
    ],
  }

  constructor() {
  }

  build(): DynamicTableViewIndexDto {
    return this.value
  }

  withColumnUuid(uuid: string): DynamicTableViewIndexDtoBuilder {
    this.value.visibleColumns[0].uuid = uuid

    return this
  }

  withUuid(uuid: string): DynamicTableViewIndexDtoBuilder {
    this.value.uuid = uuid

    return this
  }
}
