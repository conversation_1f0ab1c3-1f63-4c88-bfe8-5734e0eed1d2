import type {
  AnnouncementType,
  Locale,
  PublishStatus,
} from '@/client'
import type { CalendarDate } from '@/models/date/calendarDate.model'
import type { TiptapJSONContent } from '@/models/editor/tiptapJsonContent.model'
import type { NewsAnnouncementUuid } from '@/models/news/newsAnnouncementUuid.model'

export interface NewsAnnouncementDetailTranslation {
  uuid: string
  title: string | null
  createdAt: CalendarDate
  updatedAt: CalendarDate
  content: TiptapJSONContent | null
  language: Locale
}

export interface NewsAnnouncementDetailAuthor {
  uuid: string
  email: string
  firstName: string | null
  fullName: string
  lastName: string | null
}

export interface NewsAnnouncementDetail {
  uuid: NewsAnnouncementUuid
  createdAt: CalendarDate
  endDate: CalendarDate | null
  startDate: CalendarDate | null
  updatedAt: CalendarDate
  author: NewsAnnouncementDetailAuthor
  publishStatus: PublishStatus | null
  translations: NewsAnnouncementDetailTranslation[]
  type: AnnouncementType
}
