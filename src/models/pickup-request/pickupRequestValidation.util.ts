import { PickUpTransportMode } from '@/client'
import type { PickupRequestUpdateForm } from '@/models/pickup-request/update/pickupRequestUpdateForm.model'
import type { PickupRequestWasteMaterialForm } from '@/models/pickup-request/update/steps/pickupRequestWasteForm.model'
import { CustomerUtil } from '@/utils/customer.util'

export class BasePickupRequestValidationUtil {
  private readonly pickupRequestState: Partial<PickupRequestUpdateForm>

  constructor(pickupRequestState: Partial<PickupRequestUpdateForm>) {
    this.pickupRequestState = pickupRequestState
  }

  public get isCustomerGerman(): boolean {
    return this.pickupRequestState.customerCountryCode !== null
      && this.pickupRequestState.customerCountryCode !== undefined
      && CustomerUtil.isGerman(this.pickupRequestState.customerCountryCode)
  }

  public get isCustomerIrish(): boolean {
    return this.pickupRequestState.customerCountryCode !== null
      && this.pickupRequestState.customerCountryCode !== undefined
      && CustomerUtil.isIrish(this.pickupRequestState.customerCountryCode)
  }

  public get isTransportBulkIsoTank(): boolean {
    return this.pickupRequestState.transportMode === PickUpTransportMode.BULK_ISO_TANK
  }

  public get isTransportBulkSkipsContainer(): boolean {
    return this.pickupRequestState.transportMode === PickUpTransportMode.BULK_SKIPS_CONTAINER
  }

  public get isTransportBulkVacuumOrRoadTankers(): boolean {
    return this.pickupRequestState.transportMode === PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS
  }

  public get isTransportPackagedCurtainSiderTruck(): boolean {
    return this.pickupRequestState.transportMode === PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK
  }
}

export class PickupRequestRequiredUtil extends BasePickupRequestValidationUtil {
  public get isContainerTransportTypeRequired(): boolean {
    return this.isTransportBulkIsoTank || this.isTransportBulkSkipsContainer
  }

  public get isContainerTypeRequired(): boolean {
    return this.isTransportBulkSkipsContainer && this.isCustomerGerman
  }

  public get isContainerVolumeSizeRequired(): boolean {
    return (this.isTransportBulkIsoTank
      || this.isTransportBulkVacuumOrRoadTankers
      || this.isTransportBulkSkipsContainer)
    && (this.isCustomerGerman || this.isCustomerIrish)
  }

  public isCostCenterRequired(material: PickupRequestWasteMaterialForm): boolean {
    return material.isCostCenterRequired === true
  }

  public get isEstimatedWeightOrVolumeRequired(): boolean {
    return true
  }

  public isHazardInducersRequired(material: PickupRequestWasteMaterialForm): boolean {
    return material.unNumber?.isHazardous === true && this.isCustomerGerman
  }

  public get isPackagingTypeRequired(): boolean {
    return this.isTransportPackagedCurtainSiderTruck
  }

  public isPoNumberRequired(material: PickupRequestWasteMaterialForm): boolean {
    return material.isPoNumberRequired === true
  }

  public get isQuantityLabelsRequired(): boolean {
    return !this.isCustomerGerman
  }

  public get isQuantityPackagesRequired(): boolean {
    return this.isTransportPackagedCurtainSiderTruck
  }

  public get isQuantityPalletsRequired(): boolean {
    return this.isCustomerGerman
  }

  public get isTankerTypeRequired(): boolean {
    return this.isTransportBulkVacuumOrRoadTankers
  }

  public get isUnNumberRequired(): boolean {
    return true
  }
}

export class PickupRequestValidationUtil extends BasePickupRequestValidationUtil {
  public get isContainerVolumeSizeAllowed(): boolean {
    return (this.isTransportBulkIsoTank
      || this.isTransportBulkVacuumOrRoadTankers
      || this.isTransportBulkSkipsContainer)
    && (this.isCustomerGerman || this.isCustomerIrish)
  }

  public get isHazardInducersAllowed(): boolean {
    return this.isCustomerGerman
  }

  public get isMaterialContainerCoveredAllowed(): boolean {
    return this.isTransportBulkSkipsContainer
  }

  public get isMaterialContainerNumberAllowed(): boolean {
    return this.isTransportBulkSkipsContainer || this.isTransportBulkIsoTank
  }

  public get isMaterialContainerTransportTypeAllowed(): boolean {
    return this.isTransportBulkSkipsContainer || this.isTransportBulkIsoTank
  }

  public get isMaterialContainerTypeAllowed(): boolean {
    return this.isTransportBulkSkipsContainer
  }

  public get isMaterialPackagingTypeAllowed(): boolean {
    return this.isTransportPackagedCurtainSiderTruck
  }

  public get isMaterialQuantityContainersAllowed(): boolean {
    return this.isTransportBulkSkipsContainer
  }

  public get isMaterialQuantityLabelsAllowed(): boolean {
    return this.isTransportPackagedCurtainSiderTruck
  }

  public get isMaterialQuantityPackagesAllowed(): boolean {
    return this.isTransportPackagedCurtainSiderTruck
  }

  public get isMaterialQuantityPalletsAllowed(): boolean {
    return this.isTransportPackagedCurtainSiderTruck
  }

  public get isMaterialTankerTypeAllowed(): boolean {
    return this.isTransportBulkVacuumOrRoadTankers
  }

  public isMaterialTfsNumberAllowed(material: PickupRequestWasteMaterialForm): boolean {
    return material.tfs === true
  }

  public get isPackagingRemarkAllowed(): boolean {
    return this.isTransportPackagedCurtainSiderTruck
  }

  public get isPackagingRequestMaterialsAllowed(): boolean {
    return this.isTransportPackagedCurtainSiderTruck
  }

  public get isReconciliationNumberAllowed(): boolean {
    return this.isCustomerGerman
  }

  public get isReturnPackagingAllowed(): boolean {
    return this.isTransportPackagedCurtainSiderTruck
  }

  public isSerialNumberAllowed(material: PickupRequestWasteMaterialForm): boolean {
    return material.tfs === true
  }

  public get isTotalQuantityPalletsAllowed(): boolean {
    return this.isTransportPackagedCurtainSiderTruck
  }
}
