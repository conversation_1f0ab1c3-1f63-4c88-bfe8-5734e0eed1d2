import {
  PickUpRequestStatus,
  PickUpTransportMode,
} from '@/client'
import { AddressDtoBuilder } from '@/models/address/addressDto.builder.ts'
import { CalendarDateTimeTransformer } from '@/models/date/calendarDateTime.transformer.ts'
import type { PickupRequestDetailDto } from '@/models/pickup-request/detail/pickupRequestDetailDto.model'
import { UuidUtil } from '@/utils/uuid.util.ts'

const EMPTY_PICKUP_REQUEST_DETAIL: PickupRequestDetailDto = {
  uuid: UuidUtil.getRandom(),
  createdAt: CalendarDateTimeTransformer.toDto(new Date()),
  endDate: CalendarDateTimeTransformer.toDto(new Date()),
  startDate: CalendarDateTimeTransformer.toDto(new Date()),
  updatedAt: CalendarDateTimeTransformer.toDto(new Date()),
  isReturnPackaging: false,
  isTransportByIndaver: null,
  isWicConfirmed: false,
  additionalFiles: [],
  customer: null,
  materials: [],
  needsWicConfirmation: false,
  packagingRemark: null,
  packagingRequestMaterials: [],
  pickUpAddresses: [],
  remarks: null,
  sendCopyToContacts: [],
  status: PickUpRequestStatus.DRAFT,
  totalQuantityPallets: null,
  transportMode: null,
  wasteProducer: null,
}

const FILLED_PICKUP_REQUEST_DETAIL: PickupRequestDetailDto = {
  uuid: UuidUtil.getRandom(),
  createdAt: CalendarDateTimeTransformer.toDto(new Date()),
  endDate: CalendarDateTimeTransformer.toDto(new Date()),
  startDate: CalendarDateTimeTransformer.toDto(new Date()),
  updatedAt: CalendarDateTimeTransformer.toDto(new Date()),
  isReturnPackaging: true,
  isTransportByIndaver: true,
  isWicConfirmed: false,
  additionalFiles: [],
  customer: {
    id: 'test',
    name: 'Customer name',
    address: new AddressDtoBuilder().build(),
  },
  materials: [],
  needsWicConfirmation: false,
  packagingRemark: 'Packaging remark',
  packagingRequestMaterials: [],
  pickUpAddresses: [],
  remarks: 'Remarks',
  sendCopyToContacts: [],
  status: PickUpRequestStatus.DRAFT,
  totalQuantityPallets: 0,
  transportMode: PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK,
  wasteProducer: {
    id: 'test',
    name: 'test',
    address: new AddressDtoBuilder().build(),
  },
}

export class PickupRequestDetailDtoBuilder {
  private value: PickupRequestDetailDto = EMPTY_PICKUP_REQUEST_DETAIL
  constructor() {
  }

  build(): PickupRequestDetailDto {
    return this.value
  }

  withData(): PickupRequestDetailDtoBuilder {
    this.value = FILLED_PICKUP_REQUEST_DETAIL

    return this
  }
}
