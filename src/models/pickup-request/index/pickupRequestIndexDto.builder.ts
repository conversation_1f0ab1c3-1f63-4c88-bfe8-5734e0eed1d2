import type { TransportMode } from '@/client'
import { PickUpRequestStatus } from '@/client'
import { CalendarDateTransformer } from '@/models/date/calendarDate.transformer'
import type { PickupRequestIndexDto } from '@/models/pickup-request/index/pickupRequestIndexDto.model'
import { UuidUtil } from '@/utils/uuid.util'

export class PickupRequestIndexDtoBuilder {
  private value: PickupRequestIndexDto = {
    uuid: UuidUtil.getRandom(),
    customerId: '********',
    pickUpAddressId: [
      '********',
    ],
    wasteProducerId: '********',
    confirmedTransportDate: null,
    createdAt: null,
    requestedEndDate: CalendarDateTransformer.toDto(new Date()),
    requestedStartDate: CalendarDateTransformer.toDto(new Date()),
    isHazardous: [
      false,
    ],
    isTransportByIndaver: null,
    accountManager: null,
    containerNumber: [],
    contractItem: [],
    contractNumber: [],
    costCenter: [],
    customerName: 'Customer name',
    customerReference: null,
    dateOfRequest: CalendarDateTransformer.toDto(new Date()),
    deliveryInfo: null,
    disposalCertificateNumber: null,
    ewc: null,
    materialAnalysis: null,
    nameInstallation: null,
    nameOfApplicant: 'Applicant Name',
    orderNumber: null,
    pickUpAddressName: [
      'Pick-up Address',
    ],
    requestNumber: '********',
    salesOrder: null,
    status: PickUpRequestStatus.DRAFT,
    tfsNumber: null,
    transportMode: null as TransportMode | null,
    treatmentCenterName: null,
    wasteMaterial: [],
    wasteProducerName: 'Waste Producer name',
  }

  constructor() {}

  build(): PickupRequestIndexDto {
    return this.value
  }

  withAccountManager(name: string | null): PickupRequestIndexDtoBuilder {
    this.value.accountManager = name

    return this
  }

  withConfirmedTransportDate(date: Date | null): PickupRequestIndexDtoBuilder {
    this.value.confirmedTransportDate = date ? CalendarDateTransformer.toDto(date) : null

    return this
  }

  withContainerNumber(values: string[]): PickupRequestIndexDtoBuilder {
    this.value.containerNumber = values

    return this
  }

  withContractItem(values: string[]): PickupRequestIndexDtoBuilder {
    this.value.contractItem = values

    return this
  }

  withContractNumber(values: string[]): PickupRequestIndexDtoBuilder {
    this.value.contractNumber = values

    return this
  }

  withCostCenter(values: string[]): PickupRequestIndexDtoBuilder {
    this.value.costCenter = values

    return this
  }

  withCustomerName(name: string): PickupRequestIndexDtoBuilder {
    this.value.customerName = name

    return this
  }

  withDateOfRequest(date: Date): PickupRequestIndexDtoBuilder {
    this.value.dateOfRequest = CalendarDateTransformer.toDto(date)

    return this
  }

  withDisposalCertificateNumber(value: string | null): PickupRequestIndexDtoBuilder {
    this.value.disposalCertificateNumber = value

    return this
  }

  withEwc(value: string | null): PickupRequestIndexDtoBuilder {
    this.value.ewc = value

    return this
  }

  withIsTransportByIndaver(value: boolean | null): PickupRequestIndexDtoBuilder {
    this.value.isTransportByIndaver = value

    return this
  }

  withMaterialAnalysis(value: string | null): PickupRequestIndexDtoBuilder {
    this.value.materialAnalysis = value

    return this
  }

  withNameInstallation(value: string | null): PickupRequestIndexDtoBuilder {
    this.value.nameInstallation = value

    return this
  }

  withNameOfApplicant(name: string | null): PickupRequestIndexDtoBuilder {
    this.value.nameOfApplicant = name

    return this
  }

  withOrderNumber(value: string | null): PickupRequestIndexDtoBuilder {
    this.value.orderNumber = value

    return this
  }

  withPickUpAddressName(name: string[]): PickupRequestIndexDtoBuilder {
    this.value.pickUpAddressName = name

    return this
  }

  withRequestedEndDate(date: Date | null): PickupRequestIndexDtoBuilder {
    this.value.requestedEndDate = date ? CalendarDateTransformer.toDto(date) : null

    return this
  }

  withRequestedStartDate(date: Date | null): PickupRequestIndexDtoBuilder {
    this.value.requestedStartDate = date ? CalendarDateTransformer.toDto(date) : null

    return this
  }

  withRequestNumber(requestNumber: string): PickupRequestIndexDtoBuilder {
    this.value.requestNumber = requestNumber

    return this
  }

  withTfsNumber(value: string | null): PickupRequestIndexDtoBuilder {
    this.value.tfsNumber = value

    return this
  }

  withTransportMode(mode: TransportMode | null): PickupRequestIndexDtoBuilder {
    this.value.transportMode = mode

    return this
  }

  withTreatmentCenterName(value: string | null): PickupRequestIndexDtoBuilder {
    this.value.treatmentCenterName = value

    return this
  }

  withWasteMaterial(values: string[]): PickupRequestIndexDtoBuilder {
    this.value.wasteMaterial = values

    return this
  }

  withWasteProducerName(name: string): PickupRequestIndexDtoBuilder {
    this.value.wasteProducerName = name

    return this
  }
}
