import type {
  PickUpRequestStatus,
  PickUpTransportMode,
} from '@/client'
import type { CustomBooleanFilterValue } from '@/composables/custom-boolean-filter/customBooleanFilter.composable'
import type { CalendarDateRange } from '@/models/date/calendarDateRange.model'
import type {
  WithFilterQuery,
  WithSortQuery,
} from '@/types/query.type'

export interface PickupRequestIndexQueryParams extends WithFilterQuery<{ customerId?: {
  id: string
  name: string
}
pickUpAddressId?: string
wasteProducerId?: {
  id: string
  name: string
}
confirmedTransportDate?: CalendarDateRange
requestDate?: CalendarDateRange
isHazardous?: CustomBooleanFilterValue
isTransportByIndaver?: CustomBooleanFilterValue
accountManager?: string
containerNumber?: string
contractItem?: string
contractNumber?: string
costCenter?: string
customerReference?: string
dateOfRequest?: CalendarDateRange
deliveryInfo?: string
disposalCertificateNumber?: string
ewc?: {
  label: string
  value: string
}
materialAnalysis?: string
nameInstallation?: string
nameOfApplicant?: string
orderNumber?: string
requestNumber?: string
salesOrder?: string
statuses: PickUpRequestStatus[]
transportMode?: PickUpTransportMode
treatmentCenterName?: string
wasteMaterial?: string }>, WithSortQuery<
  'applicationDate' |
  'confirmedTransportDate' |
  'containerNumber' |
  'contractItem' |
  'contractNumber' |
  'customerReference' |
  'disposalCertificateNumber' |
  'nameInstallation' |
  'orderNumber' |
  'pickUpAddressId' |
  'requestedStartDate' |
  'requestNumber' |
  'salesOrder' |
  'treatmentCenterName' |
  'wasteMaterial' |
  'wasteProducerId'
  > {}
