import type {
  PickUpRequestStatus,
  TransportMode,
} from '@/client'
import type { PickupRequestUuid } from '@/models/pickup-request/pickupRequestUuid.model'

export interface PickupRequestIndex {
  uuid: PickupRequestUuid | null
  customerId: string | null
  pickupAddressId: string[] | null
  wasteProducerId: string | null
  confirmedTransportDate: Date | null
  requestedEndDate: Date | null
  requestedStartDate: Date | null
  isHazardous: boolean
  isTransportByIndaver: boolean | null
  accountManager: string | null
  containerNumber: string[]
  contractItem: string[]
  contractNumber: string[]
  costCenter: string[]
  customerName: string | null
  customerReference: string | null
  dateOfRequest: Date | null
  deliveryInfo: string | null
  disposalCertificateNumber: string | null
  ewc: string | null
  materialAnalysis: string | null
  nameInstallation: string | null
  nameOfApplicant: string | null
  orderNumber: string | null
  pickupAddressName: string[] | null
  requestNumber: string | null
  salesOrder: string | null
  status: PickUpRequestStatus
  tfsNumber: string | null
  transportMode: TransportMode | null
  treatmentCenterName: string | null
  wasteMaterial: string[]
  wasteProducerName: string | null
}

export enum PickupRequestIndexTableTabs {
  AWAITING_BOOKING = 'awaiting_booking',
  BOOKED = 'booked',
  CANCELLED = 'cancelled',
  DRAFTS = 'drafts',
  INDASCAN_DRAFTS = 'indascan_drafts',
  SUBMITTED = 'submitted',
}
