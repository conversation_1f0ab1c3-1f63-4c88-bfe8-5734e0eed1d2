import type { ViewPickUpRequestIndexV1Data } from '@/client'
import {
  PickUpRequestStatus,
  ViewPickUpRequestIndexSortKey,
} from '@/client'
import { CalendarDateRangeTransformer } from '@/models/date/calendarDate.transformer'
import type { PickupRequestIndexQueryParams } from '@/models/pickup-request/index/pickupRequestIndexQueryParams.model'
import type { OffsetPagination } from '@/types/pagination.type'
import { CustomBooleanFilterUtil } from '@/utils/customBooleanFilter.util'
import { SortUtil } from '@/utils/sort.util.ts'

export class PickupRequestIndexQueryParamsTransformer {
  static toDto(options: OffsetPagination<PickupRequestIndexQueryParams>): ViewPickUpRequestIndexV1Data['query'] {
    return {
      filter: {
        customerId: options.filters.customerId?.id || undefined,
        pickUpAddressId: options.filters.pickUpAddressId || undefined,
        wasteProducerId: options.filters.wasteProducerId?.id || undefined,
        confirmedTransportDate: CalendarDateRangeTransformer.toDto(options.filters.confirmedTransportDate ?? null)
          || undefined,
        requestDate: CalendarDateRangeTransformer.toDto(options.filters.requestDate ?? null) || undefined,
        isHazardous: CustomBooleanFilterUtil.toDto(options.filters.isHazardous),
        isTransportByIndaver: CustomBooleanFilterUtil.toDto(options.filters.isTransportByIndaver),
        accountManager: options.filters.accountManager || undefined,
        containerNumber: options.filters.containerNumber || undefined,
        contractItem: options.filters.contractItem || undefined,
        contractNumber: options.filters.contractNumber || undefined,
        costCenter: options.filters.costCenter || undefined,
        customerReference: options.filters.customerReference || undefined,
        dateOfRequest: CalendarDateRangeTransformer.toDto(options.filters.dateOfRequest ?? null) || undefined,
        deliveryInfo: options.filters.deliveryInfo || undefined,
        disposalCertificateNumber: options.filters.disposalCertificateNumber || undefined,
        ewc: options.filters.ewc?.value || undefined,
        materialAnalysis: options.filters.materialAnalysis || undefined,
        nameInstallation: options.filters.nameInstallation || undefined,
        nameOfApplicant: options.filters.nameOfApplicant || undefined,
        orderNumber: options.filters.orderNumber || undefined,
        requestNumber: options.filters.requestNumber || undefined,
        salesOrder: options.filters.salesOrder || undefined,
        statuses: options.filters.statuses.length === 0
          ? [
              PickUpRequestStatus.CANCELLED,
              PickUpRequestStatus.CONFIRMED,
              PickUpRequestStatus.PENDING,
            ]
          : options.filters.statuses,
        transportMode: options.filters.transportMode || undefined,
        treatmentCenterName: options.filters.treatmentCenterName || undefined,
        wasteMaterial: options.filters.wasteMaterial || undefined,
      },
      pagination: options.pagination,
      sort: SortUtil.toDto(options.sort, {
        pickUpAddressId: ViewPickUpRequestIndexSortKey.PICK_UP_ADDRESS_ID,
        wasteProducerId: ViewPickUpRequestIndexSortKey.WASTE_PRODUCER_ID,
        applicationDate: ViewPickUpRequestIndexSortKey.APPLICATION_DATE,
        confirmedTransportDate: ViewPickUpRequestIndexSortKey.CONFIRMED_TRANSPORT_DATE,
        requestedStartDate: ViewPickUpRequestIndexSortKey.REQUESTED_START_DATE,
        containerNumber: ViewPickUpRequestIndexSortKey.CONTAINER_NUMBER,
        contractItem: ViewPickUpRequestIndexSortKey.CONTRACT_ITEM,
        contractNumber: ViewPickUpRequestIndexSortKey.CONTRACT_NUMBER,
        customerReference: ViewPickUpRequestIndexSortKey.CUSTOMER_REFERENCE,
        disposalCertificateNumber: ViewPickUpRequestIndexSortKey.DISPOSAL_CERTIFICATE_NUMBER,
        nameInstallation: ViewPickUpRequestIndexSortKey.INSTALLATION_NAME,
        orderNumber: ViewPickUpRequestIndexSortKey.ORDER_NUMBER,
        requestNumber: ViewPickUpRequestIndexSortKey.REQUEST_NUMBER,
        salesOrder: ViewPickUpRequestIndexSortKey.SALES_ORDER,
        treatmentCenterName: ViewPickUpRequestIndexSortKey.TREATMENT_CENTER_NAME,
        wasteMaterial: ViewPickUpRequestIndexSortKey.WASTE_MATERIAL,
      }),
    }
  }
}
