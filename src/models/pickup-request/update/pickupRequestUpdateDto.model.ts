import type { UpdatePickUpRequestCommand } from '@/client'

export type PickupRequestUpdateDto = UpdatePickUpRequestCommand

export type PickupRequestCustomerAndLocationStepUpdateDto = Pick<PickupRequestUpdateDto,
  'customerId'
  | 'customerName'
  | 'pickUpAddressIds'
  | 'pickUpAddressNames'
  | 'wasteProducerId'
  | 'wasteProducerName'
>

export type PickupRequestWasteStepUpdateDto = Pick<PickupRequestUpdateDto,
  'isTransportByIndaver'
  | 'materials'
  | 'transportMode'
>

export type PickupRequestPackagingStepUpdateDto = Pick<PickupRequestUpdateDto,
  'isReturnPackaging'
  | 'packagingRemark'
  | 'totalQuantityPallets'
>

export type PickupRequestPackagingRequestStepUpdateDto = Pick<PickupRequestUpdateDto,
  'packagingRequestMaterials'
>

export type PickupRequestPlanningStepUpdateDto = Pick<PickupRequestUpdateDto,
  'additionalFiles'
  | 'endDate'
  | 'isWicConfirmed'
  | 'remarks'
  | 'startDate'
>
export type PickupRequestStartDateUpdateDto = Pick<PickupRequestUpdateDto,
  'startDate'
  | 'startTime'
>
