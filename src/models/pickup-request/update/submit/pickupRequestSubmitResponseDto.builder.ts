import { CalendarDateTimeTransformer } from '@/models/date/calendarDateTime.transformer'
import type { PickupRequestSubmitResponseDto } from '@/models/pickup-request/update/submit/pickupRequestSubmitResponseDto.model'
import { UuidUtil } from '@/utils/uuid.util'

export class PickupRequestSubmitResponseDtoBuilder {
  private value: PickupRequestSubmitResponseDto = {
    uuid: UuidUtil.getRandom(),
    createdAt: CalendarDateTimeTransformer.toDto(new Date()),
    submittedOn: CalendarDateTimeTransformer.toDto(new Date()),
    updatedAt: CalendarDateTimeTransformer.toDto(new Date()),
    requestNumber: '12345',
  }

  constructor() {}

  build(): PickupRequestSubmitResponseDto {
    return this.value
  }

  withUuid(uuid: string): PickupRequestSubmitResponseDtoBuilder {
    this.value.uuid = uuid

    return this
  }
}
