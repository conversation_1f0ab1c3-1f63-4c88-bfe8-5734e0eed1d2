import { z } from 'zod'

import { PickUpTransportMode } from '@/client'
import { CustomerCountryCode } from '@/models/enums/customerCountryCode.enum'
import { pickupRequestPackagingWasteFormSchema } from '@/models/pickup-request/update/steps/pickupRequestWasteForm.model'
import { wizardFormBaseStepSchema } from '@/models/wizard-form/wizardFormBaseStep.model'
import { CustomerUtil } from '@/utils/customer.util'
import { ZodUtil } from '@/utils/zod'

// NOTE: Min/Max values are validation constraints from SAP! Do not change without checking with SAP first.

export const pickupRequestPackagingFormSchema = wizardFormBaseStepSchema.and(
  z.object({
    isReturnPackaging: z.boolean(),
    customerCountryCode: z.nativeEnum(CustomerCountryCode).nullable(),
    materials: pickupRequestPackagingWasteFormSchema.array(),
    packagingRemark: z.string().max(200).nullable(),
    totalQuantityPallets: z.number().min(0).max(999).nullable(),
    transportMode: z.nativeEnum(PickUpTransportMode).nullable(),
  }),
).superRefine((data, ctx) => {
  if (!data.isFinalized) {
    return
  }

  /**
   * COUNTRY SPECIFICS
   * - quantityPallets is required for DE, optional for others.
   * - quantityLabels is optional for DE, required for others.
   *
   * - containerType is required for DE,
   *    only when "Bulk waste in skips/containers" is selected.
   *
   * - containerVolumeSize is required for DE and IE, only when any bulk transport is selected.
   */

  if (data.transportMode === PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK) {
    if (data.customerCountryCode !== null && CustomerUtil.isGerman(data.customerCountryCode)) {
      for (const [
        index,
        material,
      ] of data.materials.entries()) {
        ZodUtil.validateField(
          `materials.${index}.quantityPallets`,
          z.number().min(1).max(999),
          material.quantityPallets,
          ctx,
        )
      }
    }
    else {
      for (const [
        index,
        material,
      ] of data.materials.entries()) {
        ZodUtil.validateField(
          `materials.${index}.quantityLabels`,
          z.number().min(0).max(999),
          material.quantityLabels,
          ctx,
        )
      }
    }

    const totalQuantityPalletsCalculated = data.materials.reduce((sum, material) => {
      return sum + (material.quantityPallets || 0)
    }, 0)

    ZodUtil.validateField(
      `totalQuantityPallets`,
      z.number().min(totalQuantityPalletsCalculated),
      data.totalQuantityPallets,
      ctx,
    )

    if (data.isReturnPackaging === true) {
      ZodUtil.validateString(
        `packagingRemark`,
        data.packagingRemark,
        ctx,
      )
    }

    for (const [
      index,
      material,
    ] of data.materials.entries()) {
      ZodUtil.validateField(
        `materials.${index}.packagingType`,
        z.object({ name: z.string() }),
        material.packagingType,
        ctx,
      )
      ZodUtil.validateNumber(
        `materials.${index}.quantityPackages`,
        material.quantityPackages,
        ctx,
      )
    }
  }

  if (data.transportMode === PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS) {
    for (const [
      index,
      material,
    ] of data.materials.entries()) {
      ZodUtil.validateField(
        `materials.${index}.tankerType`,
        z.string(),
        material.tankerType,
        ctx,
      )

      if (
        data.customerCountryCode !== null
        && (CustomerUtil.isGerman(data.customerCountryCode) || CustomerUtil.isIrish(data.customerCountryCode))
      ) {
        ZodUtil.validateString(
          `materials.${index}.containerVolumeSize`,
          material.containerVolumeSize,
          ctx,
        )
      }
    }
  }

  if (data.transportMode === PickUpTransportMode.BULK_SKIPS_CONTAINER) {
    if (data.customerCountryCode !== null && CustomerUtil.isGerman(data.customerCountryCode)) {
      for (const [
        index,
        material,
      ] of data.materials.entries()) {
        ZodUtil.validateField(
          `materials.${index}.containerType`,
          z.object({}),
          material.containerType,
          ctx,
        )
      }
    }
  }

  if (data.transportMode === PickUpTransportMode.BULK_ISO_TANK
    || data.transportMode === PickUpTransportMode.BULK_SKIPS_CONTAINER) {
    if (
      data.customerCountryCode !== null
      && (CustomerUtil.isGerman(data.customerCountryCode) || CustomerUtil.isIrish(data.customerCountryCode))
    ) {
      for (const [
        index,
        material,
      ] of data.materials.entries()) {
        ZodUtil.validateString(
          `materials.${index}.containerVolumeSize`,
          material.containerVolumeSize,
          ctx,
        )
      }
    }
  }
})

export type PickupRequestPackagingForm = z.infer<typeof pickupRequestPackagingFormSchema>
