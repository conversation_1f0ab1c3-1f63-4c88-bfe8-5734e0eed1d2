import { z } from 'zod'

import { calendarDateSchema } from '@/models/date/calendarDate.model'
import { calendarTimeSchema } from '@/models/date/calendarTime.model'
import { s3FileSchema } from '@/models/s3-file/s3File.model'
import { wizardFormBaseStepSchema } from '@/models/wizard-form/wizardFormBaseStep.model'
import { i18nPlugin } from '@/plugins/i18n.plugin'
import { DateUtil } from '@/utils/date.util'
import { ZodUtil } from '@/utils/zod'

export const pickupRequestPlanningFormSchema = wizardFormBaseStepSchema.and(
  z.object({
    isWicConfirmationRequired: z.boolean(),
    isWicConfirmed: z.boolean(),
    additionalFiles: s3FileSchema.array(),
    range: z.object({
      from: calendarDateSchema.nullable(),
      until: calendarDateSchema.nullable(),
    }),
    remarks: z.string().nullable(),
  }),
).superRefine((data, ctx) => {
  if (!data.isFinalized) {
    return
  }

  ZodUtil.validateField(
    'range.from',
    calendarDateSchema,
    data.range.from,
    ctx,
  )

  if (data.isWicConfirmationRequired) {
    ZodUtil.validateField(
      'isWicConfirmed',
      z.literal(true),
      data.isWicConfirmed,
      ctx,
      i18nPlugin.global.t('validation.required'),
    )
  }

  if (data.range.from && !DateUtil.isAfter(data.range.from, new Date())) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: i18nPlugin.global.t('module.pickup_request.update.planning.date_in_future'),
      path: [
        'range',
        'from',
      ],
    })
  }
})

export const pickupRequestWeeklyPlanningFormSchema = wizardFormBaseStepSchema.and(
  z.object({
    startDate: calendarDateSchema.nullable(),
    startTime: calendarTimeSchema.nullable(),
  }),
).superRefine((data, ctx) => {
  if (!data.isFinalized) {
    return
  }

  ZodUtil.validateField(
    'startDate',
    calendarDateSchema,
    data.startDate,
    ctx,
  )
})

export type PickupRequestPlanningForm = z.infer<typeof pickupRequestPlanningFormSchema>
export type PickupRequestWeeklyPlanningForm = z.infer<typeof pickupRequestWeeklyPlanningFormSchema>
