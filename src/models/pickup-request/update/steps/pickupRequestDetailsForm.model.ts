import { z } from 'zod'

import {
  PickUpTransportMode,
  WasteMeasurementUnit,
} from '@/client'
import { CustomerCountryCode } from '@/models/enums/customerCountryCode.enum'
import { pickupRequestPackagingWasteFormSchema } from '@/models/pickup-request/update/steps/pickupRequestWasteForm.model'
import { wizardFormBaseStepSchema } from '@/models/wizard-form/wizardFormBaseStep.model'
import { i18nPlugin } from '@/plugins/i18n.plugin.ts'
import { CustomerUtil } from '@/utils/customer.util'
import { ZodUtil } from '@/utils/zod'

// NOTE: Min/Max values are validation constraints from SAP! Do not change without checking with SAP first.

export const pickupRequestDetailsFormSchema = wizardFormBaseStepSchema.and(
  z.object({
    isPackagingAdded: z.boolean(),
    isReturnPackaging: z.boolean(),
    isTransportByIndaver: z.boolean(),
    customerCountryCode: z.nativeEnum(CustomerCountryCode).nullable(),
    materials: pickupRequestPackagingWasteFormSchema.array(),
    packagingRemark: z.string().nullable(),
    totalQuantityPallets: z.number().nullable(),
    transportMode: z.nativeEnum(PickUpTransportMode).nullable(),
  }),
).superRefine((data, ctx) => {
  if (!data.isFinalized) {
    return
  }

  ZodUtil.validateField(
    'transportMode',
    z.nativeEnum(PickUpTransportMode),
    data.transportMode,
    ctx,
  )

  ZodUtil.validateField(
    'materials',
    z.array(z.any()).min(1),
    data.materials,
    ctx,
    i18nPlugin.global.t('module.pickup_request.update.waste.contract_line.too_little_selected'),
  )

  if (data.transportMode === PickUpTransportMode.PACKAGED_CURTAIN_SIDER_TRUCK) {
    const totalQuantityPalletsCalculated = data.materials.reduce((sum, material) => {
      return sum + (material.quantityPallets || 0)
    }, 0)

    ZodUtil.validateField(
      `totalQuantityPallets`,
      z.number().min(totalQuantityPalletsCalculated),
      data.totalQuantityPallets,
      ctx,
    )

    if (data.isReturnPackaging === true) {
      ZodUtil.validateString(
        `packagingRemark`,
        data.packagingRemark,
        ctx,
      )
    }

    for (const [
      index,
      material,
    ] of data.materials.entries()) {
      ZodUtil.validateField(
        `materials.${index}.packagingType`,
        z.object({ name: z.string() }),
        material.packagingType,
        ctx,
      )
      ZodUtil.validateNumber(
        `materials.${index}.quantityPackages`,
        material.quantityPackages,
        ctx,
      )

      if (data.customerCountryCode !== null && CustomerUtil.isGerman(data.customerCountryCode)) {
        ZodUtil.validateField(
          `materials.${index}.quantityPallets`,
          z.number().min(1).max(999),
          material.quantityPallets,
          ctx,
        )
      }
      else {
        ZodUtil.validateField(
          `materials.${index}.quantityLabels`,
          z.number().min(0).max(999),
          material.quantityLabels,
          ctx,
        )
      }
    }
  }

  if (data.transportMode === PickUpTransportMode.BULK_ISO_TANK) {
    ZodUtil.validateField(
      'materials',
      z.array(
        z.object({ amount: z.number().min(1).max(1) }),
      ).length(1),
      data.materials,
      ctx,
      i18nPlugin.global.t('module.pickup_request.update.waste.contract_line.too_many_selected'),
    )

    for (const [
      index,
      material,
    ] of data.materials.entries()) {
      ZodUtil.validateField(
        `materials.${index}.containerTransportType`,
        z.string(),
        material.containerTransportType,
        ctx,
      )
      if (
        data.customerCountryCode !== null
        && (CustomerUtil.isGerman(data.customerCountryCode) || CustomerUtil.isIrish(data.customerCountryCode))
      ) {
        ZodUtil.validateString(
          `materials.${index}.containerVolumeSize`,
          material.containerVolumeSize,
          ctx,
        )
      }
    }
  }

  if (data.transportMode === PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS) {
    ZodUtil.validateField(
      'materials',
      z.array(
        z.object({ amount: z.number().min(1).max(1) }),
      ).length(1),
      data.materials,
      ctx,
      i18nPlugin.global.t('module.pickup_request.update.waste.contract_line.too_many_selected'),
    )

    for (const [
      index,
      material,
    ] of data.materials.entries()) {
      ZodUtil.validateField(
        `materials.${index}.tankerType`,
        z.string(),
        material.tankerType,
        ctx,
      )

      if (
        data.customerCountryCode !== null
        && (CustomerUtil.isGerman(data.customerCountryCode) || CustomerUtil.isIrish(data.customerCountryCode))
      ) {
        ZodUtil.validateString(
          `materials.${index}.containerVolumeSize`,
          material.containerVolumeSize,
          ctx,
        )
      }
    }
  }

  if (data.transportMode === PickUpTransportMode.BULK_SKIPS_CONTAINER) {
    if (data.materials.length > 3) {
      ctx.addIssue({
        code: 'custom',
        message: i18nPlugin.global.t('module.pickup_request.update.waste.contract_line.too_many_selected'),
        path: [
          'materials',
        ],
      })
    }
    for (const [
      index,
      material,
    ] of data.materials.entries()) {
      ZodUtil.validateField(
        `materials.${index}.containerTransportType`,
        z.string(),
        material.containerTransportType,
        ctx,
      )

      if (data.customerCountryCode !== null && CustomerUtil.isGerman(data.customerCountryCode)) {
        ZodUtil.validateField(
          `materials.${index}.containerType`,
          z.object({}),
          material.containerType,
          ctx,
        )
      }
      if (
        data.customerCountryCode !== null
        && (CustomerUtil.isGerman(data.customerCountryCode) || CustomerUtil.isIrish(data.customerCountryCode))
      ) {
        ZodUtil.validateString(
          `materials.${index}.containerVolumeSize`,
          material.containerVolumeSize,
          ctx,
        )
      }
    }
  }

  for (const [
    index,
    material,
  ] of data.materials.entries()) {
    ZodUtil.validateNumber(
      `materials.${index}.estimatedWeightOrVolumeValue`,
      material.estimatedWeightOrVolumeValue,
      ctx,
    )
    ZodUtil.validateField(
      `materials.${index}.estimatedWeightOrVolumeUnit`,
      z.nativeEnum(WasteMeasurementUnit),
      material.estimatedWeightOrVolumeUnit,
      ctx,
    )

    ZodUtil.validateField(
      `materials.${index}.unNumber.number`,
      z.string().min(1),
      material.unNumber?.number,
      ctx,
    )

    if (material.unNumber?.isHazardous === true
      && data.customerCountryCode !== null
      && CustomerUtil.isGerman(data.customerCountryCode)
    ) {
      ZodUtil.validateString(
        `materials.${index}.hazardInducers`,
        material.hazardInducers,
        ctx,
      )
    }

    if (material.isCostCenterRequired === true) {
      ZodUtil.validateString(
        `materials.${index}.costCenter`,
        material.costCenter,
        ctx,
      )
    }

    if (material.isPoNumberRequired === true) {
      ZodUtil.validateString(
        `materials.${index}.poNumber`,
        material.poNumber,
        ctx,
      )
    }
  }
})

export type PickupRequestDetailsForm = z.infer<typeof pickupRequestDetailsFormSchema>
