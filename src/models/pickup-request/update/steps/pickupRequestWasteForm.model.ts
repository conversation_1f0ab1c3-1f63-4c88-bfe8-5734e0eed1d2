import { z } from 'zod'

import {
  ContractLinePackagingType,
  PickUpTransportMode,
  WasteMeasurementUnit,
} from '@/client'
import { CustomerCountryCode } from '@/models/enums/customerCountryCode.enum'
import { wizardFormBaseStepSchema } from '@/models/wizard-form/wizardFormBaseStep.model'
import { i18nPlugin } from '@/plugins/i18n.plugin.ts'
import { ZodUtil } from '@/utils/zod'

// NOTE: Min/Max values are validation constraints from SAP! Do not change without checking with SAP first.

export const pickupRequestPackagingWasteFormSchema = z.object({
  contractLineId: z.string(),
  customerId: z.string().nullable(),
  endTreatmentCenterId: z.string().nullable(),
  pickupAddressId: z.string().nullable(),
  wasteProducerId: z.string().nullable(),
  isContainerCovered: z.boolean().nullable(),
  isCostCenterRequired: z.boolean(),
  isHazardous: z.boolean().nullable(),
  isPoNumberRequired: z.boolean(),
  amount: z.number().nullable(),
  asn: z.string().max(35).nullable(),
  containerNumber: z.string().max(40).nullable(),
  containerTransportType: z.string().nullable(),
  containerType: z.object({ name: z.string() }).nullable(),
  containerVolumeSize: z.string().nullable(),
  contractItem: z.string(),
  contractNumber: z.string(),
  costCenter: z.string().max(25).nullable(),
  customerName: z.string().nullable(),
  customerReference: z.string().nullable(),
  deliveryInfo: z.string().nullable(),
  endTreatmentCenterName: z.string().nullable(),
  esnNumber: z.string().nullable(),
  estimatedWeightOrVolumeUnit: z.nativeEnum(WasteMeasurementUnit).nullable(),
  estimatedWeightOrVolumeValue: z.number().min(0.01).max(99_999_999_999.99).nullable(),
  ewcCode: z.string().nullable(),
  hazardInducers: z.string().max(100).nullable(),
  installationName: z.string().nullable(),
  materialAnalysis: z.string().nullable(),
  materialNumber: z.string().nullable(),
  packaged: z.nativeEnum(ContractLinePackagingType).nullable(),
  packagingType: z.object({ name: z.string() }).nullable(),
  pickupAddressName: z.string().nullable(),
  poNumber: z.string().max(35).nullable(),
  position: z.string().nullable(),
  processCode: z.string().nullable(),
  quantityContainers: z.number().min(0).max(9).nullable(),
  quantityLabels: z.number().min(0).max(999).nullable(),
  quantityPackages: z.number().min(0).max(999).nullable(),
  quantityPallets: z.number().min(0).max(999).nullable(),
  reconciliationNumber: z.string().max(6).nullable(),
  remarks: z.string().nullable(),
  serialNumber: z.string().max(4).default('0000').nullable(),
  tankerType: z.string().nullable(),
  tcNumber: z.string().nullable(),
  tfs: z.boolean().nullable(),
  tfsNumber: z.string().max(15).nullable(),
  treatmentCenterName: z.string().nullable(),
  unNumber: z.object({
    isHazardous: z.boolean().nullable(),
    dangerLabel1: z.string().nullable(),
    dangerLabel2: z.string().nullable(),
    dangerLabel3: z.string().nullable(),
    description: z.string().nullable(),
    number: z.string().nullable(),
    packingGroup: z.string().nullable(),
  }).nullable(),
  wasteMaterial: z.string().nullable(),
  wasteProducerName: z.string().nullable(),
})

export const pickupRequestWasteFormSchema = wizardFormBaseStepSchema.and(
  z.object({
    isPackagingAdded: z.boolean(),
    isReturnPackaging: z.boolean(),
    isTransportByIndaver: z.boolean(),
    customerCountryCode: z.nativeEnum(CustomerCountryCode).nullable(),
    materials: pickupRequestPackagingWasteFormSchema.array(),
    packagingRemark: z.string().nullable(),
    totalQuantityPallets: z.number().nullable(),
    transportMode: z.nativeEnum(PickUpTransportMode).nullable(),
  }),
).superRefine((data, ctx) => {
  if (!data.isFinalized) {
    return
  }

  ZodUtil.validateField(
    'materials',
    z.array(z.any()).min(1),
    data.materials,
    ctx,
    i18nPlugin.global.t('module.pickup_request.update.waste.contract_line.too_little_selected'),
  )

  if (data.transportMode === PickUpTransportMode.BULK_ISO_TANK
    || data.transportMode === PickUpTransportMode.BULK_VACUUM_TANKERS_ROAD_TANKERS
  ) {
    ZodUtil.validateField(
      'materials',
      z.array(
        z.object({ amount: z.number().min(1).max(1) }),
      ).length(1),
      data.materials,
      ctx,
      i18nPlugin.global.t('module.pickup_request.update.waste.contract_line.too_many_selected'),
    )
  }
  if (data.transportMode === PickUpTransportMode.BULK_SKIPS_CONTAINER) {
    if (data.materials.length > 3) {
      ctx.addIssue({
        code: 'custom',
        message: i18nPlugin.global.t('module.pickup_request.update.waste.contract_line.too_many_selected'),
        path: [
          'materials',
        ],
      })
    }
  }

  ZodUtil.validateField(
    'transportMode',
    z.nativeEnum(PickUpTransportMode),
    data.transportMode,
    ctx,
  )
})

export type PickupRequestWasteForm = z.infer<typeof pickupRequestWasteFormSchema>
export type PickupRequestWasteMaterialForm = z.infer<typeof pickupRequestPackagingWasteFormSchema>
