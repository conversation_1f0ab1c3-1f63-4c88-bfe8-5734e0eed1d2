import { CalendarDateTransformer } from '@/models/date/calendarDate.transformer'
import { PickupRequestValidationUtil } from '@/models/pickup-request/pickupRequestValidation.util'
import type { PickupRequestSapSubmitDto } from '@/models/pickup-request/update/pickupRequestSapSubmitDto.model'
import type { PickupRequestSapUpdateDto } from '@/models/pickup-request/update/pickupRequestSapUpdateDto.model'
import type {
  PickupRequestPackagingStepUpdateDto,
  PickupRequestPlanningStepUpdateDto,
} from '@/models/pickup-request/update/pickupRequestUpdateDto.model'
import type { PickupRequestUpdateForm } from '@/models/pickup-request/update/pickupRequestUpdateForm.model'
import type { PickupRequestSubmitRequestForm } from '@/models/pickup-request/update/submit/pickupRequestSubmitForm.model'
import { S3FileArrayTransformer } from '@/models/s3-file/s3File.transformer'
import { StringUtil } from '@/utils/string.util'

export class PickupRequestSapUpdateTransformer {
  static mapPackagingStep(
    form: Partial<PickupRequestUpdateForm>,
  ): PickupRequestPackagingStepUpdateDto {
    const pickupRequestValidationUtil = new PickupRequestValidationUtil(form)

    let packagingRemark

    if (pickupRequestValidationUtil.isPackagingRemarkAllowed) {
      packagingRemark = form.packagingRemark?.trim() === '' ? null : form.packagingRemark
    }
    else {
      packagingRemark = null
    }

    return {
      isReturnPackaging: pickupRequestValidationUtil.isReturnPackagingAllowed
        ? form.isReturnPackaging
        : null,
      packagingRemark,
    }
  }

  static mapPlanningStep(
    form: Partial<PickupRequestUpdateForm>,
  ): PickupRequestPlanningStepUpdateDto {
    let endDate = null

    if (form.range?.until) {
      endDate = CalendarDateTransformer.toNullableDto(form.range.until)
    }
    else if (form.range?.from) {
      endDate = CalendarDateTransformer.toNullableDto(form.range.from)
    }

    return {
      endDate,
      startDate: CalendarDateTransformer.toNullableDto(form.range?.from ?? null),
      isWicConfirmed: form.isWicConfirmationRequired ? form.isWicConfirmed : null,
      additionalFiles: S3FileArrayTransformer.toArrayDto(form.additionalFiles ?? []),
      remarks: form.remarks,
    }
  }

  static toDto(form: Partial<PickupRequestUpdateForm>): PickupRequestSapUpdateDto {
    return {
      additionalFiles: S3FileArrayTransformer.toArrayDto(form.additionalFiles ?? []),
      materials: form.materials?.map((material) => ({
        costCenter: StringUtil.trimOrNull(material.costCenter),
        poNumber: StringUtil.trimOrNull(material.poNumber),
        position: material.position ?? '',
      })),
      remarks: form.remarks,
      ...this.mapPackagingStep(form),
      ...this.mapPlanningStep(form),

    }
  }
}

export class PickupRequestSapSubmitFormTransformer {
  static toDto(form: PickupRequestSubmitRequestForm): PickupRequestSapSubmitDto {
    return { contacts: form.contacts.filter((contact) => contact.email !== '') }
  }
}
