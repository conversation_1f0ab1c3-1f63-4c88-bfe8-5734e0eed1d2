import type {
  ContractLinePackagingType,
  PickU<PERSON>Re<PERSON>Status,
  PickUpTransportMode,
  WasteMeasurementUnit,
} from '@/client'
import type { CustomerIndex } from '@/models/customer/index/customerIndex.model'
import type { CalendarDate } from '@/models/date/calendarDate.model'
import type { PickUpAddressIndex } from '@/models/pick-up-address/index/pickUpAddressIndex.model'
import type { PickupRequestPackagingRequestMaterial } from '@/models/pickup-request/detail/pickupRequestDetail.model'
import type { S3File } from '@/models/s3-file/s3File.model'
import type { WasteProducerIndex } from '@/models/waste-producer/index/wasteProducerIndex.model'

export interface PickupRequestSapDetail {
  endDate: CalendarDate | null
  startDate: CalendarDate | null
  isReturnPackaging: boolean | null
  isTransportByIndaver: boolean | null
  isWicConfirmationRequired: boolean
  isWicConfirmed: boolean | null
  additionalFiles: S3File[]
  contacts: {
    email: string
    firstName: string
    lastName: string
  }[]
  customer: CustomerIndex | null
  materials: {
    contractLineId: string
    customerId: string | null
    endTreatmentCenterId: string | null
    pickupAddressId: string | null
    wasteProducerId: string | null
    isContainerCovered: boolean | null
    isHazardous: boolean | null
    asn: string | null
    containerNumber: string | null
    containerTransportType: string | null
    containerType: string | null
    containerVolumeSize: string | null
    contractItem: string
    contractNumber: string
    costCenter: string | null
    customerName: string | null
    customerReference: string | null
    deliveryInfo: string | null
    endTreatmentCenterName: string | null
    esnNumber: string | null
    estimatedWeightOrVolumeUnit: WasteMeasurementUnit | null
    estimatedWeightOrVolumeValue: number | null
    ewcCode: string | null
    hazardInducers: string | null
    installationName: string | null
    materialAnalysis: string | null
    materialNumber: string | null
    materialType: string | null
    packaged: ContractLinePackagingType | null
    packagingIndicator: string | null
    packagingType: string | null
    pickupAddressName: string | null
    poNumber: string | null
    position: string | null
    processCode: string | null
    quantityContainers: number | null
    quantityLabels: number | null
    quantityPackages: number | null
    quantityPallets: number | null
    reconciliationNumber: string | null
    remarks: string | null
    serialNumber: string | null
    tankerType: string | null
    tcNumber: string | null
    tfs: boolean | null
    tfsNumber: string | null
    treatmentCenterName: string | null
    unNumber: {
      isHazardous: boolean | null
      dangerLabel1: string | null
      dangerLabel2: string | null
      dangerLabel3: string | null
      description: string | null
      number: string
      packingGroup: string | null
    } | null
    wasteMaterial: string | null
    wasteProducerName: string | null
  }[]
  packagingRemark: string | null
  packagingRequestMaterials: PickupRequestPackagingRequestMaterial[]
  pickupAddresses: PickUpAddressIndex[]
  remarks: string | null
  requestNumber: string | null
  status: PickUpRequestStatus
  totalQuantityPallets: number | null
  transportMode: PickUpTransportMode | null
  wasteProducer: WasteProducerIndex | null
}
