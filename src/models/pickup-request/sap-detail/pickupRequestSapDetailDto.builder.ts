import type { PickUpTransportMode } from '@/client'
import { PickUpRequestStatus } from '@/client'
import type { PickupRequestSapDetailDto } from '@/models/pickup-request/sap-detail/pickupRequestSapDetailDto.model'

const EMPTY_SAP_PICKUP_REQUEST_DETAIL: PickupRequestSapDetailDto = {
  confirmedDate: null,
  endDate: null,
  startDate: null,
  isReturnPackaging: null,
  isTransportByIndaver: null,
  isWicConfirmed: null,
  additionalFiles: [],
  createdBy: null,
  customer: null,
  materials: [],
  needsWicConfirmation: false,
  packagingRemark: null,
  packagingRequestMaterials: [],
  pickUpAddresses: [],
  remarks: null,
  requestNumber: null,
  sendCopyToContacts: [],
  status: PickUpRequestStatus.DRAFT,
  totalQuantityPallets: null,
  transportMode: null as PickUpTransportMode | null,
  wasteProducer: null,
}

export class PickupRequestSapDetailDtoBuilder {
  private value: PickupRequestSapDetailDto = EMPTY_SAP_PICKUP_REQUEST_DETAIL

  constructor() {}

  build(): PickupRequestSapDetailDto {
    return this.value
  }
}
