import type { ViewPickUpAddressIndexV1Data } from '@/client'
import type { PickUpAddressIndexQueryParams } from '@/models/pick-up-address/index/pickUpAddressIndexQueryParams.model'
import type { OffsetPagination } from '@/types/pagination.type'
import { SearchUtil } from '@/utils/search.util.ts'

export class PickUpAddressIndexQueryParamsTransformer {
  static toDto(options: OffsetPagination<PickUpAddressIndexQueryParams>): ViewPickUpAddressIndexV1Data['query'] {
    return {
      filter: options.filters,
      pagination: options.pagination,
      search: SearchUtil.toDto(options.search),
    }
  }
}
