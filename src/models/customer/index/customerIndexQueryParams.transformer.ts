import type { ViewCustomerIndexV1Data } from '@/client'
import type { CustomerIndexQueryParams } from '@/models/customer/index/customerIndexQueryParams.model'
import type { OffsetPagination } from '@/types/pagination.type'
import { SearchUtil } from '@/utils/search.util.ts'

export class CustomerIndexQueryParamsTransformer {
  static toDto(options: OffsetPagination<CustomerIndexQueryParams>): ViewCustomerIndexV1Data['query'] {
    return {
      pagination: options.pagination,
      search: SearchUtil.toDto(options.search),
    }
  }
}
