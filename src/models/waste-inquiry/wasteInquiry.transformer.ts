import type {
  WasteFlashpointOption as WasteFlashpointApiEnum,
  WastePhOption,
} from '@/client'
import {
  EntityPart,
  StableTemperatureType as StableTemperatureTypeApi,
  WasteLegislationOption,
  WeightUnit,
} from '@/client'
import { CustomerIndexTransformer } from '@/models/customer/customer.transformer'
import type { CalendarDate } from '@/models/date/calendarDate.model'
import { CalendarDateTransformer } from '@/models/date/calendarDate.transformer'
import { CalendarDateTimeTransformer } from '@/models/date/calendarDateTime.transformer'
import type { CalendarDateTimeDto } from '@/models/date/calendarDateTimeDto.model'
import { StableTemperatureType } from '@/models/enums/stableTemperatureType.enum'
import { WasteFlashpointOption } from '@/models/enums/wasteFlashpoint.enum'
import { PickUpAddressIndexTransformer } from '@/models/pick-up-address/pickUpAddress.transformer'
import { S3FileArrayTransformer } from '@/models/s3-file/s3File.transformer'
import type { WasteInquiryCreate } from '@/models/waste-inquiry/create/wasteInquiryCreate.model'
import type { WasteInquiryCreateDto } from '@/models/waste-inquiry/create/wasteInquiryCreateDto.model'
import type {
  WasteInquiryDetail,
  WasteInquiryDetailCharacteristicsStep,
  WasteInquiryDetailCollectionStep,
  WasteInquiryDetailCompositionStep,
  WasteInquiryDetailCustomerAndLocationStep,
  WasteInquiryDetailLegislationAndPropertiesStep,
  WasteInquiryDetailTransportStep,
  WasteInquiryDetailTypeStep,
} from '@/models/waste-inquiry/detail/wasteInquiryDetail.model'
import type { WasteInquiryDetailDto } from '@/models/waste-inquiry/detail/wasteInquiryDetailDto.model'
import type { WasteInquiryIndex } from '@/models/waste-inquiry/index/wasteInquiryIndex.model'
import type { WasteInquiryIndexDto } from '@/models/waste-inquiry/index/wasteInquiryIndexDto.model'
import type { WasteInquiryIndexPagination } from '@/models/waste-inquiry/index/wasteInquiryIndexPagination.model'
import type { WasteInquiryIndexPaginationDto } from '@/models/waste-inquiry/index/wasteInquiryIndexPaginationDto.model'
import type { WasteInquirySapDetail } from '@/models/waste-inquiry/sap-detail/wasteInquirySapDetail.model'
import type { WasteInquirySapDetailDto } from '@/models/waste-inquiry/sap-detail/wasteInquirySapDetailDto.model'
import type { WasteInquirySapDocumentUpdateDto } from '@/models/waste-inquiry/update/sap/wasteInquirySapDocumentUpdateDto.model'
import type { WasteInquirySapDocumentUpdateForm } from '@/models/waste-inquiry/update/sap/wasteInquirySapDocumentUpdateForm.model'
import type { WasteInquirySubmitForm } from '@/models/waste-inquiry/update/submit/wasteInquirySubmitForm.model'
import type { WasteInquirySubmitResponse } from '@/models/waste-inquiry/update/submit/wasteInquirySubmitResponse.model'
import type { WasteInquirySubmitResponseDto } from '@/models/waste-inquiry/update/submit/wasteInquirySubmitResponseDto.model'
import type { WasteInquirySubmitDto } from '@/models/waste-inquiry/update/wasteInquirySubmitDto.model'
import type {
  WasteInquiryCharacteristicsStepUpdateDto,
  WasteInquiryCollectionStepUpdateDto,
  WasteInquiryCompositionStepUpdateDto,
  WasteInquiryCustomerAndLocationStepUpdateDto,
  WasteInquiryLegislationAndPropertiesStepUpdateDto,
  WasteInquiryTransportStepUpdateDto,
  WasteInquiryTypeStepUpdateDto,
  WasteInquiryUpdateDto,
} from '@/models/waste-inquiry/update/wasteInquiryUpdateDto.model'
import type { WasteInquiryUpdateForm } from '@/models/waste-inquiry/update/wasteInquiryUpdateForm.model'
import { WasteInquiryTransportValidationUtil } from '@/models/waste-inquiry/wasteInquiryTransportValidation.util'
import type { WasteInquiryUuid } from '@/models/waste-inquiry/wasteInquiryUuid.model'
import { WasteProducerIndexTransformer } from '@/models/waste-producer/wasteProducer.transformer'
import { StringUtil } from '@/utils/string.util'

export class WasteInquiryCreateTransformer {
  static fromDto(dto: WasteInquiryCreateDto): WasteInquiryCreate {
    return { uuid: dto.uuid as WasteInquiryUuid }
  }
}

export class WasteInquiryUpdateTransformer {
  static mapCharacteristicsStep(form: Partial<WasteInquiryUpdateForm>): WasteInquiryCharacteristicsStepUpdateDto {
    let stableTemperatureType: StableTemperatureTypeApi | undefined

    if (form.stableTemperatureType === null) {
      stableTemperatureType = undefined
    }
    else if (form.stableTemperatureType === StableTemperatureType.AMBIENT) {
      stableTemperatureType = StableTemperatureTypeApi.AMBIENT
    }
    else {
      stableTemperatureType = StableTemperatureTypeApi.OTHER
    }

    return {
      averageStableTemperature: form.stableTemperatureType === StableTemperatureType.AVERAGE
        ? form.averageTemperature
        : null,
      flashpoint: form.flashpointType === WasteFlashpointOption.UNKNOWN
        ? null
        : form.flashpointType as unknown as WasteFlashpointApiEnum ?? null,
      maxStableTemperature: form.stableTemperatureType === StableTemperatureType.MIN_MAX
        ? form.maximumTemperature
        : null,
      minStableTemperature: form.stableTemperatureType === StableTemperatureType.MIN_MAX
        ? form.minimumTemperature
        : null,
      ph: (form.phType as unknown as WastePhOption) ?? null,
      specificGravity: form.specificGravity ?? null,
      stableTemperatureType,
    }
  }

  static mapCollectionStep(form: Partial<WasteInquiryUpdateForm>): WasteInquiryCollectionStepUpdateDto {
    return {
      expectedEndDate: form.firstCollectionDate === null
        ? null
        : CalendarDateTransformer.toNullableDto(form.expectedEndDate ?? null),
      firstCollectionDate: CalendarDateTransformer.toNullableDto(form.firstCollectionDate ?? null),
      collectionRemarks: form.collectionRemarks || null,
      dischargeFrequency: form.dischargeFrequency ?? null,
      expectedPerCollectionQuantity: form.expectedPerCollectionQuantity ?? null,
      expectedPerCollectionUnit: form.expectedPerCollectionQuantity === null
        ? null
        : form.expectedPerCollectionUnit,
      expectedYearlyVolumeAmount: form.expectedYearlyVolumeAmount ?? null,
      expectedYearlyVolumeUnit: form.expectedYearlyVolumeUnit ?? null,
    }
  }

  static mapCompositionStep(form: Partial<WasteInquiryUpdateForm>): WasteInquiryCompositionStepUpdateDto {
    return {
      isSampleAvailable: form.isSampleAvailable ?? false,
      analysisReportFiles: S3FileArrayTransformer.toArrayDto(form.analysisReportFiles ?? []),
      composition: form.composition?.filter((composition) => composition.name !== null && composition.name !== ''),
      noAnalysisReport: form.hasNoAnalysisReport ?? false,
      noSds: form.hasNoSds ?? false,
      sdsFiles: S3FileArrayTransformer.toArrayDto(form.sdsFiles ?? []),
    }
  }

  static mapCustomerAndLocationStep(
    form: Partial<WasteInquiryUpdateForm>,
  ): WasteInquiryCustomerAndLocationStepUpdateDto {
    return {
      customerId: form.customer?.id ?? null,
      pickUpAddressId: form.pickUpAddress?.id ?? null,
      wasteProducerId: form.wasteProducer?.id ?? null,
      isUnknownPickUpAddress: form.isPickupAddressUnknown ?? false,
      isUnknownWasteProducer: form.isWasteProducerUnknown ?? false,
      customerName: form.customer?.name || null,
      pickUpAddressName: form.pickUpAddress?.name || null,
      wasteProducerName: form.wasteProducer?.name || null,
    }
  }

  static mapLegislationAndPropertiesStep(
    form: Partial<WasteInquiryUpdateForm>,
  ): WasteInquiryLegislationAndPropertiesStepUpdateDto {
    return {
      legislationRemarks: form.legislationRemarks || null,
      propertyRemarks: form.propertyRemarks || null,
      selectedLegislationOptions: form.selectedLegislationTypes ?? [],
      selectedPropertyOptions: form.selectedPropertyTypes ?? [],
      svhcExtra: form.selectedLegislationTypes?.includes(WasteLegislationOption.SVHC)
        ? form.svhcExtraType
        : null,
    }
  }

  static mapTransportStep(form: Partial<WasteInquiryUpdateForm>): WasteInquiryTransportStepUpdateDto {
    const wasteInquiryTransportValidationUtil = new WasteInquiryTransportValidationUtil(form)

    return {
      isLoadingByIndaver: wasteInquiryTransportValidationUtil.isLoadingByIndaverAllowed
        ? form.isLoadingByIndaver
        : null,
      isRegulatedTransport: form.regulatedTransport ?? null,
      isTankOwnedByCustomer: wasteInquiryTransportValidationUtil.isTankOwnedByCustomerAllowed
        ? form.isTankOwnedByCustomer
        : null,
      isTransportByIndaver: form.isTransportByIndaver ?? null,
      collectionRequirements: wasteInquiryTransportValidationUtil.isCollectionRequirementsAllowed
        ? form.collectionRequirements
        : null,
      containerLoadingType: wasteInquiryTransportValidationUtil.isContainerLoadingTypeAllowed
        ? form.containerLoadingType
        : null,
      hazardInducer1: form.hazardInducer1 ?? null,
      hazardInducer2: form.hazardInducer2 ?? null,
      hazardInducer3: form.hazardInducer3 ?? null,
      loadingMethod: wasteInquiryTransportValidationUtil.isLoadingMethodAllowed
        ? form.loadingMethod
        : null,
      loadingType: wasteInquiryTransportValidationUtil.isLoadingTypeAllowed
        ? form.wasteLoadingType
        : null,
      packaging: wasteInquiryTransportValidationUtil.isPackagingAllowed
        ? form.packaging?.map((packaging) => ({
            hasInnerPackaging: packaging.hasInnerPackaging ?? null,
            remarks: packaging.remarks || null,
            size: packaging.size ?? null,
            type: packaging.type ?? null,
            weightPerPieceUnit: WeightUnit.KG,
            weightPerPieceValue: packaging.weightPerPieceValue ?? null,
          }))
        : [],
      storedIn: wasteInquiryTransportValidationUtil.isStoredInAllowed
        ? form.wasteStoredIn
        : null,
      transportIn: wasteInquiryTransportValidationUtil.isTransportInAllowed
        ? form.transportIn
        : null,
      transportType: wasteInquiryTransportValidationUtil.isTransportTypeAllowed
        ? form.transportType
        : null,
      transportVolumeAmount: wasteInquiryTransportValidationUtil.isTransportVolumeAllowed
        ? form.transportVolumeAmount
        : null,
      transportVolumeUnit: wasteInquiryTransportValidationUtil.isTransportVolumeAllowed
        ? form.transportVolumeUnit
        : null,
      unNumbers: wasteInquiryTransportValidationUtil.isUnNumbersAllowed
        ? form.unNumbers
            ?.filter((unNumber) => unNumber.unNumber !== null)
            .map((unNumber) => ({
              isHazardous: unNumber.isHazardous,
              packingGroup: unNumber.packingGroup ?? null,
              unNumber: unNumber.unNumber ?? null,
            }))
        : [],
    }
  }

  static mapTypeStep(form: Partial<WasteInquiryUpdateForm>): WasteInquiryTypeStepUpdateDto {
    return {
      ewcLevel1Name: form.ewcLevel1 ?? null,
      ewcLevel2Name: form.ewcLevel2 ?? null,
      ewcLevel3Name: form.ewcLevel3 ?? null,
      packagingType: form.packagingType ?? null,
      stateOfMatter: form.stateOfMatterType ?? null,
      wasteStreamDescription: form.description || null,
      wasteStreamName: form.name || null,
    }
  }

  static toDto(form: Partial<WasteInquiryUpdateForm>): WasteInquiryUpdateDto {
    return {
      ...this.mapCustomerAndLocationStep(form),
      ...this.mapTypeStep(form),
      ...this.mapCharacteristicsStep(form),
      ...this.mapCompositionStep(form),
      ...this.mapLegislationAndPropertiesStep(form),
      ...this.mapCollectionStep(form),
      ...this.mapTransportStep(form),
    }
  }
}

export class WasteInquirySapDocumentUpdateTransformer {
  static toDto(form: WasteInquirySapDocumentUpdateForm): WasteInquirySapDocumentUpdateDto {
    return {
      documents: [
        ...form.additionalFiles.map((file) => ({
          fileUuid: file.uuid,
          entityPart: EntityPart.ADDITIONAL,
        })),
        ...form.analysisReportFiles.map((file) => ({
          fileUuid: file.uuid,
          entityPart: EntityPart.ANALYSIS_REPORT,
        })),
        ...form.sdsFiles.map((file) => ({
          fileUuid: file.uuid,
          entityPart: EntityPart.SDS,
        })),
      ],
    }
  }
}

export class WasteInquirySubmitFormTransformer {
  static toDto(form: WasteInquirySubmitForm): WasteInquirySubmitDto {
    return {
      additionalFiles: S3FileArrayTransformer.toArrayDto(form.additionalFiles),
      remarks: form.remarks || null,
      sendCopyToContacts: form.contacts.filter((contact) => StringUtil.trimOrNull(contact.email) !== null),
    }
  }
}

export class WasteInquiryDetailTransformer {
  static fromDto(dto: WasteInquiryDetailDto): WasteInquiryDetail {
    return {
      uuid: dto.uuid as WasteInquiryUuid,
      createdAt: new Date(dto.createdAt) as CalendarDate,
      submittedOn: null,
      updatedAt: new Date(dto.updatedAt) as CalendarDate,
      additionalFiles: S3FileArrayTransformer.fromDtoArray(dto.additionalFiles),
      contractItem: null,
      contractNumber: null,
      createdBy: null,
      inquiryNumber: null,
      status: null,
      ...this.mapCustomerAndLocationStep(dto),
      ...this.mapTypeStep(dto),
      ...this.mapCharacteristicsStep(dto),
      ...this.mapCompositionStep(dto),
      ...this.mapLegislationAndPropertiesStep(dto),
      ...this.mapCollectionStep(dto),
      ...this.mapTransportStep(dto),
    }
  }

  static mapCharacteristicsStep(dto: WasteInquiryDetailDto): WasteInquiryDetailCharacteristicsStep {
    return {
      averageTemperature: dto.averageStableTemperature ?? null,
      flashpointType: (dto.flashpoint as unknown as WasteFlashpointApiEnum) ?? WasteFlashpointOption.UNKNOWN,
      maximumTemperature: dto.maxStableTemperature ?? null,
      minimumTemperature: dto.minStableTemperature ?? null,
      phType: dto.ph,
      specificGravity: dto.specificGravity ?? null,
      stableTemperatureType: this.mapStableTemperature(dto),
    }
  }

  static mapCollectionStep(dto: WasteInquiryDetailDto): WasteInquiryDetailCollectionStep {
    return {
      expectedEndDate: CalendarDateTransformer.fromNullableDto(dto.expectedEndDate),
      firstCollectionDate: CalendarDateTransformer.fromNullableDto(dto.firstCollectionDate),
      collectionRemarks: dto.collectionRemarks ?? null,
      dischargeFrequency: dto.dischargeFrequency,
      expectedPerCollectionQuantity: dto.expectedPerCollectionQuantity ?? null,
      expectedPerCollectionUnit: dto.expectedPerCollectionUnit,
      expectedYearlyVolumeAmount: dto.expectedYearlyVolumeAmount ?? null,
      expectedYearlyVolumeUnit: dto.expectedYearlyVolumeUnit,
    }
  }

  static mapCompositionStep(dto: WasteInquiryDetailDto): WasteInquiryDetailCompositionStep {
    return {
      hasNoAnalysisReport: dto.noAnalysisReport,
      hasNoSds: dto.noSds,
      isSampleAvailable: dto.isSampleAvailable ?? false,
      analysisReportFiles: S3FileArrayTransformer.fromDtoArray(dto.analysisReportFiles),
      composition: dto.composition,
      sdsFiles: S3FileArrayTransformer.fromDtoArray(dto.sdsFiles),
    }
  }

  static mapCustomerAndLocationStep(dto: WasteInquiryDetailDto): WasteInquiryDetailCustomerAndLocationStep {
    return {
      isUnknownPickUpAddress: dto.isUnknownPickUpAddress,
      isUnknownWasteProducer: dto.isUnknownWasteProducer,
      customer: CustomerIndexTransformer.fromNullableDto(dto.customer),
      pickupAddress: PickUpAddressIndexTransformer.fromNullableDto(dto.pickUpAddress),
      wasteProducer: WasteProducerIndexTransformer.fromNullableDto(dto.wasteProducer),
    }
  }

  static mapLegislationAndPropertiesStep(dto: WasteInquiryDetailDto): WasteInquiryDetailLegislationAndPropertiesStep {
    return {
      legislationRemarks: dto.legislationRemarks,
      propertyRemarks: dto.propertyRemarks,
      selectedLegislationTypes: dto.selectedLegislationOptions,
      selectedPropertyTypes: dto.selectedPropertyOptions,
      svhcExtraType: dto.svhcExtra,
    }
  }

  static mapStableTemperature(dto: WasteInquiryDetailDto): StableTemperatureType | null {
    let stableTemperatureType: StableTemperatureType | null

    if (dto.stableTemperatureType === null) {
      stableTemperatureType = null
    }
    else if (dto.stableTemperatureType === StableTemperatureTypeApi.AMBIENT) {
      stableTemperatureType = StableTemperatureType.AMBIENT
    }
    else if (dto.averageStableTemperature === null) {
      stableTemperatureType = StableTemperatureType.MIN_MAX
    }
    else {
      stableTemperatureType = StableTemperatureType.AVERAGE
    }

    return stableTemperatureType
  }

  static mapTransportStep(dto: WasteInquiryDetailDto): WasteInquiryDetailTransportStep {
    return {
      isLoadingByIndaver: dto.isLoadingByIndaver ?? null,
      isTankOwnedByCustomer: dto.isTankOwnedByCustomer ?? null,
      isTransportByIndaver: dto.isTransportByIndaver ?? null,
      collectionRequirements: dto.collectionRequirements,
      containerLoadingType: dto.containerLoadingType ?? null,
      hazardInducer1: dto.hazardInducer1 ?? null,
      hazardInducer2: dto.hazardInducer2 ?? null,
      hazardInducer3: dto.hazardInducer3 ?? null,
      loadingMethod: dto.loadingMethod ?? null,
      packaging: dto.packaging.map((packaging) => ({
        hasInnerPackaging: packaging.hasInnerPackaging ?? null,
        remarks: packaging.remarks ?? null,
        size: packaging.size ?? null,
        type: packaging.type ?? null,
        weightPerPieceValue: packaging.weightPerPieceValue ?? null,
      })),
      regulatedTransport: dto.isRegulatedTransport ?? null,
      transportIn: dto.transportIn ?? null,
      transportType: dto.transportType ?? null,
      transportVolumeAmount: dto.transportVolumeAmount ?? null,
      transportVolumeUnit: dto.transportVolumeUnit ?? null,
      unNumbers: dto.unNumbers.map((unNumber) => ({
        isHazardous: unNumber.isHazardous,
        packingGroup: unNumber.packingGroup ?? null,
        unNumber: unNumber.unNumber ?? null,
      })),
      wasteLoadingType: dto.loadingType ?? null,
      wasteStoredIn: dto.storedIn ?? null,
    }
  }

  static mapTypeStep(dto: WasteInquiryDetailDto): WasteInquiryDetailTypeStep {
    return {
      ewcLevel1: dto.ewcLevel1Name,
      ewcLevel2: dto.ewcLevel2Name,
      ewcLevel3: dto.ewcLevel3Name,
      packagingType: dto.packagingType,
      stateOfMatterType: dto.stateOfMatter,
      wasteStreamDescription: dto.wasteStreamDescription,
      wasteStreamName: dto.wasteStreamName,
    }
  }
}

export class WasteInquiryIndexPaginationTransformer {
  static toDto(pagination: WasteInquiryIndexPagination): WasteInquiryIndexPaginationDto {
    return {
      filter: pagination.filter,
      sort: pagination.sort,
    }
  }
}

export class WasteInquiryIndexTransformer {
  static fromDto(dto: WasteInquiryIndexDto): WasteInquiryIndex {
    return {
      uuid: dto.uuid as WasteInquiryUuid | null,
      contractId: dto.contractId,
      customerId: dto.customerId,
      pickupAddressId: dto.pickUpAddressId,
      salesOrganisationId: dto.salesOrganisationId,
      wasteProducerId: dto.wasteProducerId,
      conformityCheck: dto.conformityCheck,
      contractItem: dto.contractItem,
      customerName: dto.customerName,
      date: CalendarDateTransformer.fromNullableDto(dto.date),
      ewcLevel1: dto.ewcLevel1,
      ewcLevel2: dto.ewcLevel2,
      ewcLevel3: dto.ewcLevel3,
      inquiryNumber: dto.inquiryNumber,
      pickupAddressName: dto.pickUpAddressName,
      requestorName: dto.requestorName,
      salesOrganisationName: dto.salesOrganisationName,
      status: dto.status,
      wasteProducerName: dto.wasteProducerName,
      wasteStreamName: dto.wasteStreamName,
    }
  }
}

export class WasteInquirySubmitResponseTransformer {
  static fromDto(dto: WasteInquirySubmitResponseDto): WasteInquirySubmitResponse {
    return {
      uuid: dto.uuid as WasteInquiryUuid,
      submittedOn: CalendarDateTimeTransformer.fromDto(dto.submittedOn as CalendarDateTimeDto),
      inquiryNumber: dto.inquiryNumber,
    }
  }
}

export class WasteInquirySapDetailTransformer {
  static fromDto(dto: WasteInquirySapDetailDto): WasteInquirySapDetail {
    let stableTemperatureType: StableTemperatureType | null

    if (dto.stableTemperatureType === StableTemperatureTypeApi.AMBIENT) {
      stableTemperatureType = StableTemperatureType.AMBIENT
    }
    else if (dto.stableTemperatureType === StableTemperatureTypeApi.OTHER
      && dto.averageStableTemperature !== null) {
      stableTemperatureType = StableTemperatureType.AVERAGE
    }
    else {
      stableTemperatureType = StableTemperatureType.MIN_MAX
    }

    return {
      expectedEndDate: CalendarDateTransformer.fromNullableDto(dto.expectedEndDate),
      firstCollectionDate: CalendarDateTransformer.fromNullableDto(dto.firstCollectionDate),
      submittedOn: CalendarDateTimeTransformer.fromNullableDto(dto.submittedOn as CalendarDateTimeDto | null),
      isLoadingByIndaver: dto.isLoadingByIndaver,
      isRegulatedTransport: dto.isRegulatedTransport,
      isSampleAvailable: dto.isSampleAvailable,
      isTankOwnedByCustomer: dto.isTankOwnedByCustomer,
      isTransportByIndaver: dto.isTransportByIndaver,
      isUnknownPickUpAddress: dto.isUnknownPickUpAddress,
      isUnknownWasteProducer: dto.isUnknownWasteProducer,
      additionalFiles: S3FileArrayTransformer.fromDtoArray(dto.additionalFiles),
      analysisReportFiles: S3FileArrayTransformer.fromDtoArray(dto.analysisReportFiles),
      averageStableTemperature: dto.averageStableTemperature,
      collectionRemarks: dto.collectionRemarks,
      collectionRequirements: dto.collectionRequirements,
      composition: dto.composition,
      containerLoadingType: dto.containerLoadingType,
      contractItem: dto.contractItem,
      contractNumber: dto.contractNumber,
      createdBy: dto.createdBy,
      customer: CustomerIndexTransformer.fromNullableDto(dto.customer),
      dischargeFrequency: dto.dischargeFrequency,
      ewcLevel1Name: dto.ewcLevel1Name,
      ewcLevel2Name: dto.ewcLevel2Name,
      ewcLevel3Name: dto.ewcLevel3Name,
      expectedPerCollectionQuantity: dto.expectedPerCollectionQuantity,
      expectedPerCollectionUnit: dto.expectedPerCollectionUnit,
      expectedYearlyVolumeAmount: dto.expectedYearlyVolumeAmount,
      expectedYearlyVolumeUnit: dto.expectedYearlyVolumeUnit,
      flashpoint: (dto.flashpoint as unknown as WasteFlashpointApiEnum) ?? WasteFlashpointOption.UNKNOWN,
      hazardInducer1: dto.hazardInducer1 ?? null,
      hazardInducer2: dto.hazardInducer2 ?? null,
      hazardInducer3: dto.hazardInducer3 ?? null,
      inquiryNumber: dto.inquiryNumber,
      legislationRemarks: dto.legislationRemarks,
      loadingMethod: dto.loadingMethod,
      loadingType: dto.loadingType,
      maxStableTemperature: dto.maxStableTemperature,
      minStableTemperature: dto.minStableTemperature,
      noAnalysisReport: dto.noAnalysisReport,
      noSds: dto.noSds,
      packaging: dto.packaging.map((packaging) => ({
        hasInnerPackaging: packaging.hasInnerPackaging,
        remarks: packaging.remarks,
        size: packaging.size,
        type: packaging.type,
        weightPerPieceUnit: packaging.weightPerPieceUnit ?? WeightUnit.KG,
        weightPerPieceValue: packaging.weightPerPieceValue,
      })),
      packagingType: dto.packagingType,
      ph: dto.ph,
      pickupAddress: PickUpAddressIndexTransformer.fromNullableDto(dto.pickUpAddress),
      propertyRemarks: dto.propertyRemarks,
      remarks: dto.remarks,
      sdsFiles: S3FileArrayTransformer.fromDtoArray(dto.sdsFiles),
      selectedLegislationOptions: dto.selectedLegislationOptions,
      selectedPropertyOptions: dto.selectedPropertyOptions,
      sendCopyToContacts: dto.sendCopyToContacts,
      specificGravity: dto.specificGravity,
      stableTemperatureType,
      stateOfMatter: dto.stateOfMatter,
      status: dto.status,
      storedIn: dto.storedIn,
      svhcExtra: dto.svhcExtra,
      transportIn: dto.transportIn,
      transportType: dto.transportType,
      transportVolumeAmount: dto.transportVolumeAmount,
      transportVolumeUnit: dto.transportVolumeUnit,
      unNumbers: dto.unNumbers.map((unNumber) => ({
        isHazardous: unNumber.isHazardous,
        packingGroup: unNumber.packingGroup,
        unNumber: unNumber.unNumber,
      })),
      wasteProducer: WasteProducerIndexTransformer.fromNullableDto(dto.wasteProducer),
      wasteStreamDescription: dto.wasteStreamDescription,
      wasteStreamName: dto.wasteStreamName,
    }
  }

  static toDetail(sap: WasteInquirySapDetail): WasteInquiryDetail {
    return {
      uuid: null,
      createdAt: null,
      expectedEndDate: sap.expectedEndDate,
      firstCollectionDate: sap.firstCollectionDate,
      submittedOn: sap.submittedOn,
      updatedAt: null,
      hasNoAnalysisReport: sap.noAnalysisReport,
      hasNoSds: sap.noSds,
      isLoadingByIndaver: sap.isLoadingByIndaver,
      isSampleAvailable: sap.isSampleAvailable,
      isTankOwnedByCustomer: sap.isTankOwnedByCustomer,
      isTransportByIndaver: sap.isTransportByIndaver,
      isUnknownPickUpAddress: sap.isUnknownPickUpAddress,
      isUnknownWasteProducer: sap.isUnknownWasteProducer,
      additionalFiles: sap.additionalFiles,
      analysisReportFiles: sap.analysisReportFiles,
      averageTemperature: sap.averageStableTemperature,
      collectionRemarks: sap.collectionRemarks,
      collectionRequirements: sap.collectionRequirements,
      composition: sap.composition,
      containerLoadingType: sap.containerLoadingType,
      contractItem: sap.contractItem,
      contractNumber: sap.contractNumber,
      createdBy: sap.createdBy,
      customer: sap.customer,
      dischargeFrequency: sap.dischargeFrequency,
      ewcLevel1: sap.ewcLevel1Name,
      ewcLevel2: sap.ewcLevel2Name,
      ewcLevel3: sap.ewcLevel3Name,
      expectedPerCollectionQuantity: sap.expectedPerCollectionQuantity,
      expectedPerCollectionUnit: sap.expectedPerCollectionUnit,
      expectedYearlyVolumeAmount: sap.expectedYearlyVolumeAmount,
      expectedYearlyVolumeUnit: sap.expectedYearlyVolumeUnit,
      flashpointType: sap.flashpoint,
      hazardInducer1: sap.hazardInducer1,
      hazardInducer2: sap.hazardInducer2,
      hazardInducer3: sap.hazardInducer3,
      inquiryNumber: sap.inquiryNumber,
      legislationRemarks: sap.legislationRemarks,
      loadingMethod: sap.loadingMethod,
      maximumTemperature: sap.maxStableTemperature,
      minimumTemperature: sap.minStableTemperature,
      packaging: sap.packaging,
      packagingType: sap.packagingType,
      phType: sap.ph,
      pickupAddress: sap.pickupAddress,
      propertyRemarks: sap.propertyRemarks,
      regulatedTransport: sap.isRegulatedTransport,
      sdsFiles: sap.sdsFiles,
      selectedLegislationTypes: sap.selectedLegislationOptions,
      selectedPropertyTypes: sap.selectedPropertyOptions,
      specificGravity: sap.specificGravity,
      stableTemperatureType: sap.stableTemperatureType,
      stateOfMatterType: sap.stateOfMatter,
      status: sap.status,
      svhcExtraType: sap.svhcExtra,
      transportIn: sap.transportIn,
      transportType: sap.transportType,
      transportVolumeAmount: sap.transportVolumeAmount,
      transportVolumeUnit: sap.transportVolumeUnit,
      unNumbers: sap.unNumbers,
      wasteLoadingType: sap.loadingType,
      wasteProducer: sap.wasteProducer,
      wasteStoredIn: sap.storedIn,
      wasteStreamDescription: sap.wasteStreamDescription,
      wasteStreamName: sap.wasteStreamName,
    }
  }
}
