import type { WasteInquiryStatus } from '@/client'
import type { CalendarDate } from '@/models/date/calendarDate.model'
import type { WasteInquiryUuid } from '@/models/waste-inquiry/wasteInquiryUuid.model'

export interface WasteInquiryIndex {
  uuid: WasteInquiryUuid | null
  contractId: string | null
  customerId: string | null
  pickupAddressId: string | null
  salesOrganisationId: string | null
  wasteProducerId: string | null
  conformityCheck: boolean | null
  contractItem: string | null
  customerName: string | null
  date: CalendarDate | null
  ewcLevel1: string | null
  ewcLevel2: string | null
  ewcLevel3: string | null
  inquiryNumber: string | null
  pickupAddressName: string | null
  requestorName: string | null
  salesOrganisationName: string | null
  status: WasteInquiryStatus
  wasteProducerName: string | null
  wasteStreamName: string | null
}

export enum WasteInquiryIndexTableTabs {
  COMPLETED = 'completed',
  DRAFTS = 'drafts',
  OFFERS = 'offers',
  PENDING = 'pending',
  SUBMITTED = 'submitted',
}
