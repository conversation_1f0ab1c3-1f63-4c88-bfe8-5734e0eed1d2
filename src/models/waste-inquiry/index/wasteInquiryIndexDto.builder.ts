import { WasteInquiryStatus } from '@/client'
import { CalendarDateTransformer } from '@/models/date/calendarDate.transformer.ts'
import type { WasteInquiryIndexDto } from '@/models/waste-inquiry/index/wasteInquiryIndexDto.model'
import { UuidUtil } from '@/utils/uuid.util.ts'

export class WasteInquiryIndexDtoBuilder {
  private value: WasteInquiryIndexDto = {
    uuid: UuidUtil.getRandom(),
    contractId: null,
    customerId: '50000075',
    pickUpAddressId: '50000075',
    salesOrganisationId: '1600',
    wasteProducerId: '50000125',
    conformityCheck: false,
    contractItem: null,
    customerName: 'Inovyn Manufacturing Belgium nv',
    date: CalendarDateTransformer.toDto(new Date()),
    ewcLevel1: '01',
    ewcLevel2: '01',
    ewcLevel3: '02',
    inquiryNumber: '12345678',
    pickUpAddressName: 'Inovyn Manufacturing Belgium nv',
    requestorName: '<PERSON>to<PERSON>',
    salesOrganisationName: 'Indaver NV',
    status: WasteInquiryStatus.IN_PROGRESS,
    wasteProducerName: 'Test Waste Producer',
    wasteStreamName: 'Test Waste Stream',
  }

  constructor() {
  }

  build(): WasteInquiryIndexDto {
    return this.value
  }

  withInquiryNumber(inquiryNumber: string): WasteInquiryIndexDtoBuilder {
    this.value.inquiryNumber = inquiryNumber

    return this
  }

  withWasteProducerName(name: string): WasteInquiryIndexDtoBuilder {
    this.value.wasteProducerName = name

    return this
  }
}
