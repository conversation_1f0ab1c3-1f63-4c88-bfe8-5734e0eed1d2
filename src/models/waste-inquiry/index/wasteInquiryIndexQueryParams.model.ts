import type { WasteInquiryStatus } from '@/client'
import type { CustomBooleanFilterValue } from '@/composables/custom-boolean-filter/customBooleanFilter.composable'
import type { CalendarDateRange } from '@/models/date/calendarDateRange.model'
import type {
  WithFilterQuery,
  WithSortQuery,
} from '@/types/query.type'

export interface WasteInquiryIndexQueryParams extends WithFilterQuery<{
  contractId: string | null
  customerId: {
    id: string
    name: string
  } | null
  wasteProducerId: {
    id: string
    name: string
  } | null
  conformityCheck: CustomBooleanFilterValue
  contractItem: string | null
  date: CalendarDateRange
  ewcCode: {
    label: string
    value: string
  } | null
  inquiryNumber: string | null
  statuses: WasteInquiryStatus[]
  wasteStreamName: string | null
}>, WithSortQuery<
  'conformityCheck' |
  'contractId' |
  'contractItem' |
  'customerName' |
  'date' |
  'inquiryNumber' |
  'pickUpAddressId' |
  'salesOrganisationId' |
  'wasteProducerId' |
  'wasteProducerName' |
  'wasteStreamName'
  > {}
