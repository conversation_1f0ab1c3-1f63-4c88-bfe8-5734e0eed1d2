import {
  ContainerLoadingType,
  RegulatedTransportOption,
  StableTemperatureType,
  StateOfMatter,
  SvhcExtraOption,
  WasteDischargeFrequency,
  WasteFlashpointOption,
  WasteInquiryStatus,
  WasteLegislationOption,
  WasteLoadingMethod,
  WasteLoadingType,
  WasteMeasurementUnit,
  WastePackagingOption,
  WastePackagingType,
  WastePropertyOption,
  WasteStoredInOption,
  WasteTransportInOption,
  WasteTransportType,
  WeightUnit,
} from '@/client'
import { AddressDtoBuilder } from '@/models/address/addressDto.builder.ts'
import { CalendarDateTransformer } from '@/models/date/calendarDate.transformer.ts'
import { CalendarDateTimeTransformer } from '@/models/date/calendarDateTime.transformer.ts'
import { S3FileDtoBuilder } from '@/models/s3-file/s3FileDto.builder.ts'
import type { WasteInquiryDetailDto } from '@/models/waste-inquiry/detail/wasteInquiryDetailDto.model.ts'
import { UuidUtil } from '@/utils/uuid.util.ts'

const EMPTY_INQUIRY_DETAIL: WasteInquiryDetailDto = {
  uuid: UuidUtil.getRandom(),
  createdAt: CalendarDateTimeTransformer.toDto(new Date()),
  expectedEndDate: null,
  firstCollectionDate: null,
  submittedOn: null,
  updatedAt: CalendarDateTimeTransformer.toDto(new Date()),
  isLoadingByIndaver: null,
  isRegulatedTransport: null,
  isSampleAvailable: null,
  isTankOwnedByCustomer: null,
  isTransportByIndaver: null,
  isUnknownPickUpAddress: false,
  isUnknownWasteProducer: false,
  additionalFiles: [],
  analysisReportFiles: [],
  averageStableTemperature: null,
  collectionRemarks: null,
  collectionRequirements: null,
  composition: [],
  containerLoadingType: null,
  customer: null,
  dischargeFrequency: null,
  ewcLevel1Name: null,
  ewcLevel2Name: null,
  ewcLevel3Name: null,
  expectedPerCollectionQuantity: null,
  expectedPerCollectionUnit: null,
  expectedYearlyVolumeAmount: null,
  expectedYearlyVolumeUnit: null,
  flashpoint: null,
  hazardInducer1: null,
  hazardInducer2: null,
  hazardInducer3: null,
  legislationRemarks: null,
  loadingMethod: null,
  loadingType: null,
  maxStableTemperature: null,
  minStableTemperature: null,
  noAnalysisReport: false,
  noSds: false,
  packaging: [],
  packagingType: null,
  ph: null,
  pickUpAddress: null,
  propertyRemarks: null,
  remarks: null,
  sdsFiles: [],
  selectedLegislationOptions: [],
  selectedPropertyOptions: [],
  sendCopyToContacts: [],
  specificGravity: null,
  stableTemperatureType: null,
  stateOfMatter: null,
  status: WasteInquiryStatus.DRAFT,
  storedIn: null,
  svhcExtra: null,
  transportIn: null,
  transportType: null,
  transportVolumeAmount: null,
  transportVolumeUnit: null,
  unNumbers: [],
  wasteProducer: null,
  wasteStreamDescription: null,
  wasteStreamName: null,
}

const FILLED_INQUIRY_DETAIL: WasteInquiryDetailDto = {
  uuid: UuidUtil.getRandom(),
  createdAt: CalendarDateTimeTransformer.toDto(new Date()),
  expectedEndDate: null,
  firstCollectionDate: CalendarDateTransformer.toDto(new Date()),
  submittedOn: null,
  updatedAt: CalendarDateTimeTransformer.toDto(new Date()),
  isLoadingByIndaver: true,
  isRegulatedTransport: RegulatedTransportOption.NO,
  isSampleAvailable: true,
  isTankOwnedByCustomer: null,
  isTransportByIndaver: true,
  isUnknownPickUpAddress: false,
  isUnknownWasteProducer: false,
  additionalFiles: [
    new S3FileDtoBuilder().build(),
  ],
  analysisReportFiles: [
    new S3FileDtoBuilder().build(),
  ],
  averageStableTemperature: 10,
  collectionRemarks: 'Collection remarks',
  collectionRequirements: null,
  composition: [
    {
      name: 'Composition 1',
      maxWeight: 10,
      minWeight: 10,
    },
  ],
  containerLoadingType: ContainerLoadingType.CHAIN,
  customer: {
    id: 'test',
    name: 'Customer name',
    address: new AddressDtoBuilder().build(),
  },
  dischargeFrequency: WasteDischargeFrequency.ONCE_OFF_CAMPAIGN,
  ewcLevel1Name: 'EWC Level 1',
  ewcLevel2Name: 'EWC Level 2',
  ewcLevel3Name: 'EWC Level 3',
  expectedPerCollectionQuantity: 10,
  expectedPerCollectionUnit: WasteMeasurementUnit.M3,
  expectedYearlyVolumeAmount: 10,
  expectedYearlyVolumeUnit: WasteMeasurementUnit.PC,
  flashpoint: WasteFlashpointOption['<_23°'],
  hazardInducer1: null,
  hazardInducer2: null,
  hazardInducer3: null,
  legislationRemarks: 'Legislation remarks',
  loadingMethod: WasteLoadingMethod.GRAVITATIONAL,
  loadingType: WasteLoadingType.BEFORE_WASTE_COLLECTION,
  maxStableTemperature: 10,
  minStableTemperature: 10,
  noAnalysisReport: false,
  noSds: false,
  packaging: [
    {
      hasInnerPackaging: false,
      remarks: 'Remarks',
      size: '10',
      type: WastePackagingOption.ASF,
      weightPerPieceUnit: WeightUnit.KG,
      weightPerPieceValue: 10,
    },
  ],
  packagingType: WastePackagingType.PACKAGED,
  ph: null,
  pickUpAddress: {
    id: 'test',
    name: 'Customer name',
    address: new AddressDtoBuilder().build(),
  },
  propertyRemarks: 'Property remarks',
  remarks: 'Remarks',
  sdsFiles: [
    new S3FileDtoBuilder().build(),
  ],
  selectedLegislationOptions: [
    WasteLegislationOption.ANIMAL_BYPRODUCT,
  ],
  selectedPropertyOptions: [
    WastePropertyOption.EXPLOSIVE,
  ],
  sendCopyToContacts: [],
  specificGravity: 10,
  stableTemperatureType: StableTemperatureType.AMBIENT,
  stateOfMatter: StateOfMatter.GASEOUS,
  status: WasteInquiryStatus.DRAFT,
  storedIn: WasteStoredInOption.IBCS,
  svhcExtra: SvhcExtraOption.OTHER,
  transportIn: WasteTransportInOption.OTHER,
  transportType: WasteTransportType.OTHER,
  transportVolumeAmount: 10,
  transportVolumeUnit: WasteMeasurementUnit.PC,
  unNumbers: [],
  wasteProducer: {
    id: 'test',
    name: 'test',
    address: new AddressDtoBuilder().build(),
  },
  wasteStreamDescription: 'Waste stream description',
  wasteStreamName: 'Waste stream name',
}

export class WasteInquiryDetailDtoBuilder {
  private value: WasteInquiryDetailDto = EMPTY_INQUIRY_DETAIL
  constructor() {
  }

  build(): WasteInquiryDetailDto {
    return this.value
  }

  withData(): WasteInquiryDetailDtoBuilder {
    this.value = FILLED_INQUIRY_DETAIL

    return this
  }

  withWasteProducerName(name: string): WasteInquiryDetailDtoBuilder {
    if (!this.value.wasteProducer) {
      this.value.wasteProducer = {
        id: UuidUtil.getRandom(),
        name,
        address: new AddressDtoBuilder().build(),
      }
    }

    this.value.wasteProducer.name = name

    return this
  }
}
