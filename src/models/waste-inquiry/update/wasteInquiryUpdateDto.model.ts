import type { UpdateWasteInquiryCommand } from '@/client'

export type WasteInquiryUpdateDto = UpdateWasteInquiryCommand

export type WasteInquiryCharacteristicsStepUpdateDto = Pick<WasteInquiryUpdateDto,
  'averageStableTemperature'
  | 'flashpoint'
  | 'maxStableTemperature'
  | 'minStableTemperature'
  | 'ph'
  | 'specificGravity'
  | 'stableTemperatureType'
>

export type WasteInquiryCollectionStepUpdateDto = Pick<WasteInquiryUpdateDto,
  'collectionRemarks'
  | 'dischargeFrequency'
  | 'expectedEndDate'
  | 'expectedPerCollectionQuantity'
  | 'expectedPerCollectionUnit'
  | 'expectedYearlyVolumeAmount'
  | 'expectedYearlyVolumeUnit'
  | 'firstCollectionDate'
>

export type WasteInquiryCompositionStepUpdateDto = Pick<WasteInquiryUpdateDto,
  'analysisReportFiles'
  | 'composition'
  | 'isSampleAvailable'
  | 'noAnalysisReport'
  | 'noSds'
  | 'sdsFiles'
>

export type WasteInquiryCustomerAndLocationStepUpdateDto = Pick<WasteInquiryUpdateDto,
  'customerId'
  | 'customerName'
  | 'isUnknownPickUpAddress'
  | 'isUnknownWasteProducer'
  | 'pickUpAddressId'
  | 'pickUpAddressName'
  | 'wasteProducerId'
  | 'wasteProducerName'
>

export type WasteInquiryLegislationAndPropertiesStepUpdateDto = Pick<WasteInquiryUpdateDto,
  'legislationRemarks'
  | 'propertyRemarks'
  | 'selectedLegislationOptions'
  | 'selectedPropertyOptions'
  | 'svhcExtra'
>

export type WasteInquiryTransportStepUpdateDto = Pick<WasteInquiryUpdateDto,
  'collectionRequirements'
  | 'containerLoadingType'
  | 'hazardInducer1'
  | 'hazardInducer2'
  | 'hazardInducer3'
  | 'isLoadingByIndaver'
  | 'isRegulatedTransport'
  | 'isTankOwnedByCustomer'
  | 'isTransportByIndaver'
  | 'loadingMethod'
  | 'loadingType'
  | 'packaging'
  | 'storedIn'
  | 'transportIn'
  | 'transportType'
  | 'transportVolumeAmount'
  | 'transportVolumeUnit'
  | 'unNumbers'
>

export type WasteInquiryTypeStepUpdateDto = Pick<WasteInquiryUpdateDto,
  'ewcLevel1Name'
  | 'ewcLevel2Name'
  | 'ewcLevel3Name'
  | 'packagingType'
  | 'stateOfMatter'
  | 'wasteStreamDescription'
  | 'wasteStreamName'
>
