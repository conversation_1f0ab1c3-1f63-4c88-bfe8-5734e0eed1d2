import { z } from 'zod'

import {
  CollectionRequirementOption,
  ContainerLoadingType,
  PackingGroup,
  RegulatedTransportOption,
  StateOfMatter,
  WasteLoadingMethod,
  WasteLoadingType,
  WasteMeasurementUnit,
  WastePackagingOption,
  WastePackagingType,
  WasteStoredInOption,
  WasteTransportInOption,
  WasteTransportType,
} from '@/client'
import { WasteInquiryTransportValidationUtil } from '@/models/waste-inquiry/wasteInquiryTransportValidation.util'
import { wizardFormBaseStepSchema } from '@/models/wizard-form/wizardFormBaseStep.model'
import { ZodUtil } from '@/utils/zod'

// NOTE: Min/Max values are validation constraints from SAP! Do not change without checking with SAP first.

const unNumberSchema = z.object({
  isHazardous: z.boolean(),
  packingGroup: z.nativeEnum(PackingGroup).nullable(),
  unNumber: z.string().regex(/^[a-z0-9]+$/i).nullable(),
})

const packagingSchema = z.object({
  hasInnerPackaging: z.boolean().nullable(),
  remarks: z.string().max(1333).nullable(),
  size: z.string().max(10).nullable(),
  type: z.nativeEnum(WastePackagingOption).nullable(),
  weightPerPieceValue: z.number().min(0.01).max(99_999_999_999.99).nullable(),
})

export const wasteInquiryTransportFormSchema = wizardFormBaseStepSchema.and(
  z.object({
    isLoadingByIndaver: z.boolean().nullable(),
    isTankOwnedByCustomer: z.boolean().nullable(),
    isTransportByIndaver: z.boolean().nullable(),
    collectionRequirements: z.nativeEnum(CollectionRequirementOption).nullable(),
    containerLoadingType: z.nativeEnum(ContainerLoadingType).nullable(),
    dependencies: z.object({
      packagingType: z.nativeEnum(WastePackagingType).nullable().readonly(),
      stateOfMatter: z.nativeEnum(StateOfMatter).nullable().readonly(),
    }),
    hazardInducer1: z.string().nullable(),
    hazardInducer2: z.string().nullable(),
    hazardInducer3: z.string().nullable(),
    loadingMethod: z.nativeEnum(WasteLoadingMethod).nullable(),
    packaging: packagingSchema.array(),
    regulatedTransport: z.nativeEnum(RegulatedTransportOption).nullable(),
    transportIn: z.nativeEnum(WasteTransportInOption).nullable(),
    transportType: z.nativeEnum(WasteTransportType).nullable(),
    transportVolumeAmount: z.number().min(0).max(99_999_999_999.99).nullable(),
    transportVolumeUnit: z.nativeEnum(WasteMeasurementUnit).nullable(),
    unNumbers: unNumberSchema.array(),
    wasteLoadingType: z.nativeEnum(WasteLoadingType).nullable(),
    wasteStoredIn: z.nativeEnum(WasteStoredInOption).nullable(),
  }),
).superRefine((data, ctx) => {
  if (!data.isFinalized) {
    return
  }

  const wasteInquiryTransportValidationUtil = new WasteInquiryTransportValidationUtil({
    ...data,
    ...data.dependencies,
  })

  ZodUtil.validateBoolean('isTransportByIndaver', data.isTransportByIndaver, ctx)

  if (wasteInquiryTransportValidationUtil.isTransportByIndaver) {
    ZodUtil.validateString('regulatedTransport', data.regulatedTransport, ctx)
  }
  if (wasteInquiryTransportValidationUtil.isUnNumberRequired) {
    const finalUnNumberSchema = z.object({
      isHazardous: z.boolean(),
      packingGroup: z.nativeEnum(PackingGroup),
      unNumber: z.string().min(1),
    })

    ZodUtil.validateField('unNumbers', finalUnNumberSchema.array(), data.unNumbers, ctx)
  }

  if (wasteInquiryTransportValidationUtil.isContainerLoadingTypeRequired) {
    ZodUtil.validateString('containerLoadingType', data.containerLoadingType, ctx)
  }
  if (wasteInquiryTransportValidationUtil.isLoadingMethodRequired) {
    ZodUtil.validateString('loadingMethod', data.loadingMethod, ctx)
  }
  if (wasteInquiryTransportValidationUtil.isTransportTypeRequired) {
    ZodUtil.validateString('transportType', data.transportType, ctx)
  }
  if (wasteInquiryTransportValidationUtil.isPackagingRequired) {
    const finalPackagingSchema = z.object({
      hasInnerPackaging: z.boolean(),
      remarks: z.string().nullable(),
      size: z.string().min(1),
      type: z.nativeEnum(WastePackagingOption),
      weightPerPieceValue: z.number().min(0),
    })

    ZodUtil.validateField('packaging', finalPackagingSchema.array(), data.packaging, ctx)
  }
  if (wasteInquiryTransportValidationUtil.isTransportInRequired) {
    ZodUtil.validateString('transportIn', data.transportIn, ctx)
  }
  if (wasteInquiryTransportValidationUtil.isLoadingTypeRequired) {
    ZodUtil.validateString('wasteLoadingType', data.wasteLoadingType, ctx)
  }
  if (wasteInquiryTransportValidationUtil.isStoredInRequired) {
    ZodUtil.validateString('wasteStoredIn', data.wasteStoredIn, ctx)
  }
  if (wasteInquiryTransportValidationUtil.isTransportVolumeRequired) {
    ZodUtil.validateNumber('transportVolumeAmount', data.transportVolumeAmount, ctx)
    ZodUtil.validateString('transportVolumeUnit', data.transportVolumeUnit, ctx)
  }
  if (wasteInquiryTransportValidationUtil.isTankOwnedByCustomerRequired) {
    ZodUtil.validateBoolean('isTankOwnedByCustomer', data.isTankOwnedByCustomer, ctx)
  }
  if (wasteInquiryTransportValidationUtil.isCollectionRequirementsRequired) {
    ZodUtil.validateString('collectionRequirements', data.collectionRequirements, ctx)
  }
  if (data.unNumbers.some((num) => num.isHazardous === true)) {
    ZodUtil.validateString('hazardInducer1', data.hazardInducer1, ctx)
  }
})

export type WasteInquiryTransportForm = z.infer<typeof wasteInquiryTransportFormSchema>
export type UnNumber = z.infer<typeof unNumberSchema>
export type Packaging = z.infer<typeof packagingSchema>
