import type {
  CollectionRequirementOption,
  ContactTypeResponse,
  ContainerLoadingType,
  RegulatedTransportOption,
  StateOfMatter,
  SvhcExtraOption,
  WasteComposition,
  WasteDischargeFrequency,
  WasteFlashpointOption,
  WasteInquiryStatus,
  WasteInquiryUnNumberResponse,
  WasteLegislationOption,
  WasteLoadingMethod,
  WasteLoadingType,
  WasteMeasurementUnit,
  WastePackagingResponse,
  WastePackagingType,
  WastePhOption,
  WastePropertyOption,
  WasteStoredInOption,
  WasteTransportInOption,
  WasteTransportType,
} from '@/client'
import type { CustomerIndex } from '@/models/customer/index/customerIndex.model'
import type { CalendarDate } from '@/models/date/calendarDate.model'
import type { CalendarDateTime } from '@/models/date/calendarDateTime.model'
import type { StableTemperatureType } from '@/models/enums/stableTemperatureType.enum'
import type { PickUpAddressIndex } from '@/models/pick-up-address/index/pickUpAddressIndex.model'
import type { S3File } from '@/models/s3-file/s3File.model'
import type { WasteProducerIndex } from '@/models/waste-producer/index/wasteProducerIndex.model'

export interface WasteInquirySapDetail {
  expectedEndDate: CalendarDate | null
  firstCollectionDate: CalendarDate | null
  submittedOn: CalendarDateTime | null
  isLoadingByIndaver: boolean | null
  isRegulatedTransport: RegulatedTransportOption | null
  isSampleAvailable: boolean | null
  isTankOwnedByCustomer: boolean | null
  isTransportByIndaver: boolean | null
  isUnknownPickUpAddress: boolean
  isUnknownWasteProducer: boolean
  additionalFiles: S3File[]
  analysisReportFiles: S3File[]
  averageStableTemperature: number | null
  collectionRemarks: string | null
  collectionRequirements: CollectionRequirementOption | null
  composition: WasteComposition[]
  containerLoadingType: ContainerLoadingType | null
  contractItem: string | null
  contractNumber: string | null
  createdBy: string | null
  customer: CustomerIndex | null
  dischargeFrequency: WasteDischargeFrequency | null
  ewcLevel1Name: string | null
  ewcLevel2Name: string | null
  ewcLevel3Name: string | null
  expectedPerCollectionQuantity: number | null
  expectedPerCollectionUnit: WasteMeasurementUnit | null
  expectedYearlyVolumeAmount: number | null
  expectedYearlyVolumeUnit: WasteMeasurementUnit | null
  flashpoint: WasteFlashpointOption | null
  hazardInducer1: string | null
  hazardInducer2: string | null
  hazardInducer3: string | null
  inquiryNumber: string | null
  legislationRemarks: string | null
  loadingMethod: WasteLoadingMethod | null
  loadingType: WasteLoadingType | null
  maxStableTemperature: number | null
  minStableTemperature: number | null
  noAnalysisReport: boolean
  noSds: boolean
  packaging: WastePackagingResponse[]
  packagingType: WastePackagingType | null
  ph: WastePhOption | null
  pickupAddress: PickUpAddressIndex | null
  propertyRemarks: string | null
  remarks: string | null
  sdsFiles: S3File[]
  selectedLegislationOptions: WasteLegislationOption[]
  selectedPropertyOptions: WastePropertyOption[]
  sendCopyToContacts: ContactTypeResponse[]
  specificGravity: number | null
  stableTemperatureType: StableTemperatureType | null
  stateOfMatter: StateOfMatter | null
  status: WasteInquiryStatus
  storedIn: WasteStoredInOption | null
  svhcExtra: SvhcExtraOption | null
  transportIn: WasteTransportInOption | null
  transportType: WasteTransportType | null
  transportVolumeAmount: number | null
  transportVolumeUnit: WasteMeasurementUnit | null
  unNumbers: WasteInquiryUnNumberResponse[]
  wasteProducer: WasteProducerIndex | null
  wasteStreamDescription: string | null
  wasteStreamName: string | null
}
