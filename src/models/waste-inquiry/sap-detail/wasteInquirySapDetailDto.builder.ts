import { WasteInquiryStatus } from '@/client'
import type { WasteInquirySapDetailDto } from '@/models/waste-inquiry/sap-detail/wasteInquirySapDetailDto.model'

const EMPTY_SAP_INQUIRY_DETAIL: WasteInquirySapDetailDto = {
  expectedEndDate: null,
  firstCollectionDate: null,
  submittedOn: null,
  isLoadingByIndaver: null,
  isRegulatedTransport: null,
  isSampleAvailable: null,
  isTankOwnedByCustomer: null,
  isTransportByIndaver: null,
  isUnknownPickUpAddress: false,
  isUnknownWasteProducer: false,
  additionalFiles: [],
  analysisReportFiles: [],
  averageStableTemperature: null,
  collectionRemarks: null,
  collectionRequirements: null,
  composition: [],
  containerLoadingType: null,
  contractItem: null,
  contractNumber: null,
  createdBy: null,
  customer: null,
  dischargeFrequency: null,
  ewcLevel1Name: null,
  ewcLevel2Name: null,
  ewcLevel3Name: null,
  expectedPerCollectionQuantity: null,
  expectedPerCollectionUnit: null,
  expectedYearlyVolumeAmount: null,
  expectedYearlyVolumeUnit: null,
  flashpoint: null,
  hazardInducer1: null,
  hazardInducer2: null,
  hazardInducer3: null,
  inquiryNumber: null,
  legislationRemarks: null,
  loadingMethod: null,
  loadingType: null,
  maxStableTemperature: null,
  minStableTemperature: null,
  noAnalysisReport: false,
  noSds: false,
  packaging: [],
  packagingType: null,
  ph: null,
  pickUpAddress: null,
  propertyRemarks: null,
  remarks: null,
  sdsFiles: [],
  selectedLegislationOptions: [],
  selectedPropertyOptions: [],
  sendCopyToContacts: [],
  specificGravity: null,
  stableTemperatureType: null,
  stateOfMatter: null,
  status: WasteInquiryStatus.IN_PROGRESS,
  storedIn: null,
  svhcExtra: null,
  transportIn: null,
  transportType: null,
  transportVolumeAmount: null,
  transportVolumeUnit: null,
  unNumbers: [],
  wasteProducer: null,
  wasteStreamDescription: null,
  wasteStreamName: null,
}

export class WasteInquirySapDetailDtoBuilder {
  private value: WasteInquirySapDetailDto = EMPTY_SAP_INQUIRY_DETAIL
  constructor() {
  }

  build(): WasteInquirySapDetailDto {
    return this.value
  }
}
