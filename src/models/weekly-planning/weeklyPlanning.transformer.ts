import { CustomerIndexTransformer } from '@/models/customer/customer.transformer'
import { CalendarDateTransformer } from '@/models/date/calendarDate.transformer'
import { CalendarTimeTransformer } from '@/models/date/calendarTime.transformer'
import type { CalendarTimeDto } from '@/models/date/calendarTimeDto.model'
import type { PickUpAddressIndexDto } from '@/models/pick-up-address/index/pickUpAddressIndexDto.model'
import { PickUpAddressIndexTransformer } from '@/models/pick-up-address/pickUpAddress.transformer'
import { S3FileArrayTransformer } from '@/models/s3-file/s3File.transformer'
import { WasteProducerIndexTransformer } from '@/models/waste-producer/wasteProducer.transformer'
import type { WeeklyPlanningCreateDto } from '@/models/weekly-planning/create/weeklyPlanningCreateDto.model'
import type {
  WeeklyPlanningDetail,
  WeeklyPlanningDetailCustomerAndLocationStep,
  WeeklyPlanningDetailExtraInfoStep,
  WeeklyPlanningDetailSubmitStep,
  WeeklyPlanningDetailWasteStep,
} from '@/models/weekly-planning/detail/weeklyPlanningDetail.model'
import type { WeeklyPlanningDetailDto } from '@/models/weekly-planning/detail/weeklyPlanningDetailDto.model'
import type { WeeklyPlanningSubmitRequestForm } from '@/models/weekly-planning/update/submit/weeklyPlanningSubmitForm.model'
import type { WeeklyPlanningSubmitResponse } from '@/models/weekly-planning/update/submit/weeklyPlanningSubmitResponse.model'
import type { WeeklyPlanningSubmitResponseDto } from '@/models/weekly-planning/update/submit/weeklyPlanningSubmitResponseDto.model'
import type { WeeklyPlanningSubmitDto } from '@/models/weekly-planning/update/weeklyPlanningSubmitDto.model'
import type {
  WeeklyPlanningCustomerAndLocationStepUpdateDto,
  WeeklyPlanningExtraInfoStepUpdateDto,
  WeeklyPlanningUpdateDto,
} from '@/models/weekly-planning/update/weeklyPlanningUpdateDto.model'
import type { WeeklyPlanningUpdateForm } from '@/models/weekly-planning/update/weeklyPlanningUpdateForm.model'
import type { WeeklyPlanningUuid } from '@/models/weekly-planning/weeklyPlanningUuid.model'
import { StringUtil } from '@/utils/string.util'

export class WeeklyPlanningCreateTransformer {
  static fromDto(dto: WeeklyPlanningCreateDto): WeeklyPlanningUuid {
    return dto.uuid as WeeklyPlanningUuid
  }
}

export class WeeklyPlanningSubmitTransformer {
  static toDto(form: WeeklyPlanningSubmitRequestForm): WeeklyPlanningSubmitDto {
    return { sendCopyToContacts: form.contacts.filter((contact) => StringUtil.trimOrNull(contact.email) !== null) }
  }
}
export class WeeklyPlanningSubmitResponseTransformer {
  static fromDto(dto: WeeklyPlanningSubmitResponseDto): WeeklyPlanningSubmitResponse {
    return {
      uuid: dto.uuid as WeeklyPlanningUuid,
      submittedOn: CalendarDateTransformer.fromDto(dto.submittedOn),
      inquiryNumber: dto.inquiryNumber,
    }
  }
}

export class WeeklyPlanningUpdateTransformer {
  static mapCustomerAndLocationStep(
    form: Partial<WeeklyPlanningUpdateForm>,
  ): WeeklyPlanningCustomerAndLocationStepUpdateDto {
    return {
      customerId: form.customer?.id ?? null,
      wasteProducerId: form.wasteProducer?.id ?? null,
      pickUpAddressIds: form.pickupAddresses?.map((address) => address.id) ?? [],
    }
  }

  static mapExtraInfoStep(
    form: Partial<WeeklyPlanningUpdateForm>,
  ): WeeklyPlanningExtraInfoStepUpdateDto {
    return {
      additionalFiles: S3FileArrayTransformer.toArrayDto(form.additionalFiles ?? []),
      remarks: form.remarks,
    }
  }

  static toDto(form: Partial<WeeklyPlanningUpdateForm>): WeeklyPlanningUpdateDto {
    return {
      ...this.mapCustomerAndLocationStep(form),
      ...this.mapExtraInfoStep(form),
    }
  }
}

export class WeeklyPlanningDetailTransformer {
  static fromDto(dto: WeeklyPlanningDetailDto): WeeklyPlanningDetail {
    return {
      uuid: dto.uuid as WeeklyPlanningUuid,
      createdAt: CalendarDateTransformer.fromDto(dto.createdAt),
      updatedAt: CalendarDateTransformer.fromDto(dto.updatedAt),
      ...this.mapCustomerAndLocationStep(dto),
      ...this.mapWasteStep(dto),
      ...this.mapExtraInfoStep(dto),
      ...this.mapSubmitStep(dto),
    }
  }

  static mapCustomerAndLocationStep(dto: WeeklyPlanningDetailDto): WeeklyPlanningDetailCustomerAndLocationStep {
    return {
      customer: CustomerIndexTransformer.fromNullableDto(dto.customer),
      pickUpAddresses: dto.pickUpAddresses.map(
        (address: PickUpAddressIndexDto) => PickUpAddressIndexTransformer.fromDto(address),
      ),
      wasteProducer: WasteProducerIndexTransformer.fromNullableDto(dto.wasteProducer),
    }
  }

  static mapExtraInfoStep(dto: WeeklyPlanningDetailDto): WeeklyPlanningDetailExtraInfoStep {
    return {
      additionalFiles: S3FileArrayTransformer.fromDtoArray(dto.additionalFiles),
      remarks: dto.remarks,
    }
  }

  static mapSubmitStep(dto: WeeklyPlanningDetailDto): WeeklyPlanningDetailSubmitStep {
    return {
      contacts: dto.sendCopyToContacts.map((contact) => ({
        email: contact.email,
        firstName: contact.firstName,
        lastName: contact.lastName,
      })),
    }
  }

  static mapWasteStep(dto: WeeklyPlanningDetailDto): WeeklyPlanningDetailWasteStep {
    return {
      pickUpRequests: dto.pickUpRequests.map((pickUpRequest) => ({
        uuid: pickUpRequest.uuid,
        contractLineId: pickUpRequest.contractLineId,
        customerId: pickUpRequest.customerId,
        endTreatmentCenterId: pickUpRequest.endTreatmentCenterId,
        pickupAddressId: pickUpRequest.pickUpAddressId,
        wasteProducerId: pickUpRequest.wasteProducerId,
        createdAt: CalendarDateTransformer.fromDto(pickUpRequest.createdAt),
        startDate: CalendarDateTransformer.fromNullableDto(pickUpRequest.startDate),
        startTime: CalendarTimeTransformer.fromDto(pickUpRequest.startTime as CalendarTimeDto),
        updatedAt: CalendarDateTransformer.fromDto(pickUpRequest.updatedAt),
        isHazardous: pickUpRequest.isHazardous,
        asn: pickUpRequest.asn,
        contractItem: pickUpRequest.contractItem,
        contractNumber: pickUpRequest.contractNumber,
        customerName: pickUpRequest.customerName,
        customerReference: pickUpRequest.customerReference,
        deliveryInfo: pickUpRequest.deliveryInfo,
        endTreatmentCenterName: pickUpRequest.endTreatmentCenterName,
        esnNumber: pickUpRequest.esnNumber,
        ewcCode: pickUpRequest.ewcCode,
        installationName: pickUpRequest.installationName,
        materialAnalysis: pickUpRequest.materialAnalysis,
        materialNumber: pickUpRequest.materialNumber,
        materialType: pickUpRequest.materialType,
        packaged: pickUpRequest.packaged,
        packagingIndicator: pickUpRequest.packagingIndicator,
        pickupAddressName: pickUpRequest.pickUpAddressName,
        processCode: pickUpRequest.processCode,
        remarks: pickUpRequest.remarks,
        tcNumber: pickUpRequest.tcNumber,
        tfs: pickUpRequest.tfs,
        treatmentCenterName: pickUpRequest.treatmentCenterName,
        wasteMaterial: pickUpRequest.wasteMaterial,
        wasteProducerName: pickUpRequest.wasteProducerName,
      })),
    }
  }
}
