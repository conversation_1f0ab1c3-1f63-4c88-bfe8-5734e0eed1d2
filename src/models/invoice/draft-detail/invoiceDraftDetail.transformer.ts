import { CalendarDateTransformer } from '@/models/date/calendarDate.transformer.ts'
import type { InvoiceDraftDetail } from '@/models/invoice/draft-detail/invoiceDraftDetail.model.ts'
import type { InvoiceDraftDetailDto } from '@/models/invoice/draft-detail/invoiceDraftDetailDto.model.ts'

export class InvoiceDraftDetailTransformer {
  static fromDto(dto: InvoiceDraftDetailDto): InvoiceDraftDetail {
    return {
      customerId: dto.customerId,
      payerId: dto.payerId,
      autoApprovedOn: CalendarDateTransformer.fromNullableDto(dto.autoApprovedOn),
      customerApprovalDate: CalendarDateTransformer.fromNullableDto(dto.customerApprovalDate),
      firstReminderOn: CalendarDateTransformer.fromNullableDto(dto.firstReminderOn),
      issuedOn: CalendarDateTransformer.fromDto(dto.issuedOn),
      secondReminderOn: CalendarDateTransformer.fromNullableDto(dto.secondReminderOn),
      accountDocumentNumber: dto.accountDocumentNumber ?? null,
      currency: dto.currency,
      customerApprovalBy: dto.customerApprovalBy,
      firstReminderMailStatus: dto.firstReminderMailStatus,
      invoiceNumber: dto.invoiceNumber,
      netAmount: dto.netAmount,
      payerName: dto.payerName,
      poNumber: dto.poNumber ?? null,
      secondReminderMailStatus: dto.secondReminderMailStatus,
      status: dto.status,
      thirdReminderMailStatus: dto.thirdReminderMailStatus,
      vatAmount: dto.vatAmount,
    }
  }
}
