import type { ViewDraftInvoiceDetailResponse } from '@/client/types.gen'
import {
  DraftInvoiceStatus,
  MailStatus,
} from '@/client/types.gen'
import { CalendarDateTransformer } from '@/models/date/calendarDate.transformer.ts'

const DEFAULT_INVOICE_DRAFT_DETAIL: ViewDraftInvoiceDetailResponse = {
  customerId: 'CUST-1',
  payerId: 'PAYER-1',
  autoApprovedOn: null,
  customerApprovalDate: null,
  firstReminderOn: null,
  issuedOn: CalendarDateTransformer.toDto(new Date()),
  secondReminderOn: null,
  accountDocumentNumber: null,
  currency: 'EUR',
  customerApprovalBy: null,
  firstReminderMailStatus: MailStatus.NOT_SENT,
  invoiceNumber: 'DR-0001',
  netAmount: '1000.00',
  payerName: 'Payer Name',
  poNumber: null,
  secondReminderMailStatus: MailStatus.NOT_SENT,
  status: DraftInvoiceStatus.TO_BE_APPROVED_BY_CUSTOMER,
  thirdReminderMailStatus: MailStatus.NOT_SENT,
  vatAmount: '210.00',
}

export class InvoiceDraftDetailDtoBuilder {
  private value: ViewDraftInvoiceDetailResponse = { ...DEFAULT_INVOICE_DRAFT_DETAIL }

  build(): ViewDraftInvoiceDetailResponse {
    return this.value
  }

  withCurrency(currency: string): this {
    this.value.currency = currency

    return this
  }

  withInvoiceNumber(invoiceNumber: string): this {
    this.value.invoiceNumber = invoiceNumber

    return this
  }

  withIssuedOn(date: Date): this {
    this.value.issuedOn = CalendarDateTransformer.toDto(date)

    return this
  }

  withNetAmount(amount: string): this {
    this.value.netAmount = amount

    return this
  }

  withStatus(status: DraftInvoiceStatus): this {
    this.value.status = status

    return this
  }

  withVatAmount(amount: string): this {
    this.value.vatAmount = amount

    return this
  }
}
