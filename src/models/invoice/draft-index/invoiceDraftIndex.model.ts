import type {
  DraftInvoiceStatus,
  MailStatus,
} from '@/client'
import type { CalendarDate } from '@/models/date/calendarDate.model'

export interface InvoiceDraftIndex {
  customerId: string
  payerId: string
  autoApprovedOn: CalendarDate | null
  customerApprovalDate: CalendarDate | null
  firstReminderOn: CalendarDate | null
  issuedOn: CalendarDate
  secondReminderOn: CalendarDate | null
  accountDocumentNumber: string | null
  currency: string
  customerApprovalBy: string | null
  firstReminderMailStatus: MailStatus
  invoiceNumber: string
  netAmount: string
  payerName: string
  poNumber: string | null
  secondReminderMailStatus: MailStatus
  status: DraftInvoiceStatus
  thirdReminderMailStatus: MailStatus
  vatAmount: string
}

export enum InvoiceIndexDraftTableTabs {
  APPROVED = 'approved',
  REJECTED = 'rejected',
  TO_BE_APPROVED = 'toBeApproved',
}
