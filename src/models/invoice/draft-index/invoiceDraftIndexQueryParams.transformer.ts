import type { ViewDraftInvoiceIndexV1Data } from '@/client'
import { CalendarDateRangeTransformer } from '@/models/date/calendarDate.transformer'
import type { InvoiceDraftIndexQueryParams } from '@/models/invoice/draft-index/invoiceDraftIndexQueryParams.model'
import type { OffsetPagination } from '@/types/pagination.type'

export class InvoiceDraftIndexQueryParamsTransformer {
  static toDto(options: OffsetPagination<InvoiceDraftIndexQueryParams>): ViewDraftInvoiceIndexV1Data['query'] {
    return {
      filter: {
        customerId: options.filters.customerId?.id || undefined,
        payerId: options.filters.payerId?.id || undefined,
        issuedOn: CalendarDateRangeTransformer.toDto(options.filters.issuedOn) ?? undefined,
        invoiceNumber: options.filters.invoiceNumber || undefined,
        statuses: options.filters.statuses,
      },
      pagination: options.pagination,
    }
  }
}
