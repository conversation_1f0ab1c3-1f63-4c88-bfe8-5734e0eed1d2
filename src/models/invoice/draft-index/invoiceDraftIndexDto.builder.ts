import type { DraftInvoiceResponse } from '@/client/types.gen'
import {
  DraftInvoiceStatus,
  MailStatus,
} from '@/client/types.gen'

export class InvoiceDraftIndexDtoBuilder {
  private value: DraftInvoiceResponse = {
    customerId: 'C-1',
    payerId: 'P-1',
    autoApprovedOn: null,
    customerApprovalDate: null,
    firstReminderOn: null,
    issuedOn: '2025-02-01',
    secondReminderOn: null,
    accountDocumentNumber: null,
    currency: 'EUR',
    customerApprovalBy: null,
    firstReminderMailStatus: MailStatus.NOT_SENT,
    invoiceNumber: 'DR-001',
    netAmount: '500.00',
    payerName: 'Payer Draft',
    poNumber: null,
    secondReminderMailStatus: MailStatus.NOT_SENT,
    status: DraftInvoiceStatus.TO_BE_APPROVED_BY_CUSTOMER,
    thirdReminderMailStatus: MailStatus.NOT_SENT,
    vatAmount: '105.00',
  }

  build(): DraftInvoiceResponse {
    return this.value
  }

  withCurrency(currency: string): this {
    this.value.currency = currency

    return this
  }

  withInvoiceNumber(invoiceNumber: string): this {
    this.value.invoiceNumber = invoiceNumber

    return this
  }

  withIssuedOn(issuesOn: string): this {
    this.value.issuedOn = issuesOn

    return this
  }

  withNetAmount(netAmount: string): this {
    this.value.netAmount = netAmount

    return this
  }

  withPayerName(payerName: string): this {
    this.value.payerName = payerName

    return this
  }

  withVatAmount(vatAmount: string): this {
    this.value.vatAmount = vatAmount

    return this
  }
}
