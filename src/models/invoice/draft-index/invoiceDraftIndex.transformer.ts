import { CalendarDateTransformer } from '@/models/date/calendarDate.transformer.ts'
import type { InvoiceDraftIndex } from '@/models/invoice/draft-index/invoiceDraftIndex.model.ts'
import type { InvoiceDraftIndexDto } from '@/models/invoice/draft-index/invoiceDraftIndexDto.model.ts'

export class InvoiceDraftIndexTransformer {
  static fromDto(dto: InvoiceDraftIndexDto): InvoiceDraftIndex {
    return {
      customerId: dto.customerId,
      payerId: dto.payerId,
      autoApprovedOn: CalendarDateTransformer.fromNullableDto(dto.autoApprovedOn),
      customerApprovalDate: CalendarDateTransformer.fromNullableDto(dto.customerApprovalDate),
      firstReminderOn: CalendarDateTransformer.fromNullableDto(dto.firstReminderOn),
      issuedOn: CalendarDateTransformer.fromDto(dto.issuedOn),
      secondReminderOn: CalendarDateTransformer.fromNullableDto(dto.secondReminderOn),
      accountDocumentNumber: dto.accountDocumentNumber,
      currency: dto.currency,
      customerApprovalBy: dto.customerApprovalBy,
      firstReminderMailStatus: dto.firstReminderMailStatus,
      invoiceNumber: dto.invoiceNumber,
      netAmount: dto.netAmount,
      payerName: dto.payerName,
      poNumber: dto.poNumber,
      secondReminderMailStatus: dto.secondReminderMailStatus,
      status: dto.status,
      thirdReminderMailStatus: dto.thirdReminderMailStatus,
      vatAmount: dto.vatAmount,
    }
  }
}
