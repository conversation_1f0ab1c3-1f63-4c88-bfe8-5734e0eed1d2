import type { DraftInvoiceFilterStatus } from '@/client'
import type { CalendarDateRange } from '@/models/date/calendarDateRange.model'
import type { WithFilterQuery } from '@/types/query.type'

export interface InvoiceDraftIndexQueryParams extends WithFilterQuery<{
  customerId: {
    id: string
    name: string
  } | null
  payerId: {
    id: string
    name: string
  } | null
  issuedOn: CalendarDateRange | null
  invoiceNumber: string | undefined
  statuses: DraftInvoiceFilterStatus[]
}> {}
