import type { ExportInvoicesExcelFilterQuery } from '@/client'
import type { InvoiceExportExcelFilter } from '@/models/invoice/export/invoiceExportExcelFilter.model'

export class InvoiceExportExcelFilterTransformer {
  static toDto(model: InvoiceExportExcelFilter): ExportInvoicesExcelFilterQuery {
    return {
      columns: model.columns,
      statuses: model.statuses,
      translatedColumns: model.translatedColumns,
    }
  }
}
