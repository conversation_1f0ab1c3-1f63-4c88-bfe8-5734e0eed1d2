import type { ExportDraftInvoicesExcelFilterQuery } from '@/client'
import type { InvoiceDraftExportExcelFilter } from '@/models/invoice/export/invoiceDraftExportExcelFilter.model'

export class InvoiceDraftExportExcelFilterTransformer {
  static toDto(model: InvoiceDraftExportExcelFilter): ExportDraftInvoicesExcelFilterQuery {
    return {
      columns: model.columns,
      statuses: model.statuses,
      translatedColumns: model.translatedColumns,
    }
  }
}
