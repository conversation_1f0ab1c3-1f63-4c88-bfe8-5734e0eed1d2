import type {
  InvoiceStatus,
  InvoiceType,
} from '@/client'
import type { CalendarDate } from '@/models/date/calendarDate.model'

export interface InvoiceDetail {
  payerId: string
  dueOn: CalendarDate | null
  issuedOn: CalendarDate
  accountDocumentNumber: string | null
  accountManagerName: string | null
  certificateFileName: string | null
  companyName: string
  currency: string
  customerName: string
  customerReference: string | null
  invoiceNumber: string
  netAmount: string
  payerName: string
  status: InvoiceStatus
  type: InvoiceType
  vatAmount: string
}
