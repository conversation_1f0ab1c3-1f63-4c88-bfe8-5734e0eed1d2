import type { ViewInvoiceResponse } from '@/client'
import { CalendarDateTransformer } from '@/models/date/calendarDate.transformer'
import type { InvoiceDetail } from '@/models/invoice/detail/invoiceDetail.model'

export class InvoiceDetailTransformer {
  static fromDto(dto: ViewInvoiceResponse): InvoiceDetail {
    return {
      payerId: dto.payerId,
      dueOn: CalendarDateTransformer.fromNullableDto(dto.dueOn),
      issuedOn: CalendarDateTransformer.fromDto(dto.issuedOn),
      accountDocumentNumber: dto.accountDocumentNumber,
      accountManagerName: dto.accountManagerName,
      certificateFileName: dto.certificateFileName,
      companyName: dto.companyName,
      currency: dto.currency,
      customerName: dto.customerName,
      customerReference: dto.customerReference,
      invoiceNumber: dto.invoiceNumber,
      netAmount: dto.netAmount,
      payerName: dto.payerName,
      status: dto.status,
      type: dto.type,
      vatAmount: dto.vatAmount,
    }
  }
}
