import type { ViewInvoiceIndexV1Data } from '@/client'
import { ViewInvoiceIndexSortKey } from '@/client'
import { CalendarDateRangeTransformer } from '@/models/date/calendarDate.transformer'
import type { InvoiceIndexQueryParams } from '@/models/invoice/index/invoiceIndexQueryParams.model'
import type { OffsetPagination } from '@/types/pagination.type'
import { SortUtil } from '@/utils/sort.util'

export class InvoiceIndexQueryParamsTransformer {
  static toDto(options: OffsetPagination<InvoiceIndexQueryParams>): ViewInvoiceIndexV1Data['query'] {
    return {
      filter: {
        customerId: options.filters.customerId?.id || undefined,
        payerId: options.filters.payerId?.id || undefined,
        dueDate: CalendarDateRangeTransformer.toDto(options.filters.dueDate) ?? undefined,
        issueDate: CalendarDateRangeTransformer.toDto(options.filters.issueDate) ?? undefined,
        accountDocumentNumber: options.filters.accountDocumentNumber || undefined,
        accountManagerName: options.filters.accountManagerName || undefined,
        companyName: options.filters.companyName || undefined,
        customerReference: options.filters.customerReference || undefined,
        invoiceNumber: options.filters.invoiceNumber || undefined,
        statuses: options.filters.statuses,
        type: options.filters.type || undefined,
      },
      pagination: options.pagination,
      sort: SortUtil.toDto(options.sort, {
        dueDate: ViewInvoiceIndexSortKey.DUE_DATE,
        issueDate: ViewInvoiceIndexSortKey.ISSUE_DATE,
        accountDocumentNumber: ViewInvoiceIndexSortKey.ACCOUNT_DOCUMENT_NUMBER,
        companyName: ViewInvoiceIndexSortKey.COMPANY_NAME,
        customerName: ViewInvoiceIndexSortKey.CUSTOMER_NAME,
        customerReference: ViewInvoiceIndexSortKey.CUSTOMER_REFERENCE,
        invoiceNumber: ViewInvoiceIndexSortKey.INVOICE_NUMBER,
        netAmount: ViewInvoiceIndexSortKey.NET_AMOUNT,
        payerName: ViewInvoiceIndexSortKey.PAYER_NAME,
      }),
    }
  }
}
