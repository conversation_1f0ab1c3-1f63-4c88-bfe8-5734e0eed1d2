import type { InvoiceResponse } from '@/client/types.gen'
import {
  InvoiceStatus,
  InvoiceType,
} from '@/client/types.gen'

export class InvoiceIndexDtoBuilder {
  private value: InvoiceResponse = {
    customerId: 'C-1',
    payerId: 'P-1',
    dueOn: '2025-01-31',
    issuedOn: '2025-01-01',
    accountDocumentNumber: null,
    accountManagerName: null,
    companyName: 'Company A',
    currency: 'EUR',
    customerName: 'Customer A',
    customerReference: 'REF-1',
    invoiceNumber: 'INV-001',
    netAmount: '1000.00',
    payerName: 'Payer A',
    status: InvoiceStatus.OUTSTANDING,
    type: InvoiceType.INVOICE,
    vatAmount: '210.00',
  }

  build(): InvoiceResponse {
    return this.value
  }

  withCurrency(currency: string): this {
    this.value.currency = currency

    return this
  }

  withCustomerName(customerName: string): this {
    this.value.customerName = customerName

    return this
  }

  withDueOn(dueOn: string): this {
    this.value.dueOn = dueOn

    return this
  }

  withInvoiceNumber(invoiceNumber: string): this {
    this.value.invoiceNumber = invoiceNumber

    return this
  }

  withIssuedOn(issuedOn: string): this {
    this.value.issuedOn = issuedOn

    return this
  }

  withNetAmount(netAmount: string): this {
    this.value.netAmount = netAmount

    return this
  }

  withPayerName(payerName: string): this {
    this.value.payerName = payerName

    return this
  }

  withStatus(status: InvoiceStatus): this {
    this.value.status = status

    return this
  }

  withType(type: InvoiceType): this {
    this.value.type = type

    return this
  }
}
