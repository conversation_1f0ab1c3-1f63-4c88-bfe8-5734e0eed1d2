import type { InvoiceResponse } from '@/client'
import { CalendarDateTransformer } from '@/models/date/calendarDate.transformer.ts'
import type { InvoiceIndex } from '@/models/invoice/index/invoiceIndex.model.ts'

export class InvoiceIndexTransformer {
  static fromDto(dto: InvoiceResponse): InvoiceIndex {
    return {
      payerId: dto.payerId,
      dueOn: CalendarDateTransformer.fromNullableDto(dto.dueOn),
      issuedOn: CalendarDateTransformer.fromDto(dto.issuedOn),
      accountDocumentNumber: dto.accountDocumentNumber,
      accountManagerName: dto.accountManagerName,
      companyName: dto.companyName,
      currency: dto.currency,
      customerName: dto.customerName,
      customerReference: dto.customerReference,
      invoiceNumber: dto.invoiceNumber,
      netAmount: dto.netAmount,
      payerName: dto.payerName,
      status: dto.status,
      type: dto.type,
      vatAmount: dto.vatAmount,
    }
  }
}
