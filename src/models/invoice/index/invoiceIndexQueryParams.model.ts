import type {
  InvoiceFilterType,
  InvoiceStatus,
} from '@/client'
import type { CalendarDateRange } from '@/models/date/calendarDateRange.model'
import type {
  WithFilterQuery,
  WithSortQuery,
} from '@/types/query.type'

export interface InvoiceIndexQueryParams extends WithFilterQuery<{
  customerId: {
    id: string
    name: string
  } | null
  payerId: {
    id: string
    name: string
  } | null
  dueDate: CalendarDateRange | null
  issueDate: CalendarDateRange | null
  accountDocumentNumber: string | undefined
  accountManagerName: string | undefined
  companyName: string | undefined
  customerReference: string | undefined
  invoiceNumber: string | undefined
  statuses: InvoiceStatus[]
  type: InvoiceFilterType | undefined
}>,
  WithSortQuery<
    'accountDocumentNumber' |
    'companyName' |
    'customerName' |
    'customerReference' |
    'dueDate' |
    'invoiceNumber' |
    'issueDate' |
    'netAmount' |
    'payerName'
  > {}
