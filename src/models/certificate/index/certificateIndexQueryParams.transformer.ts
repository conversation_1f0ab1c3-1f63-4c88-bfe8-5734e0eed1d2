import type { ViewCertificateIndexV1Data } from '@/client'
import { ViewCertificateIndexSortKey } from '@/client'
import type { CertificateIndexQueryParams } from '@/models/certificate/index/certificateIndexQueryParams.model'
import { CalendarDateRangeTransformer } from '@/models/date/calendarDate.transformer'
import type { OffsetPagination } from '@/types/pagination.type'
import { SortUtil } from '@/utils/sort.util'

export class CertificateIndexQueryParamsTransformer {
  static toDto(params: OffsetPagination<CertificateIndexQueryParams>): ViewCertificateIndexV1Data['query'] {
    return {
      filter: {
        customerId: params.filters.customerId?.id || undefined,
        wasteProducerId: params.filters.wasteProducerId?.id || undefined,
        collectionDate: CalendarDateRangeTransformer.toDto(params.filters.collectionDate) ?? undefined,
        deliveryDate: CalendarDateRangeTransformer.toDto(params.filters.deliveryDate) ?? undefined,
        disposalDate: CalendarDateRangeTransformer.toDto(params.filters.disposalDate) ?? undefined,
        dispositionDeliveryDate: CalendarDateRangeTransformer.toDto(
          params.filters.dispositionDeliveryDate,
        ) ?? undefined,
        dispositionPickUpDate: CalendarDateRangeTransformer.toDto(params.filters.dispositionPickUpDate) ?? undefined,
        printDate: CalendarDateRangeTransformer.toDto(params.filters.printDate) ?? undefined,
        contract: params.filters.contract || undefined,
        contractItem: params.filters.contractItem || undefined,
        description: params.filters.description || undefined,
        docTypes: params.filters.docTypes || undefined,
        endTreatmentCentre: params.filters.endTreatmentCentre || undefined,
        ewc: params.filters.ewc?.value || undefined,
        invoice: params.filters.invoice?.id || undefined,
        salesOrder: params.filters.salesOrder || undefined,
        salesOrderLine: params.filters.salesOrderLine || undefined,
        tfs: params.filters.tfs || undefined,
        treatmentCentre: params.filters.treatmentCentre || undefined,
        wtfForm: params.filters.wtfForm || undefined,
      },
      pagination: params.pagination,
      sort: SortUtil.toDto(params.sort, {
        customerId: ViewCertificateIndexSortKey.CUSTOMER_ID,
        wasteProducerId: ViewCertificateIndexSortKey.WASTE_PRODUCER_ID,
        collectionDate: ViewCertificateIndexSortKey.COLLECTION_DATE,
        deliveryDate: ViewCertificateIndexSortKey.DELIVERY_DATE,
        disposalDate: ViewCertificateIndexSortKey.DISPOSAL_DATE,
        dispositionDeliveryDate: ViewCertificateIndexSortKey.DISPOSITION_DELIVERY_DATE,
        dispositionPickUpDate: ViewCertificateIndexSortKey.DISPOSITION_PICK_UP_DATE,
        printDate: ViewCertificateIndexSortKey.PRINT_DATE,
        contract: ViewCertificateIndexSortKey.CONTRACT,
        contractItem: ViewCertificateIndexSortKey.CONTRACT_ITEM,
        endTreatmentCentre: ViewCertificateIndexSortKey.END_TREATMENT_CENTRE,
        ewc: ViewCertificateIndexSortKey.EWC,
        invoice: ViewCertificateIndexSortKey.INVOICE,
        pickupAddress: ViewCertificateIndexSortKey.PICK_UP_ADDRESS,
        salesOrder: ViewCertificateIndexSortKey.SALES_ORDER,
        salesOrderLine: ViewCertificateIndexSortKey.SALES_ORDER_LINE,
        tfs: ViewCertificateIndexSortKey.TFS,
        treatmentCentre: ViewCertificateIndexSortKey.TREATMENT_CENTRE,
        wtfForm: ViewCertificateIndexSortKey.WTF_FORM,
      }),
    }
  }
}
