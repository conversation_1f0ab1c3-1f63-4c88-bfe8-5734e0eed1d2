import type { CertificateDocType } from '@/client'
import type { CalendarDateRange } from '@/models/date/calendarDateRange.model'
import type {
  WithFilterQuery,
  WithSortQuery,
} from '@/types/query.type'

export interface CertificateIndexQueryParams extends WithFilterQuery<{
  customerId: {
    id: string
    name: string
  } | null
  wasteProducerId: {
    id: string
    name: string
  } | null
  collectionDate: CalendarDateRange | null
  deliveryDate: CalendarDateRange | null
  disposalDate: CalendarDateRange | null
  dispositionDeliveryDate: CalendarDateRange | null
  dispositionPickUpDate: CalendarDateRange | null
  printDate: CalendarDateRange | null
  contract: string | undefined
  contractItem: string | undefined
  description: string | undefined
  docTypes: CertificateDocType[]
  endTreatmentCentre: string | undefined
  ewc: {
    label: string
    value: string
  } | null
  invoice: {
    id: string
    name: string
  } | null
  salesOrder: string | undefined
  salesOrderLine: string | undefined
  tfs: string | undefined
  treatmentCentre: string | undefined
  wtfForm: string | undefined
}>,
  WithSortQuery<
    'collectionDate' |
    'contract' |
    'contractItem' |
    'customerId' |
    'deliveryDate' |
    'disposalDate' |
    'dispositionDeliveryDate' |
    'dispositionPickUpDate' |
    'endTreatmentCentre' |
    'ewc' |
    'invoice' |
    'pickupAddress' |
    'printDate' |
    'salesOrder' |
    'salesOrderLine' |
    'tfs' |
    'treatmentCentre' |
    'wasteProducerId' |
    'wtfForm'
  > {}
