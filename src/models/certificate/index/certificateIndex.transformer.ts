import type { CertificateIndexResponse } from '@/client'
import type { CertificateIndex } from '@/models/certificate/index/certificateIndex.model'
import { CalendarDateTimeTransformer } from '@/models/date/calendarDateTime.transformer'

export class CertificateIndexTransformer {
  static fromDto(dto: CertificateIndexResponse): CertificateIndex {
    return {
      customerId: dto.customerId,
      pickUpAddressId: dto.pickUpAddressId,
      wasteProducerId: dto.wasteProducerId,
      collectionDate: CalendarDateTimeTransformer.fromNullableDto(dto.collectionDate),
      deliveryDate: CalendarDateTimeTransformer.fromNullableDto(dto.deliveryDate),
      disposalDate: CalendarDateTimeTransformer.fromNullableDto(dto.disposalDate),
      dispositionDeliveryDate: CalendarDateTimeTransformer.fromNullableDto(dto.dispositionDeliveryDate),
      dispositionPickUpDate: CalendarDateTimeTransformer.fromNullableDto(dto.dispositionPickUpDate),
      printDate: CalendarDateTimeTransformer.fromNullableDto(dto.printDate),
      contract: dto.contract,
      contractItem: dto.contractItem,
      customerName: dto.customerName,
      description: dto.description,
      docType: dto.docType,
      endTreatmentCentre: dto.endTreatmentCentre,
      ewcCode: dto.ewcCode,
      fileName: dto.fileName,
      invoice: dto.invoice,
      pickUpAddressName: dto.pickUpAddressName,
      salesOrder: dto.salesOrder,
      salesOrderLine: dto.salesOrderLine,
      tfs: dto.tfs,
      treatmentCentre: dto.treatmentCentre,
      wasteProducerName: dto.wasteProducerName,
      wtfForm: dto.wtfForm,

    }
  }
}
