import type { CertificateDocType } from '@/client'
import type { CalendarDateTime } from '@/models/date/calendarDateTime.model'

export interface CertificateIndex {
  customerId: string | null
  pickUpAddressId: string | null
  wasteProducerId: string | null
  collectionDate: CalendarDateTime | null
  deliveryDate: CalendarDateTime | null
  disposalDate: CalendarDateTime | null
  dispositionDeliveryDate: CalendarDateTime | null
  dispositionPickUpDate: CalendarDateTime | null
  printDate: CalendarDateTime | null
  contract: string | null
  contractItem: string | null
  customerName: string | null
  description: string | null
  docType: CertificateDocType | null
  endTreatmentCentre: string | null
  ewcCode: string | null
  fileName: string
  invoice: string | null
  pickUpAddressName: string | null
  salesOrder: string | null
  salesOrderLine: string | null
  tfs: string | null
  treatmentCentre: string | null
  wasteProducerName: string | null
  wtfForm: string | null
}
