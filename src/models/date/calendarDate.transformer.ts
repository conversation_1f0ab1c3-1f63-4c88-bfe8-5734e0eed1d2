import dayjs from 'dayjs'

import type { DateRange } from '@/client'
import type { CalendarDate } from '@/models/date/calendarDate.model.ts'
import type { CalendarDateDto } from '@/models/date/calendarDateDto.model.ts'
import type { CalendarDateForm } from '@/models/date/calendarDateForm.model'
import type { CalendarDateRange } from '@/models/date/calendarDateRange.model'

export class CalendarDateFormTransformer {
  static toDateTimeDto(value: CalendarDateForm): CalendarDateDto {
    return new Date(value).toISOString() as CalendarDateDto
  }
}

// 2024-05-24

export class CalendarDateTransformer {
  static fromDate(date: Date): CalendarDate {
    return new Date(date) as CalendarDate
  }

  static fromDto(date: string): CalendarDate {
    return new Date(date) as CalendarDate
  }

  static fromNullableDto(date: string | null): CalendarDate | null {
    return date === null ? null : this.fromDto(date)
  }

  static toDto(date: CalendarDate): CalendarDateDto {
    return dayjs(date).format('YYYY-MM-DD') as CalendarDateDto
  }

  static toNullableDto(date: CalendarDate | null): CalendarDateDto | null {
    return date === null ? null : this.toDto(date)
  }
}

export class CalendarDateRangeTransformer {
  static toDto(range: CalendarDateRange | null): DateRange | null {
    if (range === null || range.from === null || range.until === null) {
      return null
    }

    return {
      from: CalendarDateTransformer.toDto(range.from),
      to: CalendarDateTransformer.toDto(range.until),
    }
  }
}
