import type { PickUpRequestTemplateResponse } from '@/client'
import { CalendarDateTimeTransformer } from '@/models/date/calendarDateTime.transformer'
import type { PickupRequestTemplateIndex } from '@/models/pickup-request-template/index/pickupRequestTemplateIndex.model'
import type { PickupRequestTemplateUuid } from '@/models/pickup-request-template/pickupRequestTemplateUuid.model'

export class PickupRequestTemplateIndexTransformer {
  static fromDto(dto: PickUpRequestTemplateResponse): PickupRequestTemplateIndex {
    return {
      uuid: dto.uuid as PickupRequestTemplateUuid,
      createdAt: CalendarDateTimeTransformer.fromDto(dto.createdAt),
      updatedAt: CalendarDateTimeTransformer.fromDto(dto.updatedAt),
      name: dto.name,
      createdBy: dto.createdBy,
      updatedBy: dto.updatedBy,
    }
  }
}
