import type { ViewPickUpRequestTemplateIndexV1Data } from '@/client'
import { ViewPickUpRequestTemplateIndexSortQueryKey } from '@/client'
import type { PickupRequestTemplateIndexQueryParams } from '@/models/pickup-request-template/index/pickupRequestTemplateIndexQueryParams.model'
import type { OffsetPagination } from '@/types/pagination.type'
import { SearchUtil } from '@/utils/search.util'
import { SortUtil } from '@/utils/sort.util'

export class PickupRequestTemplateIndexQueryParamsTransformer {
  static toDto(params: OffsetPagination<PickupRequestTemplateIndexQueryParams>): ViewPickUpRequestTemplateIndexV1Data['query'] {
    return {
      pagination: params.pagination,
      search: SearchUtil.toDto(params.search),
      sort: SortUtil.toDto(params.sort, {
        createdAt: ViewPickUpRequestTemplateIndexSortQueryKey.CREATED_AT,
        updatedAt: ViewPickUpRequestTemplateIndexSortQueryKey.UPDATED_AT,
        name: ViewPickUpRequestTemplateIndexSortQueryKey.NAME,
        createdBy: ViewPickUpRequestTemplateIndexSortQueryKey.CREATED_BY,
        updatedBy: ViewPickUpRequestTemplateIndexSortQueryKey.UPDATED_BY,
      }),
    }
  }
}
