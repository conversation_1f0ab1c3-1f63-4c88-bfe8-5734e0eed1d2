import type { ViewPickUpRequestTemplateResponse } from '@/client'
import { PickupRequestDetailTransformer } from '@/models/pickup-request/detail/pickupRequestDetail.transformer'
import type { PickupRequestTemplateDetail } from '@/models/pickup-request-template/detail/pickupRequestTemplateDetail.model'
import type { PickupRequestTemplateUuid } from '@/models/pickup-request-template/pickupRequestTemplateUuid.model'

export class PickupRequestTemplateDetailTransformer {
  static fromDto(dto: ViewPickUpRequestTemplateResponse): PickupRequestTemplateDetail {
    return {
      ...PickupRequestDetailTransformer.fromDto(dto),
      uuid: dto.uuid as PickupRequestTemplateUuid,
      templateName: dto.templateName,
    }
  }
}
