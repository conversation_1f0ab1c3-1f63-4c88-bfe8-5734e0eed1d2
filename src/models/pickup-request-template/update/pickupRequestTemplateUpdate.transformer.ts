import type { UpdatePickUpRequestTemplateCommand } from '@/client'
import { PickupRequestUpdateTransformer } from '@/models/pickup-request/update/pickupRequestUpdate.transformer'
import type { PickupRequestTemplateUpdateForm } from '@/models/pickup-request-template/update/pickupRequestTemplateUpdateForm.model'

export class PickupRequestTemplateUpdateTransformer {
  static toDto(form: PickupRequestTemplateUpdateForm): UpdatePickUpRequestTemplateCommand {
    const {
      templateName,
      ...pickupRequest
    } = form

    return {
      ...PickupRequestUpdateTransformer.toDto(pickupRequest),
      templateName,
    }
  }
}
