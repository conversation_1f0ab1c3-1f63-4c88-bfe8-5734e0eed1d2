import type { ViewWasteProducerIndexV1Data } from '@/client'
import type { WasteProducerIndexQueryParams } from '@/models/waste-producer/index/wasteProducerIndexQueryParams.model'
import type { OffsetPagination } from '@/types/pagination.type'
import { SearchUtil } from '@/utils/search.util.ts'

export class WasteProducerIndexQueryParamsTransformer {
  static toDto(options: OffsetPagination<WasteProducerIndexQueryParams>): ViewWasteProducerIndexV1Data['query'] {
    return {
      filter: options.filters,
      pagination: options.pagination,
      search: SearchUtil.toDto(options.search),
    }
  }
}
