import type { ViewWprContractLineIndexV1Data } from '@/client'
import type { ContractLineWeeklyPlanningIndexQueryParams } from '@/models/contract-line/weekly-planning/contractLineWeeklyPlanningIndexQueryParams'
import type { OffsetPagination } from '@/types/pagination.type'

export class ContractLineWeeklyPlanningIndexQueryParamsTransformer {
  static toDto(params: OffsetPagination<ContractLineWeeklyPlanningIndexQueryParams>): ViewWprContractLineIndexV1Data['query'] {
    return {
      filter: {
        customerId: params.filters.customerId,
        wasteProducerId: params.filters.wasteProducerId ?? undefined,
        pickUpAddressIds: params.filters.pickUpAddressIds ?? undefined,
      },
      pagination: params.pagination,
    }
  }
}
