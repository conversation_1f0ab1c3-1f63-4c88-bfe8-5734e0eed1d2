import type { PickupRequestUuid } from '@/models/pickup-request/pickupRequestUuid.model'

export interface ContractLineWeeklyPlanningIndex {
  contractLineId: string
  customerId: string | null
  endTreatmentCenterId: string | null
  pickupAddressId: string | null
  pickupRequestUuid: PickupRequestUuid
  wasteProducerId: string | null
  startDate?: string | null
  startTime?: string | null
  isHazardous: boolean | null
  asn: string | null
  contractItem: string
  contractNumber: string
  customerName: string | null
  customerReference: string | null
  deliveryInfo: string | null
  endTreatmentCenterName: string | null
  esnNumber: string | null
  ewcCode: string | null
  installationName: string | null
  materialAnalysis: string | null
  materialNumber: string | null
  packaged: string | null
  pickupAddressName: string | null
  processCode: string | null
  remarks: string | null
  tcNumber: string | null
  tfs: boolean | null
  treatmentCenterName: string | null
  wasteMaterial: string | null
  wasteProducerName: string | null
}
