import type { ViewPackagingRequestContractLineIndexV1Data } from '@/client'
import type { ContractLinePackagingRequestQueryParams } from '@/models/contract-line/packaging-request/contractLinePackagingRequestQueryParams.model'
import type { OffsetPagination } from '@/types/pagination.type'

export class ContractLinePackagingRequestQueryParamsTransformer {
  static toDto(options: OffsetPagination<ContractLinePackagingRequestQueryParams>): ViewPackagingRequestContractLineIndexV1Data['query'] {
    return {
      filter: options.filters,
      pagination: options.pagination,
    }
  }
}
