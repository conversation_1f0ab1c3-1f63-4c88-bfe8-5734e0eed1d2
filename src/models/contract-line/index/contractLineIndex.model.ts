import type {
  ContractLinePackagingType,
  WasteMeasurementUnit,
} from '@/client'

export interface ContractLineIndex {
  contractLineId: string
  customerId: string | null
  endTreatmentCenterId: string | null
  pickupAddressId: string | null
  wasteProducerId: string | null
  isContainerCovered?: boolean | null
  isHazardous: boolean | null
  amount: number | null
  asn: string | null
  containerNumber?: string | null
  containerTransportType?: string | null
  containerType?: string | null
  contractItem: string
  contractNumber: string
  costCenter?: string | null
  customerName: string | null
  customerReference: string | null
  deliveryInfo: string | null
  endTreatmentCenterName: string | null
  esnNumber: string | null
  estimatedWeightOrVolumeUnit?: WasteMeasurementUnit | null
  estimatedWeightOrVolumeValue?: number | null
  ewcCode: string | null
  hazardInducers: string | null
  installationName: string | null
  materialAnalysis: string | null
  materialNumber: string | null
  packaged: ContractLinePackagingType | null
  packagingType?: string | null
  pickupAddressName: string | null
  poNumber?: string | null
  position?: string | null
  processCode: string | null
  quantityLabels?: number | null
  quantityPackages?: number | null
  quantityPallets?: number | null
  reconciliationNumber: string | null
  remarks: string | null
  tankerType?: string | null
  tcNumber: string | null
  tfs: boolean | null
  treatmentCenterName: string | null
  unNumber?: string | null
  wasteMaterial: string | null
  wasteProducerName: string | null
}
