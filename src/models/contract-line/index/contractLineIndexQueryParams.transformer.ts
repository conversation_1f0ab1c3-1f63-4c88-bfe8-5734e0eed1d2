import type { ViewContractLineIndexV1Data } from '@/client'
import { ViewContractLineIndexSortQueryKey } from '@/client'
import type { ContractLineIndexQueryParams } from '@/models/contract-line/index/contractLineIndexQueryParams.model'
import type { OffsetPagination } from '@/types/pagination.type'
import { CustomBooleanFilterUtil } from '@/utils/customBooleanFilter.util'
import { SearchUtil } from '@/utils/search.util'
import { SortUtil } from '@/utils/sort.util'

export class ContractLineIndexQueryParamsTransformer {
  static toDto(params: OffsetPagination<ContractLineIndexQueryParams>): ViewContractLineIndexV1Data['query'] {
    return {
      filter: {
        customerId: params.filters.customerId?.id ?? undefined,
        wasteProducerId: params.filters.wasteProducerId?.id ?? undefined,
        isHazardous: CustomBooleanFilterUtil.toDto(params.filters.isHazardous),
        asn: params.filters.asn || undefined,
        contractItem: params.filters.contractItem || undefined,
        contractNumber: params.filters.contractNumber || undefined,
        customerReference: params.filters.customerReference || undefined,
        deliveryInfo: params.filters.deliveryInfo || undefined,
        endTreatmentCenterName: params.filters.endTreatmentCenterName || undefined,
        esnNumber: params.filters.esnNumber || undefined,
        ewcCode: params.filters.ewcCode?.value ?? undefined,
        installationName: params.filters.installationName || undefined,
        materialAnalysis: params.filters.materialAnalysis || undefined,
        materialNumber: params.filters.materialNumber || undefined,
        packaged: CustomBooleanFilterUtil.toDto(params.filters.packaged),
        pickUpAddressIds: params.filters.pickUpAddressIds !== undefined && params.filters.pickUpAddressIds.length > 0
          ? params.filters.pickUpAddressIds.map((address) => address.id)
          : undefined,
        processCode: params.filters.processCode || undefined,
        tcNumber: params.filters.tcNumber || undefined,
        tfs: CustomBooleanFilterUtil.toDto(params.filters.tfs),
        treatmentCenterName: params.filters.treatmentCenterName || undefined,
        wasteMaterial: params.filters.wasteMaterial || undefined,
      },
      pagination: params.pagination,
      search: SearchUtil.toDto(params.search),
      sort: SortUtil.toDto(params.sort, {
        endTreatmentCenterId: ViewContractLineIndexSortQueryKey.END_TREATMENT_CENTER_ID,
        pickUpAddressId: ViewContractLineIndexSortQueryKey.PICK_UP_ADDRESS_ID,
        wasteProducerId: ViewContractLineIndexSortQueryKey.WASTE_PRODUCER_ID,
        asn: ViewContractLineIndexSortQueryKey.ASN,
        contractItem: ViewContractLineIndexSortQueryKey.CONTRACT_ITEM,
        contractNumber: ViewContractLineIndexSortQueryKey.CONTRACT_NUMBER,
        endTreatmentCenterName: ViewContractLineIndexSortQueryKey.END_TREATMENT_CENTER_NAME,
        esnNumber: ViewContractLineIndexSortQueryKey.ESN_NUMBER,
        ewcCode: ViewContractLineIndexSortQueryKey.EWC_CODE,
        installationName: ViewContractLineIndexSortQueryKey.INSTALLATION_NAME,
        pickUpAddressName: ViewContractLineIndexSortQueryKey.PICK_UP_ADDRESS_NAME,
        processCode: ViewContractLineIndexSortQueryKey.PROCESS_CODE,
        tcNumber: ViewContractLineIndexSortQueryKey.TC_NUMBER,
        treatmentCenterName: ViewContractLineIndexSortQueryKey.TREATMENT_CENTER_NAME,
        wasteMaterial: ViewContractLineIndexSortQueryKey.WASTE_MATERIAL,
        wasteProducerName: ViewContractLineIndexSortQueryKey.WASTE_PRODUCER_NAME,
      }),
    }
  }
}
