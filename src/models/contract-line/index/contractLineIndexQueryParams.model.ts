import type { CustomBooleanFilterValue } from '@/composables/custom-boolean-filter/customBooleanFilter.composable'
import type {
  WithFilterQuery,
  WithSearchQuery,
  WithSortQuery,
} from '@/types/query.type'

export interface ContractLineIndexQueryParams extends WithSearchQuery, WithSortQuery<
  'asn' |
  'contractItem' |
  'contractNumber' |
  'endTreatmentCenterId' |
  'endTreatmentCenterName' |
  'esnNumber' |
  'ewcCode' |
  'installationName' |
  'pickUpAddressId' |
  'pickUpAddressName' |
  'processCode' |
  'tcNumber' |
  'treatmentCenterName' |
  'wasteMaterial' |
  'wasteProducerId' |
  'wasteProducerName'
>, WithFilterQuery<{
    customerId?: {
      id: string
      name: string
    } | null
    wasteProducerId?: {
      id: string
      name: string
    } | null
    isHazardous?: CustomBooleanFilterValue
    asn?: string | null
    contractItem?: string | null
    contractNumber?: string | null
    customerReference?: string | null
    deliveryInfo?: string | null
    endTreatmentCenterName?: string | null
    esnNumber?: string | null
    ewcCode?: {
      label: string
      value: string
    } | null
    installationName?: string | null
    materialAnalysis?: string | null
    materialDescription?: string | null
    materialNumber?: string | null
    packaged?: CustomBooleanFilterValue
    pickUpAddressIds?: {
      id: string
      name: string
    }[]
    processCode?: string | null
    tcNumber?: string | null
    tfs?: CustomBooleanFilterValue
    treatmentCenterName?: string | null
    wasteMaterial?: string | null
    wasteProducerName?: string | null
  }> {}
