import type { ViewContractLineIndexFilterQuery } from '@/client'
import type { ContractLineIndex } from '@/models/contract-line/index/contractLineIndex.model'
import type { ContractLineGeneratePdf } from '@/models/contract-line/pdf/contractLineGeneratePdf.model.ts'
import type { ContractLineGeneratePdfResponse } from '@/models/contract-line/pdf/contractLineGeneratePdfResponse.model'
import type { ContractLineGeneratePdfResponseDto } from '@/models/contract-line/pdf/contractLineGeneratePdfResponseDto.model'

export class ContractLinePdfTransformer {
  static contractLinesToQuerySelectionDto(
    filter: ViewContractLineIndexFilterQuery,
    excludedItems?: ContractLineIndex[],
  ): ContractLineGeneratePdf {
    return {
      querySelection: {
        excludeSelection: excludedItems && excludedItems.length > 0
          ? excludedItems?.map((item) => ({
              contractItem: item.contractItem,
              contractNumber: item.contractNumber,
            }))
          : undefined,
        filter,
      },
    }
  }

  static contractLinesToSelectionDto(items: ContractLineIndex[]): ContractLineGeneratePdf {
    return {
      selection: items.map((item) => ({
        contractItem: item.contractItem,
        contractNumber: item.contractNumber,
      })),
    }
  }

  static fromDto(dto: ContractLineGeneratePdfResponseDto): ContractLineGeneratePdfResponse {
    return {
      name: dto.name,
      content: dto.content,
      mimeType: dto.mimeType,
      pageCount: dto.pageCount,
      size: dto.size,
    }
  }
}
