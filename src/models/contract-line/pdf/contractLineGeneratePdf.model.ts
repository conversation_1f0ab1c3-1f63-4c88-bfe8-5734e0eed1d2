export interface ContractLineGeneratePdf {
  querySelection?: {
    excludeSelection?: {
      contractItem: string
      contractNumber: string
    }[]
    filter: {
      customerId?: string
      endTreatmentCenterId?: string
      wasteProducerId?: string
      isHazardous?: boolean
      asn?: string
      contractItem?: string
      contractNumber?: string
      customerReference?: string
      deliveryInfo?: string
      endTreatmentCenterName?: string
      esnNumber?: string
      ewcCode?: string
      installationName?: string
      materialAnalysis?: string
      materialNumber?: string
      packagingIndicator?: string
      pickUpAddressIds?: Array<string>
      processCode?: string
      tcNumber?: string
      tfs?: boolean
      treatmentCenterName?: string
      wasteMaterial?: string
    }
  }
  selection?: {
    contractItem: string
    contractNumber: string
  }[]
}
