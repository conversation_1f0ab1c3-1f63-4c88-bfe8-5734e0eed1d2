import type { WprContractLineResponse } from '@/client'
import type { ContractLineIndexDto } from '@/models/contract-line/index/contractLineDto.model'
import type { ContractLineIndex } from '@/models/contract-line/index/contractLineIndex.model'
import type {
  ContractLineIndexPagination,
  ContractLinePackagingRequestIndexPagination,
} from '@/models/contract-line/index/contractLineIndexPagination.model.ts'
import type {
  ContractLineIndexPaginationDto,
  ContractLinePackagingRequestIndexPaginationDto,
} from '@/models/contract-line/index/contractLineIndexPaginationDto.model.ts'
import type { ContractLinePackagingRequestIndex } from '@/models/contract-line/packaging-request/contractLinePackagingRequestIndex.model'
import type { ContractLinePackagingRequestIndexDto } from '@/models/contract-line/packaging-request/contractLinePackagingRequestIndexDto.model'
import type { ContractLineWeeklyPlanningIndex } from '@/models/contract-line/weekly-planning/contractLineWeeklyPlanningIndex.model'
import type { PickupRequestUuid } from '@/models/pickup-request/pickupRequestUuid.model'
import type { PickupRequestWasteMaterialForm } from '@/models/pickup-request/update/steps/pickupRequestWasteForm.model'

export class ContractLineIndexTransformer {
  static fromDto(dto: ContractLineIndexDto): ContractLineIndex {
    return {
      contractLineId: dto.contractLineId,
      customerId: dto.customerId,
      endTreatmentCenterId: dto.endTreatmentCenterId,
      pickupAddressId: dto.pickUpAddressId,
      wasteProducerId: dto.wasteProducerId,
      isHazardous: dto.isHazardous,
      amount: null,
      asn: dto.asn,
      contractItem: dto.contractItem,
      contractNumber: dto.contractNumber,
      customerName: dto.customerName,
      customerReference: dto.customerReference,
      deliveryInfo: dto.deliveryInfo,
      endTreatmentCenterName: dto.endTreatmentCenterName,
      esnNumber: dto.esnNumber,
      ewcCode: dto.ewcCode,
      hazardInducers: null,
      installationName: dto.installationName,
      materialAnalysis: dto.materialAnalysis,
      materialNumber: dto.materialNumber,
      packaged: dto.packaged,
      pickupAddressName: dto.pickUpAddressName,
      processCode: dto.processCode,
      reconciliationNumber: null,
      remarks: dto.remarks,
      tcNumber: dto.tcNumber,
      tfs: dto.tfs,
      treatmentCenterName: dto.treatmentCenterName,
      wasteMaterial: dto.wasteMaterial,
      wasteProducerName: dto.wasteProducerName,
    }
  }
}

export class ContractLineIndexPaginationTransformer {
  static toDto(pagination: ContractLineIndexPagination): ContractLineIndexPaginationDto {
    return {
      filter: {
        customerId: pagination.filter?.customerId ?? undefined,
        wasteProducerId: pagination.filter?.wasteProducerId ?? undefined,
        pickUpAddressIds: pagination.filter?.pickUpAddressIds ?? undefined,
      },
      sort: pagination.sort,
    }
  }
}
export class ContractLinePackagingRequestIndexPaginationTransformer {
  static toDto(pagination: ContractLinePackagingRequestIndexPagination):
  ContractLinePackagingRequestIndexPaginationDto {
    return {
      filter: {
        customerId: pagination.filter?.customerId ?? undefined,
        wasteProducerId: pagination.filter?.wasteProducerId ?? undefined,
        deliveryAddressIds: pagination.filter?.deliveryAddress
          ? [
              pagination.filter?.deliveryAddress,
            ]
          : undefined,
      },
      sort: pagination.sort,
    }
  }
}

export class ContractLineWeeklyPlanningIndexTransformer {
  static fromDto(dto: WprContractLineResponse): ContractLineWeeklyPlanningIndex {
    return {
      contractLineId: dto.contractLineId,
      customerId: dto.customerId,
      endTreatmentCenterId: dto.endTreatmentCenterId,
      pickupAddressId: dto.pickUpAddressId,
      pickupRequestUuid: dto.pickUpRequestUuid as PickupRequestUuid,
      wasteProducerId: dto.wasteProducerId,
      isHazardous: dto.isHazardous,
      asn: dto.asn,
      contractItem: dto.contractItem,
      contractNumber: dto.contractNumber,
      customerName: dto.customerName,
      customerReference: dto.customerReference,
      deliveryInfo: dto.deliveryInfo,
      endTreatmentCenterName: dto.endTreatmentCenterName,
      esnNumber: dto.esnNumber,
      ewcCode: dto.ewcCode,
      installationName: dto.installationName,
      materialAnalysis: dto.materialAnalysis,
      materialNumber: dto.materialNumber,
      packaged: dto.packaged,
      pickupAddressName: dto.pickUpAddressName,
      processCode: dto.processCode,
      remarks: dto.remarks,
      tcNumber: dto.tcNumber,
      tfs: dto.tfs,
      treatmentCenterName: dto.treatmentCenterName,
      wasteMaterial: dto.wasteMaterial,
      wasteProducerName: dto.wasteProducerName,
    }
  }
}

export class ContractLineToPickupRequestMaterialTransformer {
  static toPickupMaterial(item: ContractLineIndex): PickupRequestWasteMaterialForm {
    return {
      ...item,
      isContainerCovered: item.isContainerCovered ?? null,
      isCostCenterRequired: false,
      isPoNumberRequired: false,
      amount: null,
      containerNumber: item.containerNumber ?? null,
      containerTransportType: item.containerTransportType ?? null,
      containerType: item.containerType
        ? { name: item.containerType }
        : null,
      containerVolumeSize: null,
      costCenter: item.costCenter ?? null,
      estimatedWeightOrVolumeUnit: item.estimatedWeightOrVolumeUnit ?? null,
      estimatedWeightOrVolumeValue: item.estimatedWeightOrVolumeValue ?? null,
      packagingType: item.packagingType
        ? { name: item.packagingType }
        : null,
      poNumber: item.poNumber ?? null,
      position: item.position ?? null,
      quantityContainers: null,
      quantityLabels: item.quantityLabels ?? null,
      quantityPackages: item.quantityPackages ?? null,
      quantityPallets: item.quantityPallets ?? null,
      reconciliationNumber: item.reconciliationNumber,
      serialNumber: null,
      tankerType: item.tankerType ?? null,
      tfsNumber: null,
      unNumber: item.unNumber
        ? {
            isHazardous: null,
            dangerLabel1: null,
            dangerLabel2: null,
            dangerLabel3: null,
            description: null,
            number: item.unNumber,
            packingGroup: null,
          }
        : null,
    }
  }
}

export class ContractLinePackagingRequestIndexTransformer {
  static fromDto(dto: ContractLinePackagingRequestIndexDto): ContractLinePackagingRequestIndex {
    return {
      contractLineId: dto.contractLineId,
      isSales: dto.isSales,
      amount: null,
      contractItem: dto.contractItem,
      contractNumber: dto.contractNumber,
      imageUrl: dto.imageUrl,
      materialNumber: dto.materialNumber,
      wasteMaterial: dto.wasteMaterial,
    }
  }
}
