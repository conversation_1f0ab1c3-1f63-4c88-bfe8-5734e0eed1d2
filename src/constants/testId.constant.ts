export const TEST_ID = {
  APP_PAGE: {
    TITLE: 'app-page-title',
    USER_BUTTON: 'app-page-user-button',
    USER_NAME: 'app-page-user-name',
  },
  AUTH: {
    CALLBACK: 'auth-callback',
    LEGAL_LINKS: { DISCLAIMER: 'auth-legal-links-disclaimer' },
    LOGIN: {
      EMAIL_INPUT: 'login-form-email-input',
      PASSWORD_INPUT: 'login-form-password-input',
      SUBMIT_BUTTON: 'login-form-submit-button',
    },
  },
  GUIDANCE_LETTERS: {
    ANNOUNCEMENTS_TABLE: {
      ACTIONS: {
        DOWNLOAD_ATTACHMENT: 'guidance-letters-announcements-table-actions-download-attachment',
        DOWNLOAD_PREVIEW: 'guidance-letters-announcements-table-actions-download-preview',
        DOWNLOAD_PRINT: 'guidance-letters-announcements-table-actions-download-print',
        MENU: 'guidance-letters-announcements-table-actions-menu',
      },
    },
  },
  HEADER: { SETTINGS_BUTTON: 'header-settings-button' },
  INVOICES: {
    DETAIL: { EDIT_BUTTON: 'invoice-detail-edit-button' },
    FORM: {
      SUBMIT_BUTTON: 'invoice-create-form-submit-button',
      UPDATE_SUBMIT_BUTTON: 'invoice-update-form-submit-button',
    },
    OVERVIEW: { CREATE_BUTTON: 'invoice-overview-create-button' },
    REVIEW: {
      FORM: {
        COMMENT_FIELD: 'invoice-review-form-comment-field',
        SUBMIT_BUTTON: 'invoice-review-form-submit-button',
      },
      REVIEW_DIALOG: 'invoice-review-dialog',
    },
    TABLE: {
      ACTIONS_DOWNLOAD: 'invoice-overview-table-actions-download',
      CONTAINER: 'invoice-overview-table-container',
      CURRENCY: 'invoice-overview-table-currency',
      CUSTOMER_NAME: 'invoice-overview-table-customer-name',
      DUE_ON: 'invoice-overview-table-due-on',
      FIRST_REMINDER_MAIL_STATUS: 'invoice-overview-table-first-reminder-mail-status',
      FIRST_REMINDER_ON: 'invoice-overview-table-first-reminder-on',
      INVOICE_NUMBER: 'invoice-overview-table-invoice-number',
      ISSUED_ON: 'invoice-overview-table-issued-on',
      NAME_LINK: 'invoice-overview-table-name-link',
      NET_AMOUNT: 'invoice-overview-table-net-amount',
      PAYER_NAME: 'invoice-overview-table-payer-name',
      SECOND_REMINDER_MAIL_STATUS: 'invoice-overview-table-second-reminder-mail-status',
      SECOND_REMINDER_ON: 'invoice-overview-table-second-reminder-on',
      STATUS: 'invoice-overview-table-status',
      THIRD_REMINDER_MAIL_STATUS: 'invoice-overview-table-third-reminder-status',
      TYPE: 'invoice-overview-table-type',
      VAT_AMOUNT: 'invoice-overview-table-vat-amount',
    },
  },
  NEWS: {
    ANNOUNCEMENT_CREATE_BUTTON: 'news-announcement-create-button',
    ANNOUNCEMENT_UPDATE: {
      CONTENT_FIELD: 'new-announcement-update-content-field',
      DELETE_BUTTON: 'new-announcement-update-delete-button',
      PUBLISH_BUTTON: 'new-announcement-update-publish-button',
      TITLE_FIELD: 'new-announcement-update-title-field',
    },
    ANNOUNCEMENTS_TABLE: {
      ACTIONS: {
        DELETE: 'news-announcements-table-actions-delete',
        EDIT: 'news-announcements-table-actions-edit',
        MENU: 'news-announcements-table-actions-menu',
      },
      AUTHOR: 'news-announcements-table-author',
      ENDS_ON: 'news-announcements-table-ends-on',
      PUBLISHED_ON: 'news-announcements-table-published-on',
      STATUS: 'news-announcements-table-status',
      TABLE: 'news-announcements-table',
      TITLE: 'news-announcements-table-title',
      TYPE: 'news-announcements-table-type',
    },
    ARTICLE_UPDATE: {
      CONTENT_FIELD: 'new-article-update-content-field',
      DELETE_BUTTON: 'new-article-update-delete-button',
      PUBLISH_BUTTON: 'new-article-update-publish-button',
      TITLE_FIELD: 'new-article-update-title-field',
    },
    ARTICLES_TABLE: {
      ACTIONS: {
        DELETE: 'news-articles-table-actions-delete',
        EDIT: 'news-articles-table-actions-edit',
        MENU: 'news-articles-table-actions-menu',
      },
      AUTHOR: 'news-articles-table-author',
      ENDS_ON: 'news-articles-table-ends-on',
      PUBLISHED_ON: 'news-articles-table-published-on',
      STATUS: 'news-articles-table-status',
      TABLE: 'news-articles-table',
      TITLE: 'news-articles-table-title',
    },
    NEWS_CREATE_BUTTON: 'news-news-create-button',
  },
  PICKUP: {
    OVERVIEW: {
      CREATE_BUTTON: 'pickup-overview-create-button',
      TABLE: {
        ACCOUNT_MANAGER: 'pickup-overview-table-account-manager',
        CONTAINER_NUMBER: 'pickup-overview-table-container-number',
        CONTRACT_ITEM: 'pickup-overview-table-contract-item',
        CONTRACT_NUMBER: 'pickup-overview-table-contract-number',
        COST_CENTER: 'pickup-overview-table-cost-center',
        CUSTOMER_NAME: 'pickup-overview-table-customer-name',
        DATE_OF_REQUEST: 'pickup-overview-table-date-of-request',
        DISPOSAL_CERTIFICATE_NUMBER: 'pickup-overview-table-disposal-certificate-number',
        EWC: 'pickup-overview-table-ewc',
        MATERIAL_ANALYSIS: 'pickup-overview-table-material-analysis',
        NAME_INSTALLATION: 'pickup-overview-table-name-installation',
        NAME_OF_APPLICANT: 'pickup-overview-table-name-of-applicant',
        ORDER_NUMBER: 'pickup-overview-table-order-number',
        PICK_UP_ADDRESS_NAME: 'pickup-overview-table-pick-up-address-name',
        REQUEST_NUMBER: 'pickup-overview-table-request-number',
        STATUS_COLUMN: 'pickup-overview-table-status-column',
        TFS_NUMBER: 'pickup-overview-table-tfs-number',
        TRANSPORT_MODE: 'pickup-overview-table-transport-mode',
        TREATMENT_CENTER_NAME: 'pickup-overview-table-treatment-center-name',
        WASTE_MATERIAL: 'pickup-overview-table-waste-material',
        WASTE_PRODUCER_NAME: 'pickup-overview-table-waste-producer-name',
      },
    },
    UPDATE: {
      ADMINISTRATION: {
        ACCOUNT_MANAGER_SELECT: 'pickup-update-administration-account-manager-select',
        CUSTOMER_REFERENCE_INPUT: 'pickup-update-administration-customer-reference-input',
        REMARKS_TEXTAREA: 'pickup-update-administration-remarks-textarea',
      },
      CUSTOMER_AND_LOCATION: {
        CUSTOMER_AUTOCOMPLETE: 'pickup-update-customer-and-location-customer-autocomplete',
        PICKUP_ADDRESS_AUTOCOMPLETE: 'pickup-update-customer-and-location-pickup-address-autocomplete',
        WASTE_PRODUCER_AUTOCOMPLETE: 'pickup-update-customer-and-location-waste-producer-autocomplete',
      },
      DETAILS: {
        CONTAINER_NUMBER_INPUT: 'pickup-update-details-container-number-input',
        COST_CENTER_INPUT: 'pickup-update-details-cost-center-input',
        ESTIMATED_VOLUME_INPUT: 'pickup-update-details-estimated-volume-input',
        PACKAGING_TYPE_SELECT: 'pickup-update-details-packaging-type-select',
        PO_NUMBER_INPUT: 'pickup-update-details-po-number-input',
        QUANTITY_CONTAINERS_INPUT: 'pickup-update-details-quantity-containers-input',
        TFS_NUMBER_INPUT: 'pickup-update-details-tfs-number-input',
        UN_NUMBER_SELECT: 'pickup-update-details-un-number-select',
        WASTE_MATERIAL_INPUT: 'pickup-update-details-waste-material-input',
      },
      FORM: {
        CANCEL_BUTTON: 'pickup-update-form-cancel-button',
        SAVE_BUTTON: 'pickup-update-form-save-button',
        SUBMIT_BUTTON: 'pickup-update-form-submit-button',
      },
      PACKAGING: {
        PACKAGING_TYPE_SELECT: 'pickup-update-packaging-type-select',
        QUANTITY_CONTAINERS_INPUT: 'pickup-update-packaging-quantity-containers-input',
        RETURN_PACKAGING_CHECKBOX: 'pickup-update-packaging-return-packaging-checkbox',
      },
      PACKAGING_REQUEST: {
        ADD_MATERIAL_BUTTON: 'pickup-update-packaging-request-add-material-button',
        MATERIAL_SELECT: 'pickup-update-packaging-request-material-select',
        QUANTITY_INPUT: 'pickup-update-packaging-request-quantity-input',
        REMARKS_TEXTAREA: 'pickup-update-packaging-request-remarks-textarea',
      },
      PLANNING: {
        CONFIRMED_DATE_INPUT: 'pickup-update-planning-confirmed-date-input',
        END_DATE_INPUT: 'pickup-update-planning-end-date-input',
        START_DATE_INPUT: 'pickup-update-planning-start-date-input',
        URGENCY_SELECT: 'pickup-update-planning-urgency-select',
      },
      TRANSPORT: {
        DANGEROUS_GOODS_CHECKBOX: 'pickup-update-transport-dangerous-goods-checkbox',
        TRANSPORT_BY_INDAVER_CHECKBOX: 'pickup-update-transport-by-indaver-checkbox',
        TRANSPORT_MODE_SELECT: 'pickup-update-transport-mode-select',
        UN_NUMBER_SELECT: 'pickup-update-transport-un-number-select',
      },
      WASTE: {
        ADD_SELECTED_BUTTON: 'pickup-update-waste-add-selected-button',
        CONTRACT_LINE_TABLE: 'pickup-update-waste-contract-line-table',
        SELECTED_CONTRACT_LINES_TABLE: 'pickup-update-waste-selected-contract-lines-table',
      },
    },
  },
  SETTINGS: {
    CONTACTS: {
      CREATE_BUTTON: 'settings-contacts-create-button',
      CREATE_DIALOG: 'settings-contacts-create-dialog',
      FORM: {
        CONFIRM_DELETE_BUTTON: 'settings-contacts-form-confirm-delete-button',
        DELETE_BUTTON: 'settings-contacts-form-delete-button',
        EMAIL_TEXT_FIELD: 'settings-contacts-form-email-text-field',
        FIRST_NAME_TEXT_FIELD: 'settings-contacts-form-first-name-text-field',
        LAST_NAME_TEXT_FIELD: 'settings-contacts-form-last-name-text-field',
        SUBMIT_BUTTON: 'settings-contacts-form-submit-button',
      },
      LIST_ITEM: {
        EDIT_BUTTON: 'settings-contacts-list-item-edit-button',
        EMAIL_TEXT: 'settings-contacts-list-item-email-text',
        FULL_NAME_TEXT: 'settings-contacts-full-name-text',
      },
      UPDATE_DIALOG: 'settings-contacts-update-dialog',
    },
    MENU: { CONTACTS_MENU_ITEM: 'settings-menu-contacts-menu-item' },
  },
  SHARED: {
    CONFIRM_DIALOG: {
      CONFIRM_BUTTON: 'shared-confirm-dialog-confirm-button',
      DIALOG: 'shared-confirm-dialog',
    },
    MALFORMED_RESPONSE_TOAST: 'shared-malformed-response-toast',
    WIZARD_FORM: { CONTROLS: { NEXT_BUTTON: 'shared-wizard-form-controls-next-button' } },
  },
  USERS: {
    CREATE: { SUCCESS_TOAST: 'users-create-success-toast' },
    DETAIL: { EDIT_BUTTON: 'users-detail-edit-button' },
    FORM: {
      BIRTH_DATE_INPUT: 'users-form-birth-date-input',
      FIRST_NAME_INPUT: 'users-form-first-name-input',
      LAST_NAME_INPUT: 'users-form-last-name-input',
      SUBMIT_BUTTON: 'users-form-submit-button',
      TITLE: 'users-form-title',
    },
    OVERVIEW: {
      CREATE_BUTTON: 'users-overview-create-button',
      TABLE: {
        CONTAINER: 'users-overview-table-container',
        EMAIL: 'users-overview-table-email',
      },
      TITLE: 'users-overview-title',
    },
    UPDATE: { SUCCESS_TOAST: 'users-update-success-toast' },
  },
  WASTE_INQUIRY: {
    OVERVIEW: {
      CREATE_BUTTON: 'waste-inquiry-overview-create-button',
      TABLE: {
        GO_TO_DETAIL_ACTION: 'waste-inquiry-overview-table-go-to-detail-action',
        STATUS_COLUMN: 'waste-inquiry-overview-table-status-column',
        WASTE_PRODUCER_COLUMN: 'waste-inquiry-overview-table-waste-producer-column',
      },
    },
    UPDATE: {
      COLLECTION_REMARKS_TEXT_AREA: 'waste-inquiry-update-collection-remarks-text-area',
      COLLECTION_REQUIREMENTS_RADIO_GROUP_ITEM: 'waste-inquiry-update-collection-requirements-radio-group-item',
      COMPOSITION_COMPONENT: {
        MAX_WEIGHT_NUMBER_FIELD: 'waste-inquiry-update-composition-component-max-weight-number-field',
        MIN_WEIGHT_NUMBER_FIELD: 'waste-inquiry-update-composition-component-min-weight-number-field',
        NAME_TEXT_FIELD: 'waste-inquiry-update-composition-component-name-text-field',
      },
      COMPOSITION_NO_SDS_CHECKBOX: 'waste-inquiry-update-composition-no-sds-checkbox',
      CONTAINER_LOADING_TYPE_RADIO_GROUP_ITEM: 'waste-inquiry-update-container-loading-type-radio-group-item',
      CUSTOMER_RADIO_GROUP_ITEM: 'waste-inquiry-update-customer-radio-group-item',
      DESCRIPTION_TEXT_FIELD: 'waste-inquiry-update-description-text-field',
      DISCHARGE_FREQUENCY_RADIO_GROUP_ITEM: 'waste-inquiry-update-discharge-frequency-radio-group-item',
      EWC_LEVEL_1_OPTION: 'waste-inquiry-update-ewc-level-1-option',
      EWC_LEVEL_1_SELECT: 'waste-inquiry-update-ewc-level-1-select',
      EWC_LEVEL_2_OPTION: 'waste-inquiry-update-ewc-level-2-option',
      EWC_LEVEL_2_SELECT: 'waste-inquiry-update-ewc-level-2-select',
      EWC_LEVEL_3_OPTION: 'waste-inquiry-update-ewc-level-3-option',
      EWC_LEVEL_3_SELECT: 'waste-inquiry-update-ewc-level-3-select',
      EXPECTED_PER_COLLECTION_QUANTITY_NUMBER_FIELD: 'waste-inquiry-update-expected-per-collection-quantity-number-field',
      EXPECTED_YEARLY_VOLUME_AMOUNT_NUMBER_FIELD: 'waste-inquiry-update-expected-yearly-volume-amount-number-field',
      IS_TANK_OWNED_BY_CUSTOMER_RADIO_GROUP_ITEM: 'waste-inquiry-update-is-tank-owned-by-customer-radio-group-item',
      IS_TRANSPORT_ARRANGED_BY_INDAVER_RADIO_GROUP_ITEM: 'waste-inquiry-update-is-transport-arranged-by-indaver-radio-group-item',
      LEGISLATION_CHECKBOX_ITEM: 'waste-inquiry-update-legislation-checkbox-item',
      LEGISLATION_REMARKS_TEXT_AREA: 'waste-inquiry-update-legislation-remarks-text-area',
      LOADING_METHOD_RADIO_GROUP_ITEM: 'waste-inquiry-update-loading-method-radio-group-item',
      NAME_TEXT_FIELD: 'waste-inquiry-update-name-text-field',
      NO_ANALYSIS_REPORT_CHECKBOX: 'waste-inquiry-update-no-analysis-report-checkbox',
      PACKAGING_TYPE_RADIO_GROUP_ITEM: 'waste-inquiry-update-packaging-type-radio-group-item',
      PICK_UP_ADDRESS_RADIO_GROUP_ITEM: 'waste-inquiry-update-pick-up-address-radio-group-item',
      PROPERTY_CHECKBOX_ITEM: 'waste-inquiry-update-property-checkbox-item',
      PROPERTY_REMARKS_TEXT_AREA: 'waste-inquiry-update-property-remarks-text-area',
      REGULATED_TRANSPORT_RADIO_GROUP_ITEM: 'waste-inquiry-update-regulated-transport-radio-group-item',
      RETURN_TO_OVERVIEW_BUTTON: 'waste-inquiry-update-return-to-overview-button',
      SPECIFIC_GRAVITY_TEXT_FIELD: 'waste-inquiry-update-specific-gravity-text-field',
      STATE_OF_MATTER: {
        OPTION: 'waste-inquiry-update-state-of-matter-option',
        SELECT: 'waste-inquiry-update-state-of-matter-select',
      },
      STORED_IN_RADIO_GROUP_ITEM: 'waste-inquiry-update-stored-in-radio-group-item',
      SUBMIT_DIALOG: {
        CONTACT: {
          EMAIL_TEXT_FIELD: 'waste-inquiry-update-email-text-field',
          FIRST_NAME_TEXT_FIELD: 'waste-inquiry-update-first-name-text-field',
          LAST_NAME_TEXT_FIELD: 'waste-inquiry-update-last-name-text-field',
        },
        REMARKS_TEXT_AREA: 'waste-inquiry-update-submit-dialog-remarks-text-area',
        SEND_COPY_TO_SELF_CHECKBOX: 'waste-inquiry-update-submit-dialog-send-copy-to-self-checkbox',
        SUBMIT_BUTTON: 'waste-inquiry-update-submit-dialog-submit-button',
      },
      TEMPERATURE: {
        OPTION: 'waste-inquiry-update-temperature-option',
        SELECT: 'waste-inquiry-update-temperature-select',
      },
      TRANSPORT_IN_RADIO_GROUP_ITEM: 'waste-inquiry-update-transport-in-radio-group-item',
      TRANSPORT_PACKAGING_ITEM: {
        SIZE_TEXT_FIELD: 'waste-inquiry-update-transport-packaging-size-text-field',
        TYPE: {
          OPTION: 'waste-inquiry-update-transport-packaging-type-option',
          SELECT: 'waste-inquiry-update-transport-packaging-type-select',
        },
        WEIGHT_NUMBER_FIELD: 'waste-inquiry-update-transport-packaging-weight-number-field',
      },
      TRANSPORT_TYPE_RADIO_GROUP_ITEM: 'waste-inquiry-update-transport-type-radio-group-item',
      TRANSPORT_UN_NUMBER: {
        PACKAGING_GROUP_OPTION: 'waste-inquiry-update-transport-packaging-group-option',
        UN_NUMBER_OPTION: 'waste-inquiry-update-transport-un-number-option',
        UN_NUMBER_SELECT: 'waste-inquiry-update-transport-un-number-select',
      },
      WASTE_FLASH_POINT_RADIO_GROUP_ITEM: 'waste-inquiry-update-waste-flash-point-radio-group-item',
      WASTE_LOADING_TYPE_RADIO_GROUP_ITEM: 'waste-inquiry-update-waste-loading-type-radio-group-item',
      WASTE_PH_RADIO_GROUP_ITEM: 'waste-inquiry-update-waste-ph-radio-group-item',
      WASTE_PRODUCER_RADIO_GROUP_ITEM: 'waste-inquiry-update-waste-producer-radio-group-item',
      WASTE_SAMPLE_RADIO_GROUP_ITEM: 'waste-inquiry-update-waste-sample-radio-group-item',
    },
  },
} as const
