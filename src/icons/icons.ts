import type { Component } from 'vue'

export const icons = {
  isoTank: import('./IsoTankIcon.vue'),
  alertTriangle: import('./AlertTriangleIcon.vue'),
  announcement: import('./AnnouncementIcon.vue'),
  appleLogo: import('./AppleLogoIcon.vue'),
  arrowDown: import('./ArrowDownIcon.vue'),
  arrowLeft: import('./ArrowLeftIcon.vue'),
  arrowRight: import('./ArrowRightIcon.vue'),
  arrowUp: import('./ArrowUpIcon.vue'),
  asf: import('./AsfIcon.vue'),
  asp: import('./AspIcon.vue'),
  barChartSquare: import('./BarChartSquareIcon.vue'),
  bell: import('./BellIcon.vue'),
  bigBag: import('./BigBagIcon.vue'),
  bold: import('./BoldIcon.vue'),
  bookOpen: import('./BookOpenIcon.vue'),
  bottomLeftCorner: import('./BottomLeftCornerIcon.vue'),
  bottomRightCorner: import('./BottomRightCornerIcon.vue'),
  building: import('./BuildingIcon.vue'),
  bulk: import('./BulkIcon.vue'),
  calendarCheck: import('./CalendarCheckIcon.vue'),
  calendarPlus: import('./CalendarPlusIcon.vue'),
  cardboardBox: import('./CardboardBoxIcon.vue'),
  certificate: import('./CertificateIcon.vue'),
  chain: import('./ChainIcon.vue'),
  check: import('./CheckIcon.vue'),
  checkCircle: import('./CheckCircleIcon.vue'),
  checkVerified: import('./CheckVerifiedIcon.vue'),
  chevronDown: import('./ChevronDownIcon.vue'),
  chevronLeft: import('./ChevronLeftIcon.vue'),
  chevronRight: import('./ChevronRightIcon.vue'),
  chevronUp: import('./ChevronUpIcon.vue'),
  clipboard: import('./ClipboardIcon.vue'),
  closeCircle: import('./CloseCircleIcon.vue'),
  container: import('./ContainerIcon.vue'),
  copy: import('./CopyIcon.vue'),
  curtainSideTruck: import('./CurtainSideTruckIcon.vue'),
  dashboard: import('./DashboardIcon.vue'),
  download: import('./DownloadIcon.vue'),
  drum: import('./DrumIcon.vue'),
  edit: import('./EditIcon.vue'),
  ewastra: import('./EwastraIcon.vue'),
  expand: import('./ExpandIcon.vue'),
  externalLink: import('./ExternalLinkIcon.vue'),
  file: import('./FileIcon.vue'),
  fileAttachement: import('./FileAttachementIcon.vue'),
  filterLines: import('./FilterLinesIcon.vue'),
  googleLogo: import('./GoogleLogoIcon.vue'),
  hook: import('./HookIcon.vue'),
  hookSystem: import('./HookSystemIcon.vue'),
  ibc: import('./IbcIcon.vue'),
  imagePlus: import('./ImagePlusIcon.vue'),
  impersonate: import('./ImpersonateIcon.vue'),
  infoCircle: import('./InfoCircleIcon.vue'),
  italic: import('./ItalicIcon.vue'),
  language: import('./LanguageIcon.vue'),
  link: import('./LinkIcon.vue'),
  locationPin: import('./LocationPinIcon.vue'),
  lock: import('./LockIcon.vue'),
  logout: import('./LogoutIcon.vue'),
  markerPin: import('./MarkerPinIcon.vue'),
  menu: import('./MenuIcon.vue'),
  microscope: import('./MicroscopeIcon.vue'),
  news: import('./NewsIcon.vue'),
  nonRegulatedTransport: import('./NonRegulatedTransportIcon.vue'),
  oversizedDrum: import('./OversizedDrumIcon.vue'),
  package: import('./PackageIcon.vue'),
  packaged: import('./PackagedIcon.vue'),
  packagePlus: import('./PackagePlusIcon.vue'),
  paperClip: import('./PaperClipIcon.vue'),
  pdf: import('./PdfIcon.vue'),
  pencil: import('./PencilIcon.vue'),
  permit: import('./PermitsIcon.vue'),
  plasticDrum: import('./PlasticDrumIcon.vue'),
  printer: import('./PrinterIcon.vue'),
  receipt: import('./ReceiptIcon.vue'),
  refresh: import('./RefreshIcon.vue'),
  regularPhase: import('./RegularPhaseIcon.vue'),
  regularStream: import('./RegularStreamIcon.vue'),
  regulatedTransport: import('./RegulatedTransportIcon.vue'),
  relFelTruck: import('./RelFelTruckIcon.vue'),
  reporting: import('./ReportingIcon.vue'),
  send: import('./SendIcon.vue'),
  settings: import('./SettingsIcon.vue'),
  settingsSlider: import('./SettingsSliderIcon.vue'),
  shield: import('./ShieldIcon.vue'),
  shrink: import('./ShrinkIcon.vue'),
  skip: import('./SkipIcon.vue'),
  stars: import('./StarsIcon.vue'),
  storageTank: import('./StorageTankIcon.vue'),
  switchHorizontal: import('./SwitchHorizontalIcon.vue'),
  switchVertical: import('./SwitchVerticalIcon.vue'),
  tankContainer: import('./TankContainerIcon.vue'),
  tankTrailer: import('./TankTrailerIcon.vue'),
  threeDots: import('./ThreeDotsIcon.vue'),
  threeLayerStack: import('./ThreeLayerStackIcon.vue'),
  tipperTruck: import('./TipperTruckIcon.vue'),
  trash: import('./TrashIcon.vue'),
  truck: import('./TruckIcon.vue'),
  twoLayerStack: import('./TwoLayerStackIcon.vue'),
  underline: import('./UnderlineIcon.vue'),
  unknownRegulatedTransport: import('./UnknownRegulatedTransportIcon.vue'),
  upload: import('./UploadIcon.vue'),
  user: import('./UserIcon.vue'),
  userEdit: import('./UserEditIcon.vue'),
  users: import('./UsersIcon.vue'),
  vacuumTanker: import('./VacuumTankerIcon.vue'),
  wasteRequest: import('./WasteRequestIcon.vue'),
  onceOffPhase: import('./OnceOffPhaseIcon.vue'),
  onceOffStream: import('./OnceOffStreamIcon.vue'),
} satisfies Record<string, Component>

type CustomIcons = {
  [K in keyof typeof icons]: Component
}

declare module '@wisemen/vue-core-components' {
  interface Icons extends CustomIcons { }
}

declare module '@wisemen/vue-core-components' {
  interface Icons extends CustomIcons { }
}
