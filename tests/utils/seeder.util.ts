import sign from 'jwt-encode'
import type { http as httpType } from 'msw'
import { HttpResponse } from 'msw'
import type { MockServiceWorker } from 'playwright-msw'

import { PreferenceDtoBuilder } from '@/models/preference/preferenceDto.builder.ts'
import { UserDetailDtoBuilder } from '@/models/user/detail/userDetailDto.builder.ts'
import { PaginationUtil } from '@/utils/pagination.util.ts'

interface Token {
  exp: number
}

function encodeJwt(token: Token): string {
  return sign(token, 'secret')
}

export class SeederUtil {
  static async init(http: typeof httpType, worker: MockServiceWorker): Promise<void> {
    const USER = new UserDetailDtoBuilder()
      .withFirstName('John')
      .withLastName('Doe')
      .build()

    await worker.use(
      // Authentication endpoints
      http.post('*/oauth/v2/token', () => {
        return HttpResponse.json({
          access_token: encodeJwt({ exp: new Date(new Date().getFullYear() + 1, 0, 1).getTime() }),
          expires_in: 43_199,
          id_token: encodeJwt({ exp: new Date(new Date().getFullYear() + 1, 0, 1).getTime() }),
          refresh_token: 'refresh_token',
          token_type: 'Bearer',
        })
      }),
      http.get('*/oauth/v2/authorize', () => {
        return HttpResponse.json({})
      }),
      http.get('*/api/v1/users/me', () => {
        return HttpResponse.json(USER)
      }),
      http.get('*/api/v1/me/ui-preferences', () => {
        const data = new PreferenceDtoBuilder().build()

        return HttpResponse.json(data)
      }),
      // Common API endpoints that need global mocking
      http.get('*/api/v1/customers', () => {
        return HttpResponse.json(PaginationUtil.toDto([]))
      }),
      http.get('*/api/v1/waste-producers*', () => {
        return HttpResponse.json(PaginationUtil.toDto([]))
      }),
      http.get('*/api/v1/suggested-customers', () => {
        return HttpResponse.json({ items: [] })
      }),
      http.get('*/api/v1/suggested-waste-producers', () => {
        return HttpResponse.json({ items: [] })
      }),
      http.get('*/api/v1/suggested-pick-up-addresses', () => {
        return HttpResponse.json({ items: [] })
      }),
      http.get('*/api/v1/ewc-codes', () => {
        return HttpResponse.json(PaginationUtil.toDto([]))
      }),
    )
  }
}
