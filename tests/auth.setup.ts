import path from 'node:path'
import { fileURLToPath } from 'node:url'

import { HttpResponse } from 'msw'

import { TEST_ID } from '@/constants/testId.constant.ts'
import { PreferenceDtoBuilder } from '@/models/preference/preferenceDto.builder.ts'
import { UserDetailDtoBuilder } from '@/models/user/detail/userDetailDto.builder.ts'
import { PaginationUtil } from '@/utils/pagination.util.ts'
import type { TestFixtures } from '@@/base.fixture'
import {
  expect,
  test,
} from '@@/base.fixture'

const __filename = fileURLToPath(import.meta.url) // get the resolved path to the file
const __dirname = path.dirname(__filename) // get the name of the directory
const authFile = path.join(__dirname, '../tests/.auth/user.json')

test('should authenticate the user', async ({
  http,
  page,
  worker,
}: TestFixtures) => {
  const USER = new UserDetailDtoBuilder()
    .withFirstName('John')
    .withLastName('Doe')
    .build()

  await worker.use(
    http.get('*/oauth/v2/authorize', () => {
      return HttpResponse.json({})
    }),
    http.get('*/api/v1/users/me', () => {
      return HttpResponse.json(USER)
    }),
    http.get(`*/api/v1/me/ui-preferences`, () => {
      const data = new PreferenceDtoBuilder().build()

      return HttpResponse.json(data)
    }),
    http.get('*/api/v1/customers', () => {
      return HttpResponse.json(PaginationUtil.toDto([]))
    }),
  )

  await page.route('**/oauth/v2/authorize**', async (route) => {
    await route.fulfill({
      body: '',
      headers: { location: 'http://localhost:3000/auth/callback?code=123' },
      status: 302,
    })
  })

  await page.goto('/auth/login')

  await page.getByTestId(TEST_ID.AUTH.LOGIN.SUBMIT_BUTTON).click()

  await page.waitForLoadState()

  await page.getByTestId(TEST_ID.APP_PAGE.USER_BUTTON).click()

  await expect(page.getByTestId(TEST_ID.APP_PAGE.USER_NAME)).toContainText('John Doe')

  await page.context().storageState({ path: authFile })
})
