import { HttpResponse } from 'msw'

import { TEST_ID } from '@/constants/testId.constant'
import { CustomerIndexDtoBuilder } from '@/models/customer/index/customerIndexDto.builder'
import { DynamicTableColumnIndexDtoBuilder } from '@/models/dynamic-table/column/dynamicTableColumnIndexDto.builder'
import { DynamicTableViewIndexDtoBuilder } from '@/models/dynamic-table/view/dynamicTableViewIndexDto.builder'
import { EwcCodeIndexDtoBuilder } from '@/models/ewc/index/ewcCodeIndexDto.builder'
import { PickUpAddressIndexDtoBuilder } from '@/models/pick-up-address/index/pickUpAddressIndexDto.builder'
import { WasteInquiryDetailDtoBuilder } from '@/models/waste-inquiry/detail/wasteInquiryDetailDto.builder'
import { WasteInquirySubmitResponseDtoBuilder } from '@/models/waste-inquiry/update/submit/wasteInquirySubmitResponseDto.builder'
import { WasteProducerIndexDtoBuilder } from '@/models/waste-producer/index/wasteProducerIndexDto.builder'
import { PaginationUtil } from '@/utils/pagination.util'
import { UuidUtil } from '@/utils/uuid.util'
import type { TestFixtures } from '@@/base.fixture'
import {
  expect,
  test,
} from '@@/base.fixture'
import { SeederUtil } from '@@/utils/seeder.util'

test('it should successfully submit a prefilled inquiry with extra information', async ({
  http,
  page,
  worker,
}: TestFixtures) => {
  const columnUuid = UuidUtil.getRandom()
  const WASTE_INQUIRY_TABLE_VIEW = new DynamicTableViewIndexDtoBuilder().withColumnUuid(columnUuid).build()
  const WASTE_INQUIRY_TABLE_COLUMNS = new DynamicTableColumnIndexDtoBuilder().withUuid(columnUuid).build()
  const WASTE_INQUIRY_DETAIL_1 = new WasteInquiryDetailDtoBuilder()
    .withData()
    .build()
  const WASTE_INQUIRY_SUBMIT_RESPONSE = new WasteInquirySubmitResponseDtoBuilder().build()

  await SeederUtil.init(http, worker)

  await worker.use(
    http.patch(`*/api/v1/waste-inquiries/${WASTE_INQUIRY_DETAIL_1.uuid}`, () => {
      return HttpResponse.json(WASTE_INQUIRY_DETAIL_1)
    }),
    http.post(`*/api/v1/waste-inquiries/${WASTE_INQUIRY_DETAIL_1.uuid}/submit`, () => {
      return HttpResponse.json(WASTE_INQUIRY_SUBMIT_RESPONSE)
    }),
    http.get(`*/api/v1/suggested-customers`, () => {
      return HttpResponse.json({
        items: [
          new CustomerIndexDtoBuilder().build(),
        ],
      })
    }),
    http.get(`*/api/v1/suggested-waste-producers`, () => {
      return HttpResponse.json({
        items: [
          new WasteProducerIndexDtoBuilder().build(),
        ],
      })
    }),
    http.get(`*/api/v1/suggested-pick-up-addresses`, () => {
      return HttpResponse.json({
        items: [
          new PickUpAddressIndexDtoBuilder().build(),
        ],
      })
    }),
    http.get(`*/api/v1/ewc-codes`, () => {
      return HttpResponse.json(PaginationUtil.toDto([
        new EwcCodeIndexDtoBuilder().withCode('01').build(),
        new EwcCodeIndexDtoBuilder().withCode('01 01').build(),
        new EwcCodeIndexDtoBuilder().withCode('01 01 01').build(),
      ]))
    }),
    http.get(`*/api/v1/waste-inquiries/${WASTE_INQUIRY_DETAIL_1.uuid}`, () => {
      return HttpResponse.json(WASTE_INQUIRY_DETAIL_1)
    }),
    http.get(`*/api/v1/dynamic-tables/waste-inquiry/views`, () => {
      return HttpResponse.json(PaginationUtil.toDto([
        WASTE_INQUIRY_TABLE_VIEW,
      ]))
    }),
    http.get(`*/api/v1/dynamic-tables/waste-inquiry/default-view`, () => {
      return HttpResponse.json(WASTE_INQUIRY_TABLE_VIEW)
    }),
    http.get(`*/api/v1/dynamic-tables/waste-inquiry/columns`, () => {
      return HttpResponse.json({
        items: [
          WASTE_INQUIRY_TABLE_COLUMNS,
        ],
      })
    }),
  )

  await page.goto(`/waste-inquiries/${WASTE_INQUIRY_DETAIL_1.uuid}/edit`)

  await page.getByTestId(TEST_ID.SHARED.WIZARD_FORM.CONTROLS.NEXT_BUTTON).click()
  await page.getByTestId(TEST_ID.SHARED.WIZARD_FORM.CONTROLS.NEXT_BUTTON).click()
  await page.getByTestId(TEST_ID.SHARED.WIZARD_FORM.CONTROLS.NEXT_BUTTON).click()
  await page.getByTestId(TEST_ID.SHARED.WIZARD_FORM.CONTROLS.NEXT_BUTTON).click()
  await page.getByTestId(TEST_ID.SHARED.WIZARD_FORM.CONTROLS.NEXT_BUTTON).click()
  await page.getByTestId(TEST_ID.SHARED.WIZARD_FORM.CONTROLS.NEXT_BUTTON).click()
  await page.getByTestId(TEST_ID.SHARED.WIZARD_FORM.CONTROLS.NEXT_BUTTON).click()

  await page.getByTestId(TEST_ID.WASTE_INQUIRY.UPDATE.SUBMIT_DIALOG.REMARKS_TEXT_AREA).fill('Remarks')
  await page.getByTestId(TEST_ID.WASTE_INQUIRY.UPDATE.SUBMIT_DIALOG.CONTACT.EMAIL_TEXT_FIELD).fill('<EMAIL>')
  await page.getByTestId(TEST_ID.WASTE_INQUIRY.UPDATE.SUBMIT_DIALOG.CONTACT.FIRST_NAME_TEXT_FIELD).fill('First Name')
  await page.getByTestId(TEST_ID.WASTE_INQUIRY.UPDATE.SUBMIT_DIALOG.CONTACT.LAST_NAME_TEXT_FIELD).fill('Last Name')
  await page.getByTestId(TEST_ID.WASTE_INQUIRY.UPDATE.SUBMIT_DIALOG.SUBMIT_BUTTON).click()

  await page.getByTestId(TEST_ID.WASTE_INQUIRY.UPDATE.RETURN_TO_OVERVIEW_BUTTON).click()

  await expect(page).toHaveURL(/\/waste-inquiries(\?.*)?/)
})
