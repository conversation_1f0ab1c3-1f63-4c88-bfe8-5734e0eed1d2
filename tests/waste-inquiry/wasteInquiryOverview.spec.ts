import { HttpResponse } from 'msw'

import { TEST_ID } from '@/constants/testId.constant'
import { DynamicTableColumnIndexDtoBuilder } from '@/models/dynamic-table/column/dynamicTableColumnIndexDto.builder'
import { DynamicTableViewIndexDtoBuilder } from '@/models/dynamic-table/view/dynamicTableViewIndexDto.builder'
import { EwcCodeIndexDtoBuilder } from '@/models/ewc/index/ewcCodeIndexDto.builder'
import { WasteInquiryDetailDtoBuilder } from '@/models/waste-inquiry/detail/wasteInquiryDetailDto.builder'
import { WasteInquiryIndexDtoBuilder } from '@/models/waste-inquiry/index/wasteInquiryIndexDto.builder'
import { WasteInquirySapDetailDtoBuilder } from '@/models/waste-inquiry/sap-detail/wasteInquirySapDetailDto.builder'
import { PaginationUtil } from '@/utils/pagination.util'
import { UuidUtil } from '@/utils/uuid.util'
import type { TestFixtures } from '@@/base.fixture'
import {
  expect,
  test,
} from '@@/base.fixture'
import { SeederUtil } from '@@/utils/seeder.util'

test('should navigate to update view when creating a new waste request', async ({
  http,
  page,
  worker,
}: TestFixtures) => {
  const columnUuid = UuidUtil.getRandom()
  const WASTE_INQUIRY_DETAIL_1 = new WasteInquiryDetailDtoBuilder().build()
  const WASTE_INQUIRY_TABLE_VIEW = new DynamicTableViewIndexDtoBuilder().withColumnUuid(columnUuid).build()
  const WASTE_INQUIRY_TABLE_COLUMNS = new DynamicTableColumnIndexDtoBuilder().withUuid(columnUuid).build()

  await SeederUtil.init(http, worker)

  await worker.use(
    http.post('*/api/v1/waste-inquiries', () => {
      return HttpResponse.json(WASTE_INQUIRY_DETAIL_1)
    }),
    http.get(`*/api/v1/waste-inquiries/${WASTE_INQUIRY_DETAIL_1.uuid}`, () => {
      return HttpResponse.json(WASTE_INQUIRY_DETAIL_1)
    }),
    http.get(`*/api/v1/suggested-customers`, () => {
      return HttpResponse.json({ items: [] })
    }),
    http.get(`*/api/v1/suggested-waste-producers`, () => {
      return HttpResponse.json({ items: [] })
    }),
    http.get(`*/api/v1/suggested-pick-up-addresses`, () => {
      return HttpResponse.json({ items: [] })
    }),
    http.get(`*/api/v1/dynamic-tables/waste-inquiry/views`, () => {
      return HttpResponse.json(PaginationUtil.toDto([
        WASTE_INQUIRY_TABLE_VIEW,
      ]))
    }),
    http.get(`*/api/v1/dynamic-tables/waste-inquiry/default-view`, () => {
      return HttpResponse.json(WASTE_INQUIRY_TABLE_VIEW)
    }),
    http.get(`*/api/v1/dynamic-tables/waste-inquiry/columns`, () => {
      return HttpResponse.json({
        items: [
          WASTE_INQUIRY_TABLE_COLUMNS,
        ],
      })
    }),
  )

  await page.goto('/waste-inquiries')

  await page.getByTestId(TEST_ID.WASTE_INQUIRY.OVERVIEW.CREATE_BUTTON).click()

  await expect(page).toHaveURL(`/waste-inquiries/${WASTE_INQUIRY_DETAIL_1.uuid}/edit`)
})

test('should show data in table based on the columns in the dynamic view', async ({
  http,
  page,
  worker,
}: TestFixtures) => {
  const columnUuid = UuidUtil.getRandom()
  const WASTE_INQUIRY_INDEX = new WasteInquiryIndexDtoBuilder().withWasteProducerName('Waste producer').build()
  const WASTE_INQUIRY_TABLE_VIEW = new DynamicTableViewIndexDtoBuilder().withColumnUuid(columnUuid).build()
  const WASTE_INQUIRY_TABLE_COLUMNS = new DynamicTableColumnIndexDtoBuilder().withUuid(columnUuid).build()

  await SeederUtil.init(http, worker)

  await worker.use(
    http.get('*/api/v1/waste-inquiries*', () => {
      return HttpResponse.json(PaginationUtil.toDto([
        WASTE_INQUIRY_INDEX,
      ]))
    }),
    http.get(`*/api/v1/dynamic-tables/waste-inquiry/views`, () => {
      return HttpResponse.json(PaginationUtil.toDto([
        WASTE_INQUIRY_TABLE_VIEW,
      ]))
    }),
    http.get(`*/api/v1/dynamic-tables/waste-inquiry/default-view`, () => {
      return HttpResponse.json(WASTE_INQUIRY_TABLE_VIEW)
    }),
    http.get(`*/api/v1/dynamic-tables/waste-inquiry/columns`, () => {
      return HttpResponse.json({
        items: [
          WASTE_INQUIRY_TABLE_COLUMNS,
        ],
      })
    }),
  )

  await page.goto('/waste-inquiries')

  await expect(page.getByRole('cell', { name: 'Waste producer' })).toContainText('Waste producer')
  await expect(page.getByTestId(TEST_ID.WASTE_INQUIRY.OVERVIEW.TABLE.STATUS_COLUMN)).toBeHidden()
})

test('should navigate to detail view selecting a waste request', async ({
  http,
  page,
  worker,
}: TestFixtures) => {
  const columnUuid = UuidUtil.getRandom()
  const WASTE_INQUIRY_INDEX = new WasteInquiryIndexDtoBuilder().withInquiryNumber('123456').build()
  const WASTE_INQUIRY_SAP_DETAIL_1 = new WasteInquirySapDetailDtoBuilder().build()
  const WASTE_INQUIRY_TABLE_VIEW = new DynamicTableViewIndexDtoBuilder().withColumnUuid(columnUuid).build()
  const WASTE_INQUIRY_TABLE_COLUMNS = new DynamicTableColumnIndexDtoBuilder().withUuid(columnUuid).build()

  await SeederUtil.init(http, worker)

  await worker.use(
    http.get('*/api/v1/waste-inquiries*', () => {
      return HttpResponse.json(PaginationUtil.toDto([
        WASTE_INQUIRY_INDEX,
      ]))
    }),
    http.get(`*/api/v1/waste-inquiries/sap/${WASTE_INQUIRY_INDEX.inquiryNumber}`, () => {
      return HttpResponse.json(WASTE_INQUIRY_SAP_DETAIL_1)
    }),
    http.get(`*/api/v1/suggested-customers`, () => {
      return HttpResponse.json({ items: [] })
    }),
    http.get(`*/api/v1/suggested-waste-producers`, () => {
      return HttpResponse.json({ items: [] })
    }),
    http.get(`*/api/v1/suggested-pick-up-addresses`, () => {
      return HttpResponse.json({ items: [] })
    }),
    http.get(`*/api/v1/dynamic-tables/waste-inquiry/views`, () => {
      return HttpResponse.json(PaginationUtil.toDto([
        WASTE_INQUIRY_TABLE_VIEW,
      ]))
    }),
    http.get(`*/api/v1/dynamic-tables/waste-inquiry/default-view`, () => {
      return HttpResponse.json(WASTE_INQUIRY_TABLE_VIEW)
    }),
    http.get(`*/api/v1/dynamic-tables/waste-inquiry/columns`, () => {
      return HttpResponse.json({
        items: [
          WASTE_INQUIRY_TABLE_COLUMNS,
        ],
      })
    }),
    http.get(`*/api/v1/ewc-codes`, () => {
      return HttpResponse.json(PaginationUtil.toDto([
        new EwcCodeIndexDtoBuilder().withCode('01').build(),
        new EwcCodeIndexDtoBuilder().withCode('01 01').build(),
        new EwcCodeIndexDtoBuilder().withCode('01 01 01').build(),
      ]))
    }),
  )

  await page.goto('/waste-inquiries')

  await page.getByTestId(TEST_ID.WASTE_INQUIRY.OVERVIEW.TABLE.GO_TO_DETAIL_ACTION).click()

  await expect(page).toHaveURL(`/waste-inquiries/${WASTE_INQUIRY_INDEX.inquiryNumber}`)
})
