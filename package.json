{"type": "module", "packageManager": "pnpm@9.6.0", "scripts": {"dev": "vite", "development": "vue-tsc --noEmit && vite build --mode development", "staging": "vue-tsc --noEmit && vite build --mode staging", "production": "vue-tsc --noEmit && vite build --mode production", "build": "vite build", "preview": "import-meta-env -x .env.e2e && vite preview", "openapi-ts": "openapi-ts -f openapi.config.ts", "test:unit": "vitest --run", "lint:eslint": "eslint . --fix --cache", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint": "pnpm lint:oxlint && pnpm lint:eslint", "test:e2e": "npx playwright test", "type-check": "vue-tsc --build", "prepare": "husky", "test:coverage": "nyc pnpm test:e2e && nyc report --reporter=html", "precommit": "pnpm lint && pnpm type-check && pnpm test:unit && pnpm build && pnpm test:coverage"}, "dependencies": {"@fontsource-variable/montserrat": "5.2.6", "@formkit/drag-and-drop": "0.5.3", "@hey-api/openapi-ts": "0.83.1", "@marker.io/browser": "0.20.2", "@number-flow/vue": "0.4.8", "@sentry/integrations": "7.114.0", "@sentry/tracing": "7.120.4", "@sentry/vite-plugin": "4.1.1", "@sentry/vue": "10.5.0", "@tailwindcss/vite": "4.1.12", "@tanstack/vue-query": "5.85.5", "@tiptap/extension-link": "3.2.1", "@tiptap/extension-underline": "3.2.1", "@tiptap/starter-kit": "3.2.1", "@tiptap/vue-3": "3.2.1", "@vueuse/core": "13.7.0", "@vueuse/router": "13.7.0", "@wisemen/vue-core-auth": "2.1.0", "@wisemen/vue-core-components": "1.16.0", "clone-deep": "4.0.1", "clsx": "2.1.1", "dayjs": "1.11.13", "formango": "3.0.0-alpha.5", "fuse.js": "7.1.0", "jwt-encode": "1.0.1", "libphonenumber-js": "1.12.13", "maska": "3.2.0", "motion-v": "1.7.0", "pinia": "3.0.3", "reka-ui": "2.4.1", "superjson": "2.2.2", "tailwind-merge": "2.6.0", "vue": "3.5.13", "vue-i18n": "11.1.10", "vue-router": "4.5.0", "zod": "3.24.4"}, "devDependencies": {"@axe-core/playwright": "4.10.2", "@import-meta-env/cli": "0.7.3", "@import-meta-env/unplugin": "0.6.2", "@playwright/test": "1.55.0", "@tailwindcss/typography": "0.5.16", "@tailwindcss/vite": "4.1.4", "@tanstack/vue-query-devtools": "5.85.5", "@tsconfig/node22": "22.0.2", "@types/clone-deep": "4.0.4", "@types/jsdom": "21.1.7", "@types/jwt-encode": "1.0.3", "@types/node": "24.3.0", "@vitejs/plugin-vue": "6.0.1", "@vue/language-server": "3.0.6", "@vue/test-utils": "2.4.6", "@vue/tsconfig": "0.7.0", "@wisemen/eslint-config-vue": "1.2.0-beta.1", "@wisemen/vue-core-generator": "1.2.0", "@wisemen/vue-core-modules": "2.3.7", "@wisemen/vue-core-query": "0.0.43", "autoprefixer": "10.4.21", "chalk": "5.6.0", "dotenv": "17.2.1", "eslint": "9.33.0", "eslint-plugin-playwright": "2.2.2", "husky": "9.1.7", "jsdom": "26.1.0", "msw": "2.10.5", "nyc": "17.1.0", "oxlint": "0.15.13", "playwright-msw": "3.0.1", "postcss": "8.5.6", "sass": "1.90.0", "tailwindcss": "4.1.12", "tsx": "4.20.4", "typescript": "5.9.2", "vite": "7.1.3", "vite-plugin-compression": "0.5.1", "vite-plugin-istanbul": "7.1.0", "vite-plugin-pwa": "1.0.3", "vite-plugin-vue-devtools": "8.0.0", "vitest": "3.2.4", "vue-tsc": "3.0.6", "workbox-window": "7.3.0"}, "msw": {"workerDirectory": ["public"]}}