import path from 'node:path'
import { fileURLToPath } from 'node:url'

import eslintVueConfig from '@wisemen/eslint-config-vue'
import playwright from 'eslint-plugin-playwright'

const __dirname = path.dirname(fileURLToPath(import.meta.url))

export default [
  ...(await eslintVueConfig),
  {
    ignores: [
      'public/mockServiceWorker.js',
      'src/client',
      'tests/.auth',
      'src/locales',
    ],
  },
  {
    ...playwright.configs['flat/recommended'],
    files: [
      'tests/**',
    ],
    languageOptions: { parserOptions: { tsconfigRootDir: path.join(__dirname, 'tests') } },
  },
  {
    rules: {
      '@intlify/vue-i18n/key-format-style': [
        'error',
        'snake_case',
        { splitByDots: true },
      ],
      '@intlify/vue-i18n/no-dynamic-keys': 'off',
      '@intlify/vue-i18n/no-raw-text': 'off',
      'unicorn/no-keyword-prefix': 'off',
      'vuejs-accessibility/label-has-for': 'off',
    },
  },
]
